package cn.ac.picb.vipmap.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ScrnaseqPagaTaskParam {

    @NotBlank
    private String baselineId;

    @NotBlank
    private String taskName;

    @Min(15)
    @Max(200)
    private Integer neighbors;

    @Min(0)
    @Max(1)
    private Double threshold;

    private List<String> mClusters;
}
