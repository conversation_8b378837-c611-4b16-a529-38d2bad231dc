package cn.ac.picb.vipmap.client;


import cn.ac.picb.methychip.client.MethyChipServiceApi;
import cn.ac.picb.vipmap.client.fallback.MethyChipServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/3/1 14:59
 */
@FeignClient(value = "methychip-service", fallback = MethyChipServiceClientFallback.class)
public interface MethyChipServiceClient extends MethyChipServiceApi {
}
