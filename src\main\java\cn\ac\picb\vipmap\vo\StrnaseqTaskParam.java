package cn.ac.picb.vipmap.vo;

import cn.ac.picb.strnaseq.vo.StrnaseqRowVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class StrnaseqTaskParam {

    @NotBlank
    private String taskName;

    @NotNull
    private List<StrnaseqRowVO> fileParam;

    @NotNull
    private String species;

    @NotNull
    private String version;

    private String method;

}
