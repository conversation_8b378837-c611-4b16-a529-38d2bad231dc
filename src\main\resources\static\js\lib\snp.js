/**
 * NGCircos.js is an open source interactive Javascript library which
 * provides an easy way to interactive display biological data on the web.
 * It implements a raster-based SVG visualization using the open source
 * Javascript framework jquery.js. NGCircos.js is multiplatform and works
 * in all major internet browsers (Internet Explorer, Mozilla Firefox,
 * Google Chrome, Safari, Opera). Its speed is determined by the client's
 * hardware and internet browser. For smoothest user experience, we recommend
 * Google Chrome.
 *
 * Source code, tutorial, documentation, and example data are freely available
 * from NGCircos.js website "http://bioinfo.ibp.ac.cn/NGCircos/".
 *
 * <AUTHOR> href="<EMAIL>"><PERSON></a>, <a href="<EMAIL>"><PERSON><PERSON></a>
 * @version 2.0.0
 *
 * @example
 *      var NGCircosGenome = [
 *         ["chr1" , 249250621],
 *         ["chr2" , 243199373]
 *      ];
 *      NGCircos01 = new NGCircos(NGCircosGenome,{
 *         target : "NGCircos",
 *         svgWidth : 900,
 *         svgHeight : 600
 *      });
 *      NGCircos01.draw_genome(NGCircos01.genomeLength);
 *
 **/

var NGCircos;

(function ($) {

    NGCircos = function () {
        var self = this;

        if (arguments.length >= 2) {
            self.argumentsNGCircosSettings = arguments[arguments.length - 1];//配置
            self.argumentsNGCircosGenome = arguments[arguments.length - 2];//染色体数组
            // console.log('arguments', arguments)

            self.SNP = new Array();
            self.SNPConfig = new Array();
            self.SNPGraphData = new Array();
            for (var n = 0; n < arguments.length; n++) {
                var reg = /^SNP/;
                if (reg.test(arguments[n][0])) {
                    self.SNPConfig.push(arguments[n][1]);
                    self.SNP.push(arguments[n][2]);
                    if (arguments[n][3] != undefined) {
                        self.SNPGraphData.push(arguments[n][3])
                    }
                }
            }

            self.LABEL = new Array();
            self.LABELConfig = new Array();
            for (var n = 0; n < arguments.length; n++) {
                var reg = /^LABEL/;
                if (reg.test(arguments[n][0])) {
                    self.LABELConfig.push(arguments[n][1]);
                    self.LABEL.push(arguments[n][2]);
                }
            }

            self.BACKGROUND = new Array();
            self.BACKGROUNDConfig = new Array();
            for (var n = 0; n < arguments.length; n++) {
                var reg = /^BACKGROUND/;
                if (reg.test(arguments[n][0])) {
                    self.BACKGROUNDConfig.push(arguments[n][1]);
                    self.BACKGROUND.push(arguments[n][2]);
                }
            }

            self.TEXT = new Array();
            self.TEXTConfig = new Array();
            for (var n = 0; n < arguments.length; n++) {
                var reg = /^TEXT/;
                if (reg.test(arguments[n][0])) {
                    self.TEXTConfig.push(arguments[n][1]);
                    self.TEXT.push(arguments[n][2]);
                }
            }

            //染色体色块
            self.ARC = new Array();
            self.ARCConfig = new Array();
            for (var n = 0; n < arguments.length; n++) {
                var reg = /^ARC/;
                if (reg.test(arguments[n][0])) {
                    self.ARCConfig.push(arguments[n][1]);
                    self.ARC.push(arguments[n][2]);
                }
            }

        } else {
            document.getElementById(self.settings.target).innerHTML = 'Arguments Error: at least two arguments must supplied.<br>example: new NGCircos([FUSION01,CNV01,SNP01,]NGCircosGenome,{target : "NGCircos",zoom : true})';
        }

        self.settings = {
            "target": "NGCircos",
            "svgWidth": 900,
            "svgHeight": 600,
            //zhec
            "svgClassName": "NGCircos",
            //zhec
            "chrPad": 0.04,
            "innerRadius": 246,
            "outerRadius": 270,
            "zoom": false,
            "compareEvent": false,
            "compareEventGroupGapRate": 0.1,
            "compareEventGroupDistance": 0,
            "genomeFillColor": ["rgb(153,102,0)", "rgb(102,102,0)", "rgb(153,153,30)", "rgb(204,0,0)", "rgb(255,0,0)", "rgb(255,0,204)", "rgb(255,204,204)", "rgb(255,153,0)", "rgb(255,204,0)", "rgb(255,255,0)", "rgb(204,255,0)", "rgb(0,255,0)", "rgb(53,128,0)", "rgb(0,0,204)", "rgb(102,153,255)", "rgb(153,204,255)", "rgb(0,255,255)", "rgb(204,255,255)", "rgb(153,0,204)", "rgb(204,51,255)", "rgb(204,153,255)", "rgb(102,102,102)", "rgb(153,153,153)", "rgb(204,204,204)"],
            //zhec 20190411
            "CNVxlink": false,
            //zhec 20190411
            "CNVMouseEvent": true,
//          "CNVMouseCombinationEvent":false,
            "CNVMouseClickDisplay": false,
            "CNVMouseClickColor": "red",
            "CNVMouseClickArcOpacity": 1.0,
            "CNVMouseClickArcStrokeColor": "#F26223",
            "CNVMouseClickArcStrokeWidth": 0,
            "CNVMouseClickTextFromData": "fourth",   //first,second,third,fourth column
            "CNVMouseClickTextOpacity": 1,
            "CNVMouseClickTextColor": "red",
            "CNVMouseClickTextSize": 8,
            "CNVMouseClickTextPostionX": 0,
            "CNVMouseClickTextPostionY": 0,
            "CNVMouseClickTextDrag": true,
            "CNVMouseDownDisplay": false,
            "CNVMouseDownColor": "green",
            "CNVMouseDownArcOpacity": 1.0,
            "CNVMouseDownArcStrokeColor": "#F26223",
            "CNVMouseDownArcStrokeWidth": 0,
            "CNVMouseEnterDisplay": false,
            "CNVMouseEnterColor": "yellow",
            "CNVMouseEnterArcOpacity": 1.0,
            "CNVMouseEnterArcStrokeColor": "#F26223",
            "CNVMouseEnterArcStrokeWidth": 0,
            "CNVMouseLeaveDisplay": false,
            "CNVMouseLeaveColor": "pink",
            "CNVMouseLeaveArcOpacity": 1.0,
            "CNVMouseLeaveArcStrokeColor": "#F26223",
            "CNVMouseLeaveArcStrokeWidth": 0,
            "CNVMouseMoveDisplay": false,
            "CNVMouseMoveColor": "red",
            "CNVMouseMoveArcOpacity": 1.0,
            "CNVMouseMoveArcStrokeColor": "#F26223",
            "CNVMouseMoveArcStrokeWidth": 0,
            "CNVMouseOutDisplay": false,
            "CNVMouseOutAnimationTime": 500,
            "CNVMouseOutColor": "red",
            "CNVMouseOutArcOpacity": 1.0,
            "CNVMouseOutArcStrokeColor": "red",
            "CNVMouseOutArcStrokeWidth": 0,
            "CNVMouseUpDisplay": false,
            "CNVMouseUpColor": "grey",
            "CNVMouseUpArcOpacity": 1.0,
            "CNVMouseUpArcStrokeColor": "#F26223",
            "CNVMouseUpArcStrokeWidth": 0,
            "CNVMouseOverDisplay": false,
            "CNVMouseOverColor": "red",
            "CNVMouseOverArcOpacity": 1.0,
            "CNVMouseOverArcStrokeColor": "#F26223",
            "CNVMouseOverArcStrokeWidth": 3,
            "CNVMouseOverTooltipsSetting": "style1", //custom, style1
            "CNVMouseOverTooltipsHtml": " ",
            // "CNVMouseOverTooltipsHtml01" : "chr : ",
            // "CNVMouseOverTooltipsHtml02" : "<br>start : ",
            // "CNVMouseOverTooltipsHtml03" : "<br>end : ",
            // "CNVMouseOverTooltipsHtml04" : "<br>value : ",
            // "CNVMouseOverTooltipsHtml05" : "",
            "CNVMouseOverTooltipsPosition": "absolute",
            "CNVMouseOverTooltipsBackgroundColor": "white",
            "CNVMouseOverTooltipsBorderStyle": "solid",
            "CNVMouseOverTooltipsBorderWidth": 0,
            "CNVMouseOverTooltipsPadding": "3px",
            "CNVMouseOverTooltipsBorderRadius": "3px",
            "CNVMouseOverTooltipsOpacity": 0.8,
            "HEATMAPMouseEvent": true,
            "HEATMAPMouseClickDisplay": false,
            "HEATMAPMouseClickColor": "green",            //"none","red"
            "HEATMAPMouseClickOpacity": 1.0,            //"none",1.0
            "HEATMAPMouseClickStrokeColor": "none",  //"none","#F26223"
            "HEATMAPMouseClickStrokeWidth": "none",          //"none",3
            "HEATMAPMouseDownDisplay": false,
            "HEATMAPMouseDownColor": "green",            //"none","red"
            "HEATMAPMouseDownOpacity": 1.0,            //"none",1.0
            "HEATMAPMouseDownStrokeColor": "none",  //"none","#F26223"
            "HEATMAPMouseDownStrokeWidth": "none",          //"none",3
            "HEATMAPMouseEnterDisplay": false,
            "HEATMAPMouseEnterColor": "green",            //"none","red"
            "HEATMAPMouseEnterOpacity": 1.0,            //"none",1.0
            "HEATMAPMouseEnterStrokeColor": "none",  //"none","#F26223"
            "HEATMAPMouseEnterStrokeWidth": "none",          //"none",3
            "HEATMAPMouseLeaveDisplay": false,
            "HEATMAPMouseLeaveColor": "green",            //"none","red"
            "HEATMAPMouseLeaveOpacity": 1.0,            //"none",1.0
            "HEATMAPMouseLeaveStrokeColor": "none",  //"none","#F26223"
            "HEATMAPMouseLeaveStrokeWidth": "none",          //"none",3
            "HEATMAPMouseMoveDisplay": false,
            "HEATMAPMouseMoveColor": "green",            //"none","red"
            "HEATMAPMouseMoveOpacity": 1.0,            //"none",1.0
            "HEATMAPMouseMoveStrokeColor": "none",  //"none","#F26223"
            "HEATMAPMouseMoveStrokeWidth": "none",          //"none",3
            "HEATMAPMouseOutDisplay": false,
            "HEATMAPMouseOutAnimationTime": 500,
            "HEATMAPMouseOutColor": "green",            //"none","red"
            "HEATMAPMouseOutOpacity": 1.0,            //"none",1.0
            "HEATMAPMouseOutStrokeColor": "none",  //"none","#F26223"
            "HEATMAPMouseOutStrokeWidth": "none",          //"none",3
            "HEATMAPMouseUpDisplay": false,
            "HEATMAPMouseUpColor": "green",            //"none","red"
            "HEATMAPMouseUpOpacity": 1.0,            //"none",1.0
            "HEATMAPMouseUpStrokeColor": "none",  //"none","#F26223"
            "HEATMAPMouseUpStrokeWidth": "none",          //"none",3
            "HEATMAPMouseOverDisplay": false,
            "HEATMAPMouseOverColor": "none",            //"none","red"
            "HEATMAPMouseOverOpacity": 1.0,            //"none",1.0
            "HEATMAPMouseOverStrokeColor": "none",  //"none","#F26223"
            "HEATMAPMouseOverStrokeWidth": "none",          //"none",3
            "HEATMAPMouseOverTooltipsSetting": "style1", //custom, style1
            "HEATMAPMouseOverTooltipsHtml": " ",
            // "HEATMAPMouseOverTooltipsHtml01" : "chr : ",
            // "HEATMAPMouseOverTooltipsHtml02" : "<br>position: ",
            // "HEATMAPMouseOverTooltipsHtml03" : "-",
            // "HEATMAPMouseOverTooltipsHtml04" : "<br>name : ",
            // "HEATMAPMouseOverTooltipsHtml05" : "<br>value : ",
            // "HEATMAPMouseOverTooltipsHtml06" : "",
            "HEATMAPMouseOverTooltipsPosition": "absolute",
            "HEATMAPMouseOverTooltipsBackgroundColor": "white",
            "HEATMAPMouseOverTooltipsBorderStyle": "solid",
            "HEATMAPMouseOverTooltipsBorderWidth": 0,
            "HEATMAPMouseOverTooltipsPadding": "3px",
            "HEATMAPMouseOverTooltipsBorderRadius": "3px",
            "HEATMAPMouseOverTooltipsOpacity": 0.8,
            //zhec 20190509
            "BUBBLExlink": false,
            "BUBBLEMouseEvent": true,
            "BUBBLEMouseClickDisplay": false,
            "BUBBLEMouseClickColor": "green",            //"none","red"
            "BUBBLEMouseClickOpacity": 1.0,            //"none",1.0
            "BUBBLEMouseClickStrokeColor": "none",  //"none","#F26223"
            "BUBBLEMouseClickStrokeWidth": "none",          //"none",3
            "BUBBLEMouseDownDisplay": false,
            "BUBBLEMouseDownColor": "green",            //"none","red"
            "BUBBLEMouseDownOpacity": 1.0,            //"none",1.0
            "BUBBLEMouseDownStrokeColor": "none",  //"none","#F26223"
            "BUBBLEMouseDownStrokeWidth": "none",          //"none",3
            "BUBBLEMouseEnterDisplay": false,
            "BUBBLEMouseEnterColor": "green",            //"none","red"
            "BUBBLEMouseEnterOpacity": 1.0,            //"none",1.0
            "BUBBLEMouseEnterStrokeColor": "none",  //"none","#F26223"
            "BUBBLEMouseEnterStrokeWidth": "none",          //"none",3
            "BUBBLEMouseLeaveDisplay": false,
            "BUBBLEMouseLeaveColor": "green",            //"none","red"
            "BUBBLEMouseLeaveOpacity": 1.0,            //"none",1.0
            "BUBBLEMouseLeaveStrokeColor": "none",  //"none","#F26223"
            "BUBBLEMouseLeaveStrokeWidth": "none",          //"none",3
            "BUBBLEMouseMoveDisplay": false,
            "BUBBLEMouseMoveColor": "green",            //"none","red"
            "BUBBLEMouseMoveOpacity": 1.0,            //"none",1.0
            "BUBBLEMouseMoveStrokeColor": "none",  //"none","#F26223"
            "BUBBLEMouseMoveStrokeWidth": "none",          //"none",3
            "BUBBLEMouseOutDisplay": false,
            "BUBBLEMouseOutAnimationTime": 500,
            "BUBBLEMouseOutColor": "green",            //"none","red"
            "BUBBLEMouseOutOpacity": 1.0,            //"none",1.0
            "BUBBLEMouseOutStrokeColor": "none",  //"none","#F26223"
            "BUBBLEMouseOutStrokeWidth": "none",          //"none",3
            "BUBBLEMouseUpDisplay": false,
            "BUBBLEMouseUpColor": "green",            //"none","red"
            "BUBBLEMouseUpOpacity": 1.0,            //"none",1.0
            "BUBBLEMouseUpStrokeColor": "none",  //"none","#F26223"
            "BUBBLEMouseUpStrokeWidth": "none",          //"none",3
            "BUBBLEMouseOverDisplay": false,
            "BUBBLEMouseOverColor": "none",            //"none","red"
            "BUBBLEMouseOverOpacity": 1.0,            //"none",1.0
            "BUBBLEMouseOverStrokeColor": "none",  //"none","#F26223"
            "BUBBLEMouseOverStrokeWidth": "none",          //"none",3
            "BUBBLEMouseOverTooltipsSetting": "style1", //custom, style1
            "BUBBLEMouseOverTooltipsHtml": " ",
            // "BUBBLEMouseOverTooltipsHtml01" : "chr : ",
            // "BUBBLEMouseOverTooltipsHtml02" : "<br>position: ",
            // "BUBBLEMouseOverTooltipsHtml03" : "-",
            // "BUBBLEMouseOverTooltipsHtml04" : "<br>name : ",
            // "BUBBLEMouseOverTooltipsHtml05" : "<br>value : ",
            // "BUBBLEMouseOverTooltipsHtml06" : "",
            "BUBBLEMouseOverTooltipsPosition": "absolute",
            "BUBBLEMouseOverTooltipsBackgroundColor": "white",
            "BUBBLEMouseOverTooltipsBorderStyle": "solid",
            "BUBBLEMouseOverTooltipsBorderWidth": 0,
            "BUBBLEMouseOverTooltipsPadding": "3px",
            "BUBBLEMouseOverTooltipsBorderRadius": "3px",
            "BUBBLEMouseOverTooltipsOpacity": 0.8,
            //zhec 20190509
            //zhec 20190411
            "SNPxlink": false,
            //zhec 20190411
            "SNPMouseEvent": true,
            "SNPMouseCombinationEvent": false,
            "SNPMouseCombinationImageDisplay": false,
            "SNPMouseCombinationImageTitle": "This is image",
            "SNPMouseCombinationImageTitleSize": 5,
            "SNPMouseCombinationImageTitleWeight": "bold",
            "SNPMouseCombinationImageTitleColor": "black",
            "SNPMouseCombinationImagePositionX": 0,
            "SNPMouseCombinationImagePositionY": 0,
            "SNPMouseCombinationImageHeight": 200,
            "SNPMouseCombinationImageWidth": 300,
            "SNPMouseCombinationGraphDisplay": false,
            "SNPMouseCombinationGraphTitle": "This is graph",
            "SNPMouseCombinationGraphTitleSize": 5,
            "SNPMouseCombinationGraphTitleWeight": "bold",
            "SNPMouseCombinationGraphTitleColor": "black",
            "SNPMouseCombinationGraphType": "histogram",   //histogram, pie, line
            "SNPMouseCombinationGraphPositionX": 0,
            "SNPMouseCombinationGraphPositionY": 0,
            "SNPMouseCombinationGraphHeight": 200,
            "SNPMouseCombinationGraphWidth": 300,
            "SNPMouseCombinationGraphHistogramBarColor": "blue",
            "SNPMouseCombinationGraphHistogramPadding": 30,
            "SNPMouseCombinationGraphHistogramPositionCorrectX": 0,
            "SNPMouseCombinationGraphPieAutoColor": true,
            "SNPMouseCombinationGraphPieColor": ["black", "blue", "orange", "red", "green"],
            "SNPMouseCombinationGraphPieSize": 50,
            "SNPMouseCombinationGraphPieStroke": true,
            "SNPMouseCombinationGraphPieStrokeColor": "black",
            "SNPMouseCombinationGraphPieStrokeWidth": 1,
            "SNPMouseCombinationGraphPieOpacity": 1.0,
            "SNPMouseCombinationGraphLineType": "linear",
            "SNPMouseCombinationGraphLineColor": "black",
            "SNPMouseCombinationGraphLineWidth": 1,
            "SNPMouseCombinationGraphLinePoint": false,
            "SNPMouseCombinationGraphLinePointSize": 5,
            "SNPMouseCombinationGraphLinePointAutoColor": true,
            "SNPMouseCombinationGraphLinePointColor": ["black", "blue", "orange", "red", "green"],
            "SNPMouseCombinationGraphLinePointStroke": true,
            "SNPMouseCombinationGraphLinePointStrokeColor": "black",
            "SNPMouseCombinationGraphLinePointStrokeWidth": 1,
            "SNPMouseCombinationGraphLinePointOpacity": 1,
            "SNPMouseCombinationGraphLinePositionCorrectX": 0,
            "SNPMouseCombinationTextDisplay": false,
            "SNPMouseCombinationTextColor": "red",
            "SNPMouseCombinationTextSize": 3,
            "SNPMouseCombinationTextWeight": "bold",
            "SNPMouseCombinationTextPositionCorrectX": 0,
            "SNPMouseCombinationTextPositionCorrectY": 0,
            "SNPMouseClickDisplay": false,
            "SNPMouseClickColor": "red",
            "SNPMouseClickCircleSize": 4,
            "SNPMouseClickCircleOpacity": 1.0,
            "SNPMouseClickCircleStrokeColor": "#F26223",
            "SNPMouseClickCircleStrokeWidth": 0,
            "SNPMouseClickTextFromData": "fourth",   //first,second,third,fourth column
            "SNPMouseClickTextOpacity": 1.0,
            "SNPMouseClickTextColor": "red",
            "SNPMouseClickTextSize": 8,
            "SNPMouseClickTextPostionX": 1.0,
            "SNPMouseClickTextPostionY": 10.0,
            "SNPMouseClickTextDrag": true,
            "SNPMouseDownDisplay": false,
            "SNPMouseDownColor": "green",
            "SNPMouseDownCircleSize": 4,
            "SNPMouseDownCircleOpacity": 1.0,
            "SNPMouseDownCircleStrokeColor": "#F26223",
            "SNPMouseDownCircleStrokeWidth": 0,
            "SNPMouseEnterDisplay": false,
            "SNPMouseEnterColor": "yellow",
            "SNPMouseEnterCircleSize": 4,
            "SNPMouseEnterCircleOpacity": 1.0,
            "SNPMouseEnterCircleStrokeColor": "#F26223",
            "SNPMouseEnterCircleStrokeWidth": 0,
            "SNPMouseLeaveDisplay": false,
            "SNPMouseLeaveColor": "pink",
            "SNPMouseLeaveCircleSize": 4,
            "SNPMouseLeaveCircleOpacity": 1.0,
            "SNPMouseLeaveCircleStrokeColor": "#F26223",
            "SNPMouseLeaveCircleStrokeWidth": 0,
            "SNPMouseMoveDisplay": false,
            "SNPMouseMoveColor": "red",
            "SNPMouseMoveCircleSize": 2,
            "SNPMouseMoveCircleOpacity": 1.0,
            "SNPMouseMoveCircleStrokeColor": "#F26223",
            "SNPMouseMoveCircleStrokeWidth": 0,
            "SNPMouseOutDisplay": false,
            "SNPMouseOutAnimationTime": 500,
            "SNPMouseOutColor": "red",
            "SNPMouseOutCircleSize": 2,
            "SNPMouseOutCircleOpacity": 1.0,
            "SNPMouseOutCircleStrokeColor": "red",
            "SNPMouseOutCircleStrokeWidth": 0,
            "SNPMouseUpDisplay": false,
            "SNPMouseUpColor": "grey",
            "SNPMouseUpCircleSize": 4,
            "SNPMouseUpCircleOpacity": 1.0,
            "SNPMouseUpCircleStrokeColor": "#F26223",
            "SNPMouseUpCircleStrokeWidth": 0,
            "SNPMouseOverDisplay": false,
            "SNPMouseOverColor": "red",
            "SNPMouseOverCircleSize": 2,
            "SNPMouseOverCircleOpacity": 1.0,
            "SNPMouseOverCircleStrokeColor": "#F26223",
            "SNPMouseOverCircleStrokeWidth": 3,
            "SNPMouseOverTooltipsSetting": "style1", //custom, style1
            "SNPMouseOverTooltipsHtml": " ",
            "SNPMouseOverTooltipsPosition": "absolute",
            "SNPMouseOverTooltipsBackgroundColor": "white",
            "SNPMouseOverTooltipsBorderStyle": "solid",
            "SNPMouseOverTooltipsBorderWidth": 0,
            "SNPMouseOverTooltipsPadding": "3px",
            "SNPMouseOverTooltipsBorderRadius": "3px",
            "SNPMouseOverTooltipsOpacity": 1,
            "TEXTModuleDragEvent": true,
            "LINKxlink": false,
            "LINKMouseEvent": true,
            "LINKMouseClickDisplay": false,
            "LINKMouseClickOpacity": 1.0,
            "LINKMouseClickStrokeColor": "green",
            "LINKMouseClickStrokeWidth": 4,
            "LINKMouseDownDisplay": false,
            "LINKMouseDownOpacity": 1.0,
            "LINKMouseDownStrokeColor": "#F26223",
            "LINKMouseDownStrokeWidth": 4,
            "LINKMouseEnterDisplay": false,
            "LINKMouseEnterOpacity": 1.0,
            "LINKMouseEnterStrokeColor": "#F26223",
            "LINKMouseEnterStrokeWidth": 4,
            "LINKMouseLeaveDisplay": false,
            "LINKMouseLeaveOpacity": 1.0,
            "LINKMouseLeaveStrokeColor": "#F26223",
            "LINKMouseLeaveStrokeWidth": 4,
            "LINKMouseMoveDisplay": false,
            "LINKMouseMoveOpacity": 1.0,
            "LINKMouseMoveStrokeColor": "#F26223",
            "LINKMouseMoveStrokeWidth": 4,
            "LINKMouseOutDisplay": false,
            "LINKMouseOutAnimationTime": 500,
            "LINKMouseOutOpacity": 1.0,
            "LINKMouseOutStrokeColor": "red",
            "LINKMouseOutStrokeWidth": 4,
            "LINKMouseUpDisplay": false,
            "LINKMouseUpOpacity": 1.0,
            "LINKMouseUpStrokeColor": "#F26223",
            "LINKMouseUpStrokeWidth": 4,
            "LINKMouseOverDisplay": false,
            "LINKMouseOverOpacity": 1.0,
            "LINKMouseOverStrokeColor": "#F26223",
            "LINKMouseOverStrokeWidth": 3,
            "LINKMouseOverTooltipsSetting": "style1",  //custom, style1
            "LINKMouseOverTooltipsHtml": " ",
            "LINKMouseOverTooltipsPosition": "absolute",
            "LINKMouseOverTooltipsBackgroundColor": "white",
            "LINKMouseOverTooltipsBorderStyle": "solid",
            "LINKMouseOverTooltipsBorderWidth": 0,
            "LINKMouseOverTooltipsPadding": "3px",
            "LINKMouseOverTooltipsBorderRadius": "3px",
            "LINKMouseOverTooltipsOpacity": 0.8,
            "LINKLabelDragEvent": false,
            //chord
            "CHORDMouseEvent": true,
            "CHORDMouseFillColorExcluded": "#FFFFFF",
            "CHORDMouseClickDisplay": false,
            "CHORDMouseClickOpacity": 1.0,
            "CHORDMouseClickStrokeColor": "green",
            "CHORDMouseClickStrokeWidth": 4,
            "CHORDMouseDownDisplay": false,
            "CHORDMouseDownOpacity": 1.0,
            "CHORDMouseDownStrokeColor": "#F26223",
            "CHORDMouseDownStrokeWidth": 4,
            "CHORDMouseEnterDisplay": false,
            "CHORDMouseEnterOpacity": 1.0,
            "CHORDMouseEnterStrokeColor": "#F26223",
            "CHORDMouseEnterStrokeWidth": 4,
            "CHORDMouseLeaveDisplay": false,
            "CHORDMouseLeaveOpacity": 1.0,
            "CHORDMouseLeaveStrokeColor": "#F26223",
            "CHORDMouseLeaveStrokeWidth": 4,
            "CHORDMouseMoveDisplay": false,
            "CHORDMouseMoveOpacity": 1.0,
            "CHORDMouseMoveStrokeColor": "#F26223",
            "CHORDMouseMoveStrokeWidth": 4,
            "CHORDMouseOutDisplay": false,
            "CHORDMouseOutAnimationTime": 500,
            "CHORDMouseOutOpacity": 1.0,
            "CHORDMouseOutStrokeColor": "red",
            "CHORDMouseOutStrokeWidth": 4,
            "CHORDMouseUpDisplay": false,
            "CHORDMouseUpOpacity": 1.0,
            "CHORDMouseUpStrokeColor": "#F26223",
            "CHORDMouseUpStrokeWidth": 4,
            "CHORDMouseOverDisplay": false,
            "CHORDMouseOverOpacity": 1.0,
            "CHORDMouseOverStrokeColor": "#F26223",
            "CHORDMouseOverStrokeWidth": 3,
//          "CHORDMouseOverTooltipsHtml01" : "CHORD : ",
//          "CHORDMouseOverTooltipsHtml02" : "",
//          "CHORDMouseOverTooltipsPosition" : "absolute",
//          "CHORDMouseOverTooltipsBackgroundColor" : "white",
//          "CHORDMouseOverTooltipsBorderStyle" : "solid",
//          "CHORDMouseOverTooltipsBorderWidth" : 0,
//          "CHORDMouseOverTooltipsPadding" : "3px",
//          "CHORDMouseOverTooltipsBorderRadius" : "3px",
//          "CHORDMouseOverTooltipsOpacity" : 0.8,
            //chord

            //COMPARE
//          "COMPAREMouseEvent" : true,
//          "COMPAREMouseClickDisplay" : false,
//          "COMPAREMouseClickOpacity" : 1.0,
//          "COMPAREMouseClickStrokeColor" : "green",
//          "COMPAREMouseClickStrokeWidth" : 4,
//          "COMPAREMouseDownDisplay" : false,
//          "COMPAREMouseDownOpacity" : 1.0,
//          "COMPAREMouseDownStrokeColor" : "#F26223",
//          "COMPAREMouseDownStrokeWidth" : 4,
//          "COMPAREMouseEnterDisplay" : false,
//          "COMPAREMouseEnterOpacity" : 1.0,
//          "COMPAREMouseEnterStrokeColor" : "#F26223",
//          "COMPAREMouseEnterStrokeWidth" : 4,
//          "COMPAREMouseLeaveDisplay" : false,
//          "COMPAREMouseLeaveOpacity" : 1.0,
//          "COMPAREMouseLeaveStrokeColor" : "#F26223",
//          "COMPAREMouseLeaveStrokeWidth" : 4,
//          "COMPAREMouseMoveDisplay" : false,
//          "COMPAREMouseMoveOpacity" : 1.0,
//          "COMPAREMouseMoveStrokeColor" : "#F26223",
//          "COMPAREMouseMoveStrokeWidth" : 4,
//          "COMPAREMouseOutDisplay" : false,
//          "COMPAREMouseOutAnimationTime" : 500,
//          "COMPAREMouseOutOpacity" : 1.0,
//          "COMPAREMouseOutStrokeColor" : "red",
//          "COMPAREMouseOutStrokeWidth" : 4,
//          "COMPAREMouseUpDisplay" : false,
//          "COMPAREMouseUpOpacity" : 1.0,
//          "COMPAREMouseUpStrokeColor" : "#F26223",
//          "COMPAREMouseUpStrokeWidth" : 4,
//          "COMPAREMouseOverDisplay" : false,
//          "COMPAREMouseOverOpacity" : 1.0,
//          "COMPAREMouseOverStrokeColor" : "#F26223",
//          "COMPAREMouseOverStrokeWidth" : 3,
            //COMPARE

            //zhec 20190411
            "HISTOGRAMxlink": false,
            //zhec 20190411
            "HISTOGRAMMouseEvent": true,
            "HISTOGRAMMouseClickDisplay": false,
            "HISTOGRAMMouseClickColor": "red",            //"none","red"
            "HISTOGRAMMouseClickOpacity": 1.0,            //"none",1.0
            "HISTOGRAMMouseClickStrokeColor": "none",  //"none","#F26223"
            "HISTOGRAMMouseClickStrokeWidth": "none",          //"none",3
            "HISTOGRAMMouseDownDisplay": false,
            "HISTOGRAMMouseDownColor": "red",            //"none","red"
            "HISTOGRAMMouseDownOpacity": 1.0,            //"none",1.0
            "HISTOGRAMMouseDownStrokeColor": "none",  //"none","#F26223"
            "HISTOGRAMMouseDownStrokeWidth": "none",          //"none",3
            "HISTOGRAMMouseEnterDisplay": false,
            "HISTOGRAMMouseEnterColor": "red",            //"none","red"
            "HISTOGRAMMouseEnterOpacity": 1.0,            //"none",1.0
            "HISTOGRAMMouseEnterStrokeColor": "none",  //"none","#F26223"
            "HISTOGRAMMouseEnterStrokeWidth": "none",          //"none",3
            "HISTOGRAMMouseLeaveDisplay": false,
            "HISTOGRAMMouseLeaveColor": "red",            //"none","red"
            "HISTOGRAMMouseLeaveOpacity": 1.0,            //"none",1.0
            "HISTOGRAMMouseLeaveStrokeColor": "none",  //"none","#F26223"
            "HISTOGRAMMouseLeaveStrokeWidth": "none",          //"none",3
            "HISTOGRAMMouseMoveDisplay": false,
            "HISTOGRAMMouseMoveColor": "red",            //"none","red"
            "HISTOGRAMMouseMoveOpacity": 1.0,            //"none",1.0
            "HISTOGRAMMouseMoveStrokeColor": "none",  //"none","#F26223"
            "HISTOGRAMMouseMoveStrokeWidth": "none",          //"none",3
            "HISTOGRAMMouseOutDisplay": false,
            "HISTOGRAMMouseOutAnimationTime": 500,
            "HISTOGRAMMouseOutColor": "red",            //"none","red"
            "HISTOGRAMMouseOutOpacity": 1.0,            //"none",1.0
            "HISTOGRAMMouseOutStrokeColor": "none",  //"none","#F26223"
            "HISTOGRAMMouseOutStrokeWidth": "none",          //"none",3
            "HISTOGRAMMouseUpDisplay": false,
            "HISTOGRAMMouseUpColor": "red",            //"none","red"
            "HISTOGRAMMouseUpOpacity": 1.0,            //"none",1.0
            "HISTOGRAMMouseUpStrokeColor": "none",  //"none","#F26223"
            "HISTOGRAMMouseUpStrokeWidth": "none",          //"none",3
            "HISTOGRAMMouseOverDisplay": false,
            "HISTOGRAMMouseOverColor": "red",            //"none","red"
            "HISTOGRAMMouseOverOpacity": 1.0,            //"none",1.0
            "HISTOGRAMMouseOverStrokeColor": "none",  //"none","#F26223"
            "HISTOGRAMMouseOverStrokeWidth": "none",          //"none",3
            "HISTOGRAMMouseOverTooltipsSetting": "style1", //custom, style1
            "HISTOGRAMMouseOverTooltipsHtml": " ",
            // "HISTOGRAMMouseOverTooltipsHtml01" : "chr :",
            // "HISTOGRAMMouseOverTooltipsHtml02" : "<br>position: ",
            // "HISTOGRAMMouseOverTooltipsHtml03" : "-",
            // "HISTOGRAMMouseOverTooltipsHtml04" : "<br>name : ",
            // "HISTOGRAMMouseOverTooltipsHtml05" : "<br>value : ",
            // "HISTOGRAMMouseOverTooltipsHtml06" : "",
            "HISTOGRAMMouseOverTooltipsPosition": "absolute",
            "HISTOGRAMMouseOverTooltipsBackgroundColor": "white",
            "HISTOGRAMMouseOverTooltipsBorderStyle": "solid",
            "HISTOGRAMMouseOverTooltipsBorderWidth": 0,
            "HISTOGRAMMouseOverTooltipsPadding": "3px",
            "HISTOGRAMMouseOverTooltipsBorderRadius": "3px",
            "HISTOGRAMMouseOverTooltipsOpacity": 0.8,
            "LINEMouseEvent": true,
            "LINEMouseClickDisplay": false,
            "LINEMouseClickLineOpacity": 1,           //"none"
            "LINEMouseClickLineStrokeColor": "red",   //"none"
            "LINEMouseClickLineStrokeWidth": "none",  //"none"
            "LINEMouseDownDisplay": false,
            "LINEMouseDownLineOpacity": 1,           //"none"
            "LINEMouseDownLineStrokeColor": "red",   //"none"
            "LINEMouseDownLineStrokeWidth": "none",  //"none"
            "LINEMouseEnterDisplay": false,
            "LINEMouseEnterLineOpacity": 1,           //"none"
            "LINEMouseEnterLineStrokeColor": "red",   //"none"
            "LINEMouseEnterLineStrokeWidth": "none",  //"none"
            "LINEMouseLeaveDisplay": false,
            "LINEMouseLeaveLineOpacity": 1,           //"none"
            "LINEMouseLeaveLineStrokeColor": "red",   //"none"
            "LINEMouseLeaveLineStrokeWidth": "none",  //"none"
            "LINEMouseMoveDisplay": false,
            "LINEMouseMoveLineOpacity": 1,           //"none"
            "LINEMouseMoveLineStrokeColor": "red",   //"none"
            "LINEMouseMoveLineStrokeWidth": "none",  //"none"
            "LINEMouseOutDisplay": false,
            "LINEMouseOutAnimationTime": 500,
            "LINEMouseOutLineOpacity": 1.0,   //"none"
            "LINEMouseOutLineStrokeColor": "red",    //"none"
            "LINEMouseOutLineStrokeWidth": "none",    //"none"
            "LINEMouseUpDisplay": false,
            "LINEMouseUpLineOpacity": 1,           //"none"
            "LINEMouseUpLineStrokeColor": "red",   //"none"
            "LINEMouseUpLineStrokeWidth": "none",  //"none"
            "LINEMouseOverDisplay": false,
            "LINEMouseOverLineOpacity": 1,           //"none"
            "LINEMouseOverLineStrokeColor": "red",   //"none"
            "LINEMouseOverLineStrokeWidth": "none",  //"none"
            "LINEMouseOverTooltipsSetting": "style1", //custom, style1
            "LINEMouseOverTooltipsHtml": " ",
            // "LINEMouseOverTooltipsHtml01" : "Line",
            "LINEMouseOverTooltipsPosition": "absolute",
            "LINEMouseOverTooltipsBackgroundColor": "white",
            "LINEMouseOverTooltipsBorderStyle": "solid",
            "LINEMouseOverTooltipsBorderWidth": 0,
            "LINEMouseOverTooltipsPadding": "3px",
            "LINEMouseOverTooltipsBorderRadius": "3px",
            "LINEMouseOverTooltipsOpacity": 0.8,
            //WIG
            "WIGMouseEvent": true,
            "WIGMouseClickDisplay": false,
            "WIGMouseClickLineOpacity": 1,           //"none"
            "WIGMouseClickLineStrokeColor": "red",   //"none"
            "WIGMouseClickLineStrokeWidth": "none",  //"none"
            "WIGMouseClickFillColor": "none",
            "WIGMouseDownDisplay": false,
            "WIGMouseDownLineOpacity": 1,           //"none"
            "WIGMouseDownLineStrokeColor": "red",   //"none"
            "WIGMouseDownLineStrokeWidth": "none",  //"none"
            "WIGMouseDownFillColor": "none",
            "WIGMouseEnterDisplay": false,
            "WIGMouseEnterLineOpacity": 1,           //"none"
            "WIGMouseEnterLineStrokeColor": "red",   //"none"
            "WIGMouseEnterLineStrokeWidth": "none",  //"none"
            "WIGMouseEnterFillColor": "none",
            "WIGMouseLeaveDisplay": false,
            "WIGMouseLeaveLineOpacity": 1,           //"none"
            "WIGMouseLeaveLineStrokeColor": "red",   //"none"
            "WIGMouseLeaveLineStrokeWidth": "none",  //"none"
            "WIGMouseLeaveFillColor": "none",
            "WIGMouseMoveDisplay": false,
            "WIGMouseMoveLineOpacity": 1,           //"none"
            "WIGMouseMoveLineStrokeColor": "red",   //"none"
            "WIGMouseMoveLineStrokeWidth": "none",  //"none"
            "WIGMouseMoveFillColor": "none",
            "WIGMouseOutDisplay": false,
            "WIGMouseOutAnimationTime": 500,
            "WIGMouseOutLineOpacity": 1.0,   //"none"
            "WIGMouseOutLineStrokeColor": "red",    //"none"
            "WIGMouseOutLineStrokeWidth": "none",    //"none"
            "WIGMouseOutFillColor": "none",
            "WIGMouseUpDisplay": false,
            "WIGMouseUpLineOpacity": 1,           //"none"
            "WIGMouseUpLineStrokeColor": "red",   //"none"
            "WIGMouseUpLineStrokeWidth": "none",  //"none"
            "WIGMouseUpFillColor": "none",
            "WIGMouseOverDisplay": false,
            "WIGMouseOverLineOpacity": 1,           //"none"
            "WIGMouseOverLineStrokeColor": "red",   //"none"
            "WIGMouseOverLineStrokeWidth": "none",  //"none"
            "WIGMouseOverFillColor": "none",
            "WIGMouseOverTooltipsSetting": "style1", //custom, style1
            "WIGMouseOverTooltipsHtml": " ",
            // "WIGMouseOverTooltipsHtml01" : "Line",
            "WIGMouseOverTooltipsPosition": "absolute",
            "WIGMouseOverTooltipsBackgroundColor": "white",
            "WIGMouseOverTooltipsBorderStyle": "solid",
            "WIGMouseOverTooltipsBorderWidth": 0,
            "WIGMouseOverTooltipsPadding": "3px",
            "WIGMouseOverTooltipsBorderRadius": "3px",
            "WIGMouseOverTooltipsOpacity": 0.8,
            //WIG
            //zhec 20190411
            "SCATTERxlink": false,
            //zhec 20190411
            "SCATTERMouseEvent": true,
            "SCATTERMouseClickDisplay": false,
            "SCATTERMouseClickColor": "red",
            "SCATTERMouseClickCircleSize": 4,
            "SCATTERMouseClickCircleOpacity": 1.0,
            "SCATTERMouseClickCircleStrokeColor": "#F26223",
            "SCATTERMouseClickCircleStrokeWidth": 0,
            "SCATTERMouseClickTextFromData": "fourth",   //first,second,third,fourth column
            "SCATTERMouseClickTextOpacity": 1,
            "SCATTERMouseClickTextColor": "red",
            "SCATTERMouseClickTextSize": 8,
            "SCATTERMouseClickTextPostionX": 1.0,
            "SCATTERMouseClickTextPostionY": 10.0,
            "SCATTERMouseClickTextDrag": true,
            "SCATTERMouseDownDisplay": false,
            "SCATTERMouseDownColor": "green",
            "SCATTERMouseDownCircleSize": 4,
            "SCATTERMouseDownCircleOpacity": 1.0,
            "SCATTERMouseDownCircleStrokeColor": "#F26223",
            "SCATTERMouseDownCircleStrokeWidth": 0,
            "SCATTERMouseEnterDisplay": false,
            "SCATTERMouseEnterColor": "yellow",
            "SCATTERMouseEnterCircleSize": 4,
            "SCATTERMouseEnterCircleOpacity": 1.0,
            "SCATTERMouseEnterCircleStrokeColor": "#F26223",
            "SCATTERMouseEnterCircleStrokeWidth": 0,
            "SCATTERMouseLeaveDisplay": false,
            "SCATTERMouseLeaveColor": "pink",
            "SCATTERMouseLeaveCircleSize": 4,
            "SCATTERMouseLeaveCircleOpacity": 1.0,
            "SCATTERMouseLeaveCircleStrokeColor": "#F26223",
            "SCATTERMouseLeaveCircleStrokeWidth": 0,
            "SCATTERMouseMoveDisplay": false,
            "SCATTERMouseMoveColor": "red",
            "SCATTERMouseMoveCircleSize": 2,
            "SCATTERMouseMoveCircleOpacity": 1.0,
            "SCATTERMouseMoveCircleStrokeColor": "#F26223",
            "SCATTERMouseMoveCircleStrokeWidth": 0,
            "SCATTERMouseOutDisplay": false,
            "SCATTERMouseOutAnimationTime": 500,
            "SCATTERMouseOutColor": "red",
            "SCATTERMouseOutCircleSize": 2,
            "SCATTERMouseOutCircleOpacity": 1.0,
            "SCATTERMouseOutCircleStrokeColor": "red",
            "SCATTERMouseOutCircleStrokeWidth": 0,
            "SCATTERMouseUpDisplay": false,
            "SCATTERMouseUpColor": "grey",
            "SCATTERMouseUpCircleSize": 4,
            "SCATTERMouseUpCircleOpacity": 1.0,
            "SCATTERMouseUpCircleStrokeColor": "#F26223",
            "SCATTERMouseUpCircleStrokeWidth": 0,
            "SCATTERMouseOverDisplay": false,
            "SCATTERMouseOverColor": "red",
            "SCATTERMouseOverCircleSize": 2,
            "SCATTERMouseOverCircleOpacity": 1.0,
            "SCATTERMouseOverCircleStrokeColor": "#F26223",
            "SCATTERMouseOverCircleStrokeWidth": 3,
            "SCATTERMouseOverTooltipsSetting": "style1", //custom, style1
            "SCATTERMouseOverTooltipsHtml": " ",
            // "SCATTERMouseOverTooltipsHtml01" : "item : ",
            // "SCATTERMouseOverTooltipsHtml02" : "<br>start : ",
            // "SCATTERMouseOverTooltipsHtml03" : "<br>end : ",
            // "SCATTERMouseOverTooltipsHtml04" : "<br>name : ",
            // "SCATTERMouseOverTooltipsHtml05" : "<br>des : ",
            // "SCATTERMouseOverTooltipsHtml06" : "",
            "SCATTERMouseOverTooltipsPosition": "absolute",
            "SCATTERMouseOverTooltipsBackgroundColor": "white",
            "SCATTERMouseOverTooltipsBorderStyle": "solid",
            "SCATTERMouseOverTooltipsBorderWidth": 0,
            "SCATTERMouseOverTooltipsPadding": "3px",
            "SCATTERMouseOverTooltipsBorderRadius": "3px",
            "SCATTERMouseOverTooltipsOpacity": 0.8,
            //zhec 20190411
            "ARCxlink": false,
            //zhec 20190411
            "ARCMouseEvent": true,
            "ARCMouseClickDisplay": false,
            "ARCMouseClickColor": "red",
            "ARCMouseClickArcOpacity": 1.0,
            "ARCMouseClickArcStrokeColor": "#F26223",
            "ARCMouseClickArcStrokeWidth": 1,
            "ARCMouseClickTextFromData": "fourth",   //first,second,third,fourth column
            "ARCMouseClickTextOpacity": 1,
            "ARCMouseClickTextColor": "red",
            "ARCMouseClickTextSize": 8,
            "ARCMouseClickTextPostionX": 0,
            "ARCMouseClickTextPostionY": 0,
            "ARCMouseClickTextDrag": true,
            "ARCMouseDownDisplay": false,
            "ARCMouseDownColor": "green",
            "ARCMouseDownArcOpacity": 1.0,
            "ARCMouseDownArcStrokeColor": "#F26223",
            "ARCMouseDownArcStrokeWidth": 0,
            "ARCMouseEnterDisplay": false,
            "ARCMouseEnterColor": "yellow",
            "ARCMouseEnterArcOpacity": 1.0,
            "ARCMouseEnterArcStrokeColor": "#F26223",
            "ARCMouseEnterArcStrokeWidth": 0,
            "ARCMouseLeaveDisplay": false,
            "ARCMouseLeaveColor": "pink",
            "ARCMouseLeaveArcOpacity": 1.0,
            "ARCMouseLeaveArcStrokeColor": "#F26223",
            "ARCMouseLeaveArcStrokeWidth": 0,
            "ARCMouseMoveDisplay": false,
            "ARCMouseMoveColor": "red",
            "ARCMouseMoveArcOpacity": 1.0,
            "ARCMouseMoveArcStrokeColor": "#F26223",
            "ARCMouseMoveArcStrokeWidth": 0,
            "ARCMouseOutDisplay": false,
            "ARCMouseOutAnimationTime": 500,
            "ARCMouseOutColor": "red",
            "ARCMouseOutArcOpacity": 1.0,
            "ARCMouseOutArcStrokeColor": "red",
            "ARCMouseOutArcStrokeWidth": 0,
            "ARCMouseUpDisplay": false,
            "ARCMouseUpColor": "grey",
            "ARCMouseUpArcOpacity": 1.0,
            "ARCMouseUpArcStrokeColor": "#F26223",
            "ARCMouseUpArcStrokeWidth": 0,
            "ARCMouseOverDisplay": false,
            "ARCMouseOverColor": "red",
            "ARCMouseOverArcOpacity": 1.0,
            "ARCMouseOverArcStrokeColor": "#F26223",
            "ARCMouseOverArcStrokeWidth": 3,
            "ARCMouseOverTooltipsSetting": "style1", //custom, style1
            "ARCMouseOverTooltipsHtml": " ",
            // "ARCMouseOverTooltipsHtml01" : "item : ",
            // "ARCMouseOverTooltipsHtml02" : "<br>start : ",
            // "ARCMouseOverTooltipsHtml03" : "<br>end : ",
            // "ARCMouseOverTooltipsHtml04" : "<br>des : ",
            // "ARCMouseOverTooltipsHtml05" : "",
            "ARCMouseOverTooltipsPosition": "absolute",
            "ARCMouseOverTooltipsBackgroundColor": "white",
            "ARCMouseOverTooltipsBorderStyle": "solid",
            "ARCMouseOverTooltipsBorderWidth": 0,
            "ARCMouseOverTooltipsPadding": "3px",
            "ARCMouseOverTooltipsBorderRadius": "3px",
            "ARCMouseOverTooltipsOpacity": 0.8,
            //zhec 0401
            "GENExlink": false,
            "GENEMouseEvent": true,
            "GENEMouseClickDisplay": false,
            "GENEMouseClickColor": "red",
            "GENEMouseClickArcOpacity": 1.0,
            "GENEMouseClickArcStrokeColor": "#F26223",
            "GENEMouseClickArcStrokeWidth": 1,
            "GENEMouseClickTextFromData": "fourth",   //first,second,third,fourth column
            "GENEMouseClickTextOpacity": 1,
            "GENEMouseClickTextColor": "red",
            "GENEMouseClickTextSize": 8,
            "GENEMouseClickTextPostionX": 0,
            "GENEMouseClickTextPostionY": 0,
            "GENEMouseClickTextDrag": true,
            "GENEMouseDownDisplay": false,
            "GENEMouseDownColor": "green",
            "GENEMouseDownArcOpacity": 1.0,
            "GENEMouseDownArcStrokeColor": "#F26223",
            "GENEMouseDownArcStrokeWidth": 0,
            "GENEMouseEnterDisplay": false,
            "GENEMouseEnterColor": "yellow",
            "GENEMouseEnterArcOpacity": 1.0,
            "GENEMouseEnterArcStrokeColor": "#F26223",
            "GENEMouseEnterArcStrokeWidth": 0,
            "GENEMouseLeaveDisplay": false,
            "GENEMouseLeaveColor": "pink",
            "GENEMouseLeaveArcOpacity": 1.0,
            "GENEMouseLeaveArcStrokeColor": "#F26223",
            "GENEMouseLeaveArcStrokeWidth": 0,
            "GENEMouseMoveDisplay": false,
            "GENEMouseMoveColor": "red",
            "GENEMouseMoveArcOpacity": 1.0,
            "GENEMouseMoveArcStrokeColor": "#F26223",
            "GENEMouseMoveArcStrokeWidth": 0,
            "GENEMouseOutDisplay": false,
            "GENEMouseOutAnimationTime": 500,
            "GENEMouseOutColor": "red",
            "GENEMouseOutArcOpacity": 1.0,
            "GENEMouseOutArcStrokeColor": "red",
            "GENEMouseOutArcStrokeWidth": 0,
            "GENEMouseUpDisplay": false,
            "GENEMouseUpColor": "grey",
            "GENEMouseUpArcOpacity": 1.0,
            "GENEMouseUpArcStrokeColor": "#F26223",
            "GENEMouseUpArcStrokeWidth": 0,
            "GENEMouseOverDisplay": false,
            "GENEMouseOverColor": "red",
            "GENEMouseOverArcOpacity": 1.0,
            "GENEMouseOverArcStrokeColor": "#F26223",
            "GENEMouseOverArcStrokeWidth": 3,
            "GENEMouseOverTooltipsSetting": "style1", //custom, style1
            "GENEMouseOverTooltipsHtml": " ",
            // "GENEMouseOverTooltipsHtml01" : "gene : ",
            // "GENEMouseOverTooltipsHtml02" : "<br>start : ",
            // "GENEMouseOverTooltipsHtml03" : "<br>end : ",
            // "GENEMouseOverTooltipsHtml04" : "<br>name : ",
            // "GENEMouseOverTooltipsHtml05" : "<br>strand : ",
            // "GENEMouseOverTooltipsHtml06" : "<br>type : ",
            // "GENEMouseOverTooltipsHtml07" : "",
            "GENEMouseOverTooltipsPosition": "absolute",
            "GENEMouseOverTooltipsBackgroundColor": "white",
            "GENEMouseOverTooltipsBorderStyle": "solid",
            "GENEMouseOverTooltipsBorderWidth": 0,
            "GENEMouseOverTooltipsPadding": "3px",
            "GENEMouseOverTooltipsBorderRadius": "3px",
            "GENEMouseOverTooltipsOpacity": 0.8,
            //zhec 0401

            //zhec3
            "LOLLIPOPxlink": false,
            "LOLLIPOPMouseEvent": true,
            "LOLLIPOPMouseClickDisplay": false,
            "LOLLIPOPMouseClickColor": "red",
            "LOLLIPOPMouseClickCircleSize": 4,
            "LOLLIPOPMouseClickCircleOpacity": 1.0,
            "LOLLIPOPMouseClickCircleStrokeColor": "#F26223",
            "LOLLIPOPMouseClickCircleStrokeWidth": 0,
            "LOLLIPOPMouseClickTextFromData": "fourth",
            "LOLLIPOPMouseClickTextOpacity": 1.0,
            "LOLLIPOPMouseClickTextColor": "red",
            "LOLLIPOPMouseClickTextSize": 8,
            "LOLLIPOPMouseClickTextPostionX": 1.0,
            "LOLLIPOPMouseClickTextPostionY": 10.0,
            "LOLLIPOPMouseClickTextDrag": true,
            "LOLLIPOPMouseDownDisplay": false,
            "LOLLIPOPMouseDownColor": "green",
            "LOLLIPOPMouseDownCircleSize": 4,
            "LOLLIPOPMouseDownCircleOpacity": 1.0,
            "LOLLIPOPMouseDownCircleStrokeColor": "#F26223",
            "LOLLIPOPMouseDownCircleStrokeWidth": 0,
            "LOLLIPOPMouseEnterDisplay": false,
            "LOLLIPOPMouseEnterColor": "yellow",
            "LOLLIPOPMouseEnterCircleSize": 4,
            "LOLLIPOPMouseEnterCircleOpacity": 1.0,
            "LOLLIPOPMouseEnterCircleStrokeColor": "#F26223",
            "LOLLIPOPMouseEnterCircleStrokeWidth": 0,
            "LOLLIPOPMouseLeaveDisplay": false,
            "LOLLIPOPMouseLeaveColor": "pink",
            "LOLLIPOPMouseLeaveCircleSize": 4,
            "LOLLIPOPMouseLeaveCircleOpacity": 1.0,
            "LOLLIPOPMouseLeaveCircleStrokeColor": "#F26223",
            "LOLLIPOPMouseLeaveCircleStrokeWidth": 0,
            "LOLLIPOPMouseMoveDisplay": false,
            "LOLLIPOPMouseMoveColor": "red",
            "LOLLIPOPMouseMoveCircleSize": 2,
            "LOLLIPOPMouseMoveCircleOpacity": 1.0,
            "LOLLIPOPMouseMoveCircleStrokeColor": "#F26223",
            "LOLLIPOPMouseMoveCircleStrokeWidth": 0,
            "LOLLIPOPMouseOutDisplay": false,
            "LOLLIPOPMouseOutAnimationTime": 500,
            "LOLLIPOPMouseOutColor": "red",
            "LOLLIPOPMouseOutCircleSize": 2,
            "LOLLIPOPMouseOutCircleOpacity": 1.0,
            "LOLLIPOPMouseOutCircleStrokeColor": "red",
            "LOLLIPOPMouseOutCircleStrokeWidth": 0,
            "LOLLIPOPMouseUpDisplay": false,
            "LOLLIPOPMouseUpColor": "grey",
            "LOLLIPOPMouseUpCircleSize": 4,
            "LOLLIPOPMouseUpCircleOpacity": 1.0,
            "LOLLIPOPMouseUpCircleStrokeColor": "#F26223",
            "LOLLIPOPMouseUpCircleStrokeWidth": 0,
            "LOLLIPOPMouseOverDisplay": false,
            "LOLLIPOPMouseOverColor": "red",
            "LOLLIPOPMouseOverCircleSize": 2,
            "LOLLIPOPMouseOverCircleOpacity": 1.0,
            "LOLLIPOPMouseOverCircleStrokeColor": "#F26223",
            "LOLLIPOPMouseOverCircleStrokeWidth": 3,
            "LOLLIPOPMouseOverTooltipsSetting": "style1", //custom, style1
            "LOLLIPOPMouseOverTooltipsHtml": " ",
            // "LOLLIPOPMouseOverTooltipsHtml01" : "protein : ",
            // "LOLLIPOPMouseOverTooltipsHtml02" : "<br>chr : ",
            // "LOLLIPOPMouseOverTooltipsHtml03" : "<br>pos : ",
            // "LOLLIPOPMouseOverTooltipsHtml04" : "<br>strand : ",
            // "LOLLIPOPMouseOverTooltipsHtml05" : "<br>CancerTypeNumber : ",
            // "LOLLIPOPMouseOverTooltipsHtml06" : "<br>AA_pos : ",
            // "LOLLIPOPMouseOverTooltipsHtml07" : "<br>AA_change : ",
            // "LOLLIPOPMouseOverTooltipsHtml08" : "<br>Consequence : ",
            // "LOLLIPOPMouseOverTooltipsHtml09" : "",
            "LOLLIPOPMouseOverTooltipsPosition": "absolute",
            "LOLLIPOPMouseOverTooltipsBackgroundColor": "white",
            "LOLLIPOPMouseOverTooltipsBorderStyle": "solid",
            "LOLLIPOPMouseOverTooltipsBorderWidth": 0,
            "LOLLIPOPMouseOverTooltipsPadding": "3px",
            "LOLLIPOPMouseOverTooltipsBorderRadius": "3px",
            "LOLLIPOPMouseOverTooltipsOpacity": 0.8,

            //zhec3

            "genomeBorder": {
                "display": true,
                "borderColor": "#000",
                "borderSize": 0.5
            },
            "ticks": {
                "display": true,
                "len": 5,
                "color": "#000",
                "textSize": 8,
                "textColor": "#000",
                "scale": 10000000,
                //zhec2
                "realLength": false,
                //zhec2
                //offset from realLength
                "offset": 0,
                //offset from realLength
            },
            "genomeLabel": {
                "display": true,
                "textSize": 15,
                "textColor": "#000",
                "dx": 0.028,
                "dy": "-0.55em"
            }
        };

        self.SNPsettings = {
            "compareGroup": 1,
            "maxRadius": 200,
            "minRadius": 190,
            "SNPFillColorType": "specific", //specific,r2
            "SNPFillColor": "red",
            "SNPFillr2Color": ["13#ff0031", "#ff0031", "#ff0031", "#ff0031", "#ff0031"],
            "ValueAxisManualScale": false,
            "ValueAxisMaxScale": 10,
            "ValueAxisMinScale": 0,
            "PointType": "circle", //circle,rect
            "circleSize": 2,
            "rectWidth": 2,
            "rectHeight": 2,
            "SNPAnimationDisplay": false,
            "SNPAnimationInitialPositionX": 0,
            "SNPAnimationInitialPositionY": 0,
            "SNPAnimationTime": 2000,
            "SNPAnimationDelay": 20,
            "SNPAnimationType": "bounce",  //linear,circle,elastic,bounce
        };

        self.BACKGROUNDsettings = {
            "compareGroup": 1,
            "BginnerRadius": 180,
            "BgouterRadius": 230,
            "BgFillColor": "none",
            "BgborderColor": "#000",
            "BgborderSize": 0.5,
            "axisShow": "false",
            "axisWidth": 0.3,
            "axisColor": "#000",
            "axisOpacity": 0.5,
            "axisNum": 4,
            "BACKGROUNDAnimationDisplay": false,
            "BACKGROUNDAnimationTime": 2000,
            "BACKGROUNDAnimationDelay": 20,
            "BACKGROUNDAnimationType": "bounce",  //linear,circle,elastic,bounce    };
        };

        self.TEXTsettings = {
            "x": 20,
            "y": 20,
            "textSize": 10,
            "textWeight": "normal", //normal,bold,bolder,lighter,100,200,300,400,500,600,700,800,900
            "textColor": "#000",
            "textOpacity": 1.0,
            "rotateRate": 0,
            "text": " ",
            "TEXTAnimationDisplay": false,
            "TEXTAnimationInitialSize": 20,
            "TEXTAnimationInitialWeight": "bold",
            "TEXTAnimationInitialColor": "black",
            "TEXTAnimationInitialOpacity": 1,
            "TEXTAnimationInitialPositionX": 0,
            "TEXTAnimationInitialPositionY": 0,
            "TEXTAnimationInitialRotate": 0,
            "TEXTAnimationDelay": 50,
            "TEXTAnimationTime": 1000,
            "TEXTAnimationType": "linear",
        };

        self.ARCsettings = {
            "compareGroup": 1,
            "innerRadius": -30,
            "outerRadius": -30,
            "ARCOpacity": 1,
            "ARCAnimationDisplay": false,
            "ARCAnimationTime": 2000,
            "ARCAnimationDelay": 20,
            "ARCAnimationType": "bounce",  //linear,circle,elastic,bounce
        };

        //zhec3

        self.update_settings(self.argumentsNGCircosSettings)

        self.target = "#" + self.settings.target;
        self.svgWidth = self.settings.svgWidth;
        self.svgHeight = self.settings.svgHeight;
        //zhec
        self.svgClassName = self.settings.svgClassName;
        //zhec
        self.chrPad = self.settings.chrPad;
        self.innerRadius = self.settings.innerRadius;
        self.outerRadius = self.settings.outerRadius;
        self.zoom = self.settings.zoom;
        self.testTip = self.settings.testTip;
        self.compareEvent = self.settings.compareEvent;
        self.genomeFillColor = self.settings.genomeFillColor;
        self.genomeBorderDisplay = self.settings.genomeBorder.display;
        self.genomeBorderColor = self.settings.genomeBorder.borderColor;
        self.genomeBorderSize = self.settings.genomeBorder.borderSize;
        if (self.compareEvent == false) {
            self.genome = self.argumentsNGCircosGenome[0];
            self.genome_matrix(self.argumentsNGCircosGenome[0]);
            self.genome2 = 0;
            self.genome_matrix2(0);
        } else {
            self.genome = self.argumentsNGCircosGenome[0];
            self.genome_matrix(self.argumentsNGCircosGenome[0]);
            self.genome2 = self.argumentsNGCircosGenome[1];
            self.genome_matrix2(self.argumentsNGCircosGenome[1]);
        }
        self.ticksDisplay = self.settings.ticks.display;
        self.ticksLength = self.settings.ticks.len;
        self.ticksColor = self.settings.ticks.color;
        self.ticksTextSize = self.settings.ticks.textSize;
        self.ticksTextColor = self.settings.ticks.textColor;
        self.ticksScale = self.settings.ticks.scale;
        //zhec2
        self.ticksRealLength = self.settings.ticks.realLength
        //zhec2
        //offset
        self.ticksOffset = self.settings.ticks.offset
        //offset
        self.genomeTextDisplay = self.settings.genomeLabel.display;
        self.genomeTextSize = self.settings.genomeLabel.textSize;
        self.genomeTextColor = self.settings.genomeLabel.textColor;
        self.genomeTextDx = self.settings.genomeLabel.dx;
        self.genomeTextDy = self.settings.genomeLabel.dy;

        var labeli = self.genomeLabel.length;
        var initGenome = new Object();
        var initGenome2 = new Object();
        for (var labelk = 0; labelk < labeli; labelk++) {
            var labelInit = self.genomeLabel[labelk];
            initGenome[labelInit] = labelk;
        }
        for (var labelk = 0; labelk < labeli; labelk++) {
            var labelInit = self.genomeLabel2[labelk];
            initGenome2[labelInit] = labelk;
        }
        self.initGenome = initGenome;
        self.initGenome2 = initGenome2;
        // console.log(self.initGenome)

    }

    NGCircos.prototype.update_settings = function (settings_object) {
        var self = this;
        $.extend(self.settings, settings_object);
    }


    NGCircos.prototype.update_SNPsettings = function (settings_object) {
        var self = this;
        $.extend(self.SNPsettings, settings_object);
    }

    NGCircos.prototype.update_BACKGROUNDsettings = function (settings_object) {
        var self = this;
        $.extend(self.BACKGROUNDsettings, settings_object);
    }

    NGCircos.prototype.update_TEXTsettings = function (settings_object) {
        var self = this;
        $.extend(self.TEXTsettings, settings_object);
    }

    NGCircos.prototype.update_ARCsettings = function (settings_object) {
        var self = this;
        $.extend(self.ARCsettings, settings_object);
    }


    NGCircos.prototype.init_SNPsettings = function () {
        var self = this;
        self.SNPsettings = {
            "compareGroup": 1,
            "maxRadius": 200,
            "minRadius": 190,
            "SNPFillColorType": "specific", //specific,r2
            "SNPFillColor": "red",
            "SNPFillr2Color": ["#ff0031", "#ff0031", "#ff0031", "#ff0031", "#ff0031"],
            "ValueAxisManualScale": false,
            "ValueAxisMaxScale": 10,
            "ValueAxisMinScale": 0,
            "PointType": "circle",
            "circleSize": 2,
            "rectWidth": 2,
            "rectHeight": 2,
            "SNPAnimationDisplay": false,
            "SNPAnimationInitialPositionX": 0,
            "SNPAnimationInitialPositionY": 0,
            "SNPAnimationTime": 2000,
            "SNPAnimationDelay": 20,
            "SNPAnimationType": "bounce",  //linear,circle,elastic,bounce
        };
    }


    NGCircos.prototype.init_BACKGROUNDsettings = function () {
        var self = this;
        self.BACKGROUNDsettings = {
            "compareGroup": 1,
            "BginnerRadius": 180,
            "BgouterRadius": 230,
            "BgFillColor": "none",
            "BgborderColor": "#000",
            "BgborderSize": 0.5,
            "axisShow": "false",
            "axisWidth": 0.3,
            "axisColor": "#000",
            "axisOpacity": 0.5,
            "axisNum": 4,
            "BACKGROUNDAnimationDisplay": false,
            "BACKGROUNDAnimationTime": 2000,
            "BACKGROUNDAnimationDelay": 20,
            "BACKGROUNDAnimationType": "bounce",  //linear,circle,elastic,bounce
        };
    }


    NGCircos.prototype.init_TEXTsettings = function () {
        var self = this;
        self.TEXTsettings = {
            "x": 20,
            "y": 20,
            "textSize": 10,
            "textColor": "#000",
            "textWeight": "normal", //normal,bold,bolder,lighter,100,200,300,400,500,600,700,800,900
            "textOpacity": 1.0,
            "rotateRate": 0,
            "text": " ",
            "TEXTAnimationDisplay": false,
            "TEXTAnimationInitialSize": 20,
            "TEXTAnimationInitialWeight": "bold",
            "TEXTAnimationInitialColor": "black",
            "TEXTAnimationInitialOpacity": 1,
            "TEXTAnimationInitialPositionX": 0,
            "TEXTAnimationInitialPositionY": 0,
            "TEXTAnimationInitialRotate": 0,
            "TEXTAnimationDelay": 50,
            "TEXTAnimationTime": 1000,
            "TEXTAnimationType": "linear",
        };
    }


    NGCircos.prototype.init_ARCsettings = function () {
        var self = this;
        self.ARCsettings = {
            "compareGroup": 1,
            "innerRadius": -100,
            "outerRadius": -100,
            "ARCOpacity": 1,
            "ARCAnimationDisplay": false,
            "ARCAnimationTime": 2000,
            "ARCAnimationDelay": 20,
            "ARCAnimationType": "bounce",  //linear,circle,elastic,bounce
        };
    }

    NGCircos.prototype.genome_matrix = function (genome) {
        var self = this;
        var i = self.genome.length;
        var genomeLabel = new Array();
        var genomeLength = new Array();
        if (self.compareEvent == true) {
            genomeLabel[0] = "fake1Gap1";
            for (var k = 0; k < i; k++) {
                genomeLabel[k + 1] = self.genome[k][0];
            }
            genomeLabel[i + 1] = "fake1Gap2";
            var genomeGap = 0;
            for (var k = 0; k < i; k++) {
                genomeGap += self.genome[k][1]
            }
            genomeGap = genomeGap * self.settings.compareEventGroupGapRate
            genomeLength[0] = genomeGap;
            for (var k = 0; k < i; k++) {
                genomeLength[k + 1] = self.genome[k][1];
            }
            genomeLength[i + 1] = genomeGap;
            genomeLabel[i + 2] = "fake1Gap3";
            for (var k = i; k < 2 * i; k++) {
                genomeLabel[k + 3] = "fake1" + self.genome[2 * i - 1 - k][0];
            }
            genomeLabel[2 * i + 3] = "fake1Gap4";
            genomeLength[i + 2] = genomeGap;
            for (var k = i; k < 2 * i; k++) {
                genomeLength[k + 3] = self.genome[2 * i - 1 - k][1];
            }
            genomeLength[2 * i + 3] = genomeGap;
        } else {
            for (var k = 0; k < i; k++) {
                genomeLabel[k] = self.genome[k][0];
            }
            for (var k = 0; k < i; k++) {
                genomeLength[k] = self.genome[k][1];
            }
        }

        var i = genomeLength.length;
        var p = genomeLength.length;
        var genome = new Array();
        for (var k = 0; k < i; k++) {
            genome[k] = new Array();
            for (var j = 0; j < p; j++) {
                genome[k][j] = 0;
            }
        }
        for (var k = 0; k < i; k++) {
            genome[k][0] = genomeLength[k];
        }
        self.genomeLabel = genomeLabel;
        self.genomeLength = genome;
        //    console.log(self.genomeLabel)
        // console.log(self.genomeLength)
    }

    //compare
    NGCircos.prototype.genome_matrix2 = function (genome) {
        var self = this;
        if (genome == 0) {
            self.genomeLabel2 = 0;
            self.genomeLength2 = 0;
        } else {
            var i = self.genome2.length;
            var genomeLabel2 = new Array();
            var genomeLength2 = new Array();
            var genomeGap = 0;
            for (var k = 0; k < i; k++) {
                genomeGap += self.genome[k][1]
            }
            genomeGap = genomeGap * self.settings.compareEventGroupGapRate

            genomeLabel2[0] = "fake2Gap1";
            genomeLabel2[i + 1] = "fake2Gap2";
            genomeLabel2[i + 2] = "fake2Gap3";
            genomeLabel2[2 * i + 3] = "fake2Gap4";
            genomeLength2[0] = genomeGap;
            genomeLength2[i + 1] = genomeGap;
            genomeLength2[i + 2] = genomeGap;
            genomeLength2[2 * i + 3] = genomeGap;
            for (var k = i; k < 2 * i; k++) {
                genomeLabel2[k + 3] = self.genome2[2 * i - 1 - k][0];
            }
            for (var k = 0; k < i; k++) {
                genomeLabel2[k + 1] = "fake2" + self.genome2[k][0];
            }
            for (var k = 0; k < i; k++) {
                genomeLength2[k + 1] = self.genome2[k][1];
            }
            for (var k = i; k < 2 * i; k++) {
                genomeLength2[k + 3] = self.genome2[2 * i - 1 - k][1];
            }
            var i = genomeLength2.length;
            var p = genomeLength2.length;
            var genome = new Array();
            for (var k = 0; k < i; k++) {
                genome[k] = new Array();
                for (var j = 0; j < p; j++) {
                    genome[k][j] = 0;
                }
            }
            for (var k = 0; k < i; k++) {
                genome[k][0] = genomeLength2[k];
            }
            self.genomeLabel2 = genomeLabel2;
            self.genomeLength2 = genome;
        }
        // console.log(self.genomeLabel2)
    }
    //compare

    NGCircos.prototype.snp_value_maxmin = function (snpIn) {
        var self = this;
        var i = snpIn.length;
        var snpValueList = new Array();
        for (var k = 0; k < i; k++) {
            snpValueList[k] = snpIn[k].value;
        }
        if (self.SNPsettings.ValueAxisManualScale == true) {
            snpValueList[i] = self.SNPsettings.ValueAxisMaxScale
            snpValueList[i + 1] = self.SNPsettings.ValueAxisMinScale
        }

        Array.max = function (array) {
            return Math.max.apply(Math, array);
        }
        Array.min = function (array) {
            return Math.min.apply(Math, array);
        }
        var snpValueMax = Array.max(snpValueList);
        var snpValueMin = Array.min(snpValueList);
        var snpValueMaxmin = new Array();
        snpValueMaxmin[0] = snpValueMax;
        snpValueMaxmin[1] = snpValueMin;
        // console.log('snpValueList',snpValueMaxmin)

        return snpValueMaxmin;
    }

    //SNPr2class
    NGCircos.prototype.snp_r2Value_color = function (r2Value) {
        var self = this;
        if (parseFloat(r2Value) < 0.2) {
            return self.SNPsettings.SNPFillr2Color[0]
        } else if (parseFloat(r2Value) < 0.4) {
            return self.SNPsettings.SNPFillr2Color[1]
        } else if (parseFloat(r2Value) < 0.6) {
            return self.SNPsettings.SNPFillr2Color[2]
        } else if (parseFloat(r2Value) < 0.8) {
            return self.SNPsettings.SNPFillr2Color[3]
        } else {
            return self.SNPsettings.SNPFillr2Color[4]
        }
    }
    //SNPr2class

    NGCircos.prototype.draw_genome = function (genome, drawTime) {
        drawTime += 1
        if (genome == 0) {
            return;
        }
        var self = this;
        if (drawTime == 2) {
            self.genomeLabel = self.genomeLabel2
            self.initGenome = self.initGenome2
        }
        //console.log(self.initGenome)
        //console.log(self.genomeLabel)
        var chord = d3.layout.chord()
            .padding(self.chrPad)
            .sortSubgroups(d3.descending)
            .matrix(genome);
        var width = self.svgWidth,
            height = self.svgHeight,
            svgClassName = self.svgClassName,
            innerRadius = self.innerRadius,
            outerRadius = self.outerRadius;
        var circleCenter = width / 2
        var compareMoveDistance = 0
        if (self.settings.compareEvent == true) {
            if (drawTime == 1) {
                compareMoveDistance = self.settings.compareEventGroupDistance / 2
            }
            if (drawTime == 2) {
                compareMoveDistance = -1 * self.settings.compareEventGroupDistance / 2
            }
        }
        // console.log(compareMoveDistance)

        if (self.settings.SNPMouseCombinationEvent == true) {
            width = 2 * width
            circleCenter = width / 4
        }

        var fill = d3.scale.ordinal()
            .domain(d3.range(4))
            .range(self.genomeFillColor);


        if (drawTime == 1) {
            if (self.zoom == true) {
                function zoom() {
                    a = d3.event.translate[0] + circleCenter / 2
                    b = d3.event.translate[1] + height / 2
                    svg.attr("transform", "translate("
                        + a + "," + b
                        + ")scale(" + d3.event.scale + ")");
                }

                var svg = d3.select(self.target).append("svg")
                    .attr("width", width)
                    .attr("height", height)
                    .attr("class", svgClassName)
                    .attr("id", svgClassName)
                    .call(
                        d3.behavior.zoom()
                            .scaleExtent([0.9, 10])
                            .on("zoom", zoom)
                    )
                    .append("g")
                    //              .attr("transform", "translate(" + (circleCenter + compareMoveDistance) + "," + height / 2 + ")");
                    .attr("transform", "translate(" + circleCenter + "," + height / 2 + ")");

            } else {
                var svg = d3.select(self.target).append("svg")
                    .attr("width", width)
                    .attr("height", height)
                    .attr("class", svgClassName)
                    .attr("id", svgClassName)
                    .append("g")
                    //              .attr("transform", "translate(" + (circleCenter + compareMoveDistance) + "," + height / 2 + ")");
                    .attr("transform", "translate(" + circleCenter + "," + height / 2 + ")");
            }
        } else {
//      var svg = d3.select(self.target).select("svg").select("g").attr("transform", "translate(" + (circleCenter + compareMoveDistance) + "," + height / 2 + ")");
            var svg = d3.select(self.target).select("svg").select("g").attr("transform", "translate(" + circleCenter + "," + height / 2 + ")");


        }

        //console.log(self.genomeLabel)
        if (self.genomeBorderDisplay == true) {

            svg.append("g").selectAll("path")
                .data(chord.groups)
                .enter().append("path")
                .style("fill", function (d) {
                    return fill(d.index);
                })
                .style("stroke", self.genomeBorderColor)
                .style("stroke-width", self.genomeBorderSize)
                .style("fill-opacity", function (d) {
                    var reg = /^fake/;
                    if (reg.test(self.genomeLabel[d.index])) {
                        return 0;
                    } else {
                        return 1;
                    }
                })
                .style("stroke-opacity", function (d) {
                    var reg = /^fake/;
                    if (reg.test(self.genomeLabel[d.index])) {
                        return 0;
                    } else {
                        return 1;
                    }
                })
                .attr("d", d3.svg.arc().innerRadius(innerRadius).outerRadius(outerRadius))
                .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")")
                .attr("name", function (d) {
                    return d.index + 1;
                });
        } else {
            svg.append("g").selectAll("path")
                .data(chord.groups)
                .enter().append("path")
                .style("fill", function (d) {
                    return fill(d.index);
                })
                .style("stroke", function (d) {
                    return fill(d.index);
                })
                .style("fill-opacity", function (d) {
                    var reg = /^fake/;
                    if (reg.test(self.genomeLabel[d.index])) {
                        return 0;
                    } else {
                        return 1;
                    }
                })
                .style("stroke-opacity", function (d) {
                    var reg = /^fake/;
                    if (reg.test(self.genomeLabel[d.index])) {
                        return 0;
                    } else {
                        return 1;
                    }
                })
                .attr("d", d3.svg.arc().innerRadius(innerRadius).outerRadius(outerRadius))
                .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")")
                .attr("name", function (d) {
                    return d.index + 1;
                });
        }

        if (self.genomeTextDisplay == true) {
            svg.append("g").selectAll("text")
                .data(chord.groups)
                .enter().append("text")
                .style("fill", self.genomeTextColor)
                .style("font-size", self.genomeTextSize)
                .style("fill-opacity", function (d) {
                    var reg = /^fake/;
                    if (reg.test(self.genomeLabel[d.index])) {
                        return 0;
                    } else {
                        return 1;
                    }
                })
                .each(function (d, i) {
                    d.angle = (d.startAngle + d.endAngle) / 2 - self.genomeTextDx;
                    d.name = self.genomeLabel[i];
                })
                .attr("dy", self.genomeTextDy)
                .attr("transform", function (d) {
                    if (drawTime == 1) {
                        return "rotate(" + (d.angle * 180 / Math.PI) + ")" +
                            "translate(0," + (-1.0 * (outerRadius + 10) - compareMoveDistance) + ")" +
                            ((d.angle > Math.PI * 2 && d.angle < Math.PI * 0) ? "rotate(180)" : "");
                    }
                    if (drawTime == 2) {
                        return "rotate(" + (d.angle * 180 / Math.PI) + ")" +
                            "translate(0," + (-1.0 * (outerRadius + 10) + compareMoveDistance) + ")" +
                            ((d.angle > Math.PI * 2 && d.angle < Math.PI * 0) ? "rotate(180)" : "");
                    }

                })
                .text(function (d) {
                    return d.name;
                });
        }

        if (self.ticksDisplay == true) {
            function groupTicks(d) {
                var k = (d.endAngle - d.startAngle) / d.value;
                return d3.range(0, d.value, self.ticksScale).map(function (v, i) {
                    return {
                        angle: v * k + d.startAngle,
                        label: v / self.ticksScale + "",
                        index: d.index

                    };
                });
            }

            var ticks = svg.append("g").selectAll("g")
                .data(chord.groups)
                .enter().append("g").selectAll("g")
                .data(groupTicks)
                .enter().append("g")
                .attr("transform", function (d) {
//              return "rotate(" + (d.angle * 180 / Math.PI - 90) + ")"
//                  + "translate(" + (outerRadius - 0) + ",0)";
                    return "rotate(" + (d.angle * 180 / Math.PI - 90) + ")"
                        + "translate(" + (outerRadius - 0 + Math.sin(d.angle) * compareMoveDistance) + "," + Math.cos(d.angle) * compareMoveDistance + ")";
//              return "translate(" + (Math.sin(d.angle)*(outerRadius - 0)+compareMoveDistance) + ","+-1*Math.cos(d.angle)*(outerRadius-0)+")";
                });

            ticks.append("line")
                .attr("x1", 1)
                .attr("y1", 0)
                .attr("x2", self.ticksLength)
                .attr("y2", 0)
                .style("stroke", self.ticksColor)
                .style("stroke-opacity", function (d) {
//                console.log(d)
                    var reg = /^fake/;
                    if (reg.test(self.genomeLabel[d.index])) {
                        return 0;
                    } else {
                        return 1;
                    }
                });

            ticks.append("text")
                .attr("x", 8)
                .attr("dy", ".35em")
                .style("font-size", self.ticksTextSize)
                .style("fill", self.ticksTextColor)
                .style("fill-opacity", function (d) {
                    var reg = /^fake/;
                    if (reg.test(self.genomeLabel[d.index])) {
                        return 0;
                    } else {
                        return 1;
                    }
                })
                .attr("transform", function (d) {
                    // console.log(d)
                    // return d.angle > Math.PI ? "rotate(270)translate(0, 10)" : "rotate(270)translate(-15, 10)";

                    if (d.angle > Math.PI) {
                        return d.angle < 5 ? "rotate(270)translate(0, 10)" : "rotate(90)translate(0, -10)"
                    } else {
                        return d.angle < 1 ? "rotate(90)translate(-15, -10)" : "rotate(270)translate(-15, 10)"
                    }
                })
                // return d.angle > Math.PI ? "rotate(270)translate(0, 10)" : "rotate(270)translate(-15, 10)"; })
                .style("text-anchor", function (d) {
                    return d.angle > Math.PI ? "end" : null;
                })
                .text(function (d) {
                    if (self.ticksRealLength == true) {
                        if (self.ticksOffset != undefined) {
                            return (d.label * self.ticksScale + self.ticksOffset);
                        } else {
                            return d.label * self.ticksScale;
                        }
                    } else {
                        if (self.ticksOffset != undefined) {
                            if (d.label == 0) {
                                return d.label + 'Mb'
                            } else {
                                if (d.label % 7 === 0) {
                                    return d.label + self.ticksOffset + 'Mb'
                                }
                            }

                        } else {
                            return d.label;
                        }
                    }
                });

        }

        var drag = d3.behavior.drag()
            .on("drag", dragmove);

        function dragmove(d) {
            d3.select(this)
                .attr("x", d3.event.x)
                .attr("y", d3.event.y);
        }

        if (self.BACKGROUND.length > 0) {
            for (var backgroundi = 0; backgroundi < self.BACKGROUND.length; backgroundi++) {
                self.update_BACKGROUNDsettings(self.BACKGROUNDConfig[backgroundi]);
                if (drawTime == self.BACKGROUNDsettings.compareGroup) {
                    if (self.BACKGROUNDsettings.BACKGROUNDAnimationDisplay == false) {
                        svg.append("g").selectAll("path")
                            .data(chord.groups)
                            .enter()
                            .append("path")
                            .filter(function (d, i) {
                                if (self.settings.compareEvent == true) {
                                    if (self.BACKGROUNDsettings.compareGroup == 1) {
                                        return (i > 0 && i < (self.genome.length + 1));
                                    }
                                    if (self.BACKGROUNDsettings.compareGroup == 2) {
                                        return (i > (self.genome.length + 2) && i < (self.genome.length * 2 + 3));
                                    }
                                } else {
                                    return true;
                                }
                            })
                            .style("fill", self.BACKGROUNDsettings.BgFillColor)
                            .style("stroke", self.BACKGROUNDsettings.BgborderColor)
                            .style("stroke-width", self.BACKGROUNDsettings.BgborderSize)
                            .attr("d", d3.svg.arc().innerRadius(self.BACKGROUNDsettings.BginnerRadius).outerRadius(self.BACKGROUNDsettings.BgouterRadius))
                            .attr("transform", function (d, i) {
                                return "translate(" + compareMoveDistance + "," + 0 + ")";
                            })

                        if (self.BACKGROUNDsettings.axisShow == "true") {
                            for (i = 1; i <= self.BACKGROUNDsettings.axisNum; i++) {
                                svg.append("g").selectAll("path")
                                    .data(chord.groups)
                                    .enter().append("path")
                                    .filter(function (d, i) {
                                        if (self.settings.compareEvent == true) {
                                            if (self.BACKGROUNDsettings.compareGroup == 1) {
                                                return (i > 0 && i < (self.genome.length + 1));
                                            }
                                            if (self.BACKGROUNDsettings.compareGroup == 2) {
                                                return (i > (self.genome.length + 2) && i < (self.genome.length * 2 + 3));
                                            }
                                        } else {
                                            return true;
                                        }
                                    })
                                    .style("fill", "none")
                                    .style("opacity", self.BACKGROUNDsettings.axisOpacity)
                                    .style("stroke", self.BACKGROUNDsettings.axisColor)
                                    .style("stroke-width", self.BACKGROUNDsettings.axisWidth)
                                    .attr("d", d3.svg.arc().innerRadius(self.BACKGROUNDsettings.BginnerRadius + (self.BACKGROUNDsettings.BgouterRadius - self.BACKGROUNDsettings.BginnerRadius) / (self.BACKGROUNDsettings.axisNum + 1) * i).outerRadius(self.BACKGROUNDsettings.BginnerRadius + (self.BACKGROUNDsettings.BgouterRadius - self.BACKGROUNDsettings.BginnerRadius) / (self.BACKGROUNDsettings.axisNum + 1) * i + self.BACKGROUNDsettings.axisWidth))
                                    .attr("transform", function (d, i) {
                                        return "translate(" + compareMoveDistance + "," + 0 + ")";
                                    })
                            }
                        }
                    }
                    if (self.BACKGROUNDsettings.BACKGROUNDAnimationDisplay == true) {
                        svg.append("g").selectAll("path")
                            .data(chord.groups)
                            .enter()
                            .append("path")
                            .filter(function (d, i) {
                                if (self.settings.compareEvent == true) {
                                    if (self.BACKGROUNDsettings.compareGroup == 1) {
                                        return (i > 0 && i < (self.genome.length + 1));
                                    }
                                    if (self.BACKGROUNDsettings.compareGroup == 2) {
                                        return (i > (self.genome.length + 2) && i < (self.genome.length * 2 + 3));
                                    }
                                } else {
                                    return true;
                                }
                            })
                            .style("fill", self.BACKGROUNDsettings.BgFillColor)
                            .style("stroke", self.BACKGROUNDsettings.BgborderColor)
                            .style("stroke-width", self.BACKGROUNDsettings.BgborderSize)
                            .attr("d", d3.svg.arc().innerRadius(self.BACKGROUNDsettings.BginnerRadius).outerRadius(self.BACKGROUNDsettings.BginnerRadius))
                            .attr("transform", function (d, i) {
                                return "translate(" + compareMoveDistance + "," + 0 + ")"
                            })
                            .transition()
                            .delay(function (d, i) {
                                return (i + 1) * self.BACKGROUNDsettings.BACKGROUNDAnimationDelay;
                            })
                            .duration(self.BACKGROUNDsettings.BACKGROUNDAnimationTime)
                            .ease(self.BACKGROUNDsettings.BACKGROUNDAnimationType)
                            .attr("d", d3.svg.arc().innerRadius(self.BACKGROUNDsettings.BginnerRadius).outerRadius(self.BACKGROUNDsettings.BgouterRadius));

                        if (self.BACKGROUNDsettings.axisShow == "true") {
                            for (i = 1; i <= self.BACKGROUNDsettings.axisNum; i++) {
                                svg.append("g").selectAll("path")
                                    .data(chord.groups)
                                    .enter().append("path")
                                    .filter(function (d, i) {
                                        if (self.settings.compareEvent == true) {
                                            if (self.BACKGROUNDsettings.compareGroup == 1) {
                                                return (i > 0 && i < (self.genome.length + 1));
                                            }
                                            if (self.BACKGROUNDsettings.compareGroup == 2) {
                                                return (i > (self.genome.length + 2) && i < (self.genome.length * 2 + 3));
                                            }
                                        } else {
                                            return true;
                                        }
                                    })
                                    .style("fill", "none")
                                    .style("opacity", self.BACKGROUNDsettings.axisOpacity)
                                    .style("stroke", self.BACKGROUNDsettings.axisColor)
                                    .style("stroke-width", self.BACKGROUNDsettings.axisWidth)
                                    .attr("d", d3.svg.arc().innerRadius(self.BACKGROUNDsettings.BginnerRadius).outerRadius(self.BACKGROUNDsettings.BginnerRadius))
                                    .attr("transform", function (d, i) {
                                        return "translate(" + compareMoveDistance + "," + 0 + ")";
                                    })
                                    .transition()
                                    .delay(function (d, i) {
                                        return (i + 1) * self.BACKGROUNDsettings.BACKGROUNDAnimationDelay;
                                    })
                                    .duration(self.BACKGROUNDsettings.BACKGROUNDAnimationTime)
                                    .ease(self.BACKGROUNDsettings.BACKGROUNDAnimationType)
                                    .attr("d", d3.svg.arc().innerRadius(self.BACKGROUNDsettings.BginnerRadius + (self.BACKGROUNDsettings.BgouterRadius - self.BACKGROUNDsettings.BginnerRadius) / (self.BACKGROUNDsettings.axisNum + 1) * i).outerRadius(self.BACKGROUNDsettings.BginnerRadius + (self.BACKGROUNDsettings.BgouterRadius - self.BACKGROUNDsettings.BginnerRadius) / (self.BACKGROUNDsettings.axisNum + 1) * i + self.BACKGROUNDsettings.axisWidth))
                            }
                        }
                    }


                }
                self.init_BACKGROUNDsettings();

            }
        }

        if (self.TEXT.length > 0 && drawTime == 1) {
            for (var texti = 0; texti < self.TEXT.length; texti++) {
                self.update_TEXTsettings(self.TEXTConfig[texti]);
                if (self.TEXTsettings.TEXTAnimationDisplay == false) {
                    svg.append("text")
                        .attr("x", 0)
                        .attr("y", 0)
                        .style("opacity", self.TEXTsettings.textOpacity)
                        .style("font-size", self.TEXTsettings.textSize)
                        .style("font-weight", self.TEXTsettings.textWeight) //normal,bold,bolder,lighter,100,200,300,400,500,600,700,800,900
                        .attr("fill", self.TEXTsettings.textColor)
                        .attr("class", "dragText")
                        .attr("transform", function (d) {
                            return "translate(" + self.TEXTsettings.x + " " + self.TEXTsettings.y + ") rotate(" + self.TEXTsettings.rotateRate * 180 + ")"
                        })
                        .text(self.TEXTsettings.text);
                }
                if (self.TEXTsettings.TEXTAnimationDisplay == true) {
                    svg.append("text")
                        .attr("x", 0)
                        .attr("y", 0)
                        .style("opacity", self.TEXTsettings.TEXTAnimationInitialOpacity)
                        .style("font-size", self.TEXTsettings.TEXTAnimationInitialSize)
                        .style("font-weight", self.TEXTsettings.TEXTAnimationInitialWeight) //normal,bold,bolder,lighter,100,200,300,400,500,600,700,800,900
                        .attr("fill", self.TEXTsettings.TEXTAnimationInitialColor)
                        .attr("class", "dragText")
                        .attr("transform", function (d) {
                            return "translate(" + self.TEXTsettings.TEXTAnimationInitialPositionX + " " + self.TEXTsettings.TEXTAnimationInitialPositionY + ") rotate(" + self.TEXTsettings.TEXTAnimationInitialRotate * 180 + ")"
                        })
                        .text(self.TEXTsettings.text)
                        .transition()
                        .delay(function (d, i) {
                            return self.TEXTsettings.TEXTAnimationDelay;
                        })
                        .duration(self.TEXTsettings.TEXTAnimationTime)
                        .ease(self.TEXTsettings.TEXTAnimationType)
                        .attr("transform", function (d) {
                            return "translate(" + self.TEXTsettings.x + " " + self.TEXTsettings.y + ") rotate(" + self.TEXTsettings.rotateRate * 180 + ")"
                        });
                }
                self.init_TEXTsettings();
            }
            if (self.settings.TEXTModuleDragEvent == true) {
                svg.selectAll("text.dragText").call(drag);
            }

        }

        if (self.SNP.length > 0) {
            // console.log('self.SNP',self.SNP)
            function NGCircosSNP(d) {
                // var minValue=self.snp_value_maxmin(self.SNP[snpi])[1]
                //  var maxValue=self.snp_value_maxmin(self.SNP[snpi])[0]
                //  console.log(self.SNP)
                return self.SNP[snpi].map(function (v, i) {
                    // console.log(i)
                    var snp_k = (d[self.initGenome[v.chr]].endAngle - d[self.initGenome[v.chr]].startAngle) / d[self.initGenome[v.chr]].value;
                    return {
                        snp_angle: v.pos * snp_k + d[self.initGenome[v.chr]].startAngle,
                        snp_chr: v.chr,
                        snp_pos: v.pos,
                        snp_val: v.value,
                        snp_des: v.des,
                        snp_color: v.color,
                        snp_r2value: v.r2value,
                        snp_index: v.index,
                        x: (0 + Math.sin(v.pos * snp_k + d[self.initGenome[v.chr]].startAngle) * (self.SNPsettings.minRadius + ((v.value - 0) / (1 - 0) * (self.SNPsettings.maxRadius - self.SNPsettings.minRadius)))),  //self.snp_value_maxmin(self.SNP[snpi])[0] max
                        y: (0 - Math.cos(v.pos * snp_k + d[self.initGenome[v.chr]].startAngle) * (self.SNPsettings.minRadius + ((v.value - 0) / (1 - 0) * (self.SNPsettings.maxRadius - self.SNPsettings.minRadius)))),
                    };
                });
            }

            function NGCircosSNP2(d) {
                return self.SNP[snpi].map(function (v, i) {
                    var snp_k = (d[self.initGenome[v.chr]].endAngle - d[self.initGenome[v.chr]].startAngle) / d[self.initGenome[v.chr]].value;
                    return {
                        snp_angle: 3 * Math.PI - (v.pos * snp_k + d[self.initGenome[v.chr]].startAngle),
                        snp_chr: v.chr,
                        snp_pos: v.pos,
                        snp_val: v.value,
                        snp_des: v.des,
                        snp_color: v.color,
                        snp_r2value: v.r2value,
                        snp_link: v.link,
                        snp_click_label: "snp" + snpi + "_" + i,
                        snp_index: v.index,
                        snp_image: v.image,
                        x: (0 + Math.sin(3 * Math.PI - (v.pos * snp_k + d[self.initGenome[v.chr]].startAngle)) * (self.SNPsettings.minRadius + ((v.value - self.snp_value_maxmin(self.SNP[snpi])[1]) / (self.snp_value_maxmin(self.SNP[snpi])[0] - self.snp_value_maxmin(self.SNP[snpi])[1]) * (self.SNPsettings.maxRadius - self.SNPsettings.minRadius)))),  //self.snp_value_maxmin(self.SNP[snpi])[0] max
                        y: (0 - Math.cos(3 * Math.PI - (v.pos * snp_k + d[self.initGenome[v.chr]].startAngle)) * (self.SNPsettings.minRadius + ((v.value - self.snp_value_maxmin(self.SNP[snpi])[1]) / (self.snp_value_maxmin(self.SNP[snpi])[0] - self.snp_value_maxmin(self.SNP[snpi])[1]) * (self.SNPsettings.maxRadius - self.SNPsettings.minRadius)))),
                        snp_html: v.html,
                    };
                });
            }

            for (var snpi = 0; snpi < self.SNP.length; snpi++) {
                self.update_SNPsettings(self.SNPConfig[snpi]);
                if (drawTime == self.SNPsettings.compareGroup) {
                    //console.log(chord.groups())
                    if (self.SNPsettings.compareGroup == 1) {
                        var snp_objects = NGCircosSNP(chord.groups())
                        // console.log('snp_objects',snp_objects)
                    } else {
                        var snp_objects = NGCircosSNP2(chord.groups())
                    }
                    //console.log(snp_objects)

                    if (self.SNPsettings.PointType == "circle") {
                        if (self.SNPsettings.SNPAnimationDisplay == false) {
                            if (self.SNPsettings.SNPAnimationDisplay == false) {
                                svg.append("g")
                                    .attr("class", "NGCircosSNP")
                                    .selectAll("circle")
                                    .data(snp_objects)
                                    .enter()
                                    .append("a")
                                    .attr("xlink:href", function (d) {
                                        if (self.settings.SNPxlink == true) {
                                            return d.snp_link;
                                        }
                                    })
                                    .append("circle")
                                    .attr("id", "NGCircosSNP")
                                    .attr("fill", function (d, i) {
                                        if (self.SNPsettings.SNPFillColorType == "specific") {
                                            if (d.snp_color != undefined) {
                                                return d.snp_color;
                                            } else {
                                                return self.SNPsettings.SNPFillColor;
                                            }
                                        }
                                        if (self.SNPsettings.SNPFillColorType == "r2") {
                                            return self.snp_r2Value_color(d.snp_r2value);
                                        }
                                    })
                                    .attr("r", self.SNPsettings.circleSize)
                                    .attr("cx", function (d) {
                                        return d.x;
                                    })
                                    .attr("cy", function (d) {
                                        return d.y;
                                    })
                                    .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                            }
                        }
                        if (self.SNPsettings.SNPAnimationDisplay == true) {
                            svg.append("g")
                                .attr("class", "NGCircosSNP")
                                .selectAll("circle")
                                .data(snp_objects)
                                .enter()
                                .append("a")
                                .attr("xlink:href", function (d) {
                                    if (self.settings.SNPxlink == true) {
                                        return d.snp_link;
                                    }
                                })
                                .append("circle")
                                .attr("id", "NGCircosSNP")
                                .attr("fill", function (d, i) {
                                    if (self.SNPsettings.SNPFillColorType == "specific") {
                                        if (d.snp_color != undefined) {
                                            return d.snp_color;
                                        } else {
                                            return self.SNPsettings.SNPFillColor;
                                        }
                                    }
                                    if (self.SNPsettings.SNPFillColorType == "r2") {
                                        return self.snp_r2Value_color(d.snp_r2value);
                                    }
                                })
                                .attr("r", self.SNPsettings.circleSize)
                                .attr("cx", function (d) {
                                    return self.SNPsettings.SNPAnimationInitialPositionX;
                                })
                                .attr("cy", function (d) {
                                    return self.SNPsettings.SNPAnimationInitialPositionY;
                                })
                                .transition()
                                .delay(function (d, i) {
                                    return (i + 1) * self.SNPsettings.SNPAnimationDelay;
                                })
                                .duration(self.SNPsettings.SNPAnimationTime)
                                .ease(self.SNPsettings.SNPAnimationType)
                                .attr("cx", function (d) {
                                    return d.x;
                                })
                                .attr("cy", function (d) {
                                    return d.y;
                                })
                                .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                        }

                        if (self.settings.SNPMouseClickTextFromData == "first") {
                            svg.append("g")
                                .attr("class", "NGCircosSNPlabel")
                                .selectAll("text")
                                .data(snp_objects)
                                .enter().append("text")
                                .attr("class", "dragText")
                                .attr("id", function (d, i) {
                                    return "snp" + snpi + "_" + i;
                                })
                                .text(function (d) {
                                    return d.snp_chr;
                                })
                                .attr("x", -1000)
                                .attr("y", -1000)
                                .style("opacity", 0)
                                .style("font-size", 1)
                                .attr("fill", self.SNPsettings.SNPFillColor)
                                .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                        }
                        if (self.settings.SNPMouseClickTextFromData == "second") {
                            svg.append("g")
                                .attr("class", "NGCircosSNPlabel")
                                .selectAll("text")
                                .data(snp_objects)
                                .enter().append("text")
                                .attr("class", "dragText")
                                .attr("id", function (d, i) {
                                    return "snp" + snpi + "_" + i;
                                })
                                .text(function (d) {
                                    return d.snp_pos;
                                })
                                .attr("x", -1000)
                                .attr("y", -1000)
                                .style("opacity", 0)
                                .style("font-size", 1)
                                .attr("fill", self.SNPsettings.SNPFillColor)
                                .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                        }
                        if (self.settings.SNPMouseClickTextFromData == "third") {
                            svg.append("g")
                                .attr("class", "NGCircosSNPlabel")
                                .selectAll("text")
                                .data(snp_objects)
                                .enter().append("text")
                                .attr("class", "dragText")
                                .attr("id", function (d, i) {
                                    return "snp" + snpi + "_" + i;
                                })
                                .text(function (d) {
                                    return d.snp_val;
                                })
                                .attr("x", -1000)
                                .attr("y", -1000)
                                .style("opacity", 0)
                                .style("font-size", 1)
                                .attr("fill", self.SNPsettings.SNPFillColor)
                                .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                        }
                        if (self.settings.SNPMouseClickTextFromData == "fourth") {
                            svg.append("g")
                                .attr("class", "NGCircosSNPlabel")
                                .selectAll("text")
                                .data(snp_objects)
                                .enter().append("text")
                                .attr("class", "dragText")
                                .attr("id", function (d, i) {
                                    return "snp" + snpi + "_" + i;
                                })
                                .text(function (d) {
                                    return d.snp_des;
                                })
                                .attr("x", -1000)
                                .attr("y", -1000)
                                .style("opacity", 0)
                                .style("font-size", 1)
                                .attr("fill", self.SNPsettings.SNPFillColor)
                                .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                        }
                    }

                }

                self.init_SNPsettings();

            }

            if (self.settings.SNPMouseEvent == true) {
                var SNPMouseOnTooltip = d3.select("body")
                    .append("div")
                    .attr("class", "NGCircosSNPTooltip")
                    .attr("id", "NGCircosSNPTooltip")
                    .style("opacity", 0);

                var SNPMouseOn = svg.selectAll("#NGCircosSNP");

                //combinationSNP
                if (self.settings.SNPMouseCombinationEvent == true) {

//              console.log(self.SNP[0][0].des)
                    var parameterNum = 0;
                    parameterNum = self.SNPGraphData[0][0].length

                    var firstSNP = 0;
                    for (var i = 0; i < self.SNP[0].length; i++) {
                        if (self.SNP[0][i].index != undefined) {
                            var SNPCombinationData = self.SNPGraphData[0][self.SNP[0][i].index]
                            firstSNP = i
                            break;
                        }
                    }
                    if (self.settings.SNPMouseCombinationGraphDisplay == true) {

                        svg.append("text")
                            .attr("class", "SNPCombinationGraphTitle")
                            .attr({
                                id: "SNPCombinationGraphTitle",
                                x: self.settings.SNPMouseCombinationGraphPositionX - 20
                            })
                            .attr("y", (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight - 10))
                            .style("font-size", self.settings.SNPMouseCombinationGraphTitleSize)
                            .style("font-weight", self.settings.SNPMouseCombinationGraphTitleWeight)
                            .attr("fill", self.settings.SNPMouseCombinationGraphTitleColor)
                            .text(self.settings.SNPMouseCombinationGraphTitle + " (" + self.SNP[0][firstSNP].des + ")");

                        if (self.settings.SNPMouseCombinationGraphType == 'histogram') {
                            //x轴的比例尺
                            var xScale = d3.scale.ordinal()
                                .domain(d3.range(SNPCombinationData.length))
                                .rangeRoundBands([0, self.settings.SNPMouseCombinationGraphWidth]);
                            //y轴的比例尺
                            var yScale = d3.scale.linear()
                                .domain([0, d3.max(SNPCombinationData)])
                                .range([self.settings.SNPMouseCombinationGraphHeight, 0]);

                            //定义x轴
                            var XAxisName = self.SNPGraphData[0][0]
                            var ticksValues = []
                            for (var i = 0; i < parameterNum; i++) {
                                ticksValues.push(i)
                            }
                            var xAxis = d3.svg.axis()
                                .scale(xScale)
                                .tickValues(ticksValues)
                                .tickFormat(function (d) {
                                    return XAxisName[d];
                                })
                                .orient("bottom");
                            //定义y轴
                            var yAxis = d3.svg.axis()
                                .scale(yScale)
                                .orient("left");
                            //矩形之间的空白
                            var rectPadding = self.settings.SNPMouseCombinationGraphHistogramPadding;
                            var combinationStartX = self.settings.SNPMouseCombinationGraphPositionX
                            //添加矩形元素
                            var rects = svg.append("g")
                                .attr("class", "SNPCombinationHistogramRect")
                                .selectAll(".SNPCombinationHistogramRect")
                                .data(SNPCombinationData)
                                .enter()
                                .append("rect")
                                .attr("class", "SNPCombinationHistogramRect")
                                .attr("transform", "translate(" + combinationStartX + "," + self.settings.SNPMouseCombinationGraphPositionY + ")")
                                .attr("fill", self.settings.SNPMouseCombinationGraphHistogramBarColor)
                                .attr({
                                    id: "SNPCombinationHistogramRect",
                                    x: function (d, i) {
                                        return xScale(i) + rectPadding / 2 + self.settings.SNPMouseCombinationGraphHistogramPositionCorrectX;
                                    }
                                })
                                .attr("y", function (d) {
                                    return yScale(d) - self.settings.SNPMouseCombinationGraphHeight + 20;
                                })
                                .attr("width", xScale.rangeBand() - rectPadding)
                                .attr("height", function (d) {
                                    //console.log(yScale(d))
                                    return self.settings.SNPMouseCombinationGraphHeight - yScale(d);
                                });

                            if (self.settings.SNPMouseCombinationTextDisplay == true) {
                                //添加文字元素
                                var texts = svg.selectAll(".SNPCombinationHistogramText")
                                    .data(SNPCombinationData)
                                    .enter()
                                    .append("text")
                                    .attr("class", "SNPCombinationHistogramText")
                                    .attr("transform", "translate(" + combinationStartX + "," + self.settings.SNPMouseCombinationGraphPositionY + ")")
                                    .attr("fill", self.settings.SNPMouseCombinationTextColor)
                                    .style("font-size", self.settings.SNPMouseCombinationTextSize)
                                    .style("font-weight", self.settings.SNPMouseCombinationTextWeight)
                                    .attr({
                                        id: "SNPCombinationHistogramText",
                                        x: function (d, i) {
                                            return xScale(i) + rectPadding / 4 + self.settings.SNPMouseCombinationTextPositionCorrectX;
                                        }
                                    })
                                    .attr("y", function (d) {
                                        return yScale(d) - self.settings.SNPMouseCombinationGraphHeight + self.settings.SNPMouseCombinationTextPositionCorrectY;
                                    })
                                    .text(function (d) {
                                        return d;
                                    });
                            }

//                  //添加x轴
                            svg.append("g")
                                .attr("class", "SNPCombinationAxis")
                                .attr("transform", "translate(" + (combinationStartX - 6 + 20) + "," + (self.settings.SNPMouseCombinationGraphPositionY + 20) + ")")
                                .attr({id: "SNPCombinationAxisX"})
                                .attr("fill", "black")
                                .call(xAxis);
//
//                  //添加y轴
                            svg.append("g")
                                .attr("class", "SNPCombinationAxis")
                                .attr("transform", "translate(" + (combinationStartX + 20) + "," + (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight + 20) + ")")
                                .attr("id", "SNPCombinationAxisY")
                                .attr("fill", "black")
                                .call(yAxis);
                        }

                        if (self.settings.SNPMouseCombinationGraphType == 'pie') {
                            var XAxisName = self.SNPGraphData[0][0]

                            var pieCombination = d3.svg.arc()
                                .innerRadius(0)
                                .outerRadius(self.settings.SNPMouseCombinationGraphPieSize);

                            var pie = d3.layout.pie().sort(null)
                                .value(function (d) {
                                    return d;
                                })

//                    console.log(pie(SNPCombinationData))

                            var color20 = d3.scale.category20()
                            var fillPie = d3.scale.ordinal()
                                .range(self.settings.SNPMouseCombinationGraphPieColor);

                            svg.append("g")
                                .attr("class", "SNPCombinationPie")
                                .selectAll("path")
                                .data(pie(SNPCombinationData))
                                .enter()
                                .append("a")
                                .append("path")
                                .attr("id", "SNPCombinationPie")
                                .attr("fill", function (d, i) {
                                    if (self.settings.SNPMouseCombinationGraphPieAutoColor == true) {
                                        return color20(i);
                                    } else {
                                        return fillPie(i);
                                    }
                                })
                                .attr("transform", "translate(" + (self.settings.SNPMouseCombinationGraphPositionX + self.settings.SNPMouseCombinationGraphWidth / 2 + compareMoveDistance) + "," + (self.settings.SNPMouseCombinationGraphPositionY - (self.settings.SNPMouseCombinationGraphHeight - self.settings.SNPMouseCombinationGraphTitleSize * 2) / 2) + ")")
                                .attr("d", pieCombination)
                                .style("stroke", function (d) {
                                    if (self.settings.SNPMouseCombinationGraphPieStroke == true) {
                                        return self.settings.SNPMouseCombinationGraphPieStrokeColor;
                                    } else {
                                        return "";
                                    }
                                })
                                .style("stroke-width", function (d) {
                                    if (self.settings.SNPMouseCombinationGraphPieStroke == true) {
                                        return self.settings.SNPMouseCombinationGraphPieStrokeWidth;
                                    } else {
                                        return "0px";
                                    }
                                })
                                .style("opacity", self.settings.SNPMouseCombinationGraphPieOpacity);

                            if (self.settings.SNPMouseCombinationTextDisplay == true) {
                                //添加文字元素
                                var texts = svg.selectAll(".SNPCombinationPieText")
                                    .data(pie(SNPCombinationData))
                                    .enter()
                                    .append("text")
                                    .attr("class", "SNPCombinationPieText")
                                    .attr("transform", function (d) {
                                        d.innerRadius = 0;
                                        d.outerRadius = self.settings.SNPMouseCombinationGraphPieSize;
                                        console.log(pieCombination.centroid(d))
                                        return "translate(" + pieCombination.centroid(d) + ")";
                                    })
                                    .attr("fill", self.settings.SNPMouseCombinationTextColor)
                                    .style("font-size", self.settings.SNPMouseCombinationTextSize)
                                    .style("font-weight", self.settings.SNPMouseCombinationTextWeight)
                                    .attr({
                                        id: "SNPCombinationHistogramText",
                                        x: (self.settings.SNPMouseCombinationGraphPositionX + self.settings.SNPMouseCombinationGraphWidth / 2 + compareMoveDistance),
                                        y: (self.settings.SNPMouseCombinationGraphPositionY - (self.settings.SNPMouseCombinationGraphHeight - self.settings.SNPMouseCombinationGraphTitleSize * 2) / 2),
                                    })
                                    .text(function (d, i) {
                                        return XAxisName[i];
                                    });
                            }

                        }

                        if (self.settings.SNPMouseCombinationGraphType == 'line') {
                            //x轴的比例尺
                            var xScale = d3.scale.ordinal()
                                .domain(d3.range(SNPCombinationData.length))
                                .rangeRoundBands([0, self.settings.SNPMouseCombinationGraphWidth]);
                            //y轴的比例尺
                            var yScale = d3.scale.linear()
                                .domain([0, d3.max(SNPCombinationData)])
                                .range([self.settings.SNPMouseCombinationGraphHeight, 0]);

                            //定义x轴
                            var XAxisName = self.SNPGraphData[0][0]
                            var ticksValues = []
                            for (var i = 0; i < parameterNum; i++) {
                                ticksValues.push(i)
                            }
                            var xAxis = d3.svg.axis()
                                .scale(xScale)
                                .tickValues(ticksValues)
                                .tickFormat(function (d) {
                                    return XAxisName[d];
                                })
                                .orient("bottom");
                            //定义y轴
                            var yAxis = d3.svg.axis()
                                .scale(yScale)
                                .orient("left");

                            var combinationStartX = self.settings.SNPMouseCombinationGraphPositionX
                            //添加x轴
                            svg.append("g")
                                .attr("class", "SNPCombinationAxis")
                                .attr("transform", "translate(" + (combinationStartX - 6 + 20) + "," + (self.settings.SNPMouseCombinationGraphPositionY + 20) + ")")
                                .attr({id: "SNPCombinationAxisX"})
                                .attr("fill", "black")
                                .call(xAxis);
                            //
                            //添加y轴
                            svg.append("g")
                                .attr("class", "SNPCombinationAxis")
                                .attr("transform", "translate(" + (combinationStartX + 20) + "," + (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight + 20) + ")")
                                .attr("id", "SNPCombinationAxisY")
                                .attr("fill", "black")
                                .call(yAxis);

                            var line = d3.svg.line()
                                .x(function (d) {
                                    return xScale(d.x)
                                })
                                .y(function (d) {
                                    return yScale(d.y);
                                })
                                // 选择线条的类型
                                .interpolate(self.settings.SNPMouseCombinationGraphLineType);

                            var SNPCombinationDataLine = []

                            for (var i = 0; i < SNPCombinationData.length; i++) {
                                SNPCombinationDataLine.push({x: i, y: SNPCombinationData[i]})
                            }

                            svg.append("g")
                                .attr("class", "SNPCombinationLine")
                                .append("path")
                                .attr("id", "SNPCombinationLine")
                                .attr("d", line(SNPCombinationDataLine))
                                .attr("fill", "none")
                                .attr("transform", "translate(" + (combinationStartX + self.settings.SNPMouseCombinationGraphLinePositionCorrectX) + "," + (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight + 20) + ")")
                                .style("stroke", self.settings.SNPMouseCombinationGraphLineColor)
                                .style("stroke-width", self.settings.SNPMouseCombinationGraphLineWidth);

                            if (self.settings.SNPMouseCombinationGraphLinePoint == true) {
                                var color20 = d3.scale.category20()
                                var fillLinePoint = d3.scale.ordinal()
                                    .range(self.settings.SNPMouseCombinationGraphLinePointColor);

                                svg.append("g")
                                    .attr("class", "SNPCombinationLinePoint")
                                    .selectAll('circle')
                                    .data(SNPCombinationDataLine)
                                    .enter()
                                    .append('circle')
                                    .attr("id", "SNPCombinationLinePoint")
                                    .attr('r', self.settings.SNPMouseCombinationGraphLinePointSize)
                                    .attr('cx', function (d) {
                                        return xScale(d.x);
                                    })
                                    .attr('cy', function (d) {
                                        return yScale(d.y);
                                    })
                                    .attr('transform', function (d) {
                                        return 'translate(' + (combinationStartX + self.settings.SNPMouseCombinationGraphLinePositionCorrectX) + ',' + (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight + 20) + ')'
                                    })
                                    .attr("stroke", function (d, i) {
                                        if (self.settings.SNPMouseCombinationGraphLinePointStroke == true) {
                                            return self.settings.SNPMouseCombinationGraphLinePointStrokeColor;
                                        } else {
                                            return "none";
                                        }
                                    })
                                    .attr("stroke-width", function (d, i) {
                                        if (self.settings.SNPMouseCombinationGraphLinePointStroke == true) {
                                            return self.settings.SNPMouseCombinationGraphLinePointStrokeWidth;
                                        } else {
                                            return "none";
                                        }
                                    })
                                    .attr("opacity", self.settings.SNPMouseCombinationGraphLinePointOpacity)
                                    .attr('fill', function (d, i) {
                                        if (self.settings.SNPMouseCombinationGraphLinePointAutoColor == true) {
                                            return color20(i);
                                        } else {
                                            return fillLinePoint(i);
                                        }
                                    });
                            }


                            if (self.settings.SNPMouseCombinationTextDisplay == true) {
                                //添加文字元素
                                var texts = svg.selectAll(".SNPCombinationLineText")
                                    .data(SNPCombinationData)
                                    .enter()
                                    .append("text")
                                    .attr("class", "SNPCombinationLineText")
                                    .attr("transform", "translate(" + combinationStartX + "," + self.settings.SNPMouseCombinationGraphPositionY + ")")
                                    .attr("fill", self.settings.SNPMouseCombinationTextColor)
                                    .style("font-size", self.settings.SNPMouseCombinationTextSize)
                                    .style("font-weight", self.settings.SNPMouseCombinationTextWeight)
                                    .attr({
                                        id: "SNPCombinationLineText",
                                        x: function (d, i) {
                                            return xScale(i) + self.settings.SNPMouseCombinationTextPositionCorrectX;
                                        }
                                    })
                                    .attr("y", function (d) {
                                        return yScale(d) - self.settings.SNPMouseCombinationGraphHeight + self.settings.SNPMouseCombinationTextPositionCorrectY;
                                    })
                                    .text(function (d) {
                                        return d;
                                    });
                            }

//
                        }

                    }

                    if (self.settings.SNPMouseCombinationImageDisplay == true) {
//                console.log(self.SNP[0][0].des)
                        svg.append("text")
                            .attr("class", "SNPCombinationImageTitle")
                            .attr({
                                id: "SNPCombinationImageTitle",
                                x: self.settings.SNPMouseCombinationImagePositionX + 37
                            })
                            .attr("y", (self.settings.SNPMouseCombinationImagePositionY - 20))
                            .style("font-size", self.settings.SNPMouseCombinationImageTitleSize)
                            .style("font-weight", self.settings.SNPMouseCombinationImageTitleWeight)
                            .attr("fill", self.settings.SNPMouseCombinationImageTitleColor)
                            .text(self.settings.SNPMouseCombinationImageTitle + " (" + self.SNP[0][firstSNP].des + ")");

                        svg.append("image")
                            .attr("id", "SNPCombinationImage")
                            .attr("xlink:href", self.SNP[0][firstSNP].image)
                            .attr('x', self.settings.SNPMouseCombinationImagePositionX)
                            .attr('y', self.settings.SNPMouseCombinationImagePositionY)
                            .attr('width', self.settings.SNPMouseCombinationImageWidth)
                            .attr('height', self.settings.SNPMouseCombinationImageHeight)
                    }

                    function SNPCombinationMouseOver(d, i) {
                        d3.selectAll("#" + "SNPCombinationHistogramRect").remove();
                        d3.selectAll("#" + "SNPCombinationHistogramText").remove();
                        d3.selectAll("#" + "SNPCombinationAxisX").remove();
                        d3.selectAll("#" + "SNPCombinationAxisY").remove();
                        d3.selectAll("#" + "SNPCombinationPieText").remove();
                        d3.selectAll("#" + "SNPCombinationPie").remove();
                        d3.selectAll("#" + "SNPCombinationLine").remove();
                        d3.selectAll("#" + "SNPCombinationLineText").remove();
                        d3.selectAll("#" + "SNPCombinationLinePoint").remove();
                        d3.selectAll("#" + "SNPCombinationImage").remove();
                        d3.selectAll("#" + "SNPCombinationGraphTitle").remove();
                        d3.selectAll("#" + "SNPCombinationImageTitle").remove();

                        var drawGraph = 0
                        var parameterNum = 0;
                        parameterNum = self.SNPGraphData[0][0].length

                        if (d.snp_index != undefined) {
                            var SNPCombinationData = self.SNPGraphData[0][d.snp_index]
                            drawGraph = 1
                        }

//                  console.log(SNPCombinationData)
//                  var padding = {left:30, right:60, top:20, bottom:40};
                        if (self.settings.SNPMouseCombinationGraphDisplay == true) {

                            svg.append("text")
                                .attr("class", "SNPCombinationGraphTitle")
                                .attr({
                                    id: "SNPCombinationGraphTitle",
                                    x: self.settings.SNPMouseCombinationGraphPositionX - 20
                                })
                                .attr("y", (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight - 10))
                                .style("font-size", self.settings.SNPMouseCombinationGraphTitleSize)
                                .style("font-weight", self.settings.SNPMouseCombinationGraphTitleWeight)
                                .attr("fill", self.settings.SNPMouseCombinationGraphTitleColor)
                                .text(self.settings.SNPMouseCombinationGraphTitle + " (" + d.snp_des + ")");


                            if (self.settings.SNPMouseCombinationGraphType == 'histogram' && drawGraph == 1) {
                                //x轴的比例尺
                                var xScale = d3.scale.ordinal()
                                    .domain(d3.range(SNPCombinationData.length))
                                    .rangeRoundBands([0, self.settings.SNPMouseCombinationGraphWidth]);
                                //y轴的比例尺
                                var yScale = d3.scale.linear()
                                    .domain([0, d3.max(SNPCombinationData)])
                                    .range([self.settings.SNPMouseCombinationGraphHeight, 0]);

                                //定义x轴
                                var XAxisName = self.SNPGraphData[0][0]
                                var ticksValues = []
                                for (var i = 0; i < parameterNum; i++) {
                                    ticksValues.push(i)
                                }
                                var xAxis = d3.svg.axis()
                                    .scale(xScale)
                                    .tickValues(ticksValues)
                                    .tickFormat(function (d) {
                                        return XAxisName[d];
                                    })
                                    .orient("bottom");
                                //定义y轴
                                var yAxis = d3.svg.axis()
                                    .scale(yScale)
                                    .orient("left");
                                //矩形之间的空白
                                var rectPadding = self.settings.SNPMouseCombinationGraphHistogramPadding;
                                var combinationStartX = self.settings.SNPMouseCombinationGraphPositionX
                                //添加矩形元素
                                var rects = svg.append("g")
                                    .attr("class", "SNPCombinationHistogramRect")
                                    .selectAll(".SNPCombinationHistogramRect")
                                    .data(SNPCombinationData)
                                    .enter()
                                    .append("rect")
                                    .attr("class", "SNPCombinationHistogramRect")
                                    .attr("transform", "translate(" + combinationStartX + "," + self.settings.SNPMouseCombinationGraphPositionY + ")")
                                    .attr("fill", self.settings.SNPMouseCombinationGraphHistogramBarColor)
                                    .attr({
                                        id: "SNPCombinationHistogramRect",
                                        x: function (d, i) {
                                            return xScale(i) + rectPadding / 2 + self.settings.SNPMouseCombinationGraphHistogramPositionCorrectX;
                                        }
                                    })
                                    .attr("y", function (d) {
                                        return yScale(d) - self.settings.SNPMouseCombinationGraphHeight + 20;
                                    })
                                    .attr("width", xScale.rangeBand() - rectPadding)
                                    .attr("height", function (d) {
                                        //console.log(yScale(d))
                                        return self.settings.SNPMouseCombinationGraphHeight - yScale(d);
                                    });

                                if (self.settings.SNPMouseCombinationTextDisplay == true) {
                                    //添加文字元素
                                    var texts = svg.selectAll(".SNPCombinationHistogramText")
                                        .data(SNPCombinationData)
                                        .enter()
                                        .append("text")
                                        .attr("class", "SNPCombinationHistogramText")
                                        .attr("transform", "translate(" + combinationStartX + "," + self.settings.SNPMouseCombinationGraphPositionY + ")")
                                        .attr("fill", self.settings.SNPMouseCombinationTextColor)
                                        .style("font-size", self.settings.SNPMouseCombinationTextSize)
                                        .style("font-weight", self.settings.SNPMouseCombinationTextWeight)
                                        .attr({
                                            id: "SNPCombinationHistogramText",
                                            x: function (d, i) {
                                                return xScale(i) + rectPadding / 4 + self.settings.SNPMouseCombinationTextPositionCorrectX;
                                            }
                                        })
                                        .attr("y", function (d) {
                                            return yScale(d) - self.settings.SNPMouseCombinationGraphHeight + self.settings.SNPMouseCombinationTextPositionCorrectY;
                                        })
                                        .text(function (d) {
                                            return d;
                                        });
                                }

                                //                  //添加x轴
                                svg.append("g")
                                    .attr("class", "SNPCombinationAxis")
                                    .attr("transform", "translate(" + (combinationStartX - 6 + 20) + "," + (self.settings.SNPMouseCombinationGraphPositionY + 20) + ")")
                                    .attr({id: "SNPCombinationAxisX"})
                                    .attr("fill", "black")
                                    .call(xAxis);
                                //
                                //                  //添加y轴
                                svg.append("g")
                                    .attr("class", "SNPCombinationAxis")
                                    .attr("transform", "translate(" + (combinationStartX + 20) + "," + (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight + 20) + ")")
                                    .attr("id", "SNPCombinationAxisY")
                                    .attr("fill", "black")
                                    .call(yAxis);
                            }

                            if (self.settings.SNPMouseCombinationGraphType == 'pie' && drawGraph == 1) {

                                var XAxisName = self.SNPGraphData[0][0]

                                var pieCombination = d3.svg.arc()
                                    .innerRadius(0)
                                    .outerRadius(self.settings.SNPMouseCombinationGraphPieSize);

                                var pie = d3.layout.pie().sort(null)
                                    .value(function (d) {
                                        return d;
                                    })

                                //                    console.log(pie(SNPCombinationData))

                                var color20 = d3.scale.category20()
                                var fillPie = d3.scale.ordinal()
                                    .range(self.settings.SNPMouseCombinationGraphPieColor);

                                svg.append("g")
                                    .attr("class", "SNPCombinationPie")
                                    .selectAll("path")
                                    .data(pie(SNPCombinationData))
                                    .enter()
                                    .append("a")
                                    .append("path")
                                    .attr("id", "SNPCombinationPie")
                                    .attr("fill", function (d, i) {
                                        if (self.settings.SNPMouseCombinationGraphPieAutoColor == true) {
                                            return color20(i);
                                        } else {
                                            return fillPie(i);
                                        }
                                    })
                                    .attr("transform", "translate(" + (self.settings.SNPMouseCombinationGraphPositionX + self.settings.SNPMouseCombinationGraphWidth / 2 + compareMoveDistance) + "," + (self.settings.SNPMouseCombinationGraphPositionY - (self.settings.SNPMouseCombinationGraphHeight - self.settings.SNPMouseCombinationGraphTitleSize * 2) / 2) + ")")
                                    .attr("d", pieCombination)
                                    .style("stroke", function (d) {
                                        if (self.settings.SNPMouseCombinationGraphPieStroke == true) {
                                            return self.settings.SNPMouseCombinationGraphPieStrokeColor;
                                        } else {
                                            return "";
                                        }
                                    })
                                    .style("stroke-width", function (d) {
                                        if (self.settings.SNPMouseCombinationGraphPieStroke == true) {
                                            return self.settings.SNPMouseCombinationGraphPieStrokeWidth;
                                        } else {
                                            return "0px";
                                        }
                                    })
                                    .style("opacity", self.settings.SNPMouseCombinationGraphPieOpacity);

                                if (self.settings.SNPMouseCombinationTextDisplay == true) {
                                    //添加文字元素
                                    var texts = svg.selectAll(".SNPCombinationPieText")
                                        .data(pie(SNPCombinationData))
                                        .enter()
                                        .append("text")
                                        .attr("class", "SNPCombinationPieText")
                                        .attr("transform", function (d) {
                                            d.innerRadius = 0;
                                            d.outerRadius = self.settings.SNPMouseCombinationGraphPieSize;
                                            console.log(pieCombination.centroid(d))
                                            return "translate(" + pieCombination.centroid(d) + ")";
                                        })
                                        .attr("fill", self.settings.SNPMouseCombinationTextColor)
                                        .style("font-size", self.settings.SNPMouseCombinationTextSize)
                                        .style("font-weight", self.settings.SNPMouseCombinationTextWeight)
                                        .attr({
                                            id: "SNPCombinationHistogramText",
                                            x: (self.settings.SNPMouseCombinationGraphPositionX + self.settings.SNPMouseCombinationGraphWidth / 2 + compareMoveDistance),
                                            y: (self.settings.SNPMouseCombinationGraphPositionY - (self.settings.SNPMouseCombinationGraphHeight - self.settings.SNPMouseCombinationGraphTitleSize * 2) / 2),
                                        })
                                        .text(function (d, i) {
                                            return XAxisName[i];
                                        });
                                }
                            }

                            if (self.settings.SNPMouseCombinationGraphType == 'line' && drawGraph == 1) {
                                var xScale = d3.scale.ordinal()
                                    .domain(d3.range(SNPCombinationData.length))
                                    .rangeRoundBands([0, self.settings.SNPMouseCombinationGraphWidth]);
                                //y轴的比例尺
                                var yScale = d3.scale.linear()
                                    .domain([0, d3.max(SNPCombinationData)])
                                    .range([self.settings.SNPMouseCombinationGraphHeight, 0]);

                                //定义x轴
                                var XAxisName = self.SNPGraphData[0][0]
                                var ticksValues = []
                                for (var i = 0; i < parameterNum; i++) {
                                    ticksValues.push(i)
                                }
                                var xAxis = d3.svg.axis()
                                    .scale(xScale)
                                    .tickValues(ticksValues)
                                    .tickFormat(function (d) {
                                        return XAxisName[d];
                                    })
                                    .orient("bottom");
                                //定义y轴
                                var yAxis = d3.svg.axis()
                                    .scale(yScale)
                                    .orient("left");

                                var combinationStartX = self.settings.SNPMouseCombinationGraphPositionX
                                //添加x轴
                                svg.append("g")
                                    .attr("class", "SNPCombinationAxis")
                                    .attr("transform", "translate(" + (combinationStartX - 6 + 20) + "," + (self.settings.SNPMouseCombinationGraphPositionY + 20) + ")")
                                    .attr({id: "SNPCombinationAxisX"})
                                    .attr("fill", "black")
                                    .call(xAxis);
                                //
                                //添加y轴
                                svg.append("g")
                                    .attr("class", "SNPCombinationAxis")
                                    .attr("transform", "translate(" + (combinationStartX + 20) + "," + (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight + 20) + ")")
                                    .attr("id", "SNPCombinationAxisY")
                                    .attr("fill", "black")
                                    .call(yAxis);

                                var line = d3.svg.line()
                                    .x(function (d) {
                                        return xScale(d.x)
                                    })
                                    .y(function (d) {
                                        return yScale(d.y);
                                    })
                                    // 选择线条的类型
                                    .interpolate(self.settings.SNPMouseCombinationGraphLineType);

                                var SNPCombinationDataLine = []

                                for (var i = 0; i < SNPCombinationData.length; i++) {
                                    SNPCombinationDataLine.push({x: i, y: SNPCombinationData[i]})
                                }

                                svg.append("g")
                                    .attr("class", "SNPCombinationLine")
                                    .append("path")
                                    .attr("id", "SNPCombinationLine")
                                    .attr("d", line(SNPCombinationDataLine))
                                    .attr("fill", "none")
                                    .attr("transform", "translate(" + (combinationStartX + self.settings.SNPMouseCombinationGraphLinePositionCorrectX) + "," + (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight + 20) + ")")
                                    .style("stroke", self.settings.SNPMouseCombinationGraphLineColor)
                                    .style("stroke-width", self.settings.SNPMouseCombinationGraphLineWidth);

                                if (self.settings.SNPMouseCombinationGraphLinePoint == true) {
                                    var color20 = d3.scale.category20()
                                    var fillLinePoint = d3.scale.ordinal()
                                        .range(self.settings.SNPMouseCombinationGraphLinePointColor);

                                    svg.append("g")
                                        .attr("class", "SNPCombinationLinePoint")
                                        .selectAll('circle')
                                        .data(SNPCombinationDataLine)
                                        .enter()
                                        .append('circle')
                                        .attr("id", "SNPCombinationLinePoint")
                                        .attr('r', self.settings.SNPMouseCombinationGraphLinePointSize)
                                        .attr('cx', function (d) {
                                            return xScale(d.x);
                                        })
                                        .attr('cy', function (d) {
                                            return yScale(d.y);
                                        })
                                        .attr('transform', function (d) {
                                            return 'translate(' + (combinationStartX + self.settings.SNPMouseCombinationGraphLinePositionCorrectX) + ',' + (self.settings.SNPMouseCombinationGraphPositionY - self.settings.SNPMouseCombinationGraphHeight + 20) + ')'
                                        })
                                        .attr("stroke", function (d, i) {
                                            if (self.settings.SNPMouseCombinationGraphLinePointStroke == true) {
                                                return self.settings.SNPMouseCombinationGraphLinePointStrokeColor;
                                            } else {
                                                return "none";
                                            }
                                        })
                                        .attr("stroke-width", function (d, i) {
                                            if (self.settings.SNPMouseCombinationGraphLinePointStroke == true) {
                                                return self.settings.SNPMouseCombinationGraphLinePointStrokeWidth;
                                            } else {
                                                return "none";
                                            }
                                        })
                                        .attr("opacity", self.settings.SNPMouseCombinationGraphLinePointOpacity)
                                        .attr('fill', function (d, i) {
                                            if (self.settings.SNPMouseCombinationGraphLinePointAutoColor == true) {
                                                return color20(i);
                                            } else {
                                                return fillLinePoint(i);
                                            }
                                        });
                                }


                                if (self.settings.SNPMouseCombinationTextDisplay == true) {
                                    //添加文字元素
                                    var texts = svg.selectAll(".SNPCombinationLineText")
                                        .data(SNPCombinationData)
                                        .enter()
                                        .append("text")
                                        .attr("class", "SNPCombinationLineText")
                                        .attr("transform", "translate(" + combinationStartX + "," + self.settings.SNPMouseCombinationGraphPositionY + ")")
                                        .attr("fill", self.settings.SNPMouseCombinationTextColor)
                                        .style("font-size", self.settings.SNPMouseCombinationTextSize)
                                        .style("font-weight", self.settings.SNPMouseCombinationTextWeight)
                                        .attr({
                                            id: "SNPCombinationLineText",
                                            x: function (d, i) {
                                                return xScale(i) + self.settings.SNPMouseCombinationTextPositionCorrectX;
                                            }
                                        })
                                        .attr("y", function (d) {
                                            return yScale(d) - self.settings.SNPMouseCombinationGraphHeight + self.settings.SNPMouseCombinationTextPositionCorrectY;
                                        })
                                        .text(function (d) {
                                            return d;
                                        });
                                }
                            }

                        }

                        if (self.settings.SNPMouseCombinationImageDisplay == true) {
                            svg.append("text")
                                .attr("class", "SNPCombinationImageTitle")
                                .attr({
                                    id: "SNPCombinationImageTitle",
                                    x: self.settings.SNPMouseCombinationImagePositionX + 37
                                })
                                .attr("y", (self.settings.SNPMouseCombinationImagePositionY - 20))
                                .style("font-size", self.settings.SNPMouseCombinationImageTitleSize)
                                .style("font-weight", self.settings.SNPMouseCombinationImageTitleWeight)
                                .attr("fill", self.settings.SNPMouseCombinationImageTitleColor)
                                .text(self.settings.SNPMouseCombinationImageTitle + " (" + d.snp_des + ")");

                            svg.append("image")
                                .attr("id", "SNPCombinationImage")
                                .attr("xlink:href", d.snp_image)
                                .attr('x', self.settings.SNPMouseCombinationImagePositionX)
                                .attr('y', self.settings.SNPMouseCombinationImagePositionY)
                                .attr('width', self.settings.SNPMouseCombinationImageWidth)
                                .attr('height', self.settings.SNPMouseCombinationImageHeight)
                        }

                        if (self.settings.SNPMouseOverDisplay == true) {
                            if (self.ticksOffset != undefined) {
                                SNPMouseOnTooltip.html(function () {
                                    if (self.settings.SNPMouseOverTooltipsSetting == "style1") {
                                        return "chr : " + d.snp_chr + "<br>pos : " + (parseInt(d.snp_pos) + self.ticksOffset) + "<br>value : " + d.snp_val + " <br>des : " + d.snp_des + ""
                                    } else if (self.settings.SNPMouseOverTooltipsSetting == "custom") {
                                        return self.settings.SNPMouseOverTooltipsHtml + d.snp_html
                                    } else if (self.settings.SNPMouseOverTooltipsSetting == "style2") {
                                        return "chr : " + d.snp_chr + "<br>pos : " + (parseInt(d.snp_pos) + self.ticksOffset) + "<br># of patients : " + d.snp_val + " <br>AA : " + d.snp_des + ""
                                    }
                                })
                                    .style("left", (d3.event.pageX) + "px")
                                    .style("top", (d3.event.pageY + 20) + "px")
                                    .style("position", self.settings.SNPMouseOverTooltipsPosition)
                                    .style("background-color", self.settings.SNPMouseOverTooltipsBackgroundColor)
                                    .style("border-style", self.settings.SNPMouseOverTooltipsBorderStyle)
                                    .style("border-width", self.settings.SNPMouseOverTooltipsBorderWidth)
                                    .style("padding", self.settings.SNPMouseOverTooltipsPadding)
                                    .style("border-radius", self.settings.SNPMouseOverTooltipsBorderRadius)
                                    .style("opacity", self.settings.SNPMouseOverTooltipsOpacity)
                                d3.select(this)
                                    .style("r", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleSize == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleSize;
                                        }
                                    })
                                    .style("fill", function (d, i) {
                                        if (self.settings.SNPMouseOverColor == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverColor;
                                        }
                                    })
                                    .style("opacity", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleOpacity == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleOpacity;
                                        }
                                    })
                                    .style("stroke", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleStrokeColor == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleStrokeColor;
                                        }
                                    })
                                    .style("stroke-width", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleStrokeWidth == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleStrokeWidth;
                                        }
                                    });

                            } else {
                                SNPMouseOnTooltip.html(function () {
                                    if (self.settings.SNPMouseOverTooltipsSetting == "style1") {
                                        return "chr : " + d.snp_chr + "<br>pos : " + d.snp_pos + "<br>value : " + d.snp_val + " <br>des : " + d.snp_des + ""
                                    } else if (self.settings.SNPMouseOverTooltipsSetting == "custom") {
                                        return self.settings.SNPMouseOverTooltipsHtml + d.snp_html
                                    } else if (self.settings.SNPMouseOverTooltipsSetting == "style2") {
                                        return "chr : " + d.snp_chr + "<br>pos : " + (parseInt(d.snp_pos) + self.ticksOffset) + "<br># of patients : " + d.snp_val + " <br>AA : " + d.snp_des + ""
                                    }
                                })
                                    .style("left", (d3.event.pageX) + "px")
                                    .style("top", (d3.event.pageY + 20) + "px")
                                    .style("position", self.settings.SNPMouseOverTooltipsPosition)
                                    .style("background-color", self.settings.SNPMouseOverTooltipsBackgroundColor)
                                    .style("border-style", self.settings.SNPMouseOverTooltipsBorderStyle)
                                    .style("border-width", self.settings.SNPMouseOverTooltipsBorderWidth)
                                    .style("padding", self.settings.SNPMouseOverTooltipsPadding)
                                    .style("border-radius", self.settings.SNPMouseOverTooltipsBorderRadius)
                                    .style("opacity", self.settings.SNPMouseOverTooltipsOpacity)
                                d3.select(this)
                                    .style("r", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleSize == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleSize;
                                        }
                                    })
                                    .style("fill", function (d, i) {
                                        if (self.settings.SNPMouseOverColor == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverColor;
                                        }
                                    })
                                    .style("opacity", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleOpacity == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleOpacity;
                                        }
                                    })
                                    .style("stroke", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleStrokeColor == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleStrokeColor;
                                        }
                                    })
                                    .style("stroke-width", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleStrokeWidth == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleStrokeWidth;
                                        }
                                    });

                            }

                        }
                    }

                    SNPMouseOn.on("mouseover", SNPCombinationMouseOver)

                } else {
                    if (self.settings.SNPMouseOverDisplay == true) {
                        SNPMouseOn.on("mouseover", function (d) {
                            if (self.ticksOffset != undefined) {
                                SNPMouseOnTooltip.html(function () {
                                    if (self.settings.SNPMouseOverTooltipsSetting == "style1") {
                                        return "chr : " + d.snp_chr + "<br>pos : " + (parseInt(d.snp_pos) + self.ticksOffset) + "<br>beta value : " + parseFloat(d.snp_val).toFixed(3) + "<br> context : " + d.snp_des + " <br>"
                                    } else if (self.settings.SNPMouseOverTooltipsSetting == "custom") {
                                        return self.settings.SNPMouseOverTooltipsHtml + d.snp_html
                                    } else if (self.settings.SNPMouseOverTooltipsSetting == "style2") {
                                        return "chr : " + d.snp_chr + "<br>pos : " + (parseInt(d.snp_pos) + self.ticksOffset) + "<br># of patients : " + d.snp_val + " <br>AA : " + d.snp_des + ""
                                    }
                                })
                                    .style("left", (d3.event.pageX) + "px")
                                    .style("top", (d3.event.pageY + 20) + "px")
                                    .style("position", self.settings.SNPMouseOverTooltipsPosition)
                                    .style("background-color", self.settings.SNPMouseOverTooltipsBackgroundColor)
                                    .style("border-style", self.settings.SNPMouseOverTooltipsBorderStyle)
                                    .style("border-width", self.settings.SNPMouseOverTooltipsBorderWidth)
                                    .style("padding", self.settings.SNPMouseOverTooltipsPadding)
                                    .style("border-radius", self.settings.SNPMouseOverTooltipsBorderRadius)
                                    .style("opacity", self.settings.SNPMouseOverTooltipsOpacity)
                                d3.select(this)
                                    .style("r", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleSize == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleSize;
                                        }
                                    })
                                    .style("fill", function (d, i) {
                                        if (self.settings.SNPMouseOverColor == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverColor;
                                        }
                                    })
                                    .style("opacity", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleOpacity == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleOpacity;
                                        }
                                    })
                                    .style("stroke", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleStrokeColor == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleStrokeColor;
                                        }
                                    })
                                    .style("stroke-width", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleStrokeWidth == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleStrokeWidth;
                                        }
                                    });
                            } else {
                                SNPMouseOnTooltip.html(function () {
                                    if (self.settings.SNPMouseOverTooltipsSetting == "style1") {
                                        return "chr : " + d.snp_chr + "<br>pos : " + d.snp_pos + "<br>pvalue : " + d.snp_val + " <br>"
                                    } else if (self.settings.SNPMouseOverTooltipsSetting == "custom") {
                                        return self.settings.SNPMouseOverTooltipsHtml + d.snp_html
                                    } else if (self.settings.SNPMouseOverTooltipsSetting == "style2") {
                                        return "chr : " + d.snp_chr + "<br>pos : " + (parseInt(d.snp_pos) + self.ticksOffset) + "<br># of patients : " + d.snp_val + " <br>AA : " + d.snp_des + ""
                                    }
                                })
                                    .style("left", (d3.event.pageX) + "px")
                                    .style("top", (d3.event.pageY + 20) + "px")
                                    .style("position", self.settings.SNPMouseOverTooltipsPosition)
                                    .style("background-color", self.settings.SNPMouseOverTooltipsBackgroundColor)
                                    .style("border-style", self.settings.SNPMouseOverTooltipsBorderStyle)
                                    .style("border-width", self.settings.SNPMouseOverTooltipsBorderWidth)
                                    .style("padding", self.settings.SNPMouseOverTooltipsPadding)
                                    .style("border-radius", self.settings.SNPMouseOverTooltipsBorderRadius)
                                    .style("opacity", self.settings.SNPMouseOverTooltipsOpacity)
                                d3.select(this)
                                    .style("r", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleSize == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleSize;
                                        }
                                    })
                                    .style("fill", function (d, i) {
                                        if (self.settings.SNPMouseOverColor == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverColor;
                                        }
                                    })
                                    .style("opacity", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleOpacity == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleOpacity;
                                        }
                                    })
                                    .style("stroke", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleStrokeColor == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleStrokeColor;
                                        }
                                    })
                                    .style("stroke-width", function (d, i) {
                                        if (self.settings.SNPMouseOverCircleStrokeWidth == "none") {
                                            return "";
                                        } else {
                                            return self.settings.SNPMouseOverCircleStrokeWidth;
                                        }
                                    });

                            }

                        })
                    }


                }
                //combinationSNP

                if (self.settings.SNPMouseClickTextDrag == true) {
                    svg.selectAll("text.dragText").call(drag);
                }

                if (self.settings.SNPMouseDownDisplay == true) {
                    SNPMouseOn.on("mousedown", function (d) {
                        d3.select(this)
                            .style("r", function (d, i) {
                                if (self.settings.SNPMouseDownCircleSize == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseDownCircleSize;
                                }
                            })
                            .style("fill", function (d, i) {
                                if (self.settings.SNPMouseDownColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseDownColor;
                                }
                            })
                            .style("opacity", function (d, i) {
                                if (self.settings.SNPMouseDownCircleOpacity == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseDownCircleOpacity;
                                }
                            })
                            .style("stroke", function (d, i) {
                                if (self.settings.SNPMouseDownCircleStrokeColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseDownCircleStrokeColor;
                                }
                            })
                            .style("stroke-width", function (d, i) {
                                if (self.settings.SNPMouseDownCircleStrokeWidth == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseDownCircleStrokeWidth;
                                }
                            });
                    })
                }
                if (self.settings.SNPMouseEnterDisplay == true) {
                    SNPMouseOn.on("mouseenter", function (d) {
                        d3.select(this)
                            .style("r", function (d, i) {
                                if (self.settings.SNPMouseEnterCircleSize == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseEnterCircleSize;
                                }
                            })
                            .style("fill", function (d, i) {
                                if (self.settings.SNPMouseEnterColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseEnterColor;
                                }
                            })
                            .style("opacity", function (d, i) {
                                if (self.settings.SNPMouseEnterCircleOpacity == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseEnterCircleOpacity;
                                }
                            })
                            .style("stroke", function (d, i) {
                                if (self.settings.SNPMouseEnterCircleStrokeColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseEnterCircleStrokeColor;
                                }
                            })
                            .style("stroke-width", function (d, i) {
                                if (self.settings.SNPMouseEnterCircleStrokeWidth == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseEnterCircleStrokeWidth;
                                }
                            });
                    })
                }
                if (self.settings.SNPMouseLeaveDisplay == true) {
                    SNPMouseOn.on("mouseleave", function (d) {
                        SNPMouseOnTooltip.style("opacity", 0.0);
                        d3.select(this)
                            .style("r", function (d, i) {
                                if (self.settings.SNPMouseLeaveCircleSize == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseLeaveCircleSize;
                                }
                            })
                            .style("fill", function (d, i) {
                                if (self.settings.SNPMouseLeaveColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseLeaveColor;
                                }
                            })
                            .style("opacity", function (d, i) {
                                if (self.settings.SNPMouseLeaveCircleOpacity == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseLeaveCircleOpacity;
                                }
                            })
                            .style("stroke", function (d, i) {
                                if (self.settings.SNPMouseLeaveCircleStrokeColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseLeaveCircleStrokeColor;
                                }
                            })
                            .style("stroke-width", function (d, i) {
                                if (self.settings.SNPMouseLeaveCircleStrokeWidth == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseLeaveCircleStrokeWidth;
                                }
                            });
                    })
                }
                if (self.settings.SNPMouseUpDisplay == true) {
                    SNPMouseOn.on("mouseup", function (d) {
                        d3.select(this)
                            .style("r", function (d, i) {
                                if (self.settings.SNPMouseUpCircleSize == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseUpCircleSize;
                                }
                            })
                            .style("fill", function (d, i) {
                                if (self.settings.SNPMouseUpColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseUpColor;
                                }
                            })
                            .style("opacity", function (d, i) {
                                if (self.settings.SNPMouseUpCircleOpacity == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseUpCircleOpacity;
                                }
                            })
                            .style("stroke", function (d, i) {
                                if (self.settings.SNPMouseUpCircleStrokeColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseUpCircleStrokeColor;
                                }
                            })
                            .style("stroke-width", function (d, i) {
                                if (self.settings.SNPMouseUpCircleStrokeWidth == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseUpCircleStrokeWidth;
                                }
                            });
                    })
                }
                if (self.settings.SNPMouseMoveDisplay == true) {
                    SNPMouseOn.on("mousemove", function (d) {
                        d3.select(this)
                            .style("r", function (d, i) {
                                if (self.settings.SNPMouseMoveCircleSize == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseMoveCircleSize;
                                }
                            })
                            .style("fill", function (d, i) {
                                if (self.settings.SNPMouseMoveColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseMoveColor;
                                }
                            })
                            .style("opacity", function (d, i) {
                                if (self.settings.SNPMouseMoveCircleOpacity == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseMoveCircleOpacity;
                                }
                            })
                            .style("stroke", function (d, i) {
                                if (self.settings.SNPMouseMoveCircleStrokeColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseMoveCircleStrokeColor;
                                }
                            })
                            .style("stroke-width", function (d, i) {
                                if (self.settings.SNPMouseMoveCircleStrokeWidth == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseMoveCircleStrokeWidth;
                                }
                            });
                        SNPMouseOnTooltip.style("left", (d3.event.pageX) + "px")
                            .style("top", (d3.event.pageY + 20) + "px");
                    })
                }
                if (self.settings.SNPMouseOutDisplay == true) {
                    SNPMouseOn.on("mouseout", function (d) {
                        SNPMouseOnTooltip.style("opacity", 0.0);
                        d3.select(this)
                            .transition()
                            .duration(self.settings.SNPMouseOutAnimationTime)
                            .style("r", function (d, i) {
                                if (self.settings.SNPMouseOutCircleSize == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseOutCircleSize;
                                }
                            })
                            .style("fill", function (d, i) {
                                if (self.settings.SNPMouseOutColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseOutColor;
                                }
                            })
                            .style("opacity", function (d, i) {
                                if (self.settings.SNPMouseOutCircleOpacity == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseOutCircleOpacity;
                                }
                            })
                            .style("stroke", function (d, i) {
                                if (self.settings.SNPMouseOutCircleStrokeColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseOutCircleStrokeColor;
                                }
                            })
                            .style("stroke-width", function (d, i) {
                                if (self.settings.SNPMouseOutCircleStrokeWidth == "none") {
                                    return "";
                                } else {
                                    return self.settings.SNPMouseOutCircleStrokeWidth;
                                }
                            });
                    });
                }
            }
        }

        //zhec3
        if (self.ARC.length > 0) {
            function NGCircosARC(d) {
                return self.ARC[arci].map(function (v, i) {
                    var arc_k = (d[self.initGenome[v.chr]].endAngle - d[self.initGenome[v.chr]].startAngle) / d[self.initGenome[v.chr]].value;
                    return {
                        startAngle: v.start * arc_k + d[self.initGenome[v.chr]].startAngle,
                        endAngle: v.end * arc_k + d[self.initGenome[v.chr]].startAngle,
                        arc_chr: v.chr,
                        arc_start: v.start,
                        arc_end: v.end,
                        arc_color: v.color,
                        arc_des: v.des,
                        arc_link: v.link,
                        arc_click_label: "arc" + arci + "_" + i,
                        arc_innerRadius: innerRadius + self.ARCsettings.innerRadius,
                        arc_outerRadius: outerRadius + self.ARCsettings.outerRadius,
                        arc_html: v.html,
                    };
                });
            }

            function NGCircosARC2(d) {
                return self.ARC[arci].map(function (v, i) {
                    var arc_k = (d[self.initGenome[v.chr]].endAngle - d[self.initGenome[v.chr]].startAngle) / d[self.initGenome[v.chr]].value;
                    return {
                        startAngle: 3 * Math.PI - (v.start * arc_k + d[self.initGenome[v.chr]].startAngle),
                        endAngle: 3 * Math.PI - (v.end * arc_k + d[self.initGenome[v.chr]].startAngle),
                        arc_chr: v.chr,
                        arc_start: v.start,
                        arc_end: v.end,
                        arc_color: v.color,
                        arc_des: v.des,
                        arc_link: v.link,
                        arc_click_label: "arc" + arci + "_" + i,
                        arc_innerRadius: innerRadius + self.ARCsettings.innerRadius,
                        arc_outerRadius: outerRadius + self.ARCsettings.outerRadius,
                        arc_html: v.html,
                    };
                });
            }

            // console.log(self.ARC)
            for (var arci = 0; arci < self.ARC.length; arci++) {
                self.update_ARCsettings(self.ARCConfig[arci]);
                if (drawTime == self.ARCsettings.compareGroup) {
                    if (self.ARCsettings.compareGroup == 1) {
                        var arc_objects = NGCircosARC(chord.groups())
                    } else {
                        var arc_objects = NGCircosARC2(chord.groups())
                    }

                    if (self.ARCsettings.ARCAnimationDisplay == true) {
                        svg.append("g")
                            .attr("class", "NGCircosARC")
                            .selectAll("path.NGCircosARC")
                            .data(arc_objects)
                            .enter()
                            .append("a")
                            .attr("xlink:href", function (d) {
                                if (self.settings.ARCxlink == true) {
                                    return d.arc_link;
                                }
                            })
                            .append("path")
                            .attr("class", "NGCircosARC")
                            .attr("fill", function (d, i) {
                                return d.arc_color;
                            })
                            //                      .attr("d", function(d,i) { return arc(d,i); })
                            .style("opacity", self.ARCsettings.ARCOpacity)
                            .transition()
                            .delay(function (d, i) {
                                return (i + 1) * self.ARCsettings.ARCAnimationDelay;
                            })
                            .duration(self.ARCsettings.ARCAnimationTime)
                            .ease(self.ARCsettings.ARCAnimationType)
                            .attrTween("d", function (d, i, a) {
                                return function (t) {
                                    var arc = d3.svg.arc().innerRadius(d.arc_innerRadius).outerRadius(d.arc_outerRadius).startAngle(d.startAngle).endAngle(d.startAngle + (d.endAngle - d.startAngle) * t);
                                    return arc(d, i);
                                }
                            })

                            .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                    } else {
                        svg.append("g")
                            .attr("class", "NGCircosARC")
                            .selectAll("path.NGCircosARC")
                            .data(arc_objects)
                            .enter()
                            .append("a")
                            .attr("xlink:href", function (d) {
                                if (self.settings.ARCxlink == true) {
                                    return d.arc_link;
                                }
                            })
                            .append("path")
                            .attr("class", "NGCircosARC")
                            .attr("fill", function (d, i) {
                                return d.arc_color;
                            })
                            .style("opacity", self.ARCsettings.ARCOpacity)
                            .attr("d", function (d, i) {
                                var arc = d3.svg.arc().innerRadius(d.arc_innerRadius).outerRadius(d.arc_outerRadius);
                                return arc(d, i);
                            })
                            .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                    }
                    if (self.settings.ARCMouseClickTextFromData == "first") {
                        svg.append("g")
                            .attr("class", "NGCircosARClabel")
                            .selectAll("text")
                            .data(arc_objects)
                            .enter().append("text")
                            .attr("class", "dragText")
                            .attr("id", function (d, i) {
                                return "arc" + arci + "_" + i;
                            })
                            .text(function (d) {
                                return d.arc_chr;
                            })
                            .attr("x", -1000)
                            .attr("y", -1000)
                            .style("opacity", 0)
                            .style("font-size", 1)
                            .attr("fill", self.ARCsettings.ARCFillColor)
                            .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                    }
                    if (self.settings.ARCMouseClickTextFromData == "second") {
                        svg.append("g")
                            .attr("class", "NGCircosARClabel")
                            .selectAll("text")
                            .data(arc_objects)
                            .enter().append("text")
                            .attr("class", "dragText")
                            .attr("id", function (d, i) {
                                return "arc" + arci + "_" + i;
                            })
                            .text(function (d) {
                                return d.arc_start;
                            })
                            .attr("x", -1000)
                            .attr("y", -1000)
                            .style("opacity", 0)
                            .style("font-size", 1)
                            .attr("fill", self.ARCsettings.ARCFillColor)
                            .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                    }
                    if (self.settings.ARCMouseClickTextFromData == "third") {
                        svg.append("g")
                            .attr("class", "NGCircosARClabel")
                            .selectAll("text")
                            .data(arc_objects)
                            .enter().append("text")
                            .attr("class", "dragText")
                            .attr("id", function (d, i) {
                                return "arc" + arci + "_" + i;
                            })
                            .text(function (d) {
                                return d.arc_end;
                            })
                            .attr("x", -1000)
                            .attr("y", -1000)
                            .style("opacity", 0)
                            .style("font-size", 1)
                            .attr("fill", self.ARCsettings.ARCFillColor)
                            .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                    }
                    if (self.settings.ARCMouseClickTextFromData == "fifth") {
                        svg.append("g")
                            .attr("class", "NGCircosARClabel")
                            .selectAll("text")
                            .data(arc_objects)
                            .enter().append("text")
                            .attr("class", "dragText")
                            .attr("id", function (d, i) {
                                return "arc" + arci + "_" + i;
                            })
                            .text(function (d) {
                                return d.arc_des;
                            })
                            .attr("x", -1000)
                            .attr("y", -1000)
                            .style("opacity", 0)
                            .style("font-size", 1)
                            .attr("fill", self.ARCsettings.ARCFillColor)
                            .attr("transform", "translate(" + compareMoveDistance + "," + 0 + ")");
                    }

                }


                self.init_ARCsettings();
            }
            // console.timeEnd("codeExecution"); // 结束计时并打印执行时间
            if (self.settings.ARCMouseEvent == true) {
                var ARCMouseOnTooltip = d3.select("body")
                    .append("div")
                    .attr("class", "NGCircosARCTooltip")
                    .attr("id", "NGCircosARCTooltip")
                    .style("opacity", 0);

                var ARCMouseOn = svg.selectAll("path.NGCircosARC");
                if (self.settings.ARCMouseOverDisplay == true) {
                    ARCMouseOn.on("mouseover", function (d) {
                        if (self.ticksOffset != undefined) {
                            ARCMouseOnTooltip.html(function () {
                                if (self.settings.ARCMouseOverTooltipsSetting == "style1") {
                                    return "item : " + d.arc_chr + "<br>start : " + (parseInt(d.arc_start) + self.ticksOffset) + "<br>end : " + (parseInt(d.arc_end) + self.ticksOffset) + " <br>des : " + d.arc_des + ""
                                } else if (self.settings.ARCMouseOverTooltipsSetting == "custom") {
                                    return self.settings.ARCMouseOverTooltipsHtml + d.arc_html
                                }
                            })
                                .style("left", (d3.event.pageX) + "px")
                                .style("top", (d3.event.pageY + 20) + "px")
                                .style("position", self.settings.ARCMouseOverTooltipsPosition)
                                .style("background-color", self.settings.ARCMouseOverTooltipsBackgroundColor)
                                .style("border-style", self.settings.ARCMouseOverTooltipsBorderStyle)
                                .style("border-width", self.settings.ARCMouseOverTooltipsBorderWidth)
                                .style("padding", self.settings.ARCMouseOverTooltipsPadding)
                                .style("border-radius", self.settings.ARCMouseOverTooltipsBorderRadius)
                                .style("opacity", self.settings.ARCMouseOverTooltipsOpacity)
                            d3.select(this)
                                .style("fill", function (d, i) {
                                    if (self.settings.ARCMouseOverColor == "none") {
                                        return "";
                                    } else {
                                        return self.settings.ARCMouseOverColor;
                                    }
                                })
                                .style("opacity", function (d, i) {
                                    if (self.settings.ARCMouseOverArcOpacity == "none") {
                                        return "";
                                    } else {
                                        return self.settings.ARCMouseOverArcOpacity;
                                    }
                                })
                                .style("stroke", function (d, i) {
                                    if (self.settings.ARCMouseOverArcStrokeColor == "none") {
                                        return "";
                                    } else {
                                        return self.settings.ARCMouseOverArcStrokeColor;
                                    }
                                })
                                .style("stroke-width", function (d, i) {
                                    if (self.settings.ARCMouseOverArcStrokeWidth == "none") {
                                        return "";
                                    } else {
                                        return self.settings.ARCMouseOverArcStrokeWidth;
                                    }
                                });
                        } else {
                            ARCMouseOnTooltip.html(function () {
                                if (self.settings.ARCMouseOverTooltipsSetting == "style1") {
                                    return "item : " + d.arc_chr + "<br>start : " + (parseInt(d.arc_start) + self.ticksOffset) + "<br>end : " + (parseInt(d.arc_end) + self.ticksOffset) + " <br>des : " + d.arc_des + ""
                                } else if (self.settings.ARCMouseOverTooltipsSetting == "custom") {
                                    return self.settings.ARCMouseOverTooltipsHtml + d.arc_html
                                }
                            })
                                .style("left", (d3.event.pageX) + "px")
                                .style("top", (d3.event.pageY + 20) + "px")
                                .style("position", self.settings.ARCMouseOverTooltipsPosition)
                                .style("background-color", self.settings.ARCMouseOverTooltipsBackgroundColor)
                                .style("border-style", self.settings.ARCMouseOverTooltipsBorderStyle)
                                .style("border-width", self.settings.ARCMouseOverTooltipsBorderWidth)
                                .style("padding", self.settings.ARCMouseOverTooltipsPadding)
                                .style("border-radius", self.settings.ARCMouseOverTooltipsBorderRadius)
                                .style("opacity", self.settings.ARCMouseOverTooltipsOpacity)
                            d3.select(this)
                                .style("fill", function (d, i) {
                                    if (self.settings.ARCMouseOverColor == "none") {
                                        return "";
                                    } else {
                                        return self.settings.ARCMouseOverColor;
                                    }
                                })
                                .style("opacity", function (d, i) {
                                    if (self.settings.ARCMouseOverArcOpacity == "none") {
                                        return "";
                                    } else {
                                        return self.settings.ARCMouseOverArcOpacity;
                                    }
                                })
                                .style("stroke", function (d, i) {
                                    if (self.settings.ARCMouseOverArcStrokeColor == "none") {
                                        return "";
                                    } else {
                                        return self.settings.ARCMouseOverArcStrokeColor;
                                    }
                                })
                                .style("stroke-width", function (d, i) {
                                    if (self.settings.ARCMouseOverArcStrokeWidth == "none") {
                                        return "";
                                    } else {
                                        return self.settings.ARCMouseOverArcStrokeWidth;
                                    }
                                });
                        }

                    })
                }

                if (self.settings.ARCMouseOutDisplay == true) {
                    ARCMouseOn.on("mouseout", function (d) {
                        ARCMouseOnTooltip.style("opacity", 0.0);
                        d3.select(this)
                            .transition()
                            .duration(self.settings.ARCMouseOutAnimationTime)
                            .style("fill", function (d, i) {
                                if (self.settings.ARCMouseOutColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.ARCMouseOutColor;
                                }
                            })
                            .style("opacity", function (d, i) {
                                if (self.settings.ARCMouseOutArcOpacity == "none") {
                                    return "";
                                } else {
                                    return self.settings.ARCMouseOutArcOpacity;
                                }
                            })
                            .style("stroke", function (d, i) {
                                if (self.settings.ARCMouseOutArcStrokeColor == "none") {
                                    return "";
                                } else {
                                    return self.settings.ARCMouseOutArcStrokeColor;
                                }
                            })
                            .style("stroke-width", function (d, i) {
                                if (self.settings.ARCMouseOutArcStrokeWidth == "none") {
                                    return "";
                                } else {
                                    return self.settings.ARCMouseOutArcStrokeWidth;
                                }
                            });
                    });
                }
            }

        }
        // console.timeEnd("codeExecution"); // 结束计时并打印执行时间

    }
}(jQuery));
