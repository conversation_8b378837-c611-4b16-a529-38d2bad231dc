package cn.ac.picb.vipmap.config.security;

import cn.ac.picb.vipmap.repository.CasUserRepository;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Collection;
import java.util.List;

/**
 * VipMap自定义用户详情服务
 * 参考CustomUserDetailsService实现，从远程API获取用户信息并适配到现有的CurrentUser结构
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RdrUserDetailsService implements UserDetailsService {

    @Value("${rdr.api.get_user_info_by_token}")
    private String apiGetUserInfoByToken;

    @Autowired
    private CasUserRepository casUserRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        String body = HttpRequest.get(apiGetUserInfoByToken).header("Authorization", username).execute().body();

        JSONObject bodyJsonObject = JSONUtil.parseObj(body);
        log.debug(JSONUtil.toJsonStr(bodyJsonObject));

        Integer code = bodyJsonObject.getInt("code");

        if (ObjectUtil.isEmpty(code)) {
            log.error("根据 token 获取用户信息失败 {}", bodyJsonObject);
            throw new UsernameNotFoundException("根据 token 获取用户信息失败");
        }

        if (code != 200) {
            log.error("根据 token 获取用户信息失败 {}", bodyJsonObject);
            throw new UsernameNotFoundException("根据 token 获取用户信息失败");
        }

        JSONObject loginUserJSONObject = bodyJsonObject.getJSONObject("data");

        if (ObjectUtil.isEmpty(loginUserJSONObject)) {
            log.error("根据 token 获取用户信息失败 {}", bodyJsonObject);
            throw new UsernameNotFoundException("根据 token 获取用户信息失败");
        }

        // 适配到现有的CurrentUser结构
        String userId = loginUserJSONObject.getStr("userid");
        JSONObject sysUser = loginUserJSONObject.getJSONObject("sysUser");
        String name = sysUser.getStr("userName");

        // 保存或更新用户信息到数据库
        if (!casUserRepository.existsById(userId)) {
            CurrentUser currentUser = new CurrentUser(userId, name, name);
            casUserRepository.save(currentUser);
        }

        List<GrantedAuthority> authorities = AuthorityUtils.createAuthorityList("ROLE_USER");
        CurrentUser user = casUserRepository.findById(userId).orElseThrow(() -> new RuntimeException("用户未找到"));
        if (StrUtil.isNotBlank(user.getRole())) {
            authorities = AuthorityUtils.createAuthorityList("ROLE_" + user.getRole());
        }

        return new CustomSecurityUser(userId, name, name, true, authorities);
    }

    /**
     * 从请求中获取Token
     * 参考CustomUserDetailsService.obtainToken方法
     */
    public static String obtainToken(HttpServletRequest request, HttpServletResponse response) {
        String token = null;

        // 先从Parameter获取
        if (request.getParameter("token") != null) {
            token = request.getParameter("token").trim();
        } else if (request.getParameter("Admin-Token") != null) {
            token = request.getParameter("Admin-Token").trim();
        } else if (StrUtil.isNotBlank(request.getHeader("Admin-Token"))) {
            // 再从header获取
            token = request.getHeader("Admin-Token").trim();
        } else {
            // 再从session获取
            HttpSession session = request.getSession();
            Object tokenObj = session.getAttribute("token");
            if (tokenObj != null) {
                token = String.valueOf(tokenObj).trim();
                request.removeAttribute("token");
            } else {
                // 再从cookie获取
                Cookie[] cookies = request.getCookies();
                if (cookies != null) {
                    for (Cookie cookie : cookies) {
                        if ("Admin-Token".equalsIgnoreCase(cookie.getName().trim())) {
                            token = cookie.getValue();
                            if (StrUtil.isNotBlank(token)) {
                                token = token.trim();
                                break;
                            }
                        }
                    }
                } else {
                    log.error("无法从 cookie 获取到用户 Admin-Token");
                }
            }
        }

        // 如果 token 不为空，将其添加到响应的 Cookie 中
        if (token != null) {
            Cookie cookie = new Cookie("Admin-Token", token);
            cookie.setPath("/");
            response.addCookie(cookie);
        } else {
            log.error("无法获取到用户 Admin-Token");
        }

        return token;
    }

    /**
     * VipMap安全用户类
     * 适配现有的用户结构
     */
    public static class CustomSecurityUser extends User {
        private static final String NON_EXISTENT_PASSWORD_VALUE = "NO_PASSWORD";

        private final String id;
        private String name;

        public CustomSecurityUser(String id, String name, String username, boolean enabled, Collection<? extends GrantedAuthority> authorities) {
            super(username, NON_EXISTENT_PASSWORD_VALUE, enabled, true, true, true, authorities);
            this.id = id;
            this.name = name;
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
