package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.paean.dto.PaeanTaskDTO;
import cn.ac.picb.paean.po.PaeanTaskPO;
import cn.ac.picb.paean.vo.PaeanTaskParamVO;
import cn.ac.picb.paean.vo.PaeanTaskQueryVO;
import cn.ac.picb.vipmap.client.PaeanServiceClient;
import cn.ac.picb.vipmap.mapper.PaeanMapper;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.PaeanSearchVO;
import cn.ac.picb.vipmap.vo.PaeanTaskParam;
import cn.hutool.core.util.ArrayUtil;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaeanService {

    private final PaeanServiceClient paeanServiceClient;

    public PageResult<PaeanTaskDTO> findPage(CurrentUser user, PaeanSearchVO searchVO, PageParam pageParam) {
        PaeanTaskQueryVO queryVO = PaeanMapper.INSTANCE.convert(searchVO);
        queryVO.setUserId(user.getId());
        queryVO.setPage(pageParam.getPage());
        queryVO.setSize(pageParam.getSize());
        CommonResult<PageResult<PaeanTaskDTO>> result = paeanServiceClient.findTaskPage(queryVO);
        result.checkError();
        return result.getData();
    }

    public String createTask(CurrentUser user, PaeanTaskParam param) {
        PaeanTaskParamVO vo = PaeanMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<String> result = paeanServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<PaeanTaskPO> result = paeanServiceClient.deleteById(id);
        result.checkError();
    }

    public PaeanTaskDTO findTaskDetail(String id) {
        CommonResult<PaeanTaskDTO> result = paeanServiceClient.findDetailById(id);
        result.checkError();
        return result.getData();
    }

    public Response downloadResult(String taskId) {
        return paeanServiceClient.downloadResult(taskId, "result.zip");
    }

    public void batchDeleteTask(String[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return;
        }
        paeanServiceClient.deleteByIds(ids).checkError();
    }
}
