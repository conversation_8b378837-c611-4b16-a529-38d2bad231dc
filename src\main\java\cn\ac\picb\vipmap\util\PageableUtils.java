package cn.ac.picb.vipmap.util;

import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;

/**
 * <AUTHOR>
 */
public final class PageableUtils {

    public long size(PageResult<?> pageResult) {
        return pageResult == null ? 0L : pageResult.getTotal();
    }

    public int totalPages(PageResult<?> pageResult, PageParam pageParam) {
        if (pageResult == null) {
            return 1;
        }
        if (pageParam == null) {
            pageParam = new PageParam();
            pageParam.setPage(0);
            pageParam.setSize(20);
        }

        Long total = pageResult.getTotal();
        Integer size = pageParam.getSize();
        return (int) Math.ceil(total.doubleValue() / size.doubleValue());
    }

    public boolean isFirst(PageParam pageParam) {
        if (pageParam == null) {
            pageParam = new PageParam();
        }
        return pageParam.getPage() > 0;
    }

    public boolean hasNext(PageResult<?> pageResult, PageParam pageParam) {
        if (pageResult == null) {
            return false;
        }
        if (pageParam == null) {
            pageParam = new PageParam();
        }
        return pageParam.getPage() + 1 < totalPages(pageResult, pageParam);
    }

    public boolean isLast(PageResult<?> pageResult, PageParam pageParam) {
        if (pageResult == null) {
            return true;
        }
        if (pageParam == null) {
            pageParam = new PageParam();
            pageParam.setPage(0);
            pageParam.setSize(20);
        }
        return !hasNext(pageResult, pageParam);
    }
}
