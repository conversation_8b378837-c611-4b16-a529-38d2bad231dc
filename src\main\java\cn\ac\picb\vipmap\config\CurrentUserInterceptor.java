package cn.ac.picb.vipmap.config;

import cn.ac.picb.vipmap.config.security.RdrProperties;
import cn.ac.picb.vipmap.config.security.RdrUserDetailsService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CurrentUserInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private RdrProperties rdrProperties;

    @Autowired
    private RdrUserDetailsService rdrUserDetailsService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            // 获取token
            String token = RdrUserDetailsService.obtainToken(request, response);
            if (StrUtil.isBlank(token)) {
                if (StrUtil.containsAnyIgnoreCase(request.getRequestURI(), "/analysis/", "/admin/")) {
                    // token无效或过期，清除SecurityContext并重定向到登录页面
                    SecurityContextHolder.clearContext();
                    // 清除cookie
                    Cookie cookie = new Cookie("Admin-Token", null);
                    cookie.setMaxAge(0);
                    cookie.setPath("/");
                    response.addCookie(cookie);
                    redirectToLoginPage(request, response);
                    return false;
                } else {
                    // todo 重定向
                    return true;
                }
            }

            // 校验token是否有效
            if (!checkTokenValid(token)) {
                // token无效或过期，清除SecurityContext并重定向到登录页面
                SecurityContextHolder.clearContext();
                // 清除cookie
                Cookie cookie = new Cookie("Admin-Token", null);
                cookie.setMaxAge(0);
                cookie.setPath("/");
                response.addCookie(cookie);
                redirectToLoginPage(request, response);
                return false; // 拦截请求
            }

            // token有效，设置用户信息到上下文
            getCurrentLoginUser().ifPresent(UserContext::setUser);
            return true;
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserContext.removeUser();
    }

    /**
     * 校验token是否有效
     */
    private boolean checkTokenValid(String token) {
        try {
            String checkLoginUrl = rdrProperties.getRdrApiCheckUserLogin() + "?token=" + token;
            String body = HttpRequest.get(checkLoginUrl).header("Authorization", token).execute().body();
            cn.hutool.json.JSONObject bodyJsonObject = JSONUtil.parseObj(body);
            Integer code = bodyJsonObject.getInt("code");
            Boolean loginFlag = bodyJsonObject.getBool("data") != null && bodyJsonObject.getBool("data");

            if (ObjectUtil.isEmpty(code) || code != 200 || !loginFlag) {
                log.debug("Token校验失败: code={}, loginFlag={}", code, loginFlag);
                return false;
            }

            // token有效，加载用户信息到SecurityContext
            RdrUserDetailsService.CustomSecurityUser userDetails =
                    (RdrUserDetailsService.CustomSecurityUser) rdrUserDetailsService.loadUserByUsername(token);
            Authentication authentication = new UsernamePasswordAuthenticationToken(
                    userDetails, userDetails.getPassword(), userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);

            return true;
        } catch (Exception e) {
            log.error("Token校验异常", e);
            return false;
        }
    }

    /**
     * 重定向到登录页面
     */
    private void redirectToLoginPage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String loginPageUrl = rdrProperties.getRdrLoginPage();
        log.debug("Token无效，重定向到登录页面: {}", loginPageUrl);
        response.sendRedirect(loginPageUrl);
    }

    private Optional<CurrentUser> getCurrentLoginUser() {
        SecurityContext context = SecurityContextHolder.getContext();
        Authentication authentication = context.getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return Optional.empty();
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof RdrUserDetailsService.CustomSecurityUser) {
            RdrUserDetailsService.CustomSecurityUser user = (RdrUserDetailsService.CustomSecurityUser) principal;
            CurrentUser currentUser = new CurrentUser();
            currentUser.setId(user.getId());
            currentUser.setName(user.getName());
            currentUser.setUsername(user.getUsername());
            return Optional.of(currentUser);
        }
        return Optional.empty();
    }
}
