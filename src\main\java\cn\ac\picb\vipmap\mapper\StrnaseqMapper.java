package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.strnaseq.vo.StrnaseqTaskParamVO;
import cn.ac.picb.vipmap.vo.StrnaseqTaskParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2022/3/2 11:10
 */
@Mapper
public interface StrnaseqMapper {

    StrnaseqMapper INSTANCE = Mappers.getMapper(StrnaseqMapper.class);

    StrnaseqTaskParamVO convertToVO(StrnaseqTaskParam param);
}
