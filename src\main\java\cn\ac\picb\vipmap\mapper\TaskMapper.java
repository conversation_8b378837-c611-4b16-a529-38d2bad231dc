package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.ase.po.AseTaskPO;
import cn.ac.picb.rnaseq.po.RnaseqTaskPO;
import cn.ac.picb.vipmap.vo.TaskVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/7/18
 */
@Mapper
public interface TaskMapper {
    TaskMapper INSTANCE = Mappers.getMapper(TaskMapper.class);

    @Mapping(target = "status", ignore = true)
    TaskVO convert(RnaseqTaskPO po);

    @Mapping(target = "status", ignore = true)
    TaskVO convert(AseTaskPO po);

}
