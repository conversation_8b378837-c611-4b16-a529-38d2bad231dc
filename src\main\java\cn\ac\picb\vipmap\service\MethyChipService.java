package cn.ac.picb.vipmap.service;


import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.methychip.po.MethyChipTaskPO;
import cn.ac.picb.methychip.vo.*;
import cn.ac.picb.vipmap.client.MethyChipServiceClient;
import cn.ac.picb.vipmap.mapper.MethyChipMapper;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.MethyChipTaskParam;
import cn.ac.picb.vipmap.vo.MethyChipTaskSearchVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/15 14:58
 */
@Service
@RequiredArgsConstructor
public class MethyChipService {

    private final MethyChipServiceClient methyChipServiceClient;

    public MethyChipTaskPO createTask(CurrentUser user, MethyChipTaskParam param) {
        MethyChipTaskParamVO vo = MethyChipMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<MethyChipTaskPO> result = methyChipServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public Response downloadTemplate() {
        return methyChipServiceClient.downloadTemplateExcel();
    }

    public List<MethyChipTaskInput> uploadTemplate(MultipartFile file, CurrentUser user) {
        CommonResult<List<MethyChipTaskInput>> result = methyChipServiceClient.uploadTemplate(user.getUsername(), file);
        result.checkError();
        return result.getData();
    }

    public PageResult<MethyChipTaskPO> findPage(CurrentUser user, MethyChipTaskSearchVO search, PageParam pageParam) {
        MethyChipTaskQueryVO queryVO = MethyChipMapper.INSTANCE.convert(search);
        queryVO.setUserId(user.getId());
        queryVO.setPage(pageParam.getPage());
        queryVO.setSize(pageParam.getSize());
        CommonResult<PageResult<MethyChipTaskPO>> result = methyChipServiceClient.findTaskPage(queryVO);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<MethyChipTaskPO> result = methyChipServiceClient.deleteById(id);
        result.checkError();
    }

    public MethyChipTaskVO findTaskVO(String id) {
        CommonResult<MethyChipTaskVO> result = methyChipServiceClient.findDetailById(id);
        result.checkError();
        return result.getData();
    }

    public Response downloadResult(String taskId, String displayName) {
        return methyChipServiceClient.downloadResult(taskId, displayName);
    }

    public Object getChartData(ChartParamVO param) {
        CommonResult<Object> result = methyChipServiceClient.chartData(param);
        result.checkError();
        return result.getData();
    }
}
