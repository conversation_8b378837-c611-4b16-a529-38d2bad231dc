(function () {
    function getCookie(name) {
        return document.cookie.split(';').some(c => c.trim().startsWith(name + '='));
    }

    if (!getCookie("Admin-Token")) {
        const hash = location.hash;
        if (hash.startsWith("#/")) {
            const params = new URLSearchParams(hash.slice(2));
            const token = params.get("token");
            if (token) {
                document.cookie = `Admin-Token=${encodeURIComponent(token)}; path=/`;
            }
        }
    }
})();
