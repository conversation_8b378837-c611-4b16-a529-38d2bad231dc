<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-ase-analysis')}"></div>

            <main>
                <h4 class="border-bottom pb-2 mb-3">RNA-Seq-ASE-rMATS</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/ase/form}" class="active">Add Task</a>
                            <a th:href="@{/analysis/ase/list}">Task List</a>
                        </div>
                    </div>
                    <form id="form">
                        <div class="list-group tab-content">
                            <div class="tab-pane active show fade">
                                <div class="p-2">
                                    <div class="form-group row align-items-center mb-2">
                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Task
                                            Name</label>
                                        <div class="col-xl-10 col-lg-9 col-md-8">
                                            <input class="form-control width-300 input-name validate[required]"
                                                   name="taskName"
                                                   type="text"
                                                   onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                        </div>
                                    </div>
                                    <div class="form-group-box">

                                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Select
                                            Sample</a>
                                        <div class="collapse show" id="coll-1">
                                            <div class="pl-4 pt-2">
                                                <label>Select your input data by fill the following table or upload an
                                                    Excel file that includes the attributes</label>
                                                <div class="d-flex mb-2">
                                                    <input type="file" multiple name="" id="excel"
                                                           class="form-control form-control-sm w-50 mr-2">
                                                    <button type="button" onclick="uploadExcel()"
                                                            class="btn btn-primary btn-sm mr-2 text-nowrap">Upload
                                                    </button>
                                                    <a th:href="@{/analysis/ase/downloadTemplate}"
                                                       class="btn btn-link btn-sm text-nowrap"><i
                                                            class="fa fa-download mr-1"></i>Download template</a>
                                                </div>
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-middle mb-0">
                                                        <thead class="thead-light">
                                                        <tr>
                                                            <td width="200" class="text-center">Sample name</td>
                                                            <td class="text-center">Select file</td>
                                                            <td width="200" class="text-center">Group</td>
                                                            <td width="100"></td>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="sample-table">
                                                        <tr>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center rounded-0 sample"
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="showFileModal(this)">Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend">
                                                                  <span class="input-group-text">
                                                                    <em class="seled"></em>
                                                                  </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text" class="group">
                                                            </td>
                                                            <td>
                                                                <button type="button" class="btn btn-primary btn-sm"
                                                                        onclick="addRow(this)"><i
                                                                        class="fa fa-plus"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-secondary btn-sm"
                                                                        onclick="removeRow(this)"><i
                                                                        class="fa fa-minus"></i></button>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Quality
                                            Control</a>
                                        <div class="collapse show" id="coll-2">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <div class="col-3 custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input"
                                                                   id="Trimmomatic1" name="qcMethod" value="trimmomatic"
                                                                   checked>
                                                            <label for="Trimmomatic1" class="custom-control-label">Trimmomatic</label>
                                                        </div>
                                                        <div class="col-3 custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input"
                                                                   id="ngsqctoolkit" name="qcMethod"
                                                                   value="ngsqctoolkit">
                                                            <label for="ngsqctoolkit"
                                                                   class="custom-control-label">NGS QC Toolkit</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-3" data-toggle="collapse" class="h5 text-primary">Mapping</a>
                                        <div class="collapse show" id="coll-3">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Species</label>
                                                    <div class="col-xl-4 col-lg-3 col-md-8">
                                                        <select class="form-control" name="species" id="species">
                                                            <option value="human">Homo sapiens（human）</option>
                                                            <option value="mouse">Mus musculus（mouse）</option>
                                                        </select>
                                                    </div>
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label">Version</label>
                                                    <div class="col-xl-2 col-lg-3 col-md-4">
                                                        <select class="form-control" name="specVersion"
                                                                id="specVersion">
                                                            <option value="hg38">GRCh38</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <div class="col-3 custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input" id="STAR1"
                                                                   name="mappingMethod" value="STAR" checked>
                                                            <label for="STAR1" class="custom-control-label">STAR</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-xl-10 col-lg-9 col-md-8"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-4" data-toggle="collapse" class="h5 text-primary">Alternative
                                            Splicing</a>
                                        <div class="collapse show" id="coll-4">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <div class="custom-control custom-radio custom-control-inline col-3">
                                                            <input type="radio"
                                                                   class="custom-control-input validate[required]"
                                                                   id="rMATS"
                                                                   name="aseMethod" value="rMATS" checked>
                                                            <label for="rMATS"
                                                                   class="custom-control-label">rMATS</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Read
                                                        length<a href="javascript:;" class="ml-2 text-danger"
                                                                 data-container="body" data-html="true"
                                                                 data-trigger="focus" data-toggle="popover"
                                                                 data-placement="top"
                                                                 data-content="The length of each read"><i
                                                                class="fa fa-question-circle"></i></a></label>
                                                    <div class="col-xl-4 col-lg-4 col-md-5">
                                                        <input type="number"
                                                               class="form-control validate[required,custom[number]]"
                                                               name="readLength">
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Library
                                                        Type</label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <div class="col-3 custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input"
                                                                   id="fr-unstranded" name="libType"
                                                                   value="fr-unstranded"
                                                                   checked>
                                                            <label for="fr-unstranded" class="custom-control-label">
                                                                fr-unstranded</label>
                                                        </div>
                                                        <div class="col-3 custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input"
                                                                   id="fr-firststrand" name="libType"
                                                                   value="fr-firststrand">
                                                            <label for="fr-firststrand" class="custom-control-label">
                                                                fr-firststrand</label>
                                                        </div>
                                                        <div class="col-3 custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input"
                                                                   id="fr-secondstrand" name="libType"
                                                                   value="fr-secondstrand">
                                                            <label for="fr-secondstrand" class="custom-control-label">
                                                                fr-secondstrand</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0">The
                                                        Cutoff Splicing Difference<a href="javascript:;"
                                                                                     class="ml-2 text-danger"
                                                                                     data-container="body"
                                                                                     data-html="true"
                                                                                     data-trigger="focus"
                                                                                     data-toggle="popover"
                                                                                     data-placement="top"
                                                                                     data-content="The cutoff used in the null hypothesis test for differential splicing.Valid: 0 ≤ cutoff <1
"><i
                                                                class="fa fa-question-circle"></i></a></label>
                                                    <div class="col-xl-4 col-lg-4 col-md-5">
                                                        <input type="number"
                                                               class="form-control validate[required,custom[number],min[0],max[1]]"
                                                               name="cstat" value="0.0001">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center my-3">
                                        <a href="javascript:void(0)" onclick="submitForm(this)"
                                           class="btn btn-outline-primary btn-custom"><span>Submit</span><i
                                                class="fa fa-long-arrow-right"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
    <div id="file-modal"></div>
</div>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-zh_CN.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>

    <script>
      $('#file-modal').fileModal('/analysis/fileTree')

      $(document).ready(function () {
        $('#species').on('change', function () {
          var group = $('#species').val()
          if (group == 'human') {
            $('#specVersion').html('<option name="GRCh38" value="hg38" selected>GRCh38</option>')
          } else {
            $('#specVersion').html('<option name="GRCm38" value="mm10" selected>GRCm38</option>')
          }
        })

        $('.group').autocomplete({ source: [] })

        $('.group').on('change', function () {
          $('.group').each(function () {
            var group = $(this).val()
            if (/^[0-9]*$/.test(group)) {
              layer.msg('Group cannot use pure numbers')
              return
            }
          })
        })

        validate()
      })

      function validate () {
        $('.input-int').on('change', function () {
          var value = $.trim($(this).val())
          if (value === '') {
            return
          }
          if (!/^[1-9]+[0-9]*]*$/.test(value)) {
            layer.msg('please input number')
            $(this).val('')
          }
        })

        $('.input-double').on('change', function () {
          var value = $.trim($(this).val())
          if (value === '') {
            return
          }
          if (!/^[0-9]+.?[0-9]*]*$/.test(value)) {
            layer.msg('please input number')
            $(this).val('')
          }
        })
      }

      function bindGroupChange () {

        $('.group').on('change', function () {
          $('.group').each(function () {
            var group = $(this).val()
            if (/^[0-9]*$/.test(group)) {
              layer.msg('Group cannot use pure numbers')
              return
            }
          })
          initAutocomplete()
        })
      }

      function initAutocomplete () {
        var instance = $('.group').autocomplete('instance')
        if (instance) {
          instance.destroy()
        }
        var groupOptions = []

        $('.group').autocomplete({ source: groupOptions })
      }

      function addRow (_this) {
        // $('.group').autocomplete('destroy')
        var clone = $(_this).parents('tr:first').clone()
        clone.find('input.sample').val('')
        clone.find('input.group').val('')
        clone.find('em.seled').html('')
        $(_this).parents('tbody').append(clone)

        // initAutocomplete()
        bindGroupChange()
        validate()
      }

      function removeRow (_this) {
        var rows = $(_this).parents('tbody').find('tr')

        var rowDiv = $(_this).parents('tr')
        if (rows.length > 1) {
          rowDiv.remove()
        } else {
          rowDiv.find('input.sample').val('')
          rowDiv.find('input.group').val('1')
          rowDiv.find('div.select-file').html('')
        }
      }

      var _selectBtn

      function showFileModal (_this) {
        _selectBtn = _this

        var selectIds = []
        $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
          selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
        })
        $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
          selectIds: selectIds,
          func: function (data) {
            return !data.name || /.*\.fastq/.test(data.name) || /.*\.fastq\.gz/.test(data.name) || /.*\.fq\.gz/.test(data.name)
          }
        })
      }

      $('#file-modal').on('__SELECT_FILES__', function (e, data) {
        var nodes = data.nodes || []
        if (nodes.length === 0) {
          return
        }
        if (nodes.length > 2) {
          layer.msg('Up to 2 fastq files in "Select file"')
          return
        }

        var html = []
        $.each(nodes, function (i, node) {
          var filePath = node.path
          var fileName = node.name
          var fileSize = node.size

          html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '')
          html.push('<input type="hidden" value="' + filePath + '">')
          html.push('<input type="hidden" value="' + fileName + '">')
          html.push('<input type="hidden" value="' + fileSize + '">')
          html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
          html.push('</b>')
        })

        $(_selectBtn).parent().next().find('em.seled:first').html(html.join(''))
        $('[data-toggle="tooltip"]').tooltip()
      })

      function removeFile (_this) {
        $(_this).parent().parent().remove()
      }

      function validateRequired () {
        var flag = false
        var sampleIds = []
        $.each($('input.sample'), function () {
          var sampleId = $.trim($(this).val())
          if (sampleId === '') {
            flag = true
          }
          sampleIds.push(sampleId)
        })
        if (isRepeat(sampleIds)) {
          layer.msg('Please check the sample name, the name within the same task needs to be uniquely.')
          return false
        }

        if (flag || sampleIds.length === 0) {
          layer.msg('sample name can not be empty')
          return false
        }

        let fileNumSet = new Set()
        var rows = $('#sample-table').find('tr')
        $.each(rows, function (i, item) {
          let index = 0
          $.each($(item).find('td:eq(1)').find('b.text-primary'), function (j, it) {
            if (j > 2) {
              layer.msg('Up to 2 fastq files in "Select file"')
              return false
            }
            index = j + 1 > index ? j + 1 : index
          })
          fileNumSet.add(index)
        })

        if (fileNumSet.size > 1) {
          layer.msg('The type(single-end or paired-end) of data used </br> in the analysis should be the same.')
          return false
        }
        if (fileNumSet.has(0)) {
          layer.msg('Select file can not be empty')
          return false
        }

        var groups = []
        $.each($('input.group'), function () {
          var group = $.trim($(this).val())
          if (group === '' || /^[0-9]*$/.test(group)) {
            flag = true
          }
          groups.push(group)
        })
        if (flag || groups.length === 0) {
          layer.msg('group can not be empty or use pure numbers')
          return false
        }

        let groupMap = new Map()
        let maxCount = 0
        for (let group of groups) {
          if (groupMap.has(group)) {
            let count = groupMap.get(group) + 1
            if (count > maxCount) {
              maxCount = count
            }
            groupMap.set(group, count)
          } else {
            groupMap.set(group, 1)
          }
        }

        if (groupMap.size != 2) {
          layer.msg('The number of groups should be 2.')
          return false
        }

        $.each($('div.select-file'), function () {
          var size = $(this).find('div.btn-group').length
          if (size === 0) {
            layer.msg('please select fastq file')
            return false
          }
        })

        var readLength = $('input[name=\'readLength\']').val() || ''
        if (readLength <= 0) {
          layer.msg('Read Length must be greater than 0')
          return false
        }
        var cstat = $('input[name=\'cstat\']').val() || ''
        if (cstat < 0 || cstat >= 1) {
          layer.msg('The Cutoff Splicing Difference in range: 0 ≤ cutoff < 1')
          return false
        }
        return true
      }

      function submitForm (_this) {
        if (!$('#form').validationEngine('validate')) {
          return
        }
        if (!validateRequired()) {
          return
        }

        var formData = new FormData()

        var rows = $('#sample-table').find('tr')
        $.each(rows, function (i, item) {
          var sample = $(item).find('td:eq(0)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].sample', sample)

          $.each($(item).find('td:eq(1)').find('b.text-primary'), function (j, it) {
            var path = $(it).find('input:eq(0)').val()
            var name = $(it).find('input:eq(1)').val()
            var size = $(it).find('input:eq(2)').val()

            formData.append('inputs[' + i + '].files[' + j + '].path', path)
            formData.append('inputs[' + i + '].files[' + j + '].name', name)
            formData.append('inputs[' + i + '].files[' + j + '].size', size)
          })

          var group = $(item).find('td:eq(2)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].group', group)

        })

        var taskName = $('input[name=\'taskName\']').val()
        var qcMethod = $('input[name=\'qcMethod\']:checked').val() || ''
        var species = $('select[name=\'species\']').val() || ''
        var specVersion = $('select[name=\'specVersion\']').val() || ''
        var mappingMethod = $('input[name=\'mappingMethod\']:checked').val() || ''
        var aseMethod = $('input[name=\'aseMethod\']:checked').val() || ''
        var readLength = $('input[name=\'readLength\']').val() || ''
        var libType = $('input[name=\'libType\']:checked').val() || ''
        var cstat = $('input[name=\'cstat\']').val() || ''

        formData.append('taskName', taskName)
        formData.append('qcMethod', qcMethod)
        formData.append('species', species)
        formData.append('specVersion', specVersion)
        formData.append('mappingMethod', mappingMethod)
        formData.append('aseMethod', aseMethod)
        formData.append('readLength', readLength)
        formData.append('libType', libType)
        formData.append('cstat', cstat)

        if ($(_this).data('loading') == 'true') {
          return
        }
        $(_this).data('loading', 'true')

        $.ajax({
          url: '/analysis/ase/createTask',
          dataType: 'json',
          type: 'post',
          processData: false,
          contentType: false,
          data: formData,
          success: function (result) {
            if (result.code == 200) {
              layer.msg('submit success')
              var id = result.data
              setTimeout(function () {
                var _context_path = $('meta[name=\'_context_path\']').attr('content')
                window.location.href = $.trim(_context_path) + '/analysis/ase/list?taskId=' + id
              }, 2000)
            }
          },
          complete: function () {
            $(_this).data('loading', 'false')
          }
        })
      }

      function uploadExcel () {
        if ($('#excel').val() === '') {
          layer.msg('please select a file')
          return
        }
        var formData = new FormData()
        formData.append('file', $('#excel')[0].files[0])
        $.ajax({
          url: '/analysis/ase/uploadTemplate',
          data: formData,
          dataType: 'json',
          type: 'post',
          async: false,
          processData: false,
          contentType: false,
          success: function (result) {
            if (result.success) {
              var data = result.data || []
              if (data.length === 0) {
                layer.msg('no data')
                return
              }
              var trs = []
              $.each(data, function (idx, item) {
                var html = ['<tr>']
                html.push('<td class="td-input">')
                html.push('<input type="text" value="' + item.sample + '" class="form-control text-center rounded-0 sample" onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>')
                html.push('<td class="td-input">')
                html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>')
                html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">')
                obtainTr(html, item.files)
                html.push('</em></span></div></div></td>')

                html.push('<td class="td-input"> <input type="text" class="group"   value="' + item.group + '"> </td>')

                html.push(`<td>
                               <button type="button" class="btn btn-primary btn-sm" onclick="addRow(this)"><i class="fa fa-plus"></i>
                               </button>
                               <button type="button" class="btn btn-secondary btn-sm" onclick="removeRow(this)"><i class="fa fa-minus"></i></button>
                           </td>`)
                trs.push(html.join(''))
              })
              $('#sample-table').html(trs.join(''))
              bindGroupChange()
              // layer.msg("The data is imported successfully. Please check whether the table data result is correct")
            } else {
              layer.msg(result.message)
            }
          }
        })
      }

      function obtainTr (html, nodes) {
        if (!nodes) {
          return
        }
        $.each(nodes, function (i, node) {
          var filePath = node.path
          var fileName = node.name
          var fileSize = node.size
          html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '')
          html.push('<input type="hidden" value="' + filePath + '">')
          html.push('<input type="hidden" value="' + fileName + '">')
          html.push('<input type="hidden" value="' + fileSize + '">')
          html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
          html.push('</b>')
        })
      }

    </script>
</th:block>
</html>
