package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnaseq.dto.ReportTaskDTO;
import cn.ac.picb.scrnaseq.po.ReportTaskPO;
import cn.ac.picb.scrnaseq.vo.*;
import cn.ac.picb.vipmap.client.ScrnaseqReportServiceClient;
import org.springframework.stereotype.Component;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaseqReportServiceClientFallback implements ScrnaseqReportServiceClient {

    private final static String SERVER_NAME = "scrnaseq-service";

    @Override
    public CommonResult<PageResult<ReportTaskDTO>> findReportTaskPage(ReportQueryVO reportQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ReportTaskPO> saveReportTask(ReportTaskCreateParamVO reportTaskCreateParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ReportTaskPO> findReportById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ReportTaskDTO> findReportDtoById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ReportTaskPO> deleteReportById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SelectTaskVO> findTaskBySelectParams(SelectParamVO selectParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ReportTaskDTO> findTaskResultByParams(ReportTaskParamVO reportTaskParamVO) {
        return serverError(SERVER_NAME);
    }
}
