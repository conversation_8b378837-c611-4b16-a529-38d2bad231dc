<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">10X Genomics</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <th:block th:switch="${taskVo.genomicsTask.status}">
                        <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                    aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Prepared</p>
                        </div>
                        <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="2">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                    aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Data prepared</p>
                        </div>
                        <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="3">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                    aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Import data</p>
                        </div>
                        <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="4">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                    aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis done</p>
                        </div>
                        <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                    aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis Error</p>
                        </div>
                    </th:block>

                    <div class="form-group-box">
                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                        <div class="collapse show" id="coll-1">
                            <div class="pl-4 pt-2">
                                <div class="result-box">
                                    <h5 class="mb-3 font-16"><span>Expression matrix</span></h5>
                                    <th:block th:if="${taskVo.genomicsTask.dataMode == 'fastq'}">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm table-center table-middle">
                                                <thead>
                                                <tr class="thead-light">
                                                    <th>Sample ID</th>
                                                    <th>I1</th>
                                                    <th>R1</th>
                                                    <th>R2</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr th:each="vo : ${taskVo.matrix}">
                                                    <td th:text="${vo.sampleId}">HG00114</td>
                                                    <td>
                                                        <th:block th:each="file, sta : ${vo.row.i1}">
                                                            <span class="font-12" th:text="${file.path}">Sample1_S1_L001_R1_001.fastq.gz</span><br
                                                                th:unless="${sta.last}">
                                                        </th:block>
                                                    </td>
                                                    <td>
                                                        <th:block th:each="file, sta : ${vo.row.r1}">
                                                            <span class="font-12" th:text="${file.path}">Sample1_S1_L001_R1_001.fastq.gz</span><br
                                                                th:unless="${sta.last}">
                                                        </th:block>
                                                    </td>
                                                    <td>
                                                        <th:block th:each="file, sta : ${vo.row.r2}">
                                                            <span class="font-12" th:text="${file.path}">Sample1_S1_L001_R1_001.fastq.gz</span><br
                                                                th:unless="${sta.last}">
                                                        </th:block>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                        <div class="form-group row align-items-center m-0">
                                            <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                            <div class="col-xl-3 col-lg-3 col-md-3">
                                                <span class="text-primary" th:text="${taskVo.genomicsTask.species}">Homo sapiens（human）</span>
                                            </div>
                                            <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Version</label>
                                            <div class="col-xl-3 col-lg-3 col-md-3">
                                                <span class="text-primary" th:text="${taskVo.genomicsTask.version}">GRCH38（hg38）</span>
                                            </div>
                                            <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                            <div class="col-xl-3 col-lg-3 col-md-3">
                                                <span class="text-primary" th:text="${taskVo.genomicsTask.mapMethod}">STAR</span>
                                            </div>
                                        </div>
                                        <h6 class="border-bottom pb-2 m-0">Counting</h6>
                                        <div class="form-group row align-items-center m-0">
                                            <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                            <div class="col-xl-3 col-lg-3 col-md-3">
                                                <span class="text-primary" th:text="${taskVo.genomicsTask.countMethod}">cellranger</span>
                                            </div>
                                            <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Option</label>
                                            <div class="col-xl-3 col-lg-3 col-md-3">
                                                <span class="text-primary" th:text="${taskVo.genomicsTask.rangerMode}">count</span>
                                            </div>
                                        </div>
                                    </th:block>

                                    <th:block th:if="${taskVo.genomicsTask.dataMode == 'matrix'}">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm table-center table-middle">
                                                <thead>
                                                <tr class="thead-light">
                                                    <th>Sample ID</th>
                                                    <td>barcodes.tsv.gz</td>
                                                    <td>features.tsv.gz</td>
                                                    <td>matrix.mtx.gz</td>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr th:each="vo : ${taskVo.matrix}">
                                                    <td th:text="${vo.sampleId}">HG00114</td>
                                                    <td>
                                                        <span class="font-12" th:text="${vo.row.barcode.path}">Sample1_S1_L001_R1_001.fastq.gz</span>
                                                    </td>
                                                    <td>
                                                        <span class="font-12" th:text="${vo.row.feature.path}">Sample1_S1_L001_R1_001.fastq.gz</span>
                                                    </td>
                                                    <td>
                                                        <span class="font-12" th:text="${vo.row.matrix.path}">Sample1_S1_L001_R1_001.fastq.gz</span>

                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                        <div class="form-group row align-items-center m-0">
                                            <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                            <div class="col-xl-3 col-lg-3 col-md-3">
                                                <span class="text-primary" th:text="${taskVo.genomicsTask.species}">Homo sapiens（human）</span>
                                            </div>
                                        </div>
                                    </th:block>

                                    <th:block th:if="${taskVo.genomicsTask.dataMode == 'csv'}">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm table-center table-middle">
                                                <thead>
                                                <tr class="thead-light">
                                                    <th>Sample ID</th>
                                                    <th>csv file</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr th:each="vo : ${taskVo.matrix}">
                                                    <td th:text="${vo.sampleId}">HG00114</td>
                                                    <td>
                                                        <span class="font-12" th:text="${vo.row.csv.path}">Sample1_S1_L001_R1_001.fastq.gz</span>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                        <div class="form-group row align-items-center m-0">
                                            <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                            <div class="col-xl-3 col-lg-3 col-md-3">
                                                <span class="text-primary" th:text="${taskVo.genomicsTask.species}">Homo sapiens（human）</span>
                                            </div>
                                        </div>
                                    </th:block>

                                    <h5 class="mb-1 font-16"><span>Post processing</span></h5>
                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                        <div class="col-xl-3 col-lg-3 col-md-3">
                                            <span class="text-primary">Seurat</span>
                                        </div>
                                    </div>
                                    <h6 class="border-bottom pb-2 m-0">Cell filter</h6>
                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">number of
                                            nGene</label>
                                        <div class="col-xl-2 col-lg-2col-md-4">
                                            <span class="text-primary">[[${taskVo.genomicsTask.nGeneStart}]] - [[${taskVo.genomicsTask.nGeneEnd}]]</span>
                                        </div>
                                        <label class="col-xl-3 col-lg-2 col-md-3 col-form-label pr-0">percentage of
                                            mitochondrial genes</label>
                                        <div class="col-xl-2 col-lg-2col-md-4">
                                            <span class="text-primary">[[${taskVo.genomicsTask.toPerStart}]] - [[${taskVo.genomicsTask.toPerEnd}]]</span>
                                        </div>
                                    </div>
                                    <h6 class="border-bottom pb-2 m-0">Clustering</h6>
                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">resolution
                                            parameter</label>
                                        <div class="col-xl-2 col-lg-2col-md-4">
                                            <span class="text-primary"
                                                  th:text="${taskVo.genomicsTask.resolution}">0.8</span>
                                        </div>
                                    </div>
                                    <div class="form-group row align-items-baseline m-0"
                                         th:unless="${#strings.isEmpty(taskVo.genomicsTask.TGene)}">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">t-SNE
                                            genes</label>
                                        <div class="col-xl-2 col-lg-2col-md-4">
                                            <th:block
                                                    th:each="gene, sta : ${#strings.listSplit(taskVo.genomicsTask.TGene, ';')}">
                                                <span class="text-primary" th:text="${gene}">MS4A1</span><br
                                                    th:unless="${sta.last}">
                                            </th:block>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group-box" th:if="${taskVo.genomicsTask.status == 4}">
                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                        <div class="collapse show" id="coll-2">
                            <div class="tool-content">
                                <th:block th:if="${taskVo.genomicsTask.rangerMode == 'Count'}">
                                    <th:block
                                            th:each="sampleName: ${#strings.arraySplit(taskVo.genomicsTask.sampleIds, ';')}">
                                        <p>Cellranger HTML report:
                                            <a th:href="@{/analysis/scrnaseq/common/download(code='cellranger_report_count', runName=${taskVo.genomicsTask.taskId}, sampleName=${sampleName}, resName=${sampleName+'_web_summary.html'})}"
                                               target="_blank">[[${sampleName}]] <i
                                                    class="fa fa-download text-primary ml-2"></i>
                                            </a>
                                        </p>
                                    </th:block>
                                </th:block>
                                <th:block
                                        th:if="${taskVo.genomicsTask.rangerMode == 'Aggr' or taskVo.genomicsTask.rangerMode == 'Advanced'}">
                                    <p>Cellranger HTML report:
                                        <a th:href="@{/analysis/scrnaseq/common/download(code='cellranger_report_aggr_advanced', runName=${taskVo.genomicsTask.taskId})}"
                                           target="_blank">web_summary.html<i
                                                class="fa fa-download text-primary ml-2"></i>
                                        </a>
                                    </p>
                                </th:block>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">
                                                t-SNE for clusters</h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='tsne_for_cluster', runName=${taskVo.genomicsTask.taskId})}" download target="_blank">-->
                                            <!--                                                <i class="fa fa-download text-primary"></i>-->
                                            <!--                                            </a>-->
                                        </div>
                                        <div id="chart-01" style="width: 100%;height: 500px"></div>
                                    </div>
                                    <div class="col-lg-12"
                                         th:if="${taskVo.genomicsTask.rangerMode == 'Aggr' or taskVo.genomicsTask.rangerMode == 'Advanced'}">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">
                                                t-SNE for libraries</h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='tsne_for_libraries', runName=${taskVo.genomicsTask.taskId})}" download target="_blank">-->
                                            <!--                                                <i class="fa fa-download text-primary"></i>-->
                                            <!--                                            </a>-->
                                        </div>
                                        <div id="chart-02" style="width: 100%;height: 500px"></div>
                                    </div>
                                </div>
                                <p class="my-2">seurat DEGs: <a
                                        th:href="@{/analysis/scrnaseq/common/download(code='cluster_markers_tab', runName=${taskVo.genomicsTask.taskId})}"
                                        download
                                        target="_blank">cluster.markers.tab <i
                                        class="fa fa-download text-primary ml-2"></i></a></p>
                                <div class="row" th:unless="${#strings.isEmpty(taskVo.genomicsTask.tGene)}">
                                    <div class="col-lg-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">
                                                Elbow plot for pc</h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='Expression_level_for_t_SNE_genes', runName=${taskVo.genomicsTask.taskId})}" download-->
                                            <!--                                               target="_blank"><i class="fa fa-download text-primary"></i>-->
                                            <!--                                            </a>-->
                                        </div>
                                        <div id="chart-03" style="width: 100%;height: 500px"></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">
                                                Expression level for t-SNE genes</h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='elbow_plot', runName=${taskVo.genomicsTask.taskId})}"-->
                                            <!--                                               target="_blank"><i class="fa fa-download text-primary"></i>-->
                                            <!--                                            </a>-->
                                        </div>
                                        <div id="chart-04" class="row"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>

    <script>
        $(document).ready(function () {
            initChart01();
            initChart02();
            initChart03();
            initChart04();
        })

        function initChart01() {
            if (!document.getElementById('chart-01')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-01'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/genomics/[[${taskVo.genomicsTask.id}]]/1',
                    beforeSend: function () {
                        $("#chart-01").next().remove();
                        $("#chart-01").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-01").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-01").hide();
                            return;
                        }

                        var data = result.data;
                        var legendData = [];
                        var series = [];

                        for (let group in data) {
                            legendData.push(group);
                            series.push({
                                name: group,
                                data: data[group],
                                type: 'scatter',
                                symbolSize: 5,
                                cursor: 'default',
                                itemStyle: {
                                    color: getClusterColor(group)
                                }
                            });
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: legendData
                            },
                            xAxis: {
                                name: 'tSNE_1',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameLocation: 'center',
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            yAxis: {
                                name: 'tSNE_2',
                                nameLocation: 'center',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            series: series
                        })
                    }
                })
            }
        }

        function initChart02() {
            if (!document.getElementById('chart-02')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-02'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/genomics/[[${taskVo.genomicsTask.id}]]/2',
                    beforeSend: function () {
                        $("#chart-02").next().remove();
                        $("#chart-02").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-02").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-02").hide();
                            return;
                        }

                        var data = result.data;
                        var legendData = [];
                        var series = [];

                        for (var group in data) {
                            legendData.push(group);
                            series.push({
                                name: group,
                                data: data[group],
                                type: 'scatter',
                                symbolSize: 5,
                                cursor: 'default'
                            });
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: legendData
                            },
                            xAxis: {
                                name: 'tSNE_1',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameLocation: 'center',
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            yAxis: {
                                name: 'tSNE_2',
                                nameLocation: 'center',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            series: series
                        })
                    }
                })
            }
        }

        function initChart03() {
            if (!document.getElementById('chart-03')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-03'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/genomics/[[${taskVo.genomicsTask.id}]]/3',
                    beforeSend: function () {
                        $("#chart-03").next().remove();
                        $("#chart-03").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-03").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-03").hide();
                            return;
                        }

                        var data = result.data;

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            xAxis: {
                                name: 'PC',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameLocation: 'center',
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                                    type: 'cross'        // 默认为直线，可选为：'line' | 'shadow'
                                }
                            },
                            yAxis: {
                                name: 'Standard Deviation of PC',
                                nameLocation: 'center',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            series: [
                                {
                                    data: data,
                                    type: 'scatter',
                                    symbolSize: 5,
                                    cursor: 'default'
                                }
                            ]
                        })
                    }
                })
            }
        }

        function initChart04() {
            if (!document.getElementById('chart-04')) {
                return;
            }
            drawChart();

            function drawChart() {
                $.ajax({
                    url: '/analysis/scrnaseq/genomics/[[${taskVo.genomicsTask.id}]]/4',
                    beforeSend: function () {
                        $("#chart-04").next().remove();
                        $("#chart-04").show();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-04").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-04").hide();
                            return;
                        }

                        var data = result.data;
                        var options = [];
                        for (var name in data) {
                            var d = data[name].map(function (p) {
                                if (p[2] == 0) {
                                    return {
                                        value: [p[0], p[1], p[2]],
                                        itemStyle: {
                                            color: 'rgb(110,184,202)'
                                        }
                                    }
                                } else {
                                    return {
                                        value: [p[0], p[1], p[2]],
                                        itemStyle: {
                                            color: 'rgba(42, 6, 141,' + (0.5 + p[2] / 10) + ')'
                                        }
                                    }
                                }
                            })
                            options.push({
                                title: {
                                    text: name
                                },
                                tooltip: {
                                    trigger: 'axis',
                                    axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                                        type: 'cross'        // 默认为直线，可选为：'line' | 'shadow'
                                    },
                                    formatter: function (params) {
                                        return params[0].data.value[2];
                                    }
                                },
                                toolbox: {
                                    show: true,
                                    feature: {
                                        saveAsImage: {}
                                    }
                                },
                                xAxis: {
                                    name: 'tSNE_1',
                                    nameTextStyle: {
                                        fontWeight: 'bold'
                                    },
                                    nameLocation: 'center',
                                    nameGap: 35,
                                    splitLine: {
                                        show: false
                                    }
                                },
                                yAxis: {
                                    name: 'tSNE_2',
                                    nameLocation: 'center',
                                    nameTextStyle: {
                                        fontWeight: 'bold'
                                    },
                                    nameGap: 35,
                                    splitLine: {
                                        show: false
                                    }
                                },
                                series: [{
                                    data: d,
                                    type: 'scatter',
                                    symbolSize: 5,
                                    cursor: 'default'
                                }]
                            })
                        }

                        var length = options.length
                        for (var i = 0; i < length; i++) {
                            if (length > 1) {
                                $("#chart-04").append('<div id="chart-04-' + i + '" class="col-lg-6" style="height: 500px;">')
                            } else {
                                $("#chart-04").append('<div id="chart-04-' + i + '" class="col-lg-12" style="width: 100%;height: 500px;">')
                            }
                            var myChart = echarts.init(document.getElementById('chart-04-' + i));
                            myChart.setOption(options[i]);
                        }
                    }
                })
            }
        }
    </script>
</th:block>
</html>
