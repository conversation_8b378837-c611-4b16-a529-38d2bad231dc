package cn.ac.picb.vipmap.config.security;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * VipMap登出成功处理器
 * 参考RdrLogoutSuccessHandler实现
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class RdrLogoutSuccessHandler implements LogoutSuccessHandler {

    private final RdrProperties rdrProperties;

    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException {
        log.info("用户登出成功");

        // 重定向到登录页面或主页
        // 对于前后不分离的项目，重定向到登录页面
        response.sendRedirect(request.getContextPath() + "/home");
    }
}
