/*! jQuery Fancytree Plugin - 2.35.0 - 2020-03-27T22:41:58Z
  * https://github.com/mar10/fancytree
  * Copyright (c) 2020 <PERSON>; Licensed MIT
 */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {
		// AMD. Register as an anonymous module.
		define( [
			"jquery",
			"jquery-ui/ui/widgets/mouse",
			"jquery-ui/ui/widgets/draggable",
			"jquery-ui/ui/widgets/droppable",
			"jquery-ui/ui/effects/effect-blind",
			"jquery-ui/ui/data",
			"jquery-ui/ui/effect",
			"jquery-ui/ui/focusable",
			"jquery-ui/ui/keycode",
			"jquery-ui/ui/position",
			"jquery-ui/ui/scroll-parent",
			"jquery-ui/ui/tabbable",
			"jquery-ui/ui/unique-id",
			"jquery-ui/ui/widget"
		], factory );
	} else if ( typeof module === "object" && module.exports ) {
		// Node/CommonJS
		module.exports = factory(require("jquery"));
	} else {
		// Browser globals
		factory( jQuery );
	}
}(function( $ ) {

!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree.ui-deps"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree.ui-deps"),module.exports=e(require("jquery"))):e(jQuery)}(function(k){"use strict";if(!k.ui||!k.ui.fancytree){var o,e,g=null,v=new RegExp(/\.|\//),t=/[&<>"'/]/g,n=/[<>"'/]/g,h="$recursive_request",f="$request_target_invalid",i={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},r={16:!0,17:!0,18:!0},y={8:"backspace",9:"tab",10:"return",13:"return",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",59:";",61:"=",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scroll",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},m={16:"shift",17:"ctrl",18:"alt",91:"meta",93:"meta"},s={0:"",1:"left",2:"middle",3:"right"},b="active expanded focus folder lazy radiogroup selected unselectable unselectableIgnore".split(" "),x={},p="columns types".split(" "),N="checkbox expanded extraClasses folder icon iconTooltip key lazy partsel radiogroup refKey selected statusNodeType title tooltip type unselectable unselectableIgnore unselectableStatus".split(" "),a={},_={},l={active:!0,children:!0,data:!0,focus:!0};for(o=0;o<b.length;o++)x[b[o]]=!0;for(o=0;o<N.length;o++)e=N[o],a[e]=!0,e!==e.toLowerCase()&&(_[e.toLowerCase()]=e);return C(k.ui,"Fancytree requires jQuery UI (http://jqueryui.com)"),Date.now||(Date.now=function(){return(new Date).getTime()}),H.prototype={_findDirectChild:function(e){var t,n,i=this.children;if(i)if("string"==typeof e){for(t=0,n=i.length;t<n;t++)if(i[t].key===e)return i[t]}else{if("number"==typeof e)return this.children[e];if(e.parent===this)return e}return null},_setChildren:function(e){C(e&&(!this.children||0===this.children.length),"only init supported"),this.children=[];for(var t=0,n=e.length;t<n;t++)this.children.push(new H(this,e[t]));this.tree._callHook("treeStructureChanged",this.tree,"setChildren")},addChildren:function(e,t){var n,i,r,o,s=this.getFirstChild(),a=this.getLastChild(),l=[];for(k.isPlainObject(e)&&(e=[e]),this.children||(this.children=[]),n=0,i=e.length;n<i;n++)l.push(new H(this,e[n]));if(o=l[0],null==t?this.children=this.children.concat(l):(t=this._findDirectChild(t),C(0<=(r=k.inArray(t,this.children)),"insertBefore must be an existing child"),this.children.splice.apply(this.children,[r,0].concat(l))),s&&!t){for(n=0,i=l.length;n<i;n++)l[n].render();s!==this.getFirstChild()&&s.renderStatus(),a!==this.getLastChild()&&a.renderStatus()}else(!this.parent||this.parent.ul||this.tr)&&this.render();return 3===this.tree.options.selectMode&&this.fixSelection3FromEndNodes(),this.triggerModifyChild("add",1===l.length?l[0]:null),o},addClass:function(e){return this.toggleClass(e,!0)},addNode:function(e,t){switch(void 0!==t&&"over"!==t||(t="child"),t){case"after":return this.getParent().addChildren(e,this.getNextSibling());case"before":return this.getParent().addChildren(e,this);case"firstChild":var n=this.children?this.children[0]:null;return this.addChildren(e,n);case"child":case"over":return this.addChildren(e)}C(!1,"Invalid mode: "+t)},addPagingNode:function(e,t){var n,i;if(t=t||"child",!1!==e)return e=k.extend({title:this.tree.options.strings.moreData,statusNodeType:"paging",icon:!1},e),this.partload=!0,this.addNode(e,t);for(n=this.children.length-1;0<=n;n--)"paging"===(i=this.children[n]).statusNodeType&&this.removeChild(i);this.partload=!1},appendSibling:function(e){return this.addNode(e,"after")},applyCommand:function(e,t){return this.tree.applyCommand(e,this,t)},applyPatch:function(e){if(null===e)return this.remove(),S(this);var t,n,i={children:!0,expanded:!0,parent:!0};for(t in e)e.hasOwnProperty(t)&&(n=e[t],i[t]||k.isFunction(n)||(a[t]?this[t]=n:this.data[t]=n));return e.hasOwnProperty("children")&&(this.removeChildren(),e.children&&this._setChildren(e.children)),this.isVisible()&&(this.renderTitle(),this.renderStatus()),e.hasOwnProperty("expanded")?this.setExpanded(e.expanded):S(this)},collapseSiblings:function(){return this.tree._callHook("nodeCollapseSiblings",this)},copyTo:function(e,t,n){return e.addNode(this.toDict(!0,n),t)},countChildren:function(e){var t,n,i,r=this.children;if(!r)return 0;if(i=r.length,!1!==e)for(t=0,n=i;t<n;t++)i+=r[t].countChildren();return i},debug:function(e){4<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("log",arguments))},discard:function(){return this.warn("FancytreeNode.discard() is deprecated since 2014-02-16. Use .resetLazy() instead."),this.resetLazy()},discardMarkup:function(e){var t=e?"nodeRemoveMarkup":"nodeRemoveChildMarkup";this.tree._callHook(t,this)},error:function(e){1<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("error",arguments))},findAll:function(t){t=k.isFunction(t)?t:P(t);var n=[];return this.visit(function(e){t(e)&&n.push(e)}),n},findFirst:function(t){t=k.isFunction(t)?t:P(t);var n=null;return this.visit(function(e){if(t(e))return n=e,!1}),n},findRelatedNode:function(e,t){return this.tree.findRelatedNode(this,e,t)},_changeSelectStatusAttrs:function(e){var t=!1,n=this.tree.options,i=g.evalOption("unselectable",this,this,n,!1),r=g.evalOption("unselectableStatus",this,this,n,void 0);switch(i&&null!=r&&(e=r),e){case!1:t=this.selected||this.partsel,this.selected=!1,this.partsel=!1;break;case!0:t=!this.selected||!this.partsel,this.selected=!0,this.partsel=!0;break;case void 0:t=this.selected||!this.partsel,this.selected=!1,this.partsel=!0;break;default:C(!1,"invalid state: "+e)}return t&&this.renderStatus(),t},fixSelection3AfterClick:function(e){var t=this.isSelected();this.visit(function(e){if(e._changeSelectStatusAttrs(t),e.radiogroup)return"skip"}),this.fixSelection3FromEndNodes(e)},fixSelection3FromEndNodes:function(e){var u=this.tree.options;C(3===u.selectMode,"expected selectMode 3"),function e(t){var n,i,r,o,s,a,l,d,c=t.children;if(c&&c.length){for(l=!(a=!0),n=0,i=c.length;n<i;n++)o=e(r=c[n]),g.evalOption("unselectableIgnore",r,r,u,!1)||(!1!==o&&(l=!0),!0!==o&&(a=!1));s=!!a||!!l&&void 0}else s=null==(d=g.evalOption("unselectableStatus",t,t,u,void 0))?!!t.selected:!!d;return t.partsel&&!t.selected&&t.lazy&&null==t.children&&(s=void 0),t._changeSelectStatusAttrs(s),s}(this),this.visitParents(function(e){var t,n,i,r,o,s=e.children,a=!0,l=!1;for(t=0,n=s.length;t<n;t++)i=s[t],g.evalOption("unselectableIgnore",i,i,u,!1)||(((r=null==(o=g.evalOption("unselectableStatus",i,i,u,void 0))?!!i.selected:!!o)||i.partsel)&&(l=!0),r||(a=!1));r=!!a||!!l&&void 0,e._changeSelectStatusAttrs(r)})},fromDict:function(e){for(var t in e)a[t]?this[t]=e[t]:"data"===t?k.extend(this.data,e.data):k.isFunction(e[t])||l[t]||(this.data[t]=e[t]);e.children&&(this.removeChildren(),this.addChildren(e.children)),this.renderTitle()},getChildren:function(){if(void 0!==this.hasChildren())return this.children},getFirstChild:function(){return this.children?this.children[0]:null},getIndex:function(){return k.inArray(this,this.parent.children)},getIndexHier:function(e,n){e=e||".";var i,r=[];return k.each(this.getParentList(!1,!0),function(e,t){i=""+(t.getIndex()+1),n&&(i=("0000000"+i).substr(-n)),r.push(i)}),r.join(e)},getKeyPath:function(e){var t=this.tree.options.keyPathSeparator;return t+this.getPath(!e,"key",t)},getLastChild:function(){return this.children?this.children[this.children.length-1]:null},getLevel:function(){for(var e=0,t=this.parent;t;)e++,t=t.parent;return e},getNextSibling:function(){if(this.parent){var e,t,n=this.parent.children;for(e=0,t=n.length-1;e<t;e++)if(n[e]===this)return n[e+1]}return null},getParent:function(){return this.parent},getParentList:function(e,t){for(var n=[],i=t?this:this.parent;i;)(e||i.parent)&&n.unshift(i),i=i.parent;return n},getPath:function(e,t,n){e=!1!==e,t=t||"title",n=n||"/";var i,r=[],o=k.isFunction(t);return this.visitParents(function(e){e.parent&&(i=o?t(e):e[t],r.unshift(i))},e),r.join(n)},getPrevSibling:function(){if(this.parent){var e,t,n=this.parent.children;for(e=1,t=n.length;e<t;e++)if(n[e]===this)return n[e-1]}return null},getSelectedNodes:function(t){var n=[];return this.visit(function(e){if(e.selected&&(n.push(e),!0===t))return"skip"}),n},hasChildren:function(){if(this.lazy){if(null==this.children)return;if(0===this.children.length)return!1;if(1===this.children.length&&this.children[0].isStatusNode())return;return!0}return!(!this.children||!this.children.length)},hasClass:function(e){return 0<=(" "+(this.extraClasses||"")+" ").indexOf(" "+e+" ")},hasFocus:function(){return this.tree.hasFocus()&&this.tree.focusNode===this},info:function(e){3<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("info",arguments))},isActive:function(){return this.tree.activeNode===this},isBelowOf:function(e){return this.getIndexHier(".",5)>e.getIndexHier(".",5)},isChildOf:function(e){return this.parent&&this.parent===e},isDescendantOf:function(e){if(!e||e.tree!==this.tree)return!1;for(var t=this.parent;t;){if(t===e)return!0;t===t.parent&&k.error("Recursive parent link: "+t),t=t.parent}return!1},isExpanded:function(){return!!this.expanded},isFirstSibling:function(){var e=this.parent;return!e||e.children[0]===this},isFolder:function(){return!!this.folder},isLastSibling:function(){var e=this.parent;return!e||e.children[e.children.length-1]===this},isLazy:function(){return!!this.lazy},isLoaded:function(){return!this.lazy||void 0!==this.hasChildren()},isLoading:function(){return!!this._isLoading},isRoot:function(){return this.isRootNode()},isPartsel:function(){return!this.selected&&!!this.partsel},isPartload:function(){return!!this.partload},isRootNode:function(){return this.tree.rootNode===this},isSelected:function(){return!!this.selected},isStatusNode:function(){return!!this.statusNodeType},isPagingNode:function(){return"paging"===this.statusNodeType},isTopLevel:function(){return this.tree.rootNode===this.parent},isUndefined:function(){return void 0===this.hasChildren()},isVisible:function(){var e,t,n=this.tree.enableFilter,i=this.getParentList(!1,!1);if(n&&!this.match&&!this.subMatchCount)return!1;for(e=0,t=i.length;e<t;e++)if(!i[e].expanded)return!1;return!0},lazyLoad:function(e){k.error("FancytreeNode.lazyLoad() is deprecated since 2014-02-16. Use .load() instead.")},load:function(e){var t,n,i=this,r=this.isExpanded();return C(this.isLazy(),"load() requires a lazy node"),e||this.isUndefined()?(this.isLoaded()&&this.resetLazy(),!1===(n=this.tree._triggerNodeEvent("lazyLoad",this))?S(this):(C("boolean"!=typeof n,"lazyLoad event must return source in data.result"),t=this.tree._callHook("nodeLoadChildren",this,n),r?(this.expanded=!0,t.always(function(){i.render()})):t.always(function(){i.renderStatus()}),t)):S(this)},makeVisible:function(e){var t,n=this,i=[],r=new k.Deferred,o=this.getParentList(!1,!1),s=o.length,a=!(e&&!0===e.noAnimation),l=!(e&&!1===e.scrollIntoView);for(t=s-1;0<=t;t--)i.push(o[t].setExpanded(!0,e));return k.when.apply(k,i).done(function(){l?n.scrollIntoView(a).done(function(){r.resolve()}):r.resolve()}),r.promise()},moveTo:function(t,e,n){void 0===e||"over"===e?e="child":"firstChild"===e&&(t.children&&t.children.length?(e="before",t=t.children[0]):e="child");var i,r=this.tree,o=this.parent,s="child"===e?t:t.parent;if(this!==t){if(this.parent?s.isDescendantOf(this)&&k.error("Cannot move a node to its own descendant"):k.error("Cannot move system root"),s!==o&&o.triggerModifyChild("remove",this),1===this.parent.children.length){if(this.parent===s)return;this.parent.children=this.parent.lazy?[]:null,this.parent.expanded=!1}else C(0<=(i=k.inArray(this,this.parent.children)),"invalid source parent"),this.parent.children.splice(i,1);if((this.parent=s).hasChildren())switch(e){case"child":s.children.push(this);break;case"before":C(0<=(i=k.inArray(t,s.children)),"invalid target parent"),s.children.splice(i,0,this);break;case"after":C(0<=(i=k.inArray(t,s.children)),"invalid target parent"),s.children.splice(i+1,0,this);break;default:k.error("Invalid mode "+e)}else s.children=[this];n&&t.visit(n,!0),s===o?s.triggerModifyChild("move",this):s.triggerModifyChild("add",this),r!==t.tree&&(this.warn("Cross-tree moveTo is experimental!"),this.visit(function(e){e.tree=t.tree},!0)),r._callHook("treeStructureChanged",r,"moveTo"),o.isDescendantOf(s)||o.render(),s.isDescendantOf(o)||s===o||s.render()}},navigate:function(e,t){var n,i=k.ui.keyCode;switch(e){case"left":case i.LEFT:if(this.expanded)return this.setExpanded(!1);break;case"right":case i.RIGHT:if(!this.expanded&&(this.children||this.lazy))return this.setExpanded()}if(n=this.findRelatedNode(e)){try{n.makeVisible({scrollIntoView:!1})}catch(e){}return!1===t?(n.setFocus(),S()):n.setActive()}return this.warn("Could not find related node '"+e+"'."),S()},remove:function(){return this.parent.removeChild(this)},removeChild:function(e){return this.tree._callHook("nodeRemoveChild",this,e)},removeChildren:function(){return this.tree._callHook("nodeRemoveChildren",this)},removeClass:function(e){return this.toggleClass(e,!1)},render:function(e,t){return this.tree._callHook("nodeRender",this,e,t)},renderTitle:function(){return this.tree._callHook("nodeRenderTitle",this)},renderStatus:function(){return this.tree._callHook("nodeRenderStatus",this)},replaceWith:function(e){var t,n=this.parent,i=k.inArray(this,n.children),r=this;return C(this.isPagingNode(),"replaceWith() currently requires a paging status node"),(t=this.tree._callHook("nodeLoadChildren",this,e)).done(function(e){var t=r.children;for(o=0;o<t.length;o++)t[o].parent=n;n.children.splice.apply(n.children,[i+1,0].concat(t)),r.children=null,r.remove(),n.render()}).fail(function(){r.setExpanded()}),t},resetLazy:function(){this.removeChildren(),this.expanded=!1,this.lazy=!0,this.children=void 0,this.renderStatus()},scheduleAction:function(e,t){this.tree.timer&&(clearTimeout(this.tree.timer),this.tree.debug("clearTimeout(%o)",this.tree.timer)),this.tree.timer=null;var n=this;switch(e){case"cancel":break;case"expand":this.tree.timer=setTimeout(function(){n.tree.debug("setTimeout: trigger expand"),n.setExpanded(!0)},t);break;case"activate":this.tree.timer=setTimeout(function(){n.tree.debug("setTimeout: trigger activate"),n.setActive(!0)},t);break;default:k.error("Invalid mode "+e)}},scrollIntoView:function(e,t){if(void 0!==t&&((n=t).tree&&void 0!==n.statusNodeType))throw Error("scrollIntoView() with 'topNode' option is deprecated since 2014-05-08. Use 'options.topNode' instead.");var n,i=k.extend({effects:!0===e?{duration:200,queue:!1}:e,scrollOfs:this.tree.options.scrollOfs,scrollParent:this.tree.options.scrollParent,topNode:null},t),r=i.scrollParent,o=this.tree.$container,s=o.css("overflow-y");r?r.jquery||(r=k(r)):r=this.tree.tbody?o.scrollParent():"scroll"===s||"auto"===s?o:o.scrollParent(),r[0]!==document&&r[0]!==document.body||(this.debug("scrollIntoView(): normalizing scrollParent to 'window':",r[0]),r=k(window));var a,l,d,c=new k.Deferred,u=this,h=k(this.span).height(),f=i.scrollOfs.top||0,p=i.scrollOfs.bottom||0,g=r.height(),v=r.scrollTop(),y=r,m=r[0]===window,b=i.topNode||null,x=null;return this.isRootNode()||!this.isVisible()?(this.info("scrollIntoView(): node is invisible."),S()):(m?(l=k(this.span).offset().top,a=b&&b.span?k(b.span).offset().top:0,y=k("html,body")):(C(r[0]!==document&&r[0]!==document.body,"scrollParent should be a simple element or `window`, not document or body."),d=r.offset().top,l=k(this.span).offset().top-d+v,a=b?k(b.span).offset().top-d+v:0,g-=Math.max(0,r.innerHeight()-r[0].clientHeight)),l<v+f?x=l-f:v+g-p<l+h&&(x=l+h-g+p,b&&(C(b.isRootNode()||b.isVisible(),"topNode must be visible"),a<x&&(x=a-f))),null===x?c.resolveWith(this):i.effects?(i.effects.complete=function(){c.resolveWith(u)},y.stop(!0).animate({scrollTop:x},i.effects)):(y[0].scrollTop=x,c.resolveWith(this)),c.promise())},setActive:function(e,t){return this.tree._callHook("nodeSetActive",this,e,t)},setExpanded:function(e,t){return this.tree._callHook("nodeSetExpanded",this,e,t)},setFocus:function(e){return this.tree._callHook("nodeSetFocus",this,e)},setSelected:function(e,t){return this.tree._callHook("nodeSetSelected",this,e,t)},setStatus:function(e,t,n){return this.tree._callHook("nodeSetStatus",this,e,t,n)},setTitle:function(e){this.title=e,this.renderTitle(),this.triggerModify("rename")},sortChildren:function(e,t){var n,i,r=this.children;if(r){if(e=e||function(e,t){var n=e.title.toLowerCase(),i=t.title.toLowerCase();return n===i?0:i<n?1:-1},r.sort(e),t)for(n=0,i=r.length;n<i;n++)r[n].children&&r[n].sortChildren(e,"$norender$");"$norender$"!==t&&this.render(),this.triggerModifyChild("sort")}},toDict:function(e,t){var n,i,r,o,s={},a=this;if(k.each(N,function(e,t){(a[t]||!1===a[t])&&(s[t]=a[t])}),k.isEmptyObject(this.data)||(s.data=k.extend({},this.data),k.isEmptyObject(s.data)&&delete s.data),t){if(!1===(o=t(s,a)))return!1;"skip"===o&&(e=!1)}if(e&&k.isArray(this.children))for(s.children=[],n=0,i=this.children.length;n<i;n++)(r=this.children[n]).isStatusNode()||!1!==(o=r.toDict(!0,t))&&s.children.push(o);return s},toggleClass:function(e,t){var n,i,r=e.match(/\S+/g)||[],o=0,s=!1,a=this[this.tree.statusClassPropName],l=" "+(this.extraClasses||"")+" ";for(a&&k(a).toggleClass(e,t);n=r[o++];)if(i=0<=l.indexOf(" "+n+" "),t=void 0===t?!i:!!t)i||(l+=n+" ",s=!0);else for(;-1<l.indexOf(" "+n+" ");)l=l.replace(" "+n+" "," ");return this.extraClasses=k.trim(l),s},toggleExpanded:function(){return this.tree._callHook("nodeToggleExpanded",this)},toggleSelected:function(){return this.tree._callHook("nodeToggleSelected",this)},toString:function(){return"FancytreeNode@"+this.key+"[title='"+this.title+"']"},triggerModifyChild:function(e,t,n){var i,r=this.tree.options.modifyChild;r&&(t&&t.parent!==this&&k.error("childNode "+t+" is not a child of "+this),i={node:this,tree:this.tree,operation:e,childNode:t||null},n&&k.extend(i,n),r({type:"modifyChild"},i))},triggerModify:function(e,t){this.parent.triggerModifyChild(e,this,t)},visit:function(e,t){var n,i,r=!0,o=this.children;if(!0===t&&(!1===(r=e(this))||"skip"===r))return r;if(o)for(n=0,i=o.length;n<i&&!1!==(r=o[n].visit(e,!0));n++);return r},visitAndLoad:function(n,e,t){var i,r,o,s=this;return!n||!0!==e||!1!==(r=n(s))&&"skip"!==r?s.children||s.lazy?(i=new k.Deferred,o=[],s.load().done(function(){for(var e=0,t=s.children.length;e<t;e++){if(!1===(r=s.children[e].visitAndLoad(n,!0,!0))){i.reject();break}"skip"!==r&&o.push(r)}k.when.apply(this,o).then(function(){i.resolve()})}),i.promise()):S():t?r:S()},visitParents:function(e,t){if(t&&!1===e(this))return!1;for(var n=this.parent;n;){if(!1===e(n))return!1;n=n.parent}return!0},visitSiblings:function(e,t){var n,i,r,o=this.parent.children;for(n=0,i=o.length;n<i;n++)if(r=o[n],(t||r!==this)&&!1===e(r))return!1;return!0},warn:function(e){2<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("warn",arguments))}},F.prototype={_makeHookContext:function(e,t,n){var i,r;return void 0!==e.node?(t&&e.originalEvent!==t&&k.error("invalid args"),i=e):e.tree?i={node:e,tree:r=e.tree,widget:r.widget,options:r.widget.options,originalEvent:t,typeInfo:r.types[e.type]||{}}:e.widget?i={node:null,tree:e,widget:e.widget,options:e.widget.options,originalEvent:t}:k.error("invalid args"),n&&k.extend(i,n),i},_callHook:function(e,t,n){var i=this._makeHookContext(t),r=this[e],o=Array.prototype.slice.call(arguments,2);return k.isFunction(r)||k.error("_callHook('"+e+"') is not a function"),o.unshift(i),r.apply(this,o)},_setExpiringValue:function(e,t,n){this._tempCache[e]={value:t,expire:Date.now()+(+n||50)}},_getExpiringValue:function(e){var t=this._tempCache[e];return t&&t.expire>Date.now()?t.value:(delete this._tempCache[e],null)},_usesExtension:function(e){return 0<=k.inArray(e,this.options.extensions)},_requireExtension:function(e,t,n,i){null!=n&&(n=!!n);var r=this._local.name,o=this.options.extensions,s=k.inArray(e,o)<k.inArray(r,o),a=t&&null==this.ext[e],l=!a&&null!=n&&n!==s;return C(r&&r!==e,"invalid or same name '"+r+"' (require yourself?)"),!a&&!l||(i||(a||t?(i="'"+r+"' extension requires '"+e+"'",l&&(i+=" to be registered "+(n?"before":"after")+" itself")):i="If used together, `"+e+"` must be registered "+(n?"before":"after")+" `"+r+"`"),k.error(i),!1)},activateKey:function(e,t){var n=this.getNodeByKey(e);return n?n.setActive(!0,t):this.activeNode&&this.activeNode.setActive(!1,t),n},addPagingNode:function(e,t){return this.rootNode.addPagingNode(e,t)},applyCommand:function(e,t,n){var i;switch(t=t||this.getActiveNode(),e){case"moveUp":(i=t.getPrevSibling())&&(t.moveTo(i,"before"),t.setActive());break;case"moveDown":(i=t.getNextSibling())&&(t.moveTo(i,"after"),t.setActive());break;case"indent":(i=t.getPrevSibling())&&(t.moveTo(i,"child"),i.setExpanded(),t.setActive());break;case"outdent":t.isTopLevel()||(t.moveTo(t.getParent(),"after"),t.setActive());break;case"remove":i=t.getPrevSibling()||t.getParent(),t.remove(),i&&i.setActive();break;case"addChild":t.editCreateNode("child","");break;case"addSibling":t.editCreateNode("after","");break;case"rename":t.editStart();break;case"down":case"first":case"last":case"left":case"parent":case"right":case"up":return t.navigate(e);default:k.error("Unhandled command: '"+e+"'")}},applyPatch:function(e){var t,n,i,r,o,s,a=e.length,l=[];for(n=0;n<a;n++)C(2===(i=e[n]).length,"patchList must be an array of length-2-arrays"),r=i[0],o=i[1],(s=null===r?this.rootNode:this.getNodeByKey(r))?(t=new k.Deferred,l.push(t),s.applyPatch(o).always(T(t,s))):this.warn("could not find node with key '"+r+"'");return k.when.apply(k,l).promise()},clear:function(e){this._callHook("treeClear",this)},count:function(){return this.rootNode.countChildren()},debug:function(e){4<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("log",arguments))},destroy:function(){this.widget.destroy()},enable:function(e){!1===e?this.widget.disable():this.widget.enable()},enableUpdate:function(e){return e=!1!==e,!!this._enableUpdate==!!e?e:((this._enableUpdate=e)?(this.debug("enableUpdate(true): redraw "),this._callHook("treeStructureChanged",this,"enableUpdate"),this.render()):this.debug("enableUpdate(false)..."),!e)},error:function(e){1<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("error",arguments))},expandAll:function(t,n){var e=this.enableUpdate(!1);t=!1!==t,this.visit(function(e){!1!==e.hasChildren()&&e.isExpanded()!==t&&e.setExpanded(t,n)}),this.enableUpdate(e)},findAll:function(e){return this.rootNode.findAll(e)},findFirst:function(e){return this.rootNode.findFirst(e)},findNextNode:function(t,n){var i,r=null,e=this.getFirstChild();function o(e){if(t(e)&&(r=e),r||e===n)return!1}return t="string"==typeof t?(i=new RegExp("^"+t,"i"),function(e){return i.test(e.title)}):t,n=n||e,this.visitRows(o,{start:n,includeSelf:!1}),r||n===e||this.visitRows(o,{start:e,includeSelf:!0}),r},findRelatedNode:function(e,t,n){var i=null,r=k.ui.keyCode;switch(t){case"parent":case r.BACKSPACE:e.parent&&e.parent.parent&&(i=e.parent);break;case"first":case r.HOME:this.visit(function(e){if(e.isVisible())return i=e,!1});break;case"last":case r.END:this.visit(function(e){e.isVisible()&&(i=e)});break;case"left":case r.LEFT:e.expanded?e.setExpanded(!1):e.parent&&e.parent.parent&&(i=e.parent);break;case"right":case r.RIGHT:e.expanded||!e.children&&!e.lazy?e.children&&e.children.length&&(i=e.children[0]):(e.setExpanded(),i=e);break;case"up":case r.UP:this.visitRows(function(e){return i=e,!1},{start:e,reverse:!0,includeSelf:!1});break;case"down":case r.DOWN:this.visitRows(function(e){return i=e,!1},{start:e,includeSelf:!1});break;default:this.tree.warn("Unknown relation '"+t+"'.")}return i},generateFormElements:function(e,t,n){n=n||{};var i,r="string"==typeof e?e:"ft_"+this._id+"[]",o="string"==typeof t?t:"ft_"+this._id+"_active",s="fancytree_result_"+this._id,a=k("#"+s),l=3===this.options.selectMode&&!1!==n.stopOnParents;function d(e){a.append(k("<input>",{type:"checkbox",name:r,value:e.key,checked:!0}))}a.length?a.empty():a=k("<div>",{id:s}).hide().insertAfter(this.$container),!1!==t&&this.activeNode&&a.append(k("<input>",{type:"radio",name:o,value:this.activeNode.key,checked:!0})),n.filter?this.visit(function(e){var t=n.filter(e);if("skip"===t)return t;!1!==t&&d(e)}):!1!==e&&(i=this.getSelectedNodes(l),k.each(i,function(e,t){d(t)}))},getActiveNode:function(){return this.activeNode},getFirstChild:function(){return this.rootNode.getFirstChild()},getFocusNode:function(){return this.focusNode},getOption:function(e){return this.widget.option(e)},getNodeByKey:function(t,e){var n,i;return!e&&(n=document.getElementById(this.options.idPrefix+t))?n.ftnode?n.ftnode:null:(e=e||this.rootNode,i=null,e.visit(function(e){if(e.key===t)return i=e,!1},!0),i)},getRootNode:function(){return this.rootNode},getSelectedNodes:function(e){return this.rootNode.getSelectedNodes(e)},hasFocus:function(){return!!this._hasFocus},info:function(e){3<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("info",arguments))},isLoading:function(){var t=!1;return this.rootNode.visit(function(e){if(e._isLoading||e._requestId)return!(t=!0)},!0),t},loadKeyPath:function(e,t){var i,n,r,o=this,s=new k.Deferred,a=this.getRootNode(),l=this.options.keyPathSeparator,d=[],c=k.extend({},t);for("function"==typeof t?i=t:t&&t.callback&&(i=t.callback),c.callback=function(e,t,n){i&&i.call(e,t,n),s.notifyWith(e,[{node:t,status:n}])},null==c.matchKey&&(c.matchKey=function(e,t){return e.key===t}),k.isArray(e)||(e=[e]),n=0;n<e.length;n++)(r=e[n]).charAt(0)===l&&(r=r.substr(1)),d.push(r.split(l));return setTimeout(function(){o._loadKeyPathImpl(s,c,a,d).done(function(){s.resolve()})},0),s.promise()},_loadKeyPathImpl:function(e,o,t,n){var i,r,s,a,l,d,c,u,h,f,p=this;function g(e,t){var n,i,r=e.children;if(r)for(n=0,i=r.length;n<i;n++)if(o.matchKey(r[n],t))return r[n];return null}for(c={},r=0;r<n.length;r++)for(h=n[r],u=t;h.length;){if(s=h.shift(),!(a=g(u,s))){this.warn("loadKeyPath: key not found: "+s+" (parent: "+u+")"),o.callback(this,s,"error");break}if(0===h.length){o.callback(this,a,"ok");break}if(a.lazy&&void 0===a.hasChildren()){o.callback(this,a,"loaded"),c[s=a.key]?c[s].pathSegList.push(h):c[s]={parent:a,pathSegList:[h]};break}o.callback(this,a,"loaded"),u=a}function v(t,n,e){o.callback(p,n,"loading"),n.load().done(function(){p._loadKeyPathImpl.call(p,t,o,n,e).always(T(t,p))}).fail(function(e){p.warn("loadKeyPath: error loading lazy "+n),o.callback(p,a,"error"),t.rejectWith(p)})}for(l in i=[],c)c.hasOwnProperty(l)&&(d=c[l],f=new k.Deferred,i.push(f),v(f,d.parent,d.pathSegList));return k.when.apply(k,i).promise()},reactivate:function(e){var t,n=this.activeNode;return n?(this.activeNode=null,t=n.setActive(!0,{noFocus:!0}),e&&n.setFocus(),t):S()},reload:function(e){return this._callHook("treeClear",this),this._callHook("treeLoad",this,e)},render:function(e,t){return this.rootNode.render(e,t)},selectAll:function(t){this.visit(function(e){e.setSelected(t)})},setFocus:function(e){return this._callHook("treeSetFocus",this,e)},setOption:function(e,t){return this.widget.option(e,t)},debugTime:function(e){4<=this.options.debugLevel&&window.console.time(this+" - "+e)},debugTimeEnd:function(e){4<=this.options.debugLevel&&window.console.timeEnd(this+" - "+e)},toDict:function(e,t){var n=this.rootNode.toDict(!0,t);return e?n:n.children},toString:function(){return"Fancytree@"+this._id},_triggerNodeEvent:function(e,t,n,i){var r=this._makeHookContext(t,n,i),o=this.widget._trigger(e,n,r);return!1!==o&&void 0!==r.result?r.result:o},_triggerTreeEvent:function(e,t,n){var i=this._makeHookContext(this,t,n),r=this.widget._trigger(e,t,i);return!1!==r&&void 0!==i.result?i.result:r},visit:function(e){return this.rootNode.visit(e,!1)},visitRows:function(t,e){if(!this.rootNode.hasChildren())return!1;if(e&&e.reverse)return delete e.reverse,this._visitRowsUp(t,e);var n,i,r,o=0,s=!1===(e=e||{}).includeSelf,a=!!e.includeHidden,l=!a&&this.enableFilter,d=e.start||this.rootNode.children[0];for(i=d.parent;i;){for(n=(r=i.children).indexOf(d)+o;n<r.length;n++)if(d=r[n],!l||d.match||d.subMatchCount){if(!s&&!1===t(d))return!1;if(s=!1,d.children&&d.children.length&&(a||d.expanded)&&!1===d.visit(function(e){return!l||e.match||e.subMatchCount?!1!==t(e)&&(a||!e.children||e.expanded?void 0:"skip"):"skip"},!1))return!1}i=(d=i).parent,o=1}return!0},_visitRowsUp:function(e,t){for(var n,i,r,o=!!t.includeHidden,s=t.start||this.rootNode.children[0];;){if((n=(r=s.parent).children)[0]===s){if(!(s=r).parent)break;n=r.children}else for(i=n.indexOf(s),s=n[i-1];(o||s.expanded)&&s.children&&s.children.length;)s=(n=(r=s).children)[n.length-1];if((o||s.isVisible())&&!1===e(s))return!1}},warn:function(e){2<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("warn",arguments))}},k.extend(F.prototype,{nodeClick:function(e){var t,n,i=e.targetType,r=e.node;if("expander"===i){if(r.isLoading())return void r.debug("Got 2nd click while loading: ignored");this._callHook("nodeToggleExpanded",e)}else if("checkbox"===i)this._callHook("nodeToggleSelected",e),e.options.focusOnSelect&&this._callHook("nodeSetFocus",e,!0);else{if(t=!(n=!1),r.folder)switch(e.options.clickFolderMode){case 2:t=!(n=!0);break;case 3:n=t=!0}t&&(this.nodeSetFocus(e),this._callHook("nodeSetActive",e,!0)),n&&this._callHook("nodeToggleExpanded",e)}},nodeCollapseSiblings:function(e,t){var n,i,r,o=e.node;if(o.parent)for(i=0,r=(n=o.parent.children).length;i<r;i++)n[i]!==o&&n[i].expanded&&this._callHook("nodeSetExpanded",n[i],!1,t)},nodeDblclick:function(e){"title"===e.targetType&&4===e.options.clickFolderMode&&this._callHook("nodeToggleExpanded",e),"title"===e.targetType&&e.originalEvent.preventDefault()},nodeKeydown:function(e){var t,n,i,r=e.originalEvent,o=e.node,s=e.tree,a=e.options,l=r.which,d=r.key||String.fromCharCode(l),c=!!(r.altKey||r.ctrlKey||r.metaKey),u=!m[l]&&!y[l]&&!c,h=k(r.target),f=!0,p=!(r.ctrlKey||!a.autoActivate);if(o||(i=this.getActiveNode()||this.getFirstChild())&&(i.setFocus(),(o=e.node=this.focusNode).debug("Keydown force focus on active node")),a.quicksearch&&u&&!h.is(":input:enabled"))return 500<(n=Date.now())-s.lastQuicksearchTime&&(s.lastQuicksearchTerm=""),s.lastQuicksearchTime=n,s.lastQuicksearchTerm+=d,(t=s.findNextNode(s.lastQuicksearchTerm,s.getActiveNode()))&&t.setActive(),void r.preventDefault();switch(g.eventToString(r)){case"+":case"=":s.nodeSetExpanded(e,!0);break;case"-":s.nodeSetExpanded(e,!1);break;case"space":o.isPagingNode()?s._triggerNodeEvent("clickPaging",e,r):g.evalOption("checkbox",o,o,a,!1)?s.nodeToggleSelected(e):s.nodeSetActive(e,!0);break;case"return":s.nodeSetActive(e,!0);break;case"home":case"end":case"backspace":case"left":case"right":case"up":case"down":o.navigate(r.which,p);break;default:f=!1}f&&r.preventDefault()},nodeLoadChildren:function(o,s){var t,n,a,e=null,i=!0,l=o.tree,d=o.node,c=d.parent,r="nodeLoadChildren",u=Date.now();return k.isFunction(s)&&(s=s.call(l,{type:"source"},o),C(!k.isFunction(s),"source callback must not return another function")),k.isFunction(s.then)?e=s:s.url?e=(t=k.extend({},o.options.ajax,s)).debugDelay?(n=t.debugDelay,delete t.debugDelay,k.isArray(n)&&(n=n[0]+Math.random()*(n[1]-n[0])),d.warn("nodeLoadChildren waiting debugDelay "+Math.round(n)+" ms ..."),k.Deferred(function(e){setTimeout(function(){k.ajax(t).done(function(){e.resolveWith(this,arguments)}).fail(function(){e.rejectWith(this,arguments)})},n)})):k.ajax(t):k.isPlainObject(s)||k.isArray(s)?i=!(e={then:function(e,t){e(s,null,null)}}):k.error("Invalid source type: "+s),d._requestId&&(d.warn("Recursive load request #"+u+" while #"+d._requestId+" is pending."),d._requestId=u),i&&(l.debugTime(r),l.nodeSetStatus(o,"loading")),a=new k.Deferred,e.then(function(e,t,n){var i,r;if("json"!==s.dataType&&"jsonp"!==s.dataType||"string"!=typeof e||k.error("Ajax request returned a string (did you get the JSON dataType wrong?)."),d._requestId&&d._requestId>u)a.rejectWith(this,[h]);else if(null!==d.parent||null===c){if(o.options.postProcess){try{r=l._triggerNodeEvent("postProcess",o,o.originalEvent,{response:e,error:null,dataType:s.dataType})}catch(e){r={error:e,message:""+e,details:"postProcess failed"}}if(r.error)return i=k.isPlainObject(r.error)?r.error:{message:r.error},i=l._makeHookContext(d,null,i),void a.rejectWith(this,[i]);(k.isArray(r)||k.isPlainObject(r)&&k.isArray(r.children))&&(e=r)}else e&&e.hasOwnProperty("d")&&o.options.enableAspx&&(42===o.options.enableAspx&&l.warn("The default for enableAspx will change to `false` in the fututure. Pass `enableAspx: true` or implement postProcess to silence this warning."),e="string"==typeof e.d?k.parseJSON(e.d):e.d);a.resolveWith(this,[e])}else a.rejectWith(this,[f])},function(e,t,n){var i=l._makeHookContext(d,null,{error:e,args:Array.prototype.slice.call(arguments),message:n,details:e.status+": "+n});a.rejectWith(this,[i])}),a.done(function(e){var t,n,i;l.nodeSetStatus(o,"ok"),k.isPlainObject(e)?(C(d.isRootNode(),"source may only be an object for root nodes (expecting an array of child objects otherwise)"),C(k.isArray(e.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),t=(n=e).children,delete n.children,k.each(p,function(e,t){void 0!==n[t]&&(l[t]=n[t],delete n[t])}),k.extend(l.data,n)):t=e,C(k.isArray(t),"expected array of children"),d._setChildren(t),l.options.nodata&&0===t.length&&(k.isFunction(l.options.nodata)?i=l.options.nodata.call(l,{type:"nodata"},o):!0===l.options.nodata&&d.isRootNode()?i=l.options.strings.noData:"string"==typeof l.options.nodata&&d.isRootNode()&&(i=l.options.nodata),i&&d.setStatus("nodata",i)),l._triggerNodeEvent("loadChildren",d)}).fail(function(e){var t;e!==h?e!==f?(e.node&&e.error&&e.message?t=e:"[object Object]"===(t=l._makeHookContext(d,null,{error:e,args:Array.prototype.slice.call(arguments),message:e?e.message||e.toString():""})).message&&(t.message=""),d.warn("Load children failed ("+t.message+")",t),!1!==l._triggerNodeEvent("loadError",t,null)&&l.nodeSetStatus(o,"error",t.message,t.details)):d.warn("Lazy parent node was removed while loading: discarding response."):d.warn("Ignored response for obsolete load request #"+u+" (expected #"+d._requestId+")")}).always(function(){d._requestId=null,i&&l.debugTimeEnd(r)}),a.promise()},nodeLoadKeyPath:function(e,t){},nodeRemoveChild:function(e,t){var n,i=e.node,r=k.extend({},e,{node:t}),o=i.children;if(1===o.length)return C(t===o[0],"invalid single child"),this.nodeRemoveChildren(e);this.activeNode&&(t===this.activeNode||this.activeNode.isDescendantOf(t))&&this.activeNode.setActive(!1),this.focusNode&&(t===this.focusNode||this.focusNode.isDescendantOf(t))&&(this.focusNode=null),this.nodeRemoveMarkup(r),this.nodeRemoveChildren(r),C(0<=(n=k.inArray(t,o)),"invalid child"),i.triggerModifyChild("remove",t),t.visit(function(e){e.parent=null},!0),this._callHook("treeRegisterNode",this,!1,t),o.splice(n,1)},nodeRemoveChildMarkup:function(e){var t=e.node;t.ul&&(t.isRootNode()?k(t.ul).empty():(k(t.ul).remove(),t.ul=null),t.visit(function(e){e.li=e.ul=null}))},nodeRemoveChildren:function(e){var t=e.tree,n=e.node;n.children&&(this.activeNode&&this.activeNode.isDescendantOf(n)&&this.activeNode.setActive(!1),this.focusNode&&this.focusNode.isDescendantOf(n)&&(this.focusNode=null),this.nodeRemoveChildMarkup(e),n.triggerModifyChild("remove",null),n.visit(function(e){e.parent=null,t._callHook("treeRegisterNode",t,!1,e)}),n.lazy?n.children=[]:n.children=null,n.isRootNode()||(n.expanded=!1),this.nodeRenderStatus(e))},nodeRemoveMarkup:function(e){var t=e.node;t.li&&(k(t.li).remove(),t.li=null),this.nodeRemoveChildMarkup(e)},nodeRender:function(e,t,n,i,r){var o,s,a,l,d,c,u,h=e.node,f=e.tree,p=e.options,g=p.aria,v=!1,y=h.parent,m=!y,b=h.children,x=null;if(!1!==f._enableUpdate&&(m||y.ul)){if(C(m||y.ul,"parent UL must exist"),m||(h.li&&(t||h.li.parentNode!==h.parent.ul)&&(h.li.parentNode===h.parent.ul?x=h.li.nextSibling:this.debug("Unlinking "+h+" (must be child of "+h.parent+")"),this.nodeRemoveMarkup(e)),h.li?this.nodeRenderStatus(e):(v=!0,h.li=document.createElement("li"),(h.li.ftnode=h).key&&p.generateIds&&(h.li.id=p.idPrefix+h.key),h.span=document.createElement("span"),h.span.className="fancytree-node",g&&!h.tr&&k(h.li).attr("role","treeitem"),h.li.appendChild(h.span),this.nodeRenderTitle(e),p.createNode&&p.createNode.call(f,{type:"createNode"},e)),p.renderNode&&p.renderNode.call(f,{type:"renderNode"},e)),b){if(m||h.expanded||!0===n){for(h.ul||(h.ul=document.createElement("ul"),(!0!==i||r)&&h.expanded||(h.ul.style.display="none"),g&&k(h.ul).attr("role","group"),h.li?h.li.appendChild(h.ul):h.tree.$div.append(h.ul)),l=0,d=b.length;l<d;l++)u=k.extend({},e,{node:b[l]}),this.nodeRender(u,t,n,!1,!0);for(o=h.ul.firstChild;o;)o=(a=o.ftnode)&&a.parent!==h?(h.debug("_fixParent: remove missing "+a,o),c=o.nextSibling,o.parentNode.removeChild(o),c):o.nextSibling;for(o=h.ul.firstChild,l=0,d=b.length-1;l<d;l++)(s=b[l])===(a=o.ftnode)?o=o.nextSibling:h.ul.insertBefore(s.li,a.li)}}else h.ul&&(this.warn("remove child markup for "+h),this.nodeRemoveChildMarkup(e));m||v&&y.ul.insertBefore(h.li,x)}},nodeRenderTitle:function(e,t){var n,i,r,o,s,a,l,d=e.node,c=e.tree,u=e.options,h=u.aria,f=d.getLevel(),p=[];void 0!==t&&(d.title=t),d.span&&!1!==c._enableUpdate&&(s=h&&!1!==d.hasChildren()?" role='button'":"",f<u.minExpandLevel?(d.lazy||(d.expanded=!0),1<f&&p.push("<span "+s+" class='fancytree-expander fancytree-expander-fixed'></span>")):p.push("<span "+s+" class='fancytree-expander'></span>"),(n=g.evalOption("checkbox",d,d,u,!1))&&!d.isStatusNode()&&(s=h?" role='checkbox'":"",i="fancytree-checkbox",("radio"===n||d.parent&&d.parent.radiogroup)&&(i+=" fancytree-radio"),p.push("<span "+s+" class='"+i+"'></span>")),void 0!==d.data.iconClass&&(d.icon?k.error("'iconClass' node option is deprecated since v2.14.0: use 'icon' only instead"):(d.warn("'iconClass' node option is deprecated since v2.14.0: use 'icon' instead"),d.icon=d.data.iconClass)),!1!==(r=g.evalOption("icon",d,d,u,!0))&&(s=h?" role='presentation'":"",l=(l=g.evalOption("iconTooltip",d,d,u,null))?" title='"+A(l)+"'":"","string"==typeof r?v.test(r)?(r="/"===r.charAt(0)?r:(u.imagePath||"")+r,p.push("<img src='"+r+"' class='fancytree-icon'"+l+" alt='' />")):p.push("<span "+s+" class='fancytree-custom-icon "+r+"'"+l+"></span>"):r.text?p.push("<span "+s+" class='fancytree-custom-icon "+(r.addClass||"")+"'"+l+">"+g.escapeHtml(r.text)+"</span>"):r.html?p.push("<span "+s+" class='fancytree-custom-icon "+(r.addClass||"")+"'"+l+">"+r.html+"</span>"):p.push("<span "+s+" class='fancytree-icon'"+l+"></span>")),o="",u.renderTitle&&(o=u.renderTitle.call(c,{type:"renderTitle"},e)||""),o||(!0===(a=g.evalOption("tooltip",d,d,u,null))&&(a=d.title),o="<span class='fancytree-title'"+(a=a?" title='"+A(a)+"'":"")+(u.titlesTabbable?" tabindex='0'":"")+">"+(u.escapeTitles?g.escapeHtml(d.title):d.title)+"</span>"),p.push(o),d.span.innerHTML=p.join(""),this.nodeRenderStatus(e),u.enhanceTitle&&(e.$title=k(">span.fancytree-title",d.span),o=u.enhanceTitle.call(c,{type:"enhanceTitle"},e)||""))},nodeRenderStatus:function(e){var t,n=e.node,i=e.tree,r=e.options,o=n.hasChildren(),s=n.isLastSibling(),a=r.aria,l=r._classNames,d=[],c=n[i.statusClassPropName];c&&!1!==i._enableUpdate&&(a&&(t=k(n.tr||n.li)),d.push(l.node),i.activeNode===n&&d.push(l.active),i.focusNode===n&&d.push(l.focused),n.expanded&&d.push(l.expanded),a&&(!1===o?t.removeAttr("aria-expanded"):t.attr("aria-expanded",Boolean(n.expanded))),n.folder&&d.push(l.folder),!1!==o&&d.push(l.hasChildren),s&&d.push(l.lastsib),n.lazy&&null==n.children&&d.push(l.lazy),n.partload&&d.push(l.partload),n.partsel&&d.push(l.partsel),g.evalOption("unselectable",n,n,r,!1)&&d.push(l.unselectable),n._isLoading&&d.push(l.loading),n._error&&d.push(l.error),n.statusNodeType&&d.push(l.statusNodePrefix+n.statusNodeType),n.selected?(d.push(l.selected),a&&t.attr("aria-selected",!0)):a&&t.attr("aria-selected",!1),n.extraClasses&&d.push(n.extraClasses),!1===o?d.push(l.combinedExpanderPrefix+"n"+(s?"l":"")):d.push(l.combinedExpanderPrefix+(n.expanded?"e":"c")+(n.lazy&&null==n.children?"d":"")+(s?"l":"")),d.push(l.combinedIconPrefix+(n.expanded?"e":"c")+(n.folder?"f":"")),c.className=d.join(" "),n.li&&k(n.li).toggleClass(l.lastsib,s))},nodeSetActive:function(e,t,n){n=n||{};var i,r=e.node,o=e.tree,s=e.options,a=!0===n.noEvents,l=!0===n.noFocus,d=!1!==n.scrollIntoView;return r===o.activeNode===(t=!1!==t)?S(r):t&&!a&&!1===this._triggerNodeEvent("beforeActivate",r,e.originalEvent)?E(r,["rejected"]):(t?(o.activeNode&&(C(o.activeNode!==r,"node was active (inconsistency)"),i=k.extend({},e,{node:o.activeNode}),o.nodeSetActive(i,!1),C(null===o.activeNode,"deactivate was out of sync?")),s.activeVisible&&r.makeVisible({scrollIntoView:d}),o.activeNode=r,o.nodeRenderStatus(e),l||o.nodeSetFocus(e),a||o._triggerNodeEvent("activate",r,e.originalEvent)):(C(o.activeNode===r,"node was not active (inconsistency)"),o.activeNode=null,this.nodeRenderStatus(e),a||e.tree._triggerNodeEvent("deactivate",r,e.originalEvent)),S(r))},nodeSetExpanded:function(i,r,e){e=e||{};var t,n,o,s,a,l,d=i.node,c=i.tree,u=i.options,h=!0===e.noAnimation,f=!0===e.noEvents;if(r=!1!==r,d.expanded&&r||!d.expanded&&!r)return S(d);if(r&&!d.lazy&&!d.hasChildren())return S(d);if(!r&&d.getLevel()<u.minExpandLevel)return E(d,["locked"]);if(!f&&!1===this._triggerNodeEvent("beforeExpand",d,i.originalEvent))return E(d,["rejected"]);if(h||d.isVisible()||(h=e.noAnimation=!0),n=new k.Deferred,r&&!d.expanded&&u.autoCollapse){a=d.getParentList(!1,!0),l=u.autoCollapse;try{for(u.autoCollapse=!1,o=0,s=a.length;o<s;o++)this._callHook("nodeCollapseSiblings",a[o],e)}finally{u.autoCollapse=l}}return n.done(function(){var e=d.getLastChild();r&&u.autoScroll&&!h&&e&&c._enableUpdate?e.scrollIntoView(!0,{topNode:d}).always(function(){f||i.tree._triggerNodeEvent(r?"expand":"collapse",i)}):f||i.tree._triggerNodeEvent(r?"expand":"collapse",i)}),t=function(e){var t=u._classNames,n=u.toggleEffect;if(d.expanded=r,c._callHook("treeStructureChanged",i,r?"expand":"collapse"),c._callHook("nodeRender",i,!1,!1,!0),d.ul)if("none"!==d.ul.style.display===!!d.expanded)d.warn("nodeSetExpanded: UL.style.display already set");else{if(n&&!h)return k(d.li).addClass(t.animating),void(k.isFunction(k(d.ul)[n.effect])?k(d.ul)[n.effect]({duration:n.duration,always:function(){k(this).removeClass(t.animating),k(d.li).removeClass(t.animating),e()}}):(k(d.ul).stop(!0,!0),k(d.ul).parent().find(".ui-effects-placeholder").remove(),k(d.ul).toggle(n.effect,n.options,n.duration,function(){k(this).removeClass(t.animating),k(d.li).removeClass(t.animating),e()})));d.ul.style.display=d.expanded||!parent?"":"none"}e()},r&&d.lazy&&void 0===d.hasChildren()?d.load().done(function(){n.notifyWith&&n.notifyWith(d,["loaded"]),t(function(){n.resolveWith(d)})}).fail(function(e){t(function(){n.rejectWith(d,["load failed ("+e+")"])})}):t(function(){n.resolveWith(d)}),n.promise()},nodeSetFocus:function(e,t){var n,i=e.tree,r=e.node,o=i.options,s=!!e.originalEvent&&k(e.originalEvent.target).is(":input");if(t=!1!==t,i.focusNode){if(i.focusNode===r&&t)return;n=k.extend({},e,{node:i.focusNode}),i.focusNode=null,this._triggerNodeEvent("blur",n),this._callHook("nodeRenderStatus",n)}t&&(this.hasFocus()||(r.debug("nodeSetFocus: forcing container focus"),this._callHook("treeSetFocus",e,!0,{calledByNode:!0})),r.makeVisible({scrollIntoView:!1}),i.focusNode=r,o.titlesTabbable&&(s||k(r.span).find(".fancytree-title").focus()),o.aria&&k(i.$container).attr("aria-activedescendant",k(r.tr||r.li).uniqueId().attr("id")),this._triggerNodeEvent("focus",e),document.activeElement===i.$container.get(0)||1<=k(document.activeElement,i.$container).length||k(i.$container).focus(),o.autoScroll&&r.scrollIntoView(),this._callHook("nodeRenderStatus",e))},nodeSetSelected:function(e,t,n){n=n||{};var i=e.node,r=e.tree,o=e.options,s=!0===n.noEvents,a=i.parent;if(t=!1!==t,!g.evalOption("unselectable",i,i,o,!1)){if(i._lastSelectIntent=t,!!i.selected===t&&(3!==o.selectMode||!i.partsel||t))return t;if(!s&&!1===this._triggerNodeEvent("beforeSelect",i,e.originalEvent))return!!i.selected;t&&1===o.selectMode?(r.lastSelectedNode&&r.lastSelectedNode.setSelected(!1),i.selected=t):3!==o.selectMode||!a||a.radiogroup||i.radiogroup?a&&a.radiogroup?i.visitSiblings(function(e){e._changeSelectStatusAttrs(t&&e===i)},!0):i.selected=t:(i.selected=t,i.fixSelection3AfterClick(n)),this.nodeRenderStatus(e),r.lastSelectedNode=t?i:null,s||r._triggerNodeEvent("select",e)}},nodeSetStatus:function(i,e,t,n){var r=i.node,o=i.tree;function s(e,t){var n=r.children?r.children[0]:null;return n&&n.isStatusNode()?(k.extend(n,e),n.statusNodeType=t,o._callHook("nodeRenderTitle",n)):(r._setChildren([e]),o._callHook("treeStructureChanged",i,"setStatusNode"),r.children[0].statusNodeType=t,o.render()),r.children[0]}switch(e){case"ok":!function(){var e=r.children?r.children[0]:null;if(e&&e.isStatusNode()){try{r.ul&&(r.ul.removeChild(e.li),e.li=null)}catch(e){}1===r.children.length?r.children=[]:r.children.shift(),o._callHook("treeStructureChanged",i,"clearStatusNode")}}(),r._isLoading=!1,r._error=null,r.renderStatus();break;case"loading":r.parent||s({title:o.options.strings.loading+(t?" ("+t+")":""),checkbox:!1,tooltip:n},e),r._isLoading=!0,r._error=null,r.renderStatus();break;case"error":s({title:o.options.strings.loadError+(t?" ("+t+")":""),checkbox:!1,tooltip:n},e),r._isLoading=!1,r._error={message:t,details:n},r.renderStatus();break;case"nodata":s({title:t||o.options.strings.noData,checkbox:!1,tooltip:n},e),r._isLoading=!1,r._error=null,r.renderStatus();break;default:k.error("invalid node status "+e)}},nodeToggleExpanded:function(e){return this.nodeSetExpanded(e,!e.node.expanded)},nodeToggleSelected:function(e){var t=e.node,n=!t.selected;return t.partsel&&!t.selected&&!0===t._lastSelectIntent&&(n=!1,t.selected=!0),t._lastSelectIntent=n,this.nodeSetSelected(e,n)},treeClear:function(e){var t=e.tree;t.activeNode=null,t.focusNode=null,t.$div.find(">ul.fancytree-container").empty(),t.rootNode.children=null,t._callHook("treeStructureChanged",e,"clear")},treeCreate:function(e){},treeDestroy:function(e){this.$div.find(">ul.fancytree-container").remove(),this.$source&&this.$source.removeClass("fancytree-helper-hidden")},treeInit:function(e){var n=e.tree,i=n.options;n.$container.attr("tabindex",i.tabindex),k.each(p,function(e,t){void 0!==i[t]&&(n.info("Move option "+t+" to tree"),n[t]=i[t],delete i[t])}),i.checkboxAutoHide&&n.$container.addClass("fancytree-checkbox-auto-hide"),i.rtl?n.$container.attr("DIR","RTL").addClass("fancytree-rtl"):n.$container.removeAttr("DIR").removeClass("fancytree-rtl"),i.aria&&(n.$container.attr("role","tree"),1!==i.selectMode&&n.$container.attr("aria-multiselectable",!0)),this.treeLoad(e)},treeLoad:function(e,t){var n,i,r,o=e.tree,s=e.widget.element,a=k.extend({},e,{node:this.rootNode});if(o.rootNode.children&&this.treeClear(e),t=t||this.options.source)"string"==typeof t&&k.error("Not implemented");else switch(i=s.data("type")||"html"){case"html":(r=s.find(">ul").not(".fancytree-container").first()).length?(r.addClass("ui-fancytree-source fancytree-helper-hidden"),t=k.ui.fancytree.parseHtml(r),this.data=k.extend(this.data,L(r))):(g.warn("No `source` option was passed and container does not contain `<ul>`: assuming `source: []`."),t=[]);break;case"json":t=k.parseJSON(s.text()),s.contents().filter(function(){return 3===this.nodeType}).remove(),k.isPlainObject(t)&&(C(k.isArray(t.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),t=(n=t).children,delete n.children,k.each(p,function(e,t){void 0!==n[t]&&(o[t]=n[t],delete n[t])}),k.extend(o.data,n));break;default:k.error("Invalid data-type: "+i)}return o._triggerTreeEvent("preInit",null),this.nodeLoadChildren(a,t).done(function(){o._callHook("treeStructureChanged",e,"loadChildren"),o.render(),3===e.options.selectMode&&o.rootNode.fixSelection3FromEndNodes(),o.activeNode&&o.options.activeVisible&&o.activeNode.makeVisible(),o._triggerTreeEvent("init",null,{status:!0})}).fail(function(){o.render(),o._triggerTreeEvent("init",null,{status:!1})})},treeRegisterNode:function(e,t,n){e.tree._callHook("treeStructureChanged",e,t?"addNode":"removeNode")},treeSetFocus:function(e,t,n){var i;(t=!1!==t)!==this.hasFocus()&&(!(this._hasFocus=t)&&this.focusNode?this.focusNode.setFocus(!1):!t||n&&n.calledByNode||k(this.$container).focus(),this.$container.toggleClass("fancytree-treefocus",t),this._triggerTreeEvent(t?"focusTree":"blurTree"),t&&!this.activeNode&&(i=this._lastMousedownNode||this.getFirstChild())&&i.setFocus())},treeSetOption:function(e,t,n){var i=e.tree,r=!0,o=!1,s=!1;switch(t){case"aria":case"checkbox":case"icon":case"minExpandLevel":case"tabindex":s=o=!0;break;case"checkboxAutoHide":i.$container.toggleClass("fancytree-checkbox-auto-hide",!!n);break;case"escapeTitles":case"tooltip":s=!0;break;case"rtl":!1===n?i.$container.removeAttr("DIR").removeClass("fancytree-rtl"):i.$container.attr("DIR","RTL").addClass("fancytree-rtl"),s=!0;break;case"source":r=!1,i._callHook("treeLoad",i,n),s=!0}i.debug("set option "+t+"="+n+" <"+typeof n+">"),r&&(this.widget._super?this.widget._super.call(this.widget,t,n):k.Widget.prototype._setOption.call(this.widget,t,n)),o&&i._callHook("treeCreate",i),s&&i.render(!0,!1)},treeStructureChanged:function(e,t){}}),k.widget("ui.fancytree",{options:{activeVisible:!0,ajax:{type:"GET",cache:!1,dataType:"json"},aria:!0,autoActivate:!0,autoCollapse:!1,autoScroll:!1,checkbox:!1,clickFolderMode:4,debugLevel:null,disabled:!1,enableAspx:42,escapeTitles:!1,extensions:[],toggleEffect:{effect:"slideToggle",duration:200},generateIds:!1,icon:!0,idPrefix:"ft_",focusOnSelect:!1,keyboard:!0,keyPathSeparator:"/",minExpandLevel:1,nodata:!0,quicksearch:!1,rtl:!1,scrollOfs:{top:0,bottom:0},scrollParent:null,selectMode:2,strings:{loading:"Loading...",loadError:"Load error!",moreData:"More...",noData:"No data."},tabindex:"0",titlesTabbable:!1,tooltip:!1,treeId:null,_classNames:{node:"fancytree-node",folder:"fancytree-folder",animating:"fancytree-animating",combinedExpanderPrefix:"fancytree-exp-",combinedIconPrefix:"fancytree-ico-",hasChildren:"fancytree-has-children",active:"fancytree-active",selected:"fancytree-selected",expanded:"fancytree-expanded",lazy:"fancytree-lazy",focused:"fancytree-focused",partload:"fancytree-partload",partsel:"fancytree-partsel",radio:"fancytree-radio",unselectable:"fancytree-unselectable",lastsib:"fancytree-lastsib",loading:"fancytree-loading",error:"fancytree-error",statusNodePrefix:"fancytree-statusnode-"},lazyLoad:null,postProcess:null},_deprecationWarning:function(e){var t=this.tree;t&&3<=t.options.debugLevel&&t.warn("$().fancytree('"+e+"') is deprecated (see https://wwwendt.de/tech/fancytree/doc/jsdoc/Fancytree_Widget.html")},_create:function(){this.tree=new F(this),this.$source=this.source||"json"===this.element.data("type")?this.element:this.element.find(">ul").first();var e,t,n,i=this.options,r=i.extensions;this.tree;for(n=0;n<r.length;n++)t=r[n],(e=k.ui.fancytree._extensions[t])||k.error("Could not apply extension '"+t+"' (it is not registered, did you forget to include it?)"),this.tree.options[t]=c({},e.options,this.tree.options[t]),C(void 0===this.tree.ext[t],"Extension name must not exist as Fancytree.ext attribute: '"+t+"'"),this.tree.ext[t]={},w(this.tree,0,e,t),e;void 0!==i.icons&&(!0===i.icon?(this.tree.warn("'icons' tree option is deprecated since v2.14.0: use 'icon' instead"),i.icon=i.icons):k.error("'icons' tree option is deprecated since v2.14.0: use 'icon' only instead")),void 0!==i.iconClass&&(i.icon?k.error("'iconClass' tree option is deprecated since v2.14.0: use 'icon' only instead"):(this.tree.warn("'iconClass' tree option is deprecated since v2.14.0: use 'icon' instead"),i.icon=i.iconClass)),void 0!==i.tabbable&&(i.tabindex=i.tabbable?"0":"-1",this.tree.warn("'tabbable' tree option is deprecated since v2.17.0: use 'tabindex='"+i.tabindex+"' instead")),this.tree._callHook("treeCreate",this.tree)},_init:function(){this.tree._callHook("treeInit",this.tree),this._bind()},_setOption:function(e,t){return this.tree._callHook("treeSetOption",this.tree,e,t)},_destroy:function(){this._unbind(),this.tree._callHook("treeDestroy",this.tree)},_unbind:function(){var e=this.tree._ns;this.element.off(e),this.tree.$container.off(e),k(document).off(e)},_bind:function(){var s=this,a=this.options,o=this.tree,e=o._ns;this._unbind(),o.$container.on("focusin"+e+" focusout"+e,function(e){var t=g.getNode(e),n="focusin"===e.type;if(!n&&t&&k(e.target).is("a"))t.debug("Ignored focusout on embedded <a> element.");else{if(n){if(o._getExpiringValue("focusin"))return void o.debug("Ignored double focusin.");o._setExpiringValue("focusin",!0,50),t||(t=o._getExpiringValue("mouseDownNode"))&&o.debug("Reconstruct mouse target for focusin from recent event.")}t?o._callHook("nodeSetFocus",o._makeHookContext(t,e),n):o.tbody&&k(e.target).parents("table.fancytree-container > thead").length?o.debug("Ignore focus event outside table body.",e):o._callHook("treeSetFocus",o,n)}}).on("selectstart"+e,"span.fancytree-title",function(e){e.preventDefault()}).on("keydown"+e,function(e){if(a.disabled||!1===a.keyboard)return!0;var t,n=o.focusNode,i=o._makeHookContext(n||o,e),r=o.phase;try{return o.phase="userEvent","preventNav"===(t=n?o._triggerNodeEvent("keydown",n,e):o._triggerTreeEvent("keydown",e))?t=!0:!1!==t&&(t=o._callHook("nodeKeydown",i)),t}finally{o.phase=r}}).on("mousedown"+e,function(e){var t=g.getEventTarget(e);o._lastMousedownNode=t?t.node:null,o._setExpiringValue("mouseDownNode",o._lastMousedownNode)}).on("click"+e+" dblclick"+e,function(e){if(a.disabled)return!0;var t,n=g.getEventTarget(e),i=n.node,r=s.tree,o=r.phase;if(!i)return!0;t=r._makeHookContext(i,e);try{switch(r.phase="userEvent",e.type){case"click":return t.targetType=n.type,i.isPagingNode()?!0===r._triggerNodeEvent("clickPaging",t,e):!1!==r._triggerNodeEvent("click",t,e)&&r._callHook("nodeClick",t);case"dblclick":return t.targetType=n.type,!1!==r._triggerNodeEvent("dblclick",t,e)&&r._callHook("nodeDblclick",t)}}finally{r.phase=o}})},getActiveNode:function(){return this._deprecationWarning("getActiveNode"),this.tree.activeNode},getNodeByKey:function(e){return this._deprecationWarning("getNodeByKey"),this.tree.getNodeByKey(e)},getRootNode:function(){return this._deprecationWarning("getRootNode"),this.tree.rootNode},getTree:function(){return this._deprecationWarning("getTree"),this.tree}}),g=k.ui.fancytree,k.extend(k.ui.fancytree,{version:"2.35.0",buildType: "production",debugLevel: 3,_nextId:1,_nextNodeKey:1,_extensions:{},_FancytreeClass:F,_FancytreeNodeClass:H,jquerySupports:{positionMyOfs:function(e,t,n,i){var r,o,s,a=k.map(k.trim(e).split("."),function(e){return parseInt(e,10)}),l=k.map(Array.prototype.slice.call(arguments,1),function(e){return parseInt(e,10)});for(r=0;r<l.length;r++)if((o=a[r]||0)!==(s=l[r]||0))return s<o;return!0}(k.ui.version,1,9)},assert:function(e,t){return C(e,t)},createTree:function(e,t){var n=k(e).fancytree(t);return g.getTree(n)},debounce:function(t,n,i,r){var o;return 3===arguments.length&&"boolean"!=typeof i&&(r=i,i=!1),function(){var e=arguments;r=r||this,i&&!o&&n.apply(r,e),clearTimeout(o),o=setTimeout(function(){i||n.apply(r,e),o=null},t)}},debug:function(e){4<=k.ui.fancytree.debugLevel&&d("log",arguments)},error:function(e){1<=k.ui.fancytree.debugLevel&&d("error",arguments)},escapeHtml:function(e){return(""+e).replace(t,function(e){return i[e]})},fixPositionOptions:function(e){if((e.offset||0<=(""+e.my+e.at).indexOf("%"))&&k.error("expected new position syntax (but '%' is not supported)"),!k.ui.fancytree.jquerySupports.positionMyOfs){var t=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(e.my),n=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(e.at),i=(t[2]?+t[2]:0)+(n[2]?+n[2]:0),r=(t[4]?+t[4]:0)+(n[4]?+n[4]:0);e=k.extend({},e,{my:t[1]+" "+t[3],at:n[1]+" "+n[3]}),(i||r)&&(e.offset=i+" "+r)}return e},getEventTarget:function(e){var t,n=e&&e.target?e.target.className:"",i={node:this.getNode(e.target),type:void 0};return/\bfancytree-title\b/.test(n)?i.type="title":/\bfancytree-expander\b/.test(n)?i.type=!1===i.node.hasChildren()?"prefix":"expander":/\bfancytree-checkbox\b/.test(n)?i.type="checkbox":/\bfancytree(-custom)?-icon\b/.test(n)?i.type="icon":/\bfancytree-node\b/.test(n)?i.type="title":e&&e.target&&((t=k(e.target)).is("ul[role=group]")?((i.node&&i.node.tree||g).debug("Ignoring click on outer UL."),i.node=null):t.closest(".fancytree-title").length?i.type="title":t.closest(".fancytree-checkbox").length?i.type="checkbox":t.closest(".fancytree-expander").length&&(i.type="expander")),i},getEventTargetType:function(e){return this.getEventTarget(e).type},getNode:function(e){if(e instanceof H)return e;for(e instanceof k?e=e[0]:void 0!==e.originalEvent&&(e=e.target);e;){if(e.ftnode)return e.ftnode;e=e.parentNode}return null},getTree:function(e){var t,n=e;return e instanceof F?e:(void 0===e&&(e=0),"number"==typeof e?e=k(".fancytree-container").eq(e):"string"==typeof e?(e=k("#ft-id-"+n).eq(0)).length||(e=k(n).eq(0)):e instanceof Element||e instanceof HTMLDocument?e=k(e):e instanceof k?e=e.eq(0):void 0!==e.originalEvent&&(e=k(e.target)),(t=(e=e.closest(":ui-fancytree")).data("ui-fancytree")||e.data("fancytree"))?t.tree:null)},evalOption:function(e,t,n,i,r){var o,s,a=t.tree,l=i[e],d=n[e];return k.isFunction(l)?(o={node:t,tree:a,widget:a.widget,options:a.widget.options,typeInfo:a.types[t.type]||{}},null==(s=l.call(a,{type:e},o))&&(s=d)):s=null==d?l:d,null==s&&(s=r),s},setSpanIcon:function(e,t,n){var i=k(e);"string"==typeof n?i.attr("class",t+" "+n):(n.text?i.text(""+n.text):n.html&&(e.innerHTML=n.html),i.attr("class",t+" "+(n.addClass||"")))},eventToString:function(e){var t=e.which,n=e.type,i=[];return e.altKey&&i.push("alt"),e.ctrlKey&&i.push("ctrl"),e.metaKey&&i.push("meta"),e.shiftKey&&i.push("shift"),"click"===n||"dblclick"===n?i.push(s[e.button]+n):"wheel"===n?i.push(n):r[t]||i.push(y[t]||String.fromCharCode(t).toLowerCase()),i.join("+")},info:function(e){3<=k.ui.fancytree.debugLevel&&d("info",arguments)},keyEventToString:function(e){return this.warn("keyEventToString() is deprecated: use eventToString()"),this.eventToString(e)},overrideMethod:function(e,t,n,i){var r,o=e[t]||k.noop;e[t]=function(){var e=i||this;try{return r=e._super,e._super=o,n.apply(e,arguments)}finally{e._super=r}}},parseHtml:function(s){var a,l,d,c,u,h,f,p,e=s.find(">li"),g=[];return e.each(function(){var e,t,n=k(this),i=n.find(">span",this).first(),r=i.length?null:n.find(">a").first(),o={tooltip:null,data:{}};for(i.length?o.title=i.html():r&&r.length?(o.title=r.html(),o.data.href=r.attr("href"),o.data.target=r.attr("target"),o.tooltip=r.attr("title")):(o.title=n.html(),0<=(u=o.title.search(/<ul/i))&&(o.title=o.title.substring(0,u))),o.title=k.trim(o.title),c=0,h=b.length;c<h;c++)o[b[c]]=void 0;for(a=this.className.split(" "),d=[],c=0,h=a.length;c<h;c++)l=a[c],x[l]?o[l]=!0:d.push(l);if(o.extraClasses=d.join(" "),(f=n.attr("title"))&&(o.tooltip=f),(f=n.attr("id"))&&(o.key=f),n.attr("hideCheckbox")&&(o.checkbox=!1),(e=L(n))&&!k.isEmptyObject(e)){for(t in _)e.hasOwnProperty(t)&&(e[_[t]]=e[t],delete e[t]);for(c=0,h=N.length;c<h;c++)f=N[c],null!=(p=e[f])&&(delete e[f],o[f]=p);k.extend(o.data,e)}(s=n.find(">ul").first()).length?o.children=k.ui.fancytree.parseHtml(s):o.children=o.lazy?void 0:null,g.push(o)}),g},registerExtension:function(e){C(null!=e.name,"extensions must have a `name` property."),C(null!=e.version,"extensions must have a `version` property."),k.ui.fancytree._extensions[e.name]=e},unescapeHtml:function(e){var t=document.createElement("div");return t.innerHTML=e,0===t.childNodes.length?"":t.childNodes[0].nodeValue},warn:function(e){2<=k.ui.fancytree.debugLevel&&d("warn",arguments)}}),k.ui.fancytree}function C(e,t){e||(t=t?": "+t:"",k.error("Fancytree assertion failed"+t))}function d(e,t){var n,i,r=window.console?window.console[e]:null;if(r)try{r.apply(window.console,t)}catch(e){for(i="",n=0;n<t.length;n++)i+=t[n];r(i)}}function c(){var e,t,n,i,r,o=arguments[0]||{},s=1,a=arguments.length;if("object"==typeof o||k.isFunction(o)||(o={}),s===a)throw Error("need at least two args");for(;s<a;s++)if(null!=(e=arguments[s]))for(t in e)if(e.hasOwnProperty(t)){if(n=o[t],o===(i=e[t]))continue;i&&k.isPlainObject(i)?(r=n&&k.isPlainObject(n)?n:{},o[t]=c(r,i)):void 0!==i&&(o[t]=i)}return o}function u(e,i,t,n,r){var o,s,a,l,d;return o=i[e],s=n[e],a=i.ext[r],l=function(){return o.apply(i,arguments)},d=function(e){return o.apply(i,e)},function(){var e=i._local,t=i._super,n=i._superApply;try{return i._local=a,i._super=l,i._superApply=d,s.apply(i,arguments)}finally{i._local=e,i._super=t,i._superApply=n}}}function w(e,t,n,i){for(var r in n)"function"==typeof n[r]?"function"==typeof e[r]?e[r]=u(r,e,0,n,i):"_"===r.charAt(0)?e.ext[i][r]=u(r,e,0,n,i):k.error("Could not override tree."+r+". Use prefix '_' to create tree."+i+"._"+r):"options"!==r&&(e.ext[i][r]=n[r])}function S(e,t){return void 0===e?k.Deferred(function(){this.resolve()}).promise():k.Deferred(function(){this.resolveWith(e,t)}).promise()}function E(e,t){return void 0===e?k.Deferred(function(){this.reject()}).promise():k.Deferred(function(){this.rejectWith(e,t)}).promise()}function T(e,t){return function(){e.resolveWith(t)}}function L(e){var t=k.extend({},e.data()),n=t.json;return delete t.fancytree,delete t.uiFancytree,n&&(delete t.json,t=k.extend(t,n)),t}function A(e){return(""+e).replace(n,function(e){return i[e]})}function P(t){return t=t.toLowerCase(),function(e){return 0<=e.title.toLowerCase().indexOf(t)}}function H(e,t){var n,i,r,o;for(this.parent=e,this.tree=e.tree,this.ul=null,this.li=null,this.statusNodeType=null,this._isLoading=!1,this._error=null,this.data={},n=0,i=N.length;n<i;n++)this[r=N[n]]=t[r];for(r in null==this.unselectableIgnore&&null==this.unselectableStatus||(this.unselectable=!0),t.hideCheckbox&&k.error("'hideCheckbox' node option was removed in v2.23.0: use 'checkbox: false'"),t.data&&k.extend(this.data,t.data),t)a[r]||k.isFunction(t[r])||l[r]||(this.data[r]=t[r]);null==this.key?this.tree.options.defaultKey?(this.key=this.tree.options.defaultKey(this),C(this.key,"defaultKey() must return a unique key")):this.key="_"+g._nextNodeKey++:this.key=""+this.key,t.active&&(C(null===this.tree.activeNode,"only one active node allowed"),this.tree.activeNode=this),t.selected&&(this.tree.lastSelectedNode=this),(o=t.children)?o.length?this._setChildren(o):this.children=this.lazy?[]:null:this.children=null,this.tree._callHook("treeRegisterNode",this.tree,!0,this)}function F(e){this.widget=e,this.$div=e.element,this.options=e.options,this.options&&(void 0!==this.options.lazyload&&k.error("The 'lazyload' event is deprecated since 2014-02-25. Use 'lazyLoad' (with uppercase L) instead."),void 0!==this.options.loaderror&&k.error("The 'loaderror' event was renamed since 2014-07-03. Use 'loadError' (with uppercase E) instead."),void 0!==this.options.fx&&k.error("The 'fx' option was replaced by 'toggleEffect' since 2014-11-30."),void 0!==this.options.removeNode&&k.error("The 'removeNode' event was replaced by 'modifyChild' since 2.20 (2016-09-10).")),this.ext={},this.types={},this.columns={},this.data=L(this.$div),this._id=""+(this.options.treeId||k.ui.fancytree._nextId++),this._ns=".fancytree-"+this._id,this.activeNode=null,this.focusNode=null,this._hasFocus=null,this._tempCache={},this._lastMousedownNode=null,this._enableUpdate=!0,this.lastSelectedNode=null,this.systemFocusElement=null,this.lastQuicksearchTerm="",this.lastQuicksearchTime=0,this.viewport=null,this.statusClassPropName="span",this.ariaPropName="li",this.nodeContainerAttrName="li",this.$div.find(">ul.fancytree-container").remove();var t,n={tree:this};this.rootNode=new H(n,{title:"root",key:"root_"+this._id,children:null,expanded:!0}),this.rootNode.parent=null,t=k("<ul>",{id:"ft-id-"+this._id,class:"ui-fancytree fancytree-container fancytree-plain"}).appendTo(this.$div),this.$container=t,this.rootNode.ul=t[0],null==this.options.debugLevel&&(this.options.debugLevel=g.debugLevel)}k.ui.fancytree.warn("Fancytree: ignored duplicate include")});

/*! Extension 'jquery.fancytree.childcounter.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(i){"use strict";return i.ui.fancytree._FancytreeClass.prototype.countSelected=function(e){this.options;return this.getSelectedNodes(e).length},i.ui.fancytree._FancytreeNodeClass.prototype.updateCounters=function(){var e=this,n=i("span.fancytree-childcounter",e.span),t=e.tree.options.childcounter,o=e.countChildren(t.deep);!(e.data.childCounter=o)&&t.hideZeros||e.isExpanded()&&t.hideExpanded?n.remove():(n.length||(n=i("<span class='fancytree-childcounter'/>").appendTo(i("span.fancytree-icon,span.fancytree-custom-icon",e.span))),n.text(o)),!t.deep||e.isTopLevel()||e.isRootNode()||e.parent.updateCounters()},i.ui.fancytree.prototype.widgetMethod1=function(e){this.tree;return e},i.ui.fancytree.registerExtension({name:"childcounter",version:"2.35.0",options:{deep:!0,hideZeros:!0,hideExpanded:!1},foo:42,_appendCounter:function(e){},treeInit:function(e){e.options,e.options.childcounter;this._superApply(arguments),this.$container.addClass("fancytree-ext-childcounter")},treeDestroy:function(e){this._superApply(arguments)},nodeRenderTitle:function(e,n){var t=e.node,o=e.options.childcounter,r=null==t.data.childCounter?t.countChildren(o.deep):+t.data.childCounter;this._super(e,n),!r&&o.hideZeros||t.isExpanded()&&o.hideExpanded||i("span.fancytree-icon,span.fancytree-custom-icon",t.span).append(i("<span class='fancytree-childcounter'/>").text(r))},nodeSetExpanded:function(e,n,t){var o=e.tree;e.node;return this._superApply(arguments).always(function(){o.nodeRenderTitle(e)})}}),i.ui.fancytree});

/*! Extension 'jquery.fancytree.clones.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(h){"use strict";function y(e,t){e||(t=t?": "+t:"",h.error("Assertion failed"+t))}function i(e,t,r){for(var n,i,s=3&e.length,o=e.length-s,l=r,u=3432918353,a=461845907,c=0;c<o;)i=255&e.charCodeAt(c)|(255&e.charCodeAt(++c))<<8|(255&e.charCodeAt(++c))<<16|(255&e.charCodeAt(++c))<<24,++c,l=27492+(65535&(n=5*(65535&(l=(l^=i=(65535&(i=(i=(65535&i)*u+(((i>>>16)*u&65535)<<16)&4294967295)<<15|i>>>17))*a+(((i>>>16)*a&65535)<<16)&4294967295)<<13|l>>>19))+((5*(l>>>16)&65535)<<16)&4294967295))+((58964+(n>>>16)&65535)<<16);switch(i=0,s){case 3:i^=(255&e.charCodeAt(c+2))<<16;case 2:i^=(255&e.charCodeAt(c+1))<<8;case 1:l^=i=(65535&(i=(i=(65535&(i^=255&e.charCodeAt(c)))*u+(((i>>>16)*u&65535)<<16)&4294967295)<<15|i>>>17))*a+(((i>>>16)*a&65535)<<16)&4294967295}return l^=e.length,l=2246822507*(65535&(l^=l>>>16))+((2246822507*(l>>>16)&65535)<<16)&4294967295,l=3266489909*(65535&(l^=l>>>13))+((3266489909*(l>>>16)&65535)<<16)&4294967295,l^=l>>>16,t?("0000000"+(l>>>0).toString(16)).substr(-8):l>>>0}return h.ui.fancytree._FancytreeNodeClass.prototype.getCloneList=function(e){var t,r=this.tree,n=r.refMap[this.refKey]||null,i=r.keyMap;return n&&(t=this.key,e?n=h.map(n,function(e){return i[e]}):(n=h.map(n,function(e){return e===t?null:i[e]})).length<1&&(n=null)),n},h.ui.fancytree._FancytreeNodeClass.prototype.isClone=function(){var e=this.refKey||null,t=e&&this.tree.refMap[e]||null;return!!(t&&1<t.length)},h.ui.fancytree._FancytreeNodeClass.prototype.reRegister=function(t,e){t=null==t?null:""+t,e=null==e?null:""+e;var r=this.tree,n=this.key,i=this.refKey,s=r.keyMap,o=r.refMap,l=o[i]||null,u=!1;return null!=t&&t!==this.key&&(s[t]&&h.error("[ext-clones] reRegister("+t+"): already exists: "+this),delete s[n],s[t]=this,l&&(o[i]=h.map(l,function(e){return e===n?t:e})),this.key=t,u=!0),null!=e&&e!==this.refKey&&(l&&(1===l.length?delete o[i]:o[i]=h.map(l,function(e){return e===n?null:e})),o[e]?o[e].append(t):o[e]=[this.key],this.refKey=e,u=!0),u},h.ui.fancytree._FancytreeNodeClass.prototype.setRefKey=function(e){return this.reRegister(null,e)},h.ui.fancytree._FancytreeClass.prototype.getNodesByRef=function(e,r){var n=this.keyMap,t=this.refMap[e]||null;return t&&(t=r?h.map(t,function(e){var t=n[e];return t.isDescendantOf(r)?t:null}):h.map(t,function(e){return n[e]})).length<1&&(t=null),t},h.ui.fancytree._FancytreeClass.prototype.changeRefKey=function(e,t){var r,n=this.keyMap,i=this.refMap[e]||null;if(i){for(r=0;r<i.length;r++)n[i[r]].refKey=t;delete this.refMap[e],this.refMap[t]=i}},h.ui.fancytree.registerExtension({name:"clones",version:"2.35.0",options:{highlightActiveClones:!0,highlightClones:!1},treeCreate:function(e){this._superApply(arguments),e.tree.refMap={},e.tree.keyMap={}},treeInit:function(e){this.$container.addClass("fancytree-ext-clones"),y(null==e.options.defaultKey),e.options.defaultKey=function(e){return t=e,n=h.map(t.getParentList(!1,!0),function(e){return e.refKey||e.key}),"id_"+(r=i(n=n.join("/"),!0))+i(r+n,!0);var t,r,n},this._superApply(arguments)},treeClear:function(e){return e.tree.refMap={},e.tree.keyMap={},this._superApply(arguments)},treeRegisterNode:function(e,t,r){var n,i,s=e.tree,o=s.keyMap,l=s.refMap,u=r.key,a=r&&null!=r.refKey?""+r.refKey:null;if(r.isStatusNode())return this._super(e,t,r);if(t){if(null!=o[r.key]){var c=o[r.key],f="clones.treeRegisterNode: duplicate key '"+r.key+"': /"+r.getPath(!0)+" => "+c.getPath(!0);s.error(f),h.error(f)}o[u]=r,a&&((n=l[a])?(n.push(u),2===n.length&&e.options.clones.highlightClones&&o[n[0]].renderStatus()):l[a]=[u])}else null==o[u]&&h.error("clones.treeRegisterNode: node.key not registered: "+r.key),delete o[u],a&&(n=l[a])&&((i=n.length)<=1?(y(1===i),y(n[0]===u),delete l[a]):(!function(e,t){var r;for(r=e.length-1;0<=r;r--)if(e[r]===t)return e.splice(r,1)}(n,u),2===i&&e.options.clones.highlightClones&&o[n[0]].renderStatus()));return this._super(e,t,r)},nodeRenderStatus:function(e){var t,r,n=e.node;return r=this._super(e),e.options.clones.highlightClones&&(t=h(n[e.tree.statusClassPropName])).length&&n.isClone()&&t.addClass("fancytree-clone"),r},nodeSetActive:function(e,r,t){var n,i=e.tree.statusClassPropName,s=e.node;return n=this._superApply(arguments),e.options.clones.highlightActiveClones&&s.isClone()&&h.each(s.getCloneList(!0),function(e,t){h(t[i]).toggleClass("fancytree-active-clone",!1!==r)}),n}}),h.ui.fancytree});

/*! Extension 'jquery.fancytree.dnd.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","jquery-ui/ui/widgets/draggable","jquery-ui/ui/widgets/droppable","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(k){"use strict";var t=!1,f="fancytree-drop-accept",v="fancytree-drop-after",h="fancytree-drop-before",y="fancytree-drop-reject";function b(e){return 0===e?"":0<e?"+"+e:""+e}function r(e){var r=e.options.dnd||null,o=e.options.glyph||null;r&&(t||(k.ui.plugin.add("draggable","connectToFancytree",{start:function(e,r){var t=k(this).data("ui-draggable")||k(this).data("draggable"),a=r.helper.data("ftSourceNode")||null;if(a)return t.offset.click.top=-2,t.offset.click.left=16,a.tree.ext.dnd._onDragEvent("start",a,null,e,r,t)},drag:function(e,r){var t,a=k(this).data("ui-draggable")||k(this).data("draggable"),n=r.helper.data("ftSourceNode")||null,o=r.helper.data("ftTargetNode")||null,d=k.ui.fancytree.getNode(e.target),l=n&&n.tree.options.dnd;e.target&&!d&&0<k(e.target).closest("div.fancytree-drag-helper,#fancytree-drop-marker").length?(n||o||k.ui.fancytree).debug("Drag event over helper: ignored."):(r.helper.data("ftTargetNode",d),l&&l.updateHelper&&(t=n.tree._makeHookContext(n,e,{otherNode:d,ui:r,draggable:a,dropMarker:k("#fancytree-drop-marker")}),l.updateHelper.call(n.tree,n,t)),o&&o!==d&&o.tree.ext.dnd._onDragEvent("leave",o,n,e,r,a),d&&d.tree.options.dnd.dragDrop&&(d===o||d.tree.ext.dnd._onDragEvent("enter",d,n,e,r,a),d.tree.ext.dnd._onDragEvent("over",d,n,e,r,a)))},stop:function(e,r){var t=k(this).data("ui-draggable")||k(this).data("draggable"),a=r.helper.data("ftSourceNode")||null,n=r.helper.data("ftTargetNode")||null,o="mouseup"===e.type&&1===e.which;o||(a||n||k.ui.fancytree).debug("Drag was cancelled"),n&&(o&&n.tree.ext.dnd._onDragEvent("drop",n,a,e,r,t),n.tree.ext.dnd._onDragEvent("leave",n,a,e,r,t)),a&&a.tree.ext.dnd._onDragEvent("stop",a,null,e,r,t)}}),t=!0)),r&&r.dragStart&&e.widget.element.draggable(k.extend({addClasses:!1,appendTo:e.$container,containment:!1,delay:0,distance:4,revert:!1,scroll:!0,scrollSpeed:7,scrollSensitivity:10,connectToFancytree:!0,helper:function(e){var r,t,a,n=k.ui.fancytree.getNode(e.target);return n?(a=n.tree.options.dnd,t=k(n.span),(r=k("<div class='fancytree-drag-helper'><span class='fancytree-drag-helper-img' /></div>").css({zIndex:3,position:"relative"}).append(t.find("span.fancytree-title").clone())).data("ftSourceNode",n),o&&r.find(".fancytree-drag-helper-img").addClass(o.map._addClass+" "+o.map.dragHelper),a.initHelper&&a.initHelper.call(n.tree,n,{node:n,tree:n.tree,originalEvent:e,ui:{helper:r}}),r):"<div>ERROR?: helper requested but sourceNode not found</div>"},start:function(e,r){return!!r.helper.data("ftSourceNode")}},e.options.dnd.draggable)),r&&r.dragDrop&&e.widget.element.droppable(k.extend({addClasses:!1,tolerance:"intersect",greedy:!1},e.options.dnd.droppable))}return k.ui.fancytree.registerExtension({name:"dnd",version:"2.35.0",options:{autoExpandMS:1e3,draggable:null,droppable:null,focusOnClick:!1,preventVoidMoves:!0,preventRecursiveMoves:!0,smartRevert:!0,dropMarkerOffsetX:-24,dropMarkerInsertOffsetX:-16,dragStart:null,dragStop:null,initHelper:null,updateHelper:null,dragEnter:null,dragOver:null,dragExpand:null,dragDrop:null,dragLeave:null},treeInit:function(t){var e=t.tree;this._superApply(arguments),e.options.dnd.dragStart&&e.$container.on("mousedown",function(e){if(t.options.dnd.focusOnClick){var r=k.ui.fancytree.getNode(e);r&&r.debug("Re-enable focus that was prevented by jQuery UI draggable."),setTimeout(function(){k(e.target).closest(":tabbable").focus()},10)}}),r(e)},_setDndStatus:function(e,r,t,a,n){var o,d,l="center",s=this._local,i=this.options.dnd,p=this.options.glyph,g=e?k(e.span):null,u=k(r.span),c=u.find("span.fancytree-title");if(s.$dropMarker||(s.$dropMarker=k("<div id='fancytree-drop-marker'></div>").hide().css({"z-index":1e3}).prependTo(k(this.$div).parent()),p&&s.$dropMarker.addClass(p.map._addClass+" "+p.map.dropMarker)),"after"===a||"before"===a||"over"===a){switch(o=i.dropMarkerOffsetX||0,a){case"before":l="top",o+=i.dropMarkerInsertOffsetX||0;break;case"after":l="bottom",o+=i.dropMarkerInsertOffsetX||0}d={my:"left"+b(o)+" center",at:"left "+l,of:c},this.options.rtl&&(d.my="right"+b(-o)+" center",d.at="right "+l),s.$dropMarker.toggleClass(v,"after"===a).toggleClass("fancytree-drop-over","over"===a).toggleClass(h,"before"===a).toggleClass("fancytree-rtl",!!this.options.rtl).show().position(k.ui.fancytree.fixPositionOptions(d))}else s.$dropMarker.hide();g&&g.toggleClass(f,!0===n).toggleClass(y,!1===n),u.toggleClass("fancytree-drop-target","after"===a||"before"===a||"over"===a).toggleClass(v,"after"===a).toggleClass(h,"before"===a).toggleClass(f,!0===n).toggleClass(y,!1===n),t.toggleClass(f,!0===n).toggleClass(y,!1===n)},_onDragEvent:function(e,r,t,a,n,o){var d,l,s,i,p,g,u,c,f,v=this.options.dnd,h=this._makeHookContext(r,a,{otherNode:t,ui:n,draggable:o}),y=null,b=this,m=k(r.span);switch(v.smartRevert&&(o.options.revert="invalid"),e){case"start":r.isStatusNode()?y=!1:v.dragStart&&(y=v.dragStart(r,h)),!1===y?(this.debug("tree.dragStart() cancelled"),n.helper.trigger("mouseup").hide()):(v.smartRevert&&(i=r[h.tree.nodeContainerAttrName].getBoundingClientRect(),s=k(o.options.appendTo)[0].getBoundingClientRect(),o.originalPosition.left=Math.max(0,i.left-s.left),o.originalPosition.top=Math.max(0,i.top-s.top)),m.addClass("fancytree-drag-source"),k(document).on("keydown.fancytree-dnd,mousedown.fancytree-dnd",function(e){"keydown"===e.type&&e.which===k.ui.keyCode.ESCAPE?b.ext.dnd._cancelDrag():"mousedown"===e.type&&b.ext.dnd._cancelDrag()}));break;case"enter":y=!!(f=(!v.preventRecursiveMoves||!r.isDescendantOf(t))&&(v.dragEnter?v.dragEnter(r,h):null))&&(k.isArray(f)?{over:0<=k.inArray("over",f),before:0<=k.inArray("before",f),after:0<=k.inArray("after",f)}:{over:!0===f||"over"===f,before:!0===f||"before"===f,after:!0===f||"after"===f}),n.helper.data("enterResponse",y);break;case"over":c=null,!1===(u=n.helper.data("enterResponse"))||("string"==typeof u?c=u:(l=m.offset(),g={x:(p={x:a.pageX-l.left,y:a.pageY-l.top}).x/m.width(),y:p.y/m.height()},u.after&&.75<g.y?c="after":!u.over&&u.after&&.5<g.y?c="after":u.before&&g.y<=.25?c="before":!u.over&&u.before&&g.y<=.5?c="before":u.over&&(c="over"),v.preventVoidMoves&&(r===t?(this.debug("    drop over source node prevented"),c=null):"before"===c&&t&&r===t.getNextSibling()?(this.debug("    drop after source node prevented"),c=null):"after"===c&&t&&r===t.getPrevSibling()?(this.debug("    drop before source node prevented"),c=null):"over"===c&&t&&t.parent===r&&t.isLastSibling()&&(this.debug("    drop last child over own parent prevented"),c=null)),n.helper.data("hitMode",c))),"before"===c||"after"===c||!v.autoExpandMS||!1===r.hasChildren()||r.expanded||v.dragExpand&&!1===v.dragExpand(r,h)||r.scheduleAction("expand",v.autoExpandMS),c&&v.dragOver&&(h.hitMode=c,y=v.dragOver(r,h)),d=!1!==y&&null!==c,v.smartRevert&&(o.options.revert=!d),this._local._setDndStatus(t,r,n.helper,c,d);break;case"drop":(c=n.helper.data("hitMode"))&&v.dragDrop&&(h.hitMode=c,v.dragDrop(r,h));break;case"leave":r.scheduleAction("cancel"),n.helper.data("enterResponse",null),n.helper.data("hitMode",null),this._local._setDndStatus(t,r,n.helper,"out",void 0),v.dragLeave&&v.dragLeave(r,h);break;case"stop":m.removeClass("fancytree-drag-source"),k(document).off(".fancytree-dnd"),v.dragStop&&v.dragStop(r,h);break;default:k.error("Unsupported drag event: "+e)}return y},_cancelDrag:function(){var e=k.ui.ddmanager.current;e&&e.cancel()}}),k.ui.fancytree});

/*! Extension 'jquery.fancytree.dnd5.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(x){"use strict";var s,i,S=x.ui.fancytree,f=/Mac/.test(navigator.platform),p="fancytree-drag-source",g="fancytree-drag-remove",k="fancytree-drop-accept",C="fancytree-drop-after",M="fancytree-drop-before",A="fancytree-drop-over",T="fancytree-drop-reject",I="fancytree-drop-target",y="application/x-fancytree-node",O=null,b=null,h=null,c=null,j=null,u=null,v=null,m=null,P=null,E=null;function D(){h=b=u=m=v=E=j=null,c&&c.removeClass(p+" "+g),c=null,O&&O.hide(),i&&(i.remove(),i=null)}function L(e){return 0===e?"":0<e?"+"+e:""+e}function R(e,r){var t,o,a,n,d=r.tree,l=r.dataTransfer;"dragstart"===e.type?(r.effectAllowed=d.options.dnd5.effectAllowed,r.dropEffect=d.options.dnd5.dropEffectDefault):(r.effectAllowed=m,r.dropEffect=v),r.dropEffectSuggested=(o=e,a=(t=d).options.dnd5.dropEffectDefault,n=a,f?o.metaKey&&o.altKey?n="link":o.ctrlKey?n="link":o.metaKey?n="move":o.altKey&&(n="copy"):o.ctrlKey?n="copy":o.shiftKey?n="move":o.altKey&&(n="link"),n!==u&&t.info("evalEffectModifiers: "+o.type+" - evalEffectModifiers(): "+u+" -> "+n),u=n),r.isMove="move"===r.dropEffect,r.files=l.files||[]}function K(e,r,t){var o=r.tree,a=r.dataTransfer;return"dragstart"!==e.type&&m!==r.effectAllowed&&o.warn("effectAllowed should only be changed in dragstart event: "+e.type+": data.effectAllowed changed from "+m+" -> "+r.effectAllowed),!1===t&&(o.info("applyDropEffectCallback: allowDrop === false"),r.effectAllowed="none",r.dropEffect="none"),r.isMove="move"===r.dropEffect,m=r.effectAllowed,v=r.dropEffect,a.effectAllowed=m,a.dropEffect=v}function w(e,r){var t,o,a,n,d,l,s,i,f;if(r.options.dnd5.scroll&&(t=r.tree,o=e,d=t.options.dnd5,l=t.$scrollParent[0],s=d.scrollSensitivity,i=d.scrollSpeed,f=0,l!==document&&"HTML"!==l.tagName?(a=t.$scrollParent.offset(),n=l.scrollTop,a.top+l.offsetHeight-o.pageY<s?0<l.scrollHeight-t.$scrollParent.innerHeight()-n&&(l.scrollTop=f=n+i):0<n&&o.pageY-a.top<s&&(l.scrollTop=f=n-i)):0<(n=x(document).scrollTop())&&o.pageY-n<s?(f=n-i,x(document).scrollTop(f)):x(window).height()-(o.pageY-n)<s&&(f=n+i,x(document).scrollTop(f)),f&&t.debug("autoScroll: "+f+"px")),!r.node)return r.tree.warn("Ignored dragover for non-node"),P;var p,g,c,u,v=null,y=r.tree,b=y.options,h=b.dnd5,m=r.node,E=r.otherNode,D="center",w=x(m.span),N=w.find("span.fancytree-title");if(!1===j)return y.debug("Ignored dragover, since dragenter returned false."),!1;("string"==typeof j&&x.error("assert failed: dragenter returned string"),g=w.offset(),u=(e.pageY-g.top)/w.height(),j.after&&.75<u?v="after":!j.over&&j.after&&.5<u?v="after":j.before&&u<=.25?v="before":!j.over&&j.before&&u<=.5?v="before":j.over&&(v="over"),h.preventVoidMoves&&"move"===r.dropEffect&&(m===E?(m.debug("Drop over source node prevented."),v=null):"before"===v&&E&&m===E.getNextSibling()?(m.debug("Drop after source node prevented."),v=null):"after"===v&&E&&m===E.getPrevSibling()?(m.debug("Drop before source node prevented."),v=null):"over"===v&&E&&E.parent===m&&E.isLastSibling()&&(m.debug("Drop last child over own parent prevented."),v=null)),(r.hitMode=v)&&h.dragOver)&&(R(e,r),h.dragOver(m,r),K(e,r,!!v),v=r.hitMode);if("after"===(P=v)||"before"===v||"over"===v){switch(p=h.dropMarkerOffsetX||0,v){case"before":D="top",p+=h.dropMarkerInsertOffsetX||0;break;case"after":D="bottom",p+=h.dropMarkerInsertOffsetX||0}c={my:"left"+L(p)+" center",at:"left "+D,of:N},b.rtl&&(c.my="right"+L(-p)+" center",c.at="right "+D),O.toggleClass(C,"after"===v).toggleClass(A,"over"===v).toggleClass(M,"before"===v).show().position(S.fixPositionOptions(c))}else O.hide();return x(m.span).toggleClass(I,"after"===v||"before"===v||"over"===v).toggleClass(C,"after"===v).toggleClass(M,"before"===v).toggleClass(k,"over"===v).toggleClass(T,!1===v),v}function d(e){var r,t,o,a,n,d,l,s=this,i=s.options.dnd5,f=null,p=S.getNode(e),g=e.dataTransfer||e.originalEvent.dataTransfer,c={tree:s,node:p,options:s.options,originalEvent:e.originalEvent,widget:s.widget,hitMode:j,dataTransfer:g,otherNode:b||null,otherNodeList:h||null,otherNodeData:null,useDefaultImage:!0,dropEffect:void 0,dropEffectSuggested:void 0,effectAllowed:void 0,files:null,isCancelled:void 0,isMove:void 0};switch(e.type){case"dragenter":if(E=null,!p){s.debug("Ignore non-node "+e.type+": "+e.target.tagName+"."+e.target.className),j=!1;break}if(x(p.span).addClass(A).removeClass(k+" "+T),o=0<=x.inArray(y,g.types),i.preventNonNodes&&!o){p.debug("Reject dropping a non-node."),j=!1;break}if(i.preventForeignNodes&&(!b||b.tree!==p.tree)){p.debug("Reject dropping a foreign node."),j=!1;break}if(i.preventSameParent&&c.otherNode&&c.otherNode.tree===p.tree&&p.parent===c.otherNode.parent){p.debug("Reject dropping as sibling (same parent)."),j=!1;break}if(i.preventRecursion&&c.otherNode&&c.otherNode.tree===p.tree&&p.isDescendantOf(c.otherNode)){p.debug("Reject dropping below own ancestor."),j=!1;break}if(i.preventLazyParents&&!p.isLoaded()){p.warn("Drop over unloaded target node prevented."),j=!1;break}O.show(),R(e,c),a=i.dragEnter(p,c),n=!!(d=a)&&(l=x.isPlainObject(d)?{over:!!d.over,before:!!d.before,after:!!d.after}:x.isArray(d)?{over:0<=x.inArray("over",d),before:0<=x.inArray("before",d),after:0<=x.inArray("after",d)}:{over:!0===d||"over"===d,before:!0===d||"before"===d,after:!0===d||"after"===d},0!==Object.keys(l).length&&l),K(e,c,f=(j=n)&&(n.over||n.before||n.after));break;case"dragover":if(!p){s.debug("Ignore non-node "+e.type+": "+e.target.tagName+"."+e.target.className);break}R(e,c),f=!!(P=w(e,c)),("over"===P||!1===P)&&!p.expanded&&!1!==p.hasChildren()?E?!(i.autoExpandMS&&Date.now()-E>i.autoExpandMS)||p.isLoading()||i.dragExpand&&!1===i.dragExpand(p,c)||p.setExpanded():E=Date.now():E=null;break;case"dragleave":if(!p){s.debug("Ignore non-node "+e.type+": "+e.target.tagName+"."+e.target.className);break}if(!x(p.span).hasClass(A)){p.debug("Ignore dragleave (multi).");break}x(p.span).removeClass(A+" "+k+" "+T),p.scheduleAction("cancel"),i.dragLeave(p,c),O.hide();break;case"drop":if(0<=x.inArray(y,g.types)&&(t=g.getData(y),s.info(e.type+": getData('application/x-fancytree-node'): '"+t+"'")),t||(t=g.getData("text"),s.info(e.type+": getData('text'): '"+t+"'")),t)try{void 0!==(r=JSON.parse(t)).title&&(c.otherNodeData=r)}catch(e){}s.debug(e.type+": nodeData: '"+t+"', otherNodeData: ",c.otherNodeData),x(p.span).removeClass(A+" "+k+" "+T),c.hitMode=P,R(e,c),c.isCancelled=!P;var u=b&&b.span,v=b&&b.tree;i.dragDrop(p,c),e.preventDefault(),u&&!document.body.contains(u)&&(v===s?(s.debug("Drop handler removed source element: generating dragEnd."),i.dragEnd(b,c)):s.warn("Drop handler removed source element: dragend event may be lost.")),D()}if(f)return e.preventDefault(),!1}return x.ui.fancytree.getDragNodeList=function(){return h||[]},x.ui.fancytree.getDragNode=function(){return b},x.ui.fancytree.registerExtension({name:"dnd5",version:"2.35.0",options:{autoExpandMS:1500,dropMarkerInsertOffsetX:-16,dropMarkerOffsetX:-24,multiSource:!1,effectAllowed:"all",dropEffectDefault:"move",preventForeignNodes:!1,preventLazyParents:!0,preventNonNodes:!1,preventRecursion:!0,preventSameParent:!1,preventVoidMoves:!0,scroll:!0,scrollSensitivity:20,scrollSpeed:5,setTextTypeJson:!1,dragStart:null,dragDrag:x.noop,dragEnd:x.noop,dragEnter:null,dragOver:x.noop,dragExpand:x.noop,dragDrop:x.noop,dragLeave:x.noop},treeInit:function(e){var r,t=e.tree,o=e.options,a=o.glyph||null,n=o.dnd5;0<=x.inArray("dnd",o.extensions)&&x.error("Extensions 'dnd' and 'dnd5' are mutually exclusive."),n.dragStop&&x.error("dragStop is not used by ext-dnd5. Use dragEnd instead."),null!=n.preventRecursiveMoves&&x.error("preventRecursiveMoves was renamed to preventRecursion."),n.dragStart&&S.overrideMethod(e.options,"createNode",function(e,r){this._super.apply(this,arguments),r.node.span?r.node.span.draggable=!0:r.node.warn("Cannot add `draggable`: no span tag")}),this._superApply(arguments),this.$container.addClass("fancytree-ext-dnd5"),r=x("<span>").appendTo(this.$container),this.$scrollParent=r.scrollParent(),r.remove(),(O=x("#fancytree-drop-marker")).length||(O=x("<div id='fancytree-drop-marker'></div>").hide().css({"z-index":1e3,"pointer-events":"none"}).prependTo("body"),a&&S.setSpanIcon(O[0],a.map._addClass,a.map.dropMarker)),O.toggleClass("fancytree-rtl",!!o.rtl),n.dragStart&&t.$container.on("dragstart drag dragend",function(e){var r,t=this,o=t.options.dnd5,a=S.getNode(e),n=e.dataTransfer||e.originalEvent.dataTransfer,d={tree:t,node:a,options:t.options,originalEvent:e.originalEvent,widget:t.widget,dataTransfer:n,useDefaultImage:!0,dropEffect:void 0,dropEffectSuggested:void 0,effectAllowed:void 0,files:void 0,isCancelled:void 0,isMove:void 0};switch(e.type){case"dragstart":if(!a)return t.info("Ignored dragstart on a non-node."),!1;b=a,!1===o.multiSource?h=[a]:!0===o.multiSource?(h=t.getSelectedNodes(),a.isSelected()||h.unshift(a)):h=o.multiSource(a,d),(c=x(x.map(h,function(e){return e.span}))).addClass(p);var l=a.toDict();l.treeId=a.tree._id,r=JSON.stringify(l);try{n.setData(y,r),n.setData("text/html",x(a.span).html()),n.setData("text/plain",a.title)}catch(e){t.warn("Could not set data (IE only accepts 'text') - "+e)}return o.setTextTypeJson?n.setData("text",r):n.setData("text",a.title),R(e,d),!1===o.dragStart(a,d)?(D(),!1):(K(e,d),i=null,d.useDefaultImage&&(s=x(a.span).find(".fancytree-title"),h&&1<h.length&&(i=x("<span class='fancytree-childcounter'/>").text("+"+(h.length-1)).appendTo(s)),n.setDragImage&&n.setDragImage(s[0],-10,-10)),!0);case"drag":R(e,d),o.dragDrag(a,d),K(e,d),c.toggleClass(g,d.isMove);break;case"dragend":R(e,d),D(),d.isCancelled=!P,o.dragEnd(a,d,!P)}}.bind(t)),n.dragEnter&&t.$container.on("dragenter dragover dragleave drop",d.bind(t))}}),x.ui.fancytree});

/*! Extension 'jquery.fancytree.edit.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(l){"use strict";var t=/Mac/.test(navigator.platform),c=l.ui.fancytree.escapeHtml,o=l.ui.fancytree.unescapeHtml;return l.ui.fancytree._FancytreeNodeClass.prototype.editStart=function(){var t,i=this,e=this.tree,n=e.ext.edit,r=e.options.edit,a=l(".fancytree-title",i.span),s={node:i,tree:e,options:e.options,isNew:l(i[e.statusClassPropName]).hasClass("fancytree-edit-new"),orgTitle:i.title,input:null,dirty:!1};if(!1===r.beforeEdit.call(i,{type:"beforeEdit"},s))return!1;l.ui.fancytree.assert(!n.currentNode,"recursive edit"),n.currentNode=this,n.eventData=s,e.widget._unbind(),n.lastDraggableAttrValue=i.span.draggable,n.lastDraggableAttrValue&&(i.span.draggable=!1),l(document).on("mousedown.fancytree-edit",function(e){l(e.target).hasClass("fancytree-edit-input")||i.editEnd(!0,e)}),t=l("<input />",{class:"fancytree-edit-input",type:"text",value:e.options.escapeTitles?s.orgTitle:o(s.orgTitle)}),n.eventData.input=t,null!=r.adjustWidthOfs&&t.width(a.width()+r.adjustWidthOfs),null!=r.inputCss&&t.css(r.inputCss),a.html(t),t.focus().change(function(e){t.addClass("fancytree-edit-dirty")}).on("keydown",function(e){switch(e.which){case l.ui.keyCode.ESCAPE:i.editEnd(!1,e);break;case l.ui.keyCode.ENTER:return i.editEnd(!0,e),!1}e.stopPropagation()}).blur(function(e){return i.editEnd(!0,e)}),r.edit.call(i,{type:"edit"},s)},l.ui.fancytree._FancytreeNodeClass.prototype.editEnd=function(e,t){var i,n=this,r=this.tree,a=r.ext.edit,s=a.eventData,o=r.options.edit,d=l(".fancytree-title",n.span).find("input.fancytree-edit-input");return o.trim&&d.val(l.trim(d.val())),i=d.val(),s.dirty=i!==n.title,s.originalEvent=t,!1===e?s.save=!1:s.isNew?s.save=""!==i:s.save=s.dirty&&""!==i,!1!==o.beforeClose.call(n,{type:"beforeClose"},s)&&((!s.save||!1!==o.save.call(n,{type:"save"},s))&&(d.removeClass("fancytree-edit-dirty").off(),l(document).off(".fancytree-edit"),s.save?(n.setTitle(r.options.escapeTitles?i:c(i)),n.setFocus()):s.isNew?(n.remove(),n=s.node=null,a.relatedNode.setFocus()):(n.renderTitle(),n.setFocus()),a.eventData=null,a.currentNode=null,a.relatedNode=null,r.widget._bind(),n&&a.lastDraggableAttrValue&&(n.span.draggable=!0),l(r.$container).focus(),s.input=null,o.close.call(n,{type:"close"},s),!0))},l.ui.fancytree._FancytreeNodeClass.prototype.editCreateNode=function(e,t){var i,n=this.tree,r=this;e=e||"child",null==t?t={title:""}:"string"==typeof t?t={title:t}:l.ui.fancytree.assert(l.isPlainObject(t)),"child"!==e||this.isExpanded()||!1===this.hasChildren()?((i=this.addNode(t,e)).match=!0,l(i[n.statusClassPropName]).removeClass("fancytree-hide").addClass("fancytree-match"),i.makeVisible().done(function(){l(i[n.statusClassPropName]).addClass("fancytree-edit-new"),r.tree.ext.edit.relatedNode=r,i.editStart()})):this.setExpanded().done(function(){r.editCreateNode(e,t)})},l.ui.fancytree._FancytreeClass.prototype.isEditing=function(){return this.ext.edit?this.ext.edit.currentNode:null},l.ui.fancytree._FancytreeNodeClass.prototype.isEditing=function(){return!!this.tree.ext.edit&&this.tree.ext.edit.currentNode===this},l.ui.fancytree.registerExtension({name:"edit",version:"2.35.0",options:{adjustWidthOfs:4,allowEmpty:!1,inputCss:{minWidth:"3em"},triggerStart:["f2","mac+enter","shift+click"],trim:!0,beforeClose:l.noop,beforeEdit:l.noop,close:l.noop,edit:l.noop,save:l.noop},currentNode:null,treeInit:function(e){var n=e.tree;this._superApply(arguments),this.$container.addClass("fancytree-ext-edit").on("fancytreebeforeupdateviewport",function(e,t){var i=n.isEditing();i&&(i.info("Cancel edit due to scroll event."),i.editEnd(!1,e))})},nodeClick:function(e){var t=l.ui.fancytree.eventToString(e.originalEvent),i=e.options.edit.triggerStart;return"shift+click"===t&&0<=l.inArray("shift+click",i)&&e.originalEvent.shiftKey?(e.node.editStart(),!1):"click"===t&&0<=l.inArray("clickActive",i)&&e.node.isActive()&&!e.node.isEditing()&&l(e.originalEvent.target).hasClass("fancytree-title")?(e.node.editStart(),!1):this._superApply(arguments)},nodeDblclick:function(e){return 0<=l.inArray("dblclick",e.options.edit.triggerStart)?(e.node.editStart(),!1):this._superApply(arguments)},nodeKeydown:function(e){switch(e.originalEvent.which){case 113:if(0<=l.inArray("f2",e.options.edit.triggerStart))return e.node.editStart(),!1;break;case l.ui.keyCode.ENTER:if(0<=l.inArray("mac+enter",e.options.edit.triggerStart)&&t)return e.node.editStart(),!1}return this._superApply(arguments)}}),l.ui.fancytree});

/*! Extension 'jquery.fancytree.filter.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(g){"use strict";var m="__not_found__",x=g.ui.fancytree.escapeHtml;function C(e){return(e+"").replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1")}return g.ui.fancytree._FancytreeClass.prototype._applyFilterImpl=function(n,a,e){var t,i,r,s,l,o,d=0,c=this.options,h=c.escapeTitles,u=c.autoCollapse,p=g.extend({},c.filter,e),f="hide"===p.mode,y=!!p.leavesOnly&&!a;if("string"==typeof n){if(""===n)return this.warn("Fancytree passing an empty string as a filter is handled as clearFilter()."),void this.clearFilter();t=p.fuzzy?n.split("").reduce(function(e,t){return e+"[^"+t+"]*"+t}):C(n),r=new RegExp(".*"+t+".*","i"),s=new RegExp(C(n),"gi"),n=function(e){if(!e.title)return!1;var t,i=h?e.title:0<=(t=e.title).indexOf(">")?g("<div/>").html(t).text():t,n=!!r.test(i);return n&&p.highlight&&(e.titleWithHighlight=h?(l=i.replace(s,function(e){return"\ufff7"+e+"\ufff8"}),x(l).replace(/\uFFF7/g,"<mark>").replace(/\uFFF8/g,"</mark>")):i.replace(s,function(e){return"<mark>"+e+"</mark>"})),n}}return this.enableFilter=!0,this.lastFilterArgs=arguments,o=this.enableUpdate(!1),this.$div.addClass("fancytree-ext-filter"),f?this.$div.addClass("fancytree-ext-filter-hide"):this.$div.addClass("fancytree-ext-filter-dimm"),this.$div.toggleClass("fancytree-ext-filter-hide-expanders",!!p.hideExpanders),this.rootNode.subMatchCount=0,this.visit(function(e){delete e.match,delete e.titleWithHighlight,e.subMatchCount=0}),(i=this.getRootNode()._findDirectChild(m))&&i.remove(),c.autoCollapse=!1,this.visit(function(t){if(!y||null==t.children){var e=n(t),i=!1;if("skip"===e)return t.visit(function(e){e.match=!1},!0),"skip";e||!a&&"branch"!==e||!t.parent.match||(i=e=!0),e&&(d++,t.match=!0,t.visitParents(function(e){e!==t&&(e.subMatchCount+=1),!p.autoExpand||i||e.expanded||(e.setExpanded(!0,{noAnimation:!0,noEvents:!0,scrollIntoView:!1}),e._filterAutoExpanded=!0)},!0))}}),c.autoCollapse=u,0===d&&p.nodata&&f&&(i=p.nodata,g.isFunction(i)&&(i=i()),!0===i?i={}:"string"==typeof i&&(i={title:i}),i=g.extend({statusNodeType:"nodata",key:m,title:this.options.strings.noData},i),this.getRootNode().addNode(i).match=!0),this._callHook("treeStructureChanged",this,"applyFilter"),this.enableUpdate(o),d},g.ui.fancytree._FancytreeClass.prototype.filterNodes=function(e,t){return"boolean"==typeof t&&(t={leavesOnly:t},this.warn("Fancytree.filterNodes() leavesOnly option is deprecated since 2.9.0 / 2015-04-19. Use opts.leavesOnly instead.")),this._applyFilterImpl(e,!1,t)},g.ui.fancytree._FancytreeClass.prototype.filterBranches=function(e,t){return this._applyFilterImpl(e,!0,t)},g.ui.fancytree._FancytreeClass.prototype.clearFilter=function(){var t,e=this.getRootNode()._findDirectChild(m),i=this.options.escapeTitles,n=this.options.enhanceTitle,a=this.enableUpdate(!1);e&&e.remove(),delete this.rootNode.match,delete this.rootNode.subMatchCount,this.visit(function(e){e.match&&e.span&&(t=g(e.span).find(">span.fancytree-title"),i?t.text(e.title):t.html(e.title),n&&n({type:"enhanceTitle"},{node:e,$title:t})),delete e.match,delete e.subMatchCount,delete e.titleWithHighlight,e.$subMatchBadge&&(e.$subMatchBadge.remove(),delete e.$subMatchBadge),e._filterAutoExpanded&&e.expanded&&e.setExpanded(!1,{noAnimation:!0,noEvents:!0,scrollIntoView:!1}),delete e._filterAutoExpanded}),this.enableFilter=!1,this.lastFilterArgs=null,this.$div.removeClass("fancytree-ext-filter fancytree-ext-filter-dimm fancytree-ext-filter-hide"),this._callHook("treeStructureChanged",this,"clearFilter"),this.enableUpdate(a)},g.ui.fancytree._FancytreeClass.prototype.isFilterActive=function(){return!!this.enableFilter},g.ui.fancytree._FancytreeNodeClass.prototype.isMatched=function(){return!(this.tree.enableFilter&&!this.match)},g.ui.fancytree.registerExtension({name:"filter",version:"2.35.0",options:{autoApply:!0,autoExpand:!1,counter:!0,fuzzy:!1,hideExpandedCounter:!0,hideExpanders:!1,highlight:!0,leavesOnly:!1,nodata:!0,mode:"dimm"},nodeLoadChildren:function(e,t){var i=e.tree;return this._superApply(arguments).done(function(){i.enableFilter&&i.lastFilterArgs&&e.options.filter.autoApply&&i._applyFilterImpl.apply(i,i.lastFilterArgs)})},nodeSetExpanded:function(e,t,i){var n=e.node;return delete n._filterAutoExpanded,!t&&e.options.filter.hideExpandedCounter&&n.$subMatchBadge&&n.$subMatchBadge.show(),this._superApply(arguments)},nodeRenderStatus:function(e){var t,i=e.node,n=e.tree,a=e.options.filter,r=g(i.span).find("span.fancytree-title"),s=g(i[n.statusClassPropName]),l=e.options.enhanceTitle,o=e.options.escapeTitles;return t=this._super(e),s.length&&n.enableFilter&&(s.toggleClass("fancytree-match",!!i.match).toggleClass("fancytree-submatch",!!i.subMatchCount).toggleClass("fancytree-hide",!(i.match||i.subMatchCount)),!a.counter||!i.subMatchCount||i.isExpanded()&&a.hideExpandedCounter?i.$subMatchBadge&&i.$subMatchBadge.hide():(i.$subMatchBadge||(i.$subMatchBadge=g("<span class='fancytree-childcounter'/>"),g("span.fancytree-icon, span.fancytree-custom-icon",i.span).append(i.$subMatchBadge)),i.$subMatchBadge.show().text(i.subMatchCount)),!i.span||i.isEditing&&i.isEditing.call(i)||(i.titleWithHighlight?r.html(i.titleWithHighlight):o?r.text(i.title):r.html(i.title),l&&l({type:"enhanceTitle"},{node:i,$title:r}))),t}}),g.ui.fancytree});

/*! Extension 'jquery.fancytree.glyph.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(l){"use strict";var d=l.ui.fancytree,a={awesome3:{_addClass:"",checkbox:"icon-check-empty",checkboxSelected:"icon-check",checkboxUnknown:"icon-check icon-muted",dragHelper:"icon-caret-right",dropMarker:"icon-caret-right",error:"icon-exclamation-sign",expanderClosed:"icon-caret-right",expanderLazy:"icon-angle-right",expanderOpen:"icon-caret-down",loading:"icon-refresh icon-spin",nodata:"icon-meh",noExpander:"",radio:"icon-circle-blank",radioSelected:"icon-circle",doc:"icon-file-alt",docOpen:"icon-file-alt",folder:"icon-folder-close-alt",folderOpen:"icon-folder-open-alt"},awesome4:{_addClass:"fa",checkbox:"fa-square-o",checkboxSelected:"fa-check-square-o",checkboxUnknown:"fa-square fancytree-helper-indeterminate-cb",dragHelper:"fa-arrow-right",dropMarker:"fa-long-arrow-right",error:"fa-warning",expanderClosed:"fa-caret-right",expanderLazy:"fa-angle-right",expanderOpen:"fa-caret-down",loading:{html:"<span class='fa fa-spinner fa-pulse' />"},nodata:"fa-meh-o",noExpander:"",radio:"fa-circle-thin",radioSelected:"fa-circle",doc:"fa-file-o",docOpen:"fa-file-o",folder:"fa-folder-o",folderOpen:"fa-folder-open-o"},awesome5:{_addClass:"",checkbox:"far fa-square",checkboxSelected:"far fa-check-square",checkboxUnknown:"fas fa-square fancytree-helper-indeterminate-cb",radio:"far fa-circle",radioSelected:"fas fa-circle",radioUnknown:"far fa-dot-circle",dragHelper:"fas fa-arrow-right",dropMarker:"fas fa-long-arrow-alt-right",error:"fas fa-exclamation-triangle",expanderClosed:"fas fa-caret-right",expanderLazy:"fas fa-angle-right",expanderOpen:"fas fa-caret-down",loading:"fas fa-spinner fa-pulse",nodata:"far fa-meh",noExpander:"",doc:"far fa-file",docOpen:"far fa-file",folder:"far fa-folder",folderOpen:"far fa-folder-open"},bootstrap3:{_addClass:"glyphicon",checkbox:"glyphicon-unchecked",checkboxSelected:"glyphicon-check",checkboxUnknown:"glyphicon-expand fancytree-helper-indeterminate-cb",dragHelper:"glyphicon-play",dropMarker:"glyphicon-arrow-right",error:"glyphicon-warning-sign",expanderClosed:"glyphicon-menu-right",expanderLazy:"glyphicon-menu-right",expanderOpen:"glyphicon-menu-down",loading:"glyphicon-refresh fancytree-helper-spin",nodata:"glyphicon-info-sign",noExpander:"",radio:"glyphicon-remove-circle",radioSelected:"glyphicon-ok-circle",doc:"glyphicon-file",docOpen:"glyphicon-file",folder:"glyphicon-folder-close",folderOpen:"glyphicon-folder-open"},material:{_addClass:"material-icons",checkbox:{text:"check_box_outline_blank"},checkboxSelected:{text:"check_box"},checkboxUnknown:{text:"indeterminate_check_box"},dragHelper:{text:"play_arrow"},dropMarker:{text:"arrow-forward"},error:{text:"warning"},expanderClosed:{text:"chevron_right"},expanderLazy:{text:"last_page"},expanderOpen:{text:"expand_more"},loading:{text:"autorenew",addClass:"fancytree-helper-spin"},nodata:{text:"info"},noExpander:{text:""},radio:{text:"radio_button_unchecked"},radioSelected:{text:"radio_button_checked"},doc:{text:"insert_drive_file"},docOpen:{text:"insert_drive_file"},folder:{text:"folder"},folderOpen:{text:"folder_open"}}};function i(e,n,r,a){var o=r.map,t=o[a],c=l(e),d=c.find(".fancytree-childcounter"),i=n+" "+(o._addClass||"");"string"==typeof t?(e.innerHTML="",c.attr("class",i+" "+t).append(d)):t&&(t.text?e.textContent=""+t.text:t.html?e.innerHTML=t.html:e.innerHTML="",c.attr("class",i+" "+(t.addClass||"")).append(d))}return l.ui.fancytree.registerExtension({name:"glyph",version:"2.35.0",options:{preset:null,map:{}},treeInit:function(e){var n=e.tree,r=e.options.glyph;r.preset?(d.assert(!!a[r.preset],"Invalid value for `options.glyph.preset`: "+r.preset),r.map=l.extend({},a[r.preset],r.map)):n.warn("ext-glyph: missing `preset` option."),this._superApply(arguments),n.$container.addClass("fancytree-ext-glyph")},nodeRenderStatus:function(e){var n,r,a,o=e.node,t=l(o.span),c=e.options.glyph;return r=this._super(e),o.isRootNode()||((a=t.children("span.fancytree-expander").get(0))&&i(a,"fancytree-expander",c,o.expanded&&o.hasChildren()?"expanderOpen":o.isUndefined()?"expanderLazy":o.hasChildren()?"expanderClosed":"noExpander"),(a=o.tr?l("td",o.tr).find("span.fancytree-checkbox").get(0):t.children("span.fancytree-checkbox").get(0))&&(n=d.evalOption("checkbox",o,o,c,!1),o.parent&&o.parent.radiogroup||"radio"===n?i(a,"fancytree-checkbox fancytree-radio",c,o.selected?"radioSelected":"radio"):i(a,"fancytree-checkbox",c,o.selected?"checkboxSelected":o.partsel?"checkboxUnknown":"checkbox")),(a=t.children("span.fancytree-icon").get(0))&&i(a,"fancytree-icon",c,o.statusNodeType?o.statusNodeType:o.folder?o.expanded&&o.hasChildren()?"folderOpen":"folder":o.expanded?"docOpen":"doc")),r},nodeSetStatus:function(e,n,r,a){var o,t,c=e.options.glyph,d=e.node;return o=this._superApply(arguments),"error"!==n&&"loading"!==n&&"nodata"!==n||(d.parent?(t=l("span.fancytree-expander",d.span).get(0))&&i(t,"fancytree-expander",c,n):(t=l(".fancytree-statusnode-"+n,d[this.nodeContainerAttrName]).find("span.fancytree-icon").get(0))&&i(t,"fancytree-icon",c,n)),o}}),l.ui.fancytree});

/*! Extension 'jquery.fancytree.gridnav.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree","./jquery.fancytree.table"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree.table"),module.exports=e(require("jquery"))):e(jQuery)}(function(l){"use strict";var p=l.ui.keyCode,s={text:[p.UP,p.DOWN],checkbox:[p.UP,p.DOWN,p.LEFT,p.RIGHT],link:[p.UP,p.DOWN,p.LEFT,p.RIGHT],radiobutton:[p.UP,p.DOWN,p.LEFT,p.RIGHT],"select-one":[p.LEFT,p.RIGHT],"select-multiple":[p.LEFT,p.RIGHT]};function f(e,t){var n,i=null,r=0;return e.children().each(function(){if(t<=r)return i=l(this),!1;n=l(this).prop("colspan"),r+=n||1}),i}function u(e,t){var n,i,r,o,a,s,u=e.closest("td"),c=null;switch(t){case p.LEFT:c=u.prev();break;case p.RIGHT:c=u.next();break;case p.UP:case p.DOWN:for(n=u.parent(),r=n,a=u.get(0),s=0,r.children().each(function(){if(this===a)return!1;o=l(this).prop("colspan"),s+=o||1}),i=s;(n=t===p.UP?n.prev():n.next()).length&&(n.is(":hidden")||!(c=f(n,i))||!c.find(":input,a").length););}return c}return l.ui.fancytree.registerExtension({name:"gridnav",version:"2.35.0",options:{autofocusInput:!1,handleCursorKeys:!0},treeInit:function(i){this._requireExtension("table",!0,!0),this._superApply(arguments),this.$container.addClass("fancytree-ext-gridnav"),this.$container.on("focusin",function(e){var t,n=l.ui.fancytree.getNode(e.target);n&&!n.isActive()&&(t=i.tree._makeHookContext(n,e),i.tree._callHook("nodeSetActive",t,!0))})},nodeSetActive:function(e,t,n){var i=e.options.gridnav,r=e.node,o=e.originalEvent||{},a=l(o.target).is(":input");t=!1!==t,this._superApply(arguments),t&&(e.options.titlesTabbable?(a||(l(r.span).find("span.fancytree-title").focus(),r.setFocus()),e.tree.$container.attr("tabindex","-1")):i.autofocusInput&&!a&&l(r.tr||r.span).find(":input:enabled").first().focus())},nodeKeydown:function(e){var t,n,i,r=e.options.gridnav,o=e.originalEvent,a=l(o.target);return a.is(":input:enabled")?t=a.prop("type"):a.is("a")&&(t="link"),t&&r.handleCursorKeys?!((n=s[t])&&0<=l.inArray(o.which,n)&&(i=u(a,o.which))&&i.length)||(i.find(":input:enabled,a").focus(),!1):this._superApply(arguments)}}),l.ui.fancytree});

/*! Extension 'jquery.fancytree.multi.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(o){"use strict";return o.ui.fancytree.registerExtension({name:"multi",version:"2.35.0",options:{allowNoSelect:!1,mode:"sameParent"},treeInit:function(e){this._superApply(arguments),this.$container.addClass("fancytree-ext-multi"),1===e.options.selectMode&&o.error("Fancytree ext-multi: selectMode: 1 (single) is not compatible.")},nodeClick:function(e){var t=e.tree,i=e.node,r=t.getActiveNode()||t.getFirstChild(),n="checkbox"===e.targetType,c="expander"===e.targetType;switch(o.ui.fancytree.eventToString(e.originalEvent)){case"click":if(c)break;n||(t.selectAll(!1),i.setSelected());break;case"shift+click":t.visitRows(function(e){if(e.setSelected(),e===i)return!1},{start:r,reverse:r.isBelowOf(i)});break;case"ctrl+click":case"meta+click":return void i.toggleSelected()}return this._superApply(arguments)},nodeKeydown:function(e){var t=e.tree,i=e.node,r=e.originalEvent;switch(o.ui.fancytree.eventToString(r)){case"up":case"down":t.selectAll(!1),i.navigate(r.which,!0),t.getActiveNode().setSelected();break;case"shift+up":case"shift+down":i.navigate(r.which,!0),t.getActiveNode().setSelected()}return this._superApply(arguments)}}),o.ui.fancytree});

/*! Extension 'jquery.fancytree.persist.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(y){"use strict";var t=null,o=window.localStorage?{get:function(e){return window.localStorage.getItem(e)},set:function(e,t){window.localStorage.setItem(e,t)},remove:function(e){window.localStorage.removeItem(e)}}:null,i=window.sessionStorage?{get:function(e){return window.sessionStorage.getItem(e)},set:function(e,t){window.sessionStorage.setItem(e,t)},remove:function(e){window.sessionStorage.removeItem(e)}}:null,s=y.ui.fancytree.assert,u="active",v="expanded",p="focus",f="selected";return"function"==typeof Cookies?t={get:Cookies.get,set:function(e,t){Cookies.set(e,t,this.options.persist.cookie)},remove:Cookies.remove}:y&&"function"==typeof y.cookie&&(t={get:y.cookie,set:function(e,t){y.cookie.set(e,t,this.options.persist.cookie)},remove:y.removeCookie}),y.ui.fancytree._FancytreeClass.prototype.clearPersistData=function(e){var t=this.ext.persist,o=t.cookiePrefix;0<=(e=e||"active expanded focus selected").indexOf(u)&&t._data(o+u,null),0<=e.indexOf(v)&&t._data(o+v,null),0<=e.indexOf(p)&&t._data(o+p,null),0<=e.indexOf(f)&&t._data(o+f,null)},y.ui.fancytree._FancytreeClass.prototype.clearCookies=function(e){return this.warn("'tree.clearCookies()' is deprecated since v2.27.0: use 'clearPersistData()' instead."),this.clearPersistData(e)},y.ui.fancytree._FancytreeClass.prototype.getPersistData=function(){var e=this.ext.persist,t=e.cookiePrefix,o=e.cookieDelimiter,i={};return i[u]=e._data(t+u),i[v]=(e._data(t+v)||"").split(o),i[f]=(e._data(t+f)||"").split(o),i[p]=e._data(t+p),i},y.ui.fancytree.registerExtension({name:"persist",version:"2.35.0",options:{cookieDelimiter:"~",cookiePrefix:void 0,cookie:{raw:!1,expires:"",path:"",domain:"",secure:!1},expandLazy:!1,expandOpts:void 0,fireActivate:!0,overrideSource:!0,store:"auto",types:"active expanded focus selected"},_data:function(e,t){var o=this._local.store;if(void 0===t)return o.get.call(this,e);null===t?o.remove.call(this,e):o.set.call(this,e,t)},_appendKey:function(e,t,o){t=""+t;var i=this._local,s=this.options.persist.cookieDelimiter,r=i.cookiePrefix+e,n=i._data(r),a=n?n.split(s):[],c=y.inArray(t,a);0<=c&&a.splice(c,1),o&&a.push(t),i._data(r,a.join(s))},treeInit:function(e){var a=e.tree,c=e.options,d=this._local,l=this.options.persist;return d.cookiePrefix=l.cookiePrefix||"fancytree-"+a._id+"-",d.storeActive=0<=l.types.indexOf(u),d.storeExpanded=0<=l.types.indexOf(v),d.storeSelected=0<=l.types.indexOf(f),d.storeFocus=0<=l.types.indexOf(p),d.store=null,"auto"===l.store&&(l.store=o?"local":"cookie"),y.isPlainObject(l.store)?d.store=l.store:"cookie"===l.store?d.store=t:"local"===l.store?d.store="local"===l.store?o:i:"session"===l.store&&(d.store="local"===l.store?o:i),s(d.store,"Need a valid store."),a.$div.on("fancytreeinit",function(e){if(!1!==a._triggerTreeEvent("beforeRestore",null,{})){var t,o,i,s,r=d._data(d.cookiePrefix+p),n=!1===l.fireActivate;t=d._data(d.cookiePrefix+v),i=t&&t.split(l.cookieDelimiter),(d.storeExpanded?function e(t,o,i,s,r){var n,a,c,d,l=!1,u=t.options.persist.expandOpts,p=[],f=[];for(i=i||[],r=r||y.Deferred(),n=0,c=i.length;n<c;n++)a=i[n],(d=t.getNodeByKey(a))?s&&d.isUndefined()?(l=!0,t.debug("_loadLazyNodes: "+d+" is lazy: loading..."),"expand"===s?p.push(d.setExpanded(!0,u)):p.push(d.load())):(t.debug("_loadLazyNodes: "+d+" already loaded."),d.setExpanded(!0,u)):(f.push(a),t.debug("_loadLazyNodes: "+d+" was not yet found."));return y.when.apply(y,p).always(function(){if(l&&0<f.length)e(t,o,f,s,r);else{if(f.length)for(t.warn("_loadLazyNodes: could not load those keys: ",f),n=0,c=f.length;n<c;n++)a=i[n],o._appendKey(v,i[n],!1);r.resolve()}}),r}(a,d,i,!!l.expandLazy&&"expand",null):(new y.Deferred).resolve()).done(function(){if(d.storeSelected){if(t=d._data(d.cookiePrefix+f))for(i=t.split(l.cookieDelimiter),o=0;o<i.length;o++)(s=a.getNodeByKey(i[o]))?(void 0===s.selected||l.overrideSource&&!1===s.selected)&&(s.selected=!0,s.renderStatus()):d._appendKey(f,i[o],!1);3===a.options.selectMode&&a.visit(function(e){if(e.selected)return e.fixSelection3AfterClick(),"skip"})}d.storeActive&&(!(t=d._data(d.cookiePrefix+u))||!c.persist.overrideSource&&a.activeNode||(s=a.getNodeByKey(t))&&(s.debug("persist: set active",t),s.setActive(!0,{noFocus:!0,noEvents:n}))),d.storeFocus&&r&&(s=a.getNodeByKey(r))&&(a.options.titlesTabbable?y(s.span).find(".fancytree-title").focus():y(a.$container).focus()),a._triggerTreeEvent("restore",null,{})})}}),this._superApply(arguments)},nodeSetActive:function(e,t,o){var i,s=this._local;return t=!1!==t,i=this._superApply(arguments),s.storeActive&&s._data(s.cookiePrefix+u,this.activeNode?this.activeNode.key:null),i},nodeSetExpanded:function(e,t,o){var i,s=e.node,r=this._local;return t=!1!==t,i=this._superApply(arguments),r.storeExpanded&&r._appendKey(v,s.key,t),i},nodeSetFocus:function(e,t){var o,i=this._local;return t=!1!==t,o=this._superApply(arguments),i.storeFocus&&i._data(i.cookiePrefix+p,this.focusNode?this.focusNode.key:null),o},nodeSetSelected:function(e,t,o){var i,s,r=e.tree,n=e.node,a=this._local;return t=!1!==t,i=this._superApply(arguments),a.storeSelected&&(3===r.options.selectMode?(s=(s=y.map(r.getSelectedNodes(!0),function(e){return e.key})).join(e.options.persist.cookieDelimiter),a._data(a.cookiePrefix+f,s)):a._appendKey(f,n.key,n.selected)),i}}),y.ui.fancytree});

/*! Extension 'jquery.fancytree.table.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(b){"use strict";function S(e,t){t=t||"",e||b.error("Assertion failed "+t)}function N(e,n){e.visit(function(e){var t=e.tr;if(t&&(t.style.display=e.hide||!n?"none":""),!e.expanded)return"skip"})}return b.ui.fancytree.registerExtension({name:"table",version:"2.35.0",options:{checkboxColumnIdx:null,indentation:16,mergeStatusColumns:!0,nodeColumnIdx:0},treeInit:function(e){var t,n,r,o,d=e.tree,s=e.options,i=s.table,a=d.widget.element;if(null!=i.customStatus&&(null==s.renderStatusColumns?(d.warn("The 'customStatus' option is deprecated since v2.15.0. Use 'renderStatusColumns' instead."),s.renderStatusColumns=i.customStatus):b.error("The 'customStatus' option is deprecated since v2.15.0. Use 'renderStatusColumns' only instead.")),s.renderStatusColumns&&!0===s.renderStatusColumns&&(s.renderStatusColumns=s.renderColumns),a.addClass("fancytree-container fancytree-ext-table"),(o=a.find(">tbody")).length||(a.find(">tr").length&&b.error("Expected table > tbody > tr. If you see this please open an issue."),o=b("<tbody>").appendTo(a)),d.tbody=o[0],d.columnCount=b("thead >tr",a).last().find(">th",a).length,(r=o.children("tr").first()).length)n=r.children("td").length,d.columnCount&&n!==d.columnCount&&(d.warn("Column count mismatch between thead ("+d.columnCount+") and tbody ("+n+"): using tbody."),d.columnCount=n),r=r.clone();else for(S(1<=d.columnCount,"Need either <thead> or <tbody> with <td> elements to determine column count."),r=b("<tr />"),t=0;t<d.columnCount;t++)r.append("<td />");r.find(">td").eq(i.nodeColumnIdx).html("<span class='fancytree-node' />"),s.aria&&(r.attr("role","row"),r.find("td").attr("role","gridcell")),d.rowFragment=document.createDocumentFragment(),d.rowFragment.appendChild(r.get(0)),o.empty(),d.statusClassPropName="tr",d.ariaPropName="tr",this.nodeContainerAttrName="tr",d.$container=a,this._superApply(arguments),b(d.rootNode.ul).remove(),d.rootNode.ul=null,this.$container.attr("tabindex",s.tabindex),s.aria&&d.$container.attr("role","treegrid").attr("aria-readonly",!0)},nodeRemoveChildMarkup:function(e){e.node.visit(function(e){e.tr&&(b(e.tr).remove(),e.tr=null)})},nodeRemoveMarkup:function(e){var t=e.node;t.tr&&(b(t.tr).remove(),t.tr=null),this.nodeRemoveChildMarkup(e)},nodeRender:function(e,t,n,r,o){var d,s,i,a,l,u,c,p,h=e.tree,m=e.node,f=e.options,y=!m.parent;if(!1!==h._enableUpdate){if(o||(e.hasCollapsedParents=m.parent&&!m.parent.expanded),!y)if(m.tr&&t&&this.nodeRemoveMarkup(e),m.tr)t?this.nodeRenderTitle(e):this.nodeRenderStatus(e);else{if(e.hasCollapsedParents&&!n)return;l=h.rowFragment.firstChild.cloneNode(!0),S(u=function(e){var t,n,r=e.parent,o=r?r.children:null;if(o&&1<o.length&&o[0]!==e)for(S((n=o[b.inArray(e,o)-1]).tr);n.children&&n.children.length&&(t=n.children[n.children.length-1]).tr;)n=t;else n=r;return n}(m)),!0===r&&o?l.style.display="none":n&&e.hasCollapsedParents&&(l.style.display="none"),u.tr?(g=u.tr,x=l,g.parentNode.insertBefore(x,g.nextSibling)):(S(!u.parent,"prev. row must have a tr, or be system root"),C=h.tbody,v=l,C.insertBefore(v,C.firstChild)),m.tr=l,m.key&&f.generateIds&&(m.tr.id=f.idPrefix+m.key),(m.tr.ftnode=m).span=b("span.fancytree-node",m.tr).get(0),this.nodeRenderTitle(e),f.createNode&&f.createNode.call(h,{type:"createNode"},e)}var C,v,g,x;if(f.renderNode&&f.renderNode.call(h,{type:"renderNode"},e),(d=m.children)&&(y||n||m.expanded))for(i=0,a=d.length;i<a;i++)(p=b.extend({},e,{node:d[i]})).hasCollapsedParents=p.hasCollapsedParents||!m.expanded,this.nodeRender(p,t,n,r,!0);d&&!o&&(c=m.tr||null,s=h.tbody.firstChild,m.visit(function(e){if(e.tr){if(e.parent.expanded||"none"===e.tr.style.display||(e.tr.style.display="none",N(e,!1)),e.tr.previousSibling!==c){m.debug("_fixOrder: mismatch at node: "+e);var t=c?c.nextSibling:s;h.tbody.insertBefore(e.tr,t)}c=e.tr}}))}},nodeRenderTitle:function(e,t){var n,r,o=e.tree,d=e.node,s=e.options,i=d.isStatusNode();return r=this._super(e,t),d.isRootNode()||(s.checkbox&&!i&&null!=s.table.checkboxColumnIdx&&(n=b("span.fancytree-checkbox",d.span),b(d.tr).find("td").eq(+s.table.checkboxColumnIdx).html(n)),this.nodeRenderStatus(e),i?s.renderStatusColumns?s.renderStatusColumns.call(o,{type:"renderStatusColumns"},e):s.table.mergeStatusColumns&&d.isTopLevel()&&b(d.tr).find(">td").eq(0).prop("colspan",o.columnCount).text(d.title).addClass("fancytree-status-merged").nextAll().remove():s.renderColumns&&s.renderColumns.call(o,{type:"renderColumns"},e)),r},nodeRenderStatus:function(e){var t,n=e.node,r=e.options;this._super(e),b(n.tr).removeClass("fancytree-node"),t=(n.getLevel()-1)*r.table.indentation,r.rtl?b(n.span).css({paddingRight:t+"px"}):b(n.span).css({paddingLeft:t+"px"})},nodeSetExpanded:function(t,n,r){if(n=!1!==n,t.node.expanded&&n||!t.node.expanded&&!n)return this._superApply(arguments);var o=new b.Deferred,e=b.extend({},r,{noEvents:!0,noAnimation:!0});function d(e){N(t.node,n),e?n&&t.options.autoScroll&&!r.noAnimation&&t.node.hasChildren()?t.node.getLastChild().scrollIntoView(!0,{topNode:t.node}).always(function(){r.noEvents||t.tree._triggerNodeEvent(n?"expand":"collapse",t),o.resolveWith(t.node)}):(r.noEvents||t.tree._triggerNodeEvent(n?"expand":"collapse",t),o.resolveWith(t.node)):(r.noEvents||t.tree._triggerNodeEvent(n?"expand":"collapse",t),o.rejectWith(t.node))}return r=r||{},this._super(t,n,e).done(function(){d(!0)}).fail(function(){d(!1)}),o.promise()},nodeSetStatus:function(e,t,n,r){if("ok"===t){var o=e.node,d=o.children?o.children[0]:null;d&&d.isStatusNode()&&b(d.tr).remove()}return this._superApply(arguments)},treeClear:function(e){return this.nodeRemoveChildMarkup(this._makeHookContext(this.rootNode)),this._superApply(arguments)},treeDestroy:function(e){return this.$container.find("tbody").empty(),this.$source&&this.$source.removeClass("fancytree-helper-hidden"),this._superApply(arguments)}}),b.ui.fancytree});

/*! Extension 'jquery.fancytree.themeroller.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(l){"use strict";return l.ui.fancytree.registerExtension({name:"themeroller",version:"2.35.0",options:{activeClass:"ui-state-active",addClass:"ui-corner-all",focusClass:"ui-state-focus",hoverClass:"ui-state-hover",selectedClass:"ui-state-highlight"},treeInit:function(e){var s=e.widget.element,a=e.options.themeroller;this._superApply(arguments),"TABLE"===s[0].nodeName?(s.addClass("ui-widget ui-corner-all"),s.find(">thead tr").addClass("ui-widget-header"),s.find(">tbody").addClass("ui-widget-conent")):s.addClass("ui-widget ui-widget-content ui-corner-all"),s.on("mouseenter mouseleave",".fancytree-node",function(e){var s=l.ui.fancytree.getNode(e.target),t="mouseenter"===e.type;l(s.tr?s.tr:s.span).toggleClass(a.hoverClass+" "+a.addClass,t)})},treeDestroy:function(e){this._superApply(arguments),e.widget.element.removeClass("ui-widget ui-widget-content ui-corner-all")},nodeRenderStatus:function(e){var s={},t=e.node,a=l(t.tr?t.tr:t.span),i=e.options.themeroller;this._super(e),s[i.activeClass]=!1,s[i.focusClass]=!1,s[i.selectedClass]=!1,t.isActive()&&(s[i.activeClass]=!0),t.hasFocus()&&(s[i.focusClass]=!0),t.isSelected()&&!t.isActive()&&(s[i.selectedClass]=!0),a.toggleClass(i.activeClass,s[i.activeClass]),a.toggleClass(i.focusClass,s[i.focusClass]),a.toggleClass(i.selectedClass,s[i.selectedClass]),a.addClass(i.addClass)}}),l.ui.fancytree});

/*! Extension 'jquery.fancytree.wide.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(d){"use strict";var y=/^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;function m(e,t){var a=d("#"+(e="fancytree-style-"+e));if(!t)return a.remove(),null;a.length||(a=d("<style />").attr("id",e).addClass("fancytree-style").prop("type","text/css").appendTo("head"));try{a.html(t)}catch(e){a[0].styleSheet.cssText=t}return a}function _(e,t,a,n,l,i){var s,r="#"+e+" span.fancytree-level-",c=[];for(s=0;s<t;s++)c.push(r+(s+1)+" span.fancytree-title { padding-left: "+(s*a+n)+i+"; }");return c.push("#"+e+" div.ui-effects-wrapper ul li span.fancytree-title, #"+e+" li.fancytree-animating span.fancytree-title { padding-left: "+l+i+"; position: static; width: auto; }"),c.join("\n")}return d.ui.fancytree.registerExtension({name:"wide",version:"2.35.0",options:{iconWidth:null,iconSpacing:null,labelSpacing:null,levelOfs:null},treeCreate:function(e){this._superApply(arguments),this.$container.addClass("fancytree-ext-wide");var t,a,n,l,i,s=e.options.wide,r=d("<li id='fancytreeTemp'><span class='fancytree-node'><span class='fancytree-icon' /><span class='fancytree-title' /></span><ul />").appendTo(e.tree.$container),c=r.find(".fancytree-icon"),o=r.find("ul"),p=s.iconSpacing||c.css("margin-left"),u=s.iconWidth||c.css("width"),f=s.labelSpacing||"3px",h=s.levelOfs||o.css("padding-left");r.remove(),a=p.match(y)[2],p=parseFloat(p,10),n=f.match(y)[2],f=parseFloat(f,10),l=u.match(y)[2],u=parseFloat(u,10),i=h.match(y)[2],a===l&&i===l&&n===l||d.error("iconWidth, iconSpacing, and levelOfs must have the same css measure unit"),this._local.measureUnit=l,this._local.levelOfs=parseFloat(h),this._local.lineOfs=(1+(e.options.checkbox?1:0)+(!1===e.options.icon?0:1))*(u+p)+p,this._local.labelOfs=f,this._local.maxDepth=10,m(t=this.$container.uniqueId().attr("id"),_(t,this._local.maxDepth,this._local.levelOfs,this._local.lineOfs,this._local.labelOfs,this._local.measureUnit))},treeDestroy:function(e){return m(this.$container.attr("id"),null),this._superApply(arguments)},nodeRenderStatus:function(e){var t,a,n=e.node,l=n.getLevel();return a=this._super(e),l>this._local.maxDepth&&(t=this.$container.attr("id"),this._local.maxDepth*=2,n.debug("Define global ext-wide css up to level "+this._local.maxDepth),m(t,_(t,this._local.maxDepth,this._local.levelOfs,this._local.lineOfs,this._local.labelSpacing,this._local.measureUnit))),d(n.span).addClass("fancytree-level-"+l),a}}),d.ui.fancytree});
// Value returned by `require('jquery.fancytree')`
return $.ui.fancytree;
}));  // End of closure
