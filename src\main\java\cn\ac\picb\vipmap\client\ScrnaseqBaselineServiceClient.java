package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnaseq.client.ScrnaseqBaselineServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqBaselineServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "scrnaseq-service", contextId = "baseline", fallback = ScrnaseqBaselineServiceClientFallback.class)
public interface ScrnaseqBaselineServiceClient extends ScrnaseqBaselineServiceApi {

}
