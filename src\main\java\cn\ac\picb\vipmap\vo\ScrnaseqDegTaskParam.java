package cn.ac.picb.vipmap.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ScrnaseqDegTaskParam {

    @NotBlank
    private String baselineId;

    @NotBlank
    private String taskName;

    private String degMode;

    private List<String> cluster1List;

    private List<String> cluster2List;

    private Double cutfc;

    @Min(0)
    @Max(1)
    private Double cutp;

    private String annoDb;
}
