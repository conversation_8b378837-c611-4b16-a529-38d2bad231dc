package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.strnaseq.dto.StrnaseqDTO;
import cn.ac.picb.strnaseq.enums.StrnaseqTaskStatus;
import cn.ac.picb.strnaseq.po.StrnaseqTaskPO;
import cn.ac.picb.strnaseq.vo.ChartParamVO;
import cn.ac.picb.strnaseq.vo.StrnaseqTaskQueryVO;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.StrnaseqService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.StrnaseqTaskParam;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:21
 */
@Controller
@RequestMapping("/analysis/strnaseq")
@RequiredArgsConstructor
public class StrnaseqController {

    private final AppProperties appProperties;
    private final StrnaseqService strnaseqService;


    @RequestMapping("/form")
    public String form() {
        return "strnaseq/form";
    }

    @PostMapping("/save")
    @ResponseBody
    public CommonResult<StrnaseqTaskPO> saveGenomics(CurrentUser user, @Validated StrnaseqTaskParam param) {
        StrnaseqTaskPO task = strnaseqService.save(user, param);
        return success(task);
    }

    @GetMapping("/list")
    public String list() {
        return "strnaseq/list";
    }

    @PostMapping("/list")
    @ResponseBody
    public CommonResult<PageResult<StrnaseqTaskPO>> list(CurrentUser user, @ModelAttribute("query") StrnaseqTaskQueryVO queryVO, @ModelAttribute PageParam pageParam, Model model) {
        PageResult<StrnaseqTaskPO> page = strnaseqService.findPage(user, queryVO, pageParam);
        return success(page);
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        strnaseqService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/taskDetail")
    public String taskDetail(String id, Model model) {
        StrnaseqDTO vo = strnaseqService.findTaskDtoById(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = StrnaseqTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "strnaseq/detail";
    }

    @RequestMapping("/getImage")
    public ResponseEntity<byte[]> getImage(String taskId, String path) {
        return strnaseqService.getImage(taskId, path);
    }

    @ResponseBody
    @SneakyThrows
    @RequestMapping("/downloadFile")
    public void downloadFile(String taskId, String path) {
        Response response = strnaseqService.downloadFile(taskId, path);
        ResponseUtil.download(response);
    }

    @ResponseBody
    @RequestMapping("/getChartData")
    public CommonResult<Object> getChartData(ChartParamVO vo) {
        Object data = strnaseqService.getChartData(vo);
        return success(data);
    }


}
