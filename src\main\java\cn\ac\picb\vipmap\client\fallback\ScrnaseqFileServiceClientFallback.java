package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.scrnaseq.vo.AnnotationTabRowVO;
import cn.ac.picb.scrnaseq.vo.ChartParamVO;
import cn.ac.picb.scrnaseq.vo.SelectedTaskVO;
import cn.ac.picb.vipmap.client.ScrnaseqFileServiceClient;
import feign.Response;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaseqFileServiceClientFallback implements ScrnaseqFileServiceClient {

    private final static String SERVER_NAME = "scrnaseq-service";


    @Override
    public CommonResult<List<AnnotationTabRowVO>> findBaselineAnnotationTabRow(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<String>> findBaselineAnnotationTabClusters(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadReportResult(String s, String s1) {
        return null;
    }

    @Override
    public CommonResult<Object> getGenomicsChartData(ChartParamVO chartParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Object> getBaselineChartData(ChartParamVO chartParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Object> getPagaChartData(ChartParamVO chartParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Object> getDegChartData(ChartParamVO chartParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Object> getWgcnaChartData(ChartParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Object> getGenesChartData(ChartParamVO chartParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Map<String, List<String>>> getGeneNamesByParams(SelectedTaskVO selectedTaskVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<List<Double>>> getGeneDataByParams(SelectedTaskVO selectedTaskVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public ResponseEntity<byte[]> getGenesStackedViolinPdf(String s) {
        return null;
    }

    @Override
    public Response commonDownload(String fileEnum, String taskId, String sampleName, String displayName) {
        return null;
    }

    @Override
    public Response downloadLogs() {
        return null;
    }

}
