<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Cluster baseline</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <th:block th:switch="${taskVo.baselineTask.status}">
                        <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Prepared</p>
                        </div>
                        <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="2">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis done</p>
                        </div>
                        <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis Error</p>
                        </div>
                    </th:block>
                    <div class="form-group-box">
                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                        <div class="collapse show" id="coll-1">
                            <div class="pl-4 pt-2">
                                <div class="result-box">
                                    <div class="table-responsive mt-2">
                                        <table class="table table-bordered table-sm table-center table-middle mb-1">
                                            <thead>
                                            <tr class="thead-light">
                                                <th>Cluster Genomics ID</th>
                                                <th>Start time</th>
                                                <th>Status time</th>
                                                <th>Consuming</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td th:text="${taskVo.genomicsTask.taskId}"></td>
                                                <td th:text="${#dates.format(taskVo.genomicsTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${#dates.format(taskVo.genomicsTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${taskVo.genomicsTask.useTime}">26m55s</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-3 col-lg-2 col-md-3 col-form-label pr-0">Number of principle components</label>
                                        <div class="col-xl-2 col-lg-2 col-md-4">
                                            <span class="text-primary" th:text="${taskVo.baselineTask.selectPca}">10</span>
                                        </div>
                                    </div>
                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Annotation Method</label>
                                        <div class="col-xl-2 col-lg-2 col-md-4">
                                            <span class="text-primary" th:text="${taskVo.baselineTask.annMethod}">Elham A, et al</span>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-sm table-center table-middle">
                                            <thead>
                                            <tr class="thead-light">
                                                <th>Cell type</th>
                                                <th>Gene marker</th>
                                            </tr>
                                            </thead>
                                            <tbody th:with="types=${#strings.listSplit(taskVo.baselineTask.cellType,';')}, markers=${#strings.listSplit(taskVo.baselineTask.geneMarker,';')}">
                                            <tr th:each="idx: ${#numbers.sequence(0, types.size()-1)}">
                                                <td th:text="${types.get(idx)}">B Cells</td>
                                                <td>
                                                    <span class="font-12" th:text="${markers.get(idx)}">CD19</span>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group-box" th:if="${taskVo.baselineTask.status == 2}">
                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                        <div class="collapse show" id="coll-2">
                            <div class="tool-content">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <ul class="nav nav-pills mb-3">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-toggle="pill" href="#tab-1">cell type</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" data-toggle="pill" href="#tab-2">cluster</a>
                                            </li>
                                        </ul>
                                        <div class="tab-content">
                                            <div class="tab-pane fade show active" id="tab-1">
                                                <div class="d-flex align-items-center mb-2">
                                                    <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">t-SNE</h6>
                                                    <!--                                                    <a th:href="@{/analysis/scrnaseq/common/download(code='tsne',runName =${taskVo.baselineTask.taskId})}"><i class="fa fa-download text-primary"></i></a>-->
                                                </div>
                                                <div id="chart-061" style="width: 100%;height: 500px"></div>
                                            </div>
                                            <div class="tab-pane fade" id="tab-2">
                                                <div class="d-flex align-items-center mb-2">
                                                    <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">t-SNE for cluster</h6>
                                                    <!--                                                    <a th:href="@{/analysis/scrnaseq/common/download(code='tsne',runName =${taskVo.baselineTask.taskId})}"><i class="fa fa-download text-primary"></i></a>-->
                                                </div>
                                                <div id="chart-062" style="width: 930px;height: 500px"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="table-primary">
                                            <td>Cluster</td>
                                            <td>Gene marker</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="item : ${rows}">
                                            <td th:text="${item.cluster}"></td>
                                            <td th:text="${item.cellType}"></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">correlation for clusters</h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='correlation_for_cluster',runName =${taskVo.baselineTask.taskId})}">-->
                                            <!--                                                <i class="fa fa-download text-primary"></i>-->
                                            <!--                                            </a>-->
                                        </div>
                                        <div id="chart-07" style="width: 780px;height: 800px"></div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">Cell proportion</h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='cell_proportion',runName =${taskVo.baselineTask.taskId})}">-->
                                            <!--                                                <i class="fa fa-download text-primary"></i>-->
                                            <!--                                            </a>-->
                                        </div>
                                        <div id="chart-05" style="width: 800px;height: 800px"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tool-box">
                    <div class="tool-title">Reference</div>
                    <div class="tool-content">
                        <ul class="r-list">
                            <li>1. Cell,2018,Single-Cell Map of Diverse Immune Phenotypes in the Breast Tumor Microenvironment</li>
                            <li>2. Nature,2019,Probabilistic cell-type assignment of single-cell RNA-seq for tumor microenvironment profiling</li>
                            <li>3. Nucleic Acids Research, 2019,CellMarker: a manually curated resource of cell markers in human and mouse</li>
                            <li>4. Cell,2019, A Cellular Taxonomy of the Bone Marrow Stroma in Homeostasis and Leukemia</li>
                            <li>5. cell,2018, Chemoresistance Evolution in Triple-Negative Breast Cancer Delineated by Single Cell Sequencing</li>
                        </ul>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>
    <script>
        $(document).ready(function () {
            initChart05();
            initChart061();
            initChart062();
            initChart07();
        })

        function initChart05() {
            if (!document.getElementById('chart-05')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-05'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/baseline/[[${taskVo.baselineTask.id}]]/5',
                    beforeSend: function () {
                        $("#chart-05").next().remove();
                        $("#chart-05").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-05").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-05").hide();
                            return;
                        }

                        var data = result.data, sData = data.sData, xData = data.xData, sd = [];

                        for (var item in sData) {
                            sd.push({
                                name: item,
                                type: 'bar',
                                stack: 'batch_level',
                                data: sData[item],
                                itemStyle: {
                                    color: getClusterColor(item)
                                }
                            })
                        }
                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                                    type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: [
                                {
                                    name: 'sample',
                                    nameLocation: 'center',
                                    nameGap: 20,
                                    type: 'category',
                                    data: xData
                                }
                            ],
                            yAxis: [
                                {
                                    name: 'fraction of cell type',
                                    nameGap: 25,
                                    nameLocation: 'center',
                                    type: 'value'
                                }
                            ],
                            series: sd
                        })

                    }
                })
            }
        }

        function initChart061() {
            if (!document.getElementById('chart-061')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-061'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/baseline/[[${taskVo.baselineTask.id}]]/61',
                    beforeSend: function () {
                        $("#chart-061").next().remove();
                        $("#chart-061").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-061").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-061").hide();
                            return;
                        }

                        var data = result.data;
                        var legendData = [];
                        var series = [];

                        for (var group in data) {
                            legendData.push(group);
                            series.push({
                                name: group,
                                data: data[group],
                                type: 'scatter',
                                symbolSize: 5,
                                cursor: 'default'
                            });
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: legendData
                            },
                            xAxis: {
                                name: 'tSNE_1',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameLocation: 'center',
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            yAxis: {
                                name: 'tSNE_2',
                                nameLocation: 'center',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            series: series
                        })
                    }
                })
            }
        }

        function initChart062() {
            if (!document.getElementById('chart-062')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-062'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/baseline/[[${taskVo.baselineTask.id}]]/62',
                    beforeSend: function () {
                        $("#chart-062").next().remove();
                        $("#chart-062").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-062").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-062").hide();
                            return;
                        }

                        var data = result.data;
                        var legendData = [];
                        var series = [];

                        for (var group in data) {
                            legendData.push(group);
                            series.push({
                                name: group,
                                data: data[group],
                                type: 'scatter',
                                symbolSize: 5,
                                cursor: 'default',
                                itemStyle: {
                                    color: getClusterColor(group)
                                }
                            });
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: legendData
                            },
                            xAxis: {
                                name: 'tSNE_1',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameLocation: 'center',
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            yAxis: {
                                name: 'tSNE_2',
                                nameLocation: 'center',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            series: series
                        })
                    }
                })
            }
        }

        function initChart07() {
            if (!document.getElementById('chart-07')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-07'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/baseline/[[${taskVo.baselineTask.id}]]/7',
                    beforeSend: function () {
                        $("#chart-07").next().remove();
                        $("#chart-07").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-07").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-07").hide();
                            return;
                        }

                        var data = result.data;


                        var min;
                        var sd = data.data.map(function (item) {
                            if (!min) {
                                min = item[2];
                            }
                            min = min > item[2] ? item[2] : min;
                            return [item[1], item[0], item[2] || '-'];
                        });

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            grid: {
                                height: '50%',
                                top: '10%',
                                left: '20%'
                            },
                            xAxis: {
                                type: 'category',
                                data: data.x,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0,
                                    rotate: 90
                                }
                            },
                            yAxis: {
                                type: 'category',
                                data: data.y,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0
                                },
                                inverse: true
                            },
                            visualMap: {
                                min: min,
                                max: 1,
                                calculable: true,
                                precision: 3,
                                orient: 'horizontal',
                                left: 'center',
                                bottom: '15%',
                                inRange: {
                                    color: ['#053061', '#2fbce2', '#f5f5f5', '#e7783c', '#69001f']
                                }
                            },
                            series: [{
                                type: 'heatmap',
                                data: sd
                            }]
                        })
                    }
                })
            }
        }
    </script>
</th:block>
</html>
