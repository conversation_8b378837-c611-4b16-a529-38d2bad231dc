package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnaseq.client.ScrnaseqServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "scrnaseq-service", contextId = "common", fallback = ScrnaseqServiceClientFallback.class)
public interface ScrnaseqServiceClient extends ScrnaseqServiceApi {

}
