<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}" th:with="task=${vo.task}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('strnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">10X genomics visium</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <!--/*
                    <div style="display: none;" th:text="${task?.errMsg}"></div>
                    */-->
                </div>
                <th:block th:switch="${task.status}">
                    <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis Error</p>
                    </div>

                    <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Prepared</p>
                    </div>
                    <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="3">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis done</p>
                    </div>

                    <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Data prepared</p>
                    </div>
                </th:block>

                <ul class="detail-info row mb-2">
                    <li class="col-xl-6 col-lg-6"><span class="text-muted sm">Task ID </span>
                        <div th:text="${task.taskId}">20200722001</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted sm">Task Name </span>
                        <div th:text="${task.taskName}">20200722001</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted sm">Start Time</span>
                        <div th:text="${#dates.format(task.createTime,'yyyy-MM-dd HH:mm:ss')}">2020-11-17 16:35:25</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted sm">Consuming</span>
                        <div th:text="${task.useTime}">17小时3分</div>
                    </li>
                </ul>

                <div class="form-group-box">
                    <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                    <div class="collapse show" id="coll-1">
                        <div class="pl-4 pt-2">
                            <div class="result-box">
                                <h5 class="mb-3 font-16"><span>Expression matrix</span></h5>

                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th style="min-width: 100px">RunName</th>
                                            <th style="min-width: 50px">I1</th>
                                            <th style="min-width: 50px">R1</th>
                                            <th style="min-width: 50px">R2</th>
                                            <th style="min-width: 100px">Image File
                                                <!--                                                <a class="text-danger ml-1" data-toggle="tooltip"-->
                                                <!--                                                   data-placement="top"-->
                                                <!--                                                   title="Single H&E brightfield image in either TIFF or JPG format">-->
                                                <!--                                                    <i class="fa fa-question-circle"></i>-->
                                                <!--                                                </a>-->
                                            </th>
                                            <th style="min-width: 100px">Slide ID
                                                <!--                                                <a class="text-danger ml-1" data-toggle="tooltip"-->
                                                <!--                                                   data-placement="top"-->
                                                <!--                                                   title="Visium slide serial number, for example 'V10J25-015'">-->
                                                <!--                                                    <i class="fa fa-question-circle"></i>-->
                                                <!--                                                </a>-->
                                            </th>
                                            <th style="min-width: 100px">Area ID
                                                <!--                                                <a class="text-danger ml-1" data-toggle="tooltip"-->
                                                <!--                                                   data-placement="top"-->
                                                <!--                                                   title="Visium area identifier, for example 'A1'">-->
                                                <!--                                                    <i class="fa fa-question-circle"></i>-->
                                                <!--                                                </a>-->
                                            </th>
                                            <th style="min-width: 200px">Loupe Alignment File
                                                <!--                                                <a class="text-danger ml-1" data-toggle="tooltip"-->
                                                <!--                                                   data-placement="top"-->
                                                <!--                                                   title="Alignment file produced by the manual Loupe alignment step by LoupeBrowser (optional)">-->
                                                <!--                                                    <i class="fa fa-question-circle"></i>-->
                                                <!--                                                </a>-->
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="row:${vo.fileParam}">
                                            <td th:text="${row.runName}">HG00114</td>
                                            <td>
                                                <th:block th:each="it:${row.i1}">
                                                    <span th:text="${it.path}" class="font-12">Sample1_S1_L002_I1_001.fastq.gz</span>
                                                </th:block>
                                            </td>
                                            <td>
                                                <th:block th:each="it:${row.r1}">
                                                    <span th:text="${it.path}" class="font-12">Sample1_S1_L002_I1_001.fastq.gz</span>
                                                </th:block>
                                            </td>
                                            <td>
                                                <th:block th:each="it:${row.r2}">
                                                    <span th:text="${it.path}" class="font-12">Sample1_S1_L002_I1_001.fastq.gz</span>
                                                </th:block>
                                            </td>
                                            <td>
                                                <span class="font-12" th:text="${row?.imageFile?.path}">test.json</span>
                                            </td>
                                            <td>
                                                <th:block th:text="${row.slideId}"/>
                                            <td>
                                                <th:block th:text="${row.areaId}"/>
                                            </td>
                                            <td>
                                                <span class="font-12" th:text="${row?.loupeFile?.path}">test.json</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${task.species}">Homo species（human）</span>
                                    </div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Version</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${task.version}">GRCH38（hg38）</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Counting</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary">spaceranger</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group-box" th:if="${vo.task.status == 3}">
                    <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                    <div class="collapse show pl-4 pt-2" id="coll-2">
                        <div class="d-flex align-items-center mb-3 mt-3">
                            <label class="col-form-label pr-0">RunName：</label>
                            <select class="custom-select w-25" id="group">
                                <option th:each="row:${vo.fileParam}" th:text="${row.runName}">HG00114</option>
                            </select>
                        </div>

                        <div class="form-group row align-items-center mb-5">
                            <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Spaceranger report:</label>
                            <div class="col-xl-4 col-lg-2col-md-4">
                                <a href="#" id="file-spaceranger_report"><i class="fa fa-download"></i>web_summary.html</a><br>
                                <a href="#" id="file-filtered_feature_bc_matrix"><i class="fa fa-download"></i>filtered_feature_bc_matrix</a>
                            </div>
                            <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">DEGs of clusters:</label>
                            <div class="col-xl-4 col-lg-2col-md-4">
                                <a href="#" id="file-degs_of_clusters"><i class="fa fa-download"></i>
                                    <span id="filename-degs_of_clusters"> HG00114.cluster.markers.tab</span>
                                </a><br>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-6">
                                <h6 class="pb-2 m-0 mb-2">Average count of spot<a id="file-average_spot" href="#"> <i
                                        class="fa fa-download"></i></a></h6>
                                <div class="form-group row">
                                    <div class="col-md-12">
                                        <div class="text-center">
                                            <div id="img-average_spot"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="pb-2 m-0 mb-2">Spatial distribution of count
                                    <a href="#" id="file-spatial_count"> <i class="fa fa-download"></i></a></h6>
                                <div class="form-group row">
                                    <div class="col-md-12">
                                        <div class="text-center">
                                            <div id="img-spatial_count"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <h6 class="border-bottom pb-2 m-0 mb-2">UMAP for clusters
                            <a href="#" id="file-umap_clusters"> <i class="fa fa-download"></i></a></h6>
                        <div class="form-group row">
                            <div class="col-md-12">
                                <div class="text-center">
                                    <div class="m-auto" style="width: 80%;height: 600px" id="chart-umap_clusters"></div>
                                </div>
                            </div>
                        </div>
                        <h6 class="border-bottom pb-2 m-0 mb-2">Spatial distribution for clusters
                            <a href="#" id="file-spatial_clusters"> <i class="fa fa-download"></i></a></h6>
                        <div class="form-group row">
                            <div class="col-md-12">
                                <div class="text-center">
                                    <div id="img-spatial_clusters"></div>
                                </div>
                            </div>
                        </div>
                        <h6 class="border-bottom pb-2 m-0 mb-2">The expression of Top 6 spatially varibale features
                            <a href="#" id="file-spatial_features"> <i class="fa fa-download"></i></a></h6>
                        <div class="form-group row">
                            <div class="col-md-12">
                                <div class="text-center">
                                    <div id="img-spatial_features"></div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>

            </main>
        </div>
    </div>
</div>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>

    <script th:if="${task.status == 3}">
      let taskId = '[[${vo.task.taskId}]]'

      $(document).ready(function () {
        let runName = $("#group").val()

        changeRunName(taskId, runName)
      })

      $('#group').change(function () {
        let runName = $(this).val()
        changeRunName(taskId, runName)
      })

      function changeRunName (taskId, runName) {

        // [outputpath]/[task ID]/spaceranger/[runName]/outs/web_summary.html
        $("#file-spaceranger_report").attr('href', `[[@{/analysis/strnaseq/downloadFile}]]?taskId=${taskId}&path=/spaceranger/${runName}/outs/web_summary.html`)
        // [outputpath]/[task ID]/spaceranger/[runName]/outs/filtered_feature_bc_matrix.h5
        $("#file-filtered_feature_bc_matrix").attr('href', `[[@{/analysis/strnaseq/downloadFile}]]?taskId=${taskId}&path=/spaceranger/${runName}/outs/filtered_feature_bc_matrix.h5`)

        // [runName].cluster.markers.tab
        $('#filename-degs_of_clusters').text(`${runName}.cluster.markers.tab`)
        // [outputpath]/[task ID]/seurat/[runName]/[runName].cluster.markers.tab
        $("#file-degs_of_clusters").attr('href', `[[@{/analysis/strnaseq/downloadFile}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.cluster.markers.tab`)

        $("#img-average_spot").html(`<img src="[[@{/analysis/strnaseq/getImage}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.nCount_Spatial.Vlnplot.png">`)
        $('#file-average_spot').attr('href', `[[@{/analysis/strnaseq/downloadFile}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.nCount_Spatial.Vlnplot.png`)

        $("#img-spatial_count").html(`<img src="[[@{/analysis/strnaseq/getImage}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.nCount_Spatial.SpatialFeatureplot.png">`)
        $('#file-spatial_count').attr('href', `[[@{/analysis/strnaseq/downloadFile}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.nCount_Spatial.SpatialFeatureplot.png`)

        // [outputpath]/[task ID]/seurat/[runName]/[runName].clusters_result.UMAPplot.png
        // $("#img-umap_clusters").html(`<img src="[[@{/analysis/strnaseq/getImage}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.clusters_result.UMAPplot.png">`);
        $('#file-umap_clusters').attr('href', `[[@{/analysis/strnaseq/downloadFile}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.clusters_result.UMAPplot.png`)

        // [outputpath]/[task ID]/seurat/[runName]/[runName].clusters_result.SpatialDimplot.png
        $("#img-spatial_clusters").html(`<img src="[[@{/analysis/strnaseq/getImage}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.clusters_result.SpatialDimplot.png">`)
        $('#file-spatial_clusters').attr('href', `[[@{/analysis/strnaseq/downloadFile}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.clusters_result.SpatialDimplot.png`)

        // [outputpath]/[task ID]/seurat/[runName]/[runName].spatially.high.expressed_feature.SpatialFeatureplot.png
        $("#img-spatial_features").html(`<img src="[[@{/analysis/strnaseq/getImage}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.spatially.high.expressed_feature.SpatialFeatureplot.png">`)
        $('#file-spatial_features').attr('href', `[[@{/analysis/strnaseq/downloadFile}]]?taskId=${taskId}&path=/seurat/${runName}/${runName}.spatially.high.expressed_feature.SpatialFeatureplot.png`)

        chartUmap(taskId, runName)
      }

      function chartUmap (taskId, runName) {
        $('#chart-umap_clusters')

        if (!document.getElementById('chart-umap_clusters')) {
          return
        }
        let myChart = echarts.init(document.getElementById('chart-umap_clusters'))

        $.ajax({
          type: 'post',
          url: '/analysis/strnaseq/getChartData',
          data: {
            taskId,
            runName,
            chartFlag: 'umap_clusters'
          },
          beforeSend: function () {
            myChart.clear()
            myChart.showLoading()
          },
          complete: function () {
            myChart.hideLoading()
          },
          success: function (result) {
            if (result.error) {
              return
            }
            let data = result.data
            data = data.slice(1).reduce(function (pre, cur) {
              let name = cur[0], x = cur[1], y = cur[2], group = cur[3]
              pre[group] = pre[group] || []
              pre[group].push(cur)
              return pre
            }, {})

            let dataSeries = []
            Object.keys(data).forEach(key => {
              let v = data[key].map(it => {
                return [it[1], it[2]]
              })
              dataSeries.push({
                name: key,
                type: 'scatter',
                symbolSize: 5,
                data: v,
                itemStyle: {
                  color: getClusterColor(key)
                }
              })
            })

            // console.log({
            //     legend: {},
            //     xAxis: {
            //         type: 'value'
            //     },
            //     yAxis: {
            //         type: 'value'
            //     },
            //     series: dataSeries
            // })

            myChart.setOption({
              legend: {
                left: 'right',
                top: 'middle',
                orient: 'vertical'
              },
              xAxis: {
                type: 'value',
                name: 'UMAP_1',
                nameTextStyle: {
                  fontWeight: 'bold'
                },
                nameLocation: 'center',
                nameGap: 35,
                splitLine: {
                  show: false
                }
              },
              yAxis: {
                type: 'value',
                name: 'UMAP_2',
                nameLocation: 'center',
                nameTextStyle: {
                  fontWeight: 'bold'
                },
                nameGap: 35,
                splitLine: {
                  show: false
                }
              },
              series: dataSeries
            })
          }
        })
      }
    </script>
</th:block>
</html>
