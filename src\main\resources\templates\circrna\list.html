<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-circrna-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">RNA-Seq-circRNA</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/circrna/form}">Add Task</a>
                            <a th:href="@{/analysis/circrna/list}" class="active">Task List</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade">
                            <form th:action="@{/analysis/circrna/list}" id="search-form"
                                  class="form-inline form-task mt-2">
                                <div class="d-flex">
                                    <label class="mx-2 font-12">Task ID</label>
                                    <input name="taskId" th:value="${query.taskId}" type="text"
                                           class="form-control form-control-sm width-100">
                                    <label class="mx-2 font-12">Time</label>
                                    <div class="input-daterange input-group">
                                        <input autocomplete="off" type="text" class="form-control form-control-sm"
                                               name="start"
                                               th:value="${query.start==null?'':#calendars.format(query.start,'yyyy-MM-dd')}"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                        </div>
                                        <input autocomplete="off" type="text" class="form-control form-control-sm"
                                               name="end"
                                               th:value="${query.end==null?'':#calendars.format(query.end,'yyyy-MM-dd')}"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex mx-auto">
                                    <button type="submit" class="btn btn-primary btn-sm m-2 _submit_btn">Search</button>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table class="table table-hover table-striped table-nowrap font-12 m-0">
                                    <thead>
                                    <tr>
                                        <th scope="col">Task ID</th>
                                        <th scope="col">Start time</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Status time</th>
                                        <th scope="col">Consuming</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <th:block th:unless="${#lists.isEmpty(pageResult.content)}">
                                        <tr th:each="task : ${pageResult.content}">
                                            <td th:text="${task.taskId}">200302151050411</td>
                                            <td th:text="${#dates.format(task.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td>
                                                <th:block th:switch="${task.status}">
                                                    <th:block th:case="-1">
                                                        <span class="text-danger"><i class="fa fa-circle"></i> [[${codeDescMap.get(task.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="1">
                                                        <span class="text-info"><i class="fa fa-circle"></i> [[${codeDescMap.get(task.status)}]]</span>
                                                    </th:block>

                                                    <th:block th:case="4">
                                                        <span class="text-dark"><i class="fa fa-circle"></i> [[${codeDescMap.get(task.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="*">
                                                        <span class="text-success"><i class="fa fa-circle"></i> [[${codeDescMap.get(task.status)}]]</span>
                                                    </th:block>
                                                </th:block>
                                            </td>
                                            <td th:text="${#dates.format(task.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:text="${task.useTime}">26m55s</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a th:href="@{/analysis/circrna/{id}(id=${task.id})}"
                                                       class="text-primary" data-toggle="tooltip" title="View">
                                                        <i class="fa fa-eye font-14"></i>
                                                    </a>
                                                    <a href="javascript:void(0);" class="text-danger"
                                                       data-toggle="tooltip"
                                                       th:onclick="deleteTask([[${task.id}]])" title="Delete">
                                                        <i class="fa fa-times font-14"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </th:block>
                                    <th:block th:if="${#lists.isEmpty(pageResult)}">
                                        <tr>
                                            <td colspan="6">no data</td>
                                        </tr>
                                    </th:block>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pt-1 mb-2">
                                <div th:replace="~{base/pageable}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script>
      $(document).ready(function () {
        $('.input-daterange').datepicker({
          format: 'yyyy-mm-dd',
          toggleActive: true,
          autoclose: true,
          todayHighlight: true
        })
      })

      function deleteTask (id) {
        layer.confirm('<p class="text-center">确定删除吗？</p>', { btn: ['确认', '取消'] }, function () {
          var loadLayerIndex
          $.ajax({
            url: '/analysis/circrna/deleteTask',
            data: { 'id': id },
            dataType: 'json',
            async: false,
            method: 'post',
            beforeSend: function () {
              loadLayerIndex = layer.load(1, {
                shade: [0.1, '#fff'] //0.1透明度的白色背景
              })
            },
            success: function (result) {
              if (result.code === 200) {
                layer.msg('删除成功', { time: 500 }, function () {
                  location.reload()
                })
              } else {
                layer.alert(result.message, { icon: 2 })
              }
            },
            complete: function () {
              layer.close(loadLayerIndex)
            }
          })
        })
      }
    </script>
</th:block>
</html>
