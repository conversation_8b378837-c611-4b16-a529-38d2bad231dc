package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.ase.vo.AseTaskParamVO;
import cn.ac.picb.ase.vo.AseTaskQueryVO;
import cn.ac.picb.vipmap.vo.AseTaskParam;
import cn.ac.picb.vipmap.vo.AseTaskSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:14
 */
@Mapper
public interface AseMapper {
    AseMapper INSTANCE = Mappers.getMapper(AseMapper.class);

    AseTaskParamVO convertToVO(AseTaskParam param);

    AseTaskQueryVO convert(AseTaskSearchVO search);
}
