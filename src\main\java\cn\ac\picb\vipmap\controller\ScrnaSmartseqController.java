package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnasmartseq.dto.ScrnaSmartseqTaskDTO;
import cn.ac.picb.scrnasmartseq.enums.ScrnaSmartseqTaskStatus;
import cn.ac.picb.scrnasmartseq.po.ScrnaSmartseqTaskPO;
import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskInput;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.ScrnaSmartseqService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.ScrnaSmartseqTaskParam;
import cn.ac.picb.vipmap.vo.ScrnaSmartseqTaskSearchVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/analysis/scrnaSmartseq")
@RequiredArgsConstructor
public class ScrnaSmartseqController {

    private final ScrnaSmartseqService scrnaSmartseqService;
    private final AppProperties appProperties;

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") ScrnaSmartseqTaskSearchVO search, PageParam pageParam, Model model) {
        PageResult<ScrnaSmartseqTaskPO> pageResult = scrnaSmartseqService.findPage(user, search, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = ScrnaSmartseqTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "scrnasmartseq/list";
    }

    @RequestMapping("/uploadTemplate")
    @ResponseBody
    public CommonResult<List<ScrnaSmartseqTaskInput>> uploadTemplate(CurrentUser user, MultipartFile file) {
        List<ScrnaSmartseqTaskInput> vos = scrnaSmartseqService.uploadTemplate(file, user);
        return success(vos);
    }


    @RequestMapping("/form")
    public String form() {
        return "scrnasmartseq/form";
    }

    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated ScrnaSmartseqTaskParam param) {
        ScrnaSmartseqTaskPO task = scrnaSmartseqService.createTask(user, param);
        return success(task.getTaskId());
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        scrnaSmartseqService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/download")
    @ResponseBody
    public void downloadResult(String taskId) {
        Response response = scrnaSmartseqService.downloadResult(taskId, "");
        ResponseUtil.download(response);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        ScrnaSmartseqTaskDTO vo = scrnaSmartseqService.findTaskVO(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = ScrnaSmartseqTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "scrnasmartseq/detail";
    }

    @RequestMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemplate() {
        Response response = scrnaSmartseqService.downloadTemplate();
        ResponseUtil.download(response);
    }
}
