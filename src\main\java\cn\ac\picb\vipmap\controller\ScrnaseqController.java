package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.util.ServiceExceptionUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnaseq.dto.*;
import cn.ac.picb.scrnaseq.enums.ReportTaskStatus;
import cn.ac.picb.scrnaseq.po.*;
import cn.ac.picb.scrnaseq.vo.AnnotationTabRowVO;
import cn.ac.picb.scrnaseq.vo.SelectTaskVO;
import cn.ac.picb.scrnaseq.vo.TaskListTreeVO;
import cn.ac.picb.scrnaseq.vo.TaskTreeNodeVO;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.enums.ScrnaseqType;
import cn.ac.picb.vipmap.service.ScrnaseqService;
import cn.ac.picb.vipmap.vo.*;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;
import static cn.ac.picb.vipmap.enums.ErrorCodeConstants.PARAMS_ERROR;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/analysis/scrnaseq")
@RequiredArgsConstructor

public class ScrnaseqController {

    private final AppProperties appProperties;
    private final ScrnaseqService scrnaseqService;

    @RequestMapping("/all")
    public String tree() {
        return "scrnaseq/data-tree";
    }

    @RequestMapping("/treeData")
    @ResponseBody
    public List<TaskTreeNodeVO> treeData(CurrentUser user, String parentId, String type) {
        List<TaskTreeNodeVO> treeData = scrnaseqService.findTreeData(user, parentId, type);
        return treeData;
    }

    @RequestMapping("/findAllTaskWithTree")
    @ResponseBody
    public CommonResult<List<TaskListTreeVO>> taskListTreeData(CurrentUser user, ScrnaseqTaskSearchVO searchVO) {
        List<TaskListTreeVO> data = scrnaseqService.findAllTaskWithTree(user, searchVO);
        return success(data);
    }

    @RequestMapping("/findAllCompleteTaskWithTree")
    @ResponseBody
    public CommonResult<List<TaskListTreeVO>> findAllCompleteTaskWithTree(CurrentUser user, ScrnaseqTaskSearchVO searchVO) {
        List<TaskListTreeVO> data = scrnaseqService.findAllCompleteTaskWithTree(user, searchVO);
        return success(data);
    }


    @RequestMapping("/form/{type}")
    public String taskForm(@PathVariable("type") String type) {
        if (StrUtil.isBlank(type)) {
            throw ServiceExceptionUtil.exception(PARAMS_ERROR);
        }
        return String.format("scrnaseq/form/%s-form", type);
    }

    @PostMapping("/saveGenomics")
    @ResponseBody
    public CommonResult<TaskTreeNodeVO> saveGenomics(CurrentUser user, @Validated ScrnaseqGenomicsTaskParam param) {
        TaskTreeNodeVO node = scrnaseqService.saveGenomics(user, param);
        return success(node);
    }

    @PostMapping("/saveBaseline")
    @ResponseBody
    public CommonResult<TaskTreeNodeVO> saveBaseline(CurrentUser user, @Validated ScrnaseqBaselineTaskParam param) {
        TaskTreeNodeVO node = scrnaseqService.saveBaseline(user, param);
        return success(node);
    }

    @PostMapping("/savePaga")
    @ResponseBody
    public CommonResult<TaskTreeNodeVO> savePaga(CurrentUser user, @Validated ScrnaseqPagaTaskParam param) {
        TaskTreeNodeVO node = scrnaseqService.savePaga(user, param);
        return success(node);
    }

    @PostMapping("/saveDeg")
    @ResponseBody
    public CommonResult<TaskTreeNodeVO> saveDeg(CurrentUser user, @Validated ScrnaseqDegTaskParam param) {
        TaskTreeNodeVO node = scrnaseqService.saveDeg(user, param);
        return success(node);
    }

    @RequestMapping("/saveWganc")
    @ResponseBody
    public CommonResult<TaskTreeNodeVO> wgancSave(CurrentUser user, @Validated ScrnaseqWgcnaTaskParam param) {
        TaskTreeNodeVO node = scrnaseqService.saveWgcna(user, param);
        return success(node);
    }

    @PostMapping("/saveGenes")
    @ResponseBody
    public CommonResult<TaskTreeNodeVO> saveGenes(CurrentUser user, @Validated ScrnaseqGenesTaskParam param) {
        TaskTreeNodeVO node = scrnaseqService.saveGenes(user, param);
        return success(node);
    }

    @RequestMapping("/list")
    public String taskList(String type) {
        return String.format("scrnaseq/table/%s-table", type);
    }


    @RequestMapping("/filter")
    @ResponseBody
    public CommonResult<PageResult<ScrnaseqTaskVO>> table(CurrentUser currentUser, ScrnaseqTaskSearchVO searchVO, PageParam pageParam, Model model) {
        String type = searchVO.getType();
        ScrnaseqType scrnaseqType = EnumUtil.fromStringQuietly(ScrnaseqType.class, type);
        PageResult<ScrnaseqTaskVO> page;
        if (scrnaseqType == null) {
            throw ServiceExceptionUtil.exception(PARAMS_ERROR);
        }
        switch (scrnaseqType) {
            case genomics:
                page = scrnaseqService.findGenomicsPage(currentUser, searchVO, pageParam);
                break;
            case baseline:
                page = scrnaseqService.findBaselinePage(currentUser, searchVO, pageParam);
                break;
            case paga:
                page = scrnaseqService.findPagaPage(currentUser, searchVO, pageParam);
                break;
            case deg:
                page = scrnaseqService.findDegPage(currentUser, searchVO, pageParam);
                break;
            case genes:
                page = scrnaseqService.findGenesPage(currentUser, searchVO, pageParam);
                break;
            default:
                page = new PageResult<>();
                page.setTotal(0L);
                page.setContent(Collections.emptyList());
                break;
        }
        return success(page);
    }

    @RequestMapping("/findCompleteTask/{type}")
    @ResponseBody
    public CommonResult<List<ScrnaseqTaskVO>> table(CurrentUser currentUser, @PathVariable("type") String type) {
        ScrnaseqType scrnaseqType = EnumUtil.fromStringQuietly(ScrnaseqType.class, type);
        List<ScrnaseqTaskVO> list;
        if (scrnaseqType == null) {
            throw ServiceExceptionUtil.exception(PARAMS_ERROR);
        }
        switch (scrnaseqType) {
            case genomics:
                list = scrnaseqService.findcompleteGenomicsTask(currentUser);
                break;
            case baseline:
                list = scrnaseqService.findcompleteBaselineTask(currentUser);
                break;
            default:
                throw ServiceExceptionUtil.exception(PARAMS_ERROR);
        }
        return success(list);
    }


    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<TaskTreeNodeVO> deleteTask(String type, String id) {
        TaskTreeNodeVO node = scrnaseqService.deleteTask(type, id);
        return success(node);
    }

    @RequestMapping("/taskDetail")
    public String taskDetail(String type, String id, Model model) {
        ScrnaseqType scrnaseqType = EnumUtil.fromStringQuietly(ScrnaseqType.class, type);
        if (scrnaseqType == null) {
            throw ServiceExceptionUtil.exception(PARAMS_ERROR);
        }
        switch (scrnaseqType) {
            case genomics:
                GenomicsTaskDTO genomicsTask = scrnaseqService.findGenomicsTaskDTOById(id);
                model.addAttribute("taskVo", genomicsTask);
                break;
            case baseline:
                BaselineTaskDTO baselineTask = scrnaseqService.findBaselineTaskDTOById(id);
                model.addAttribute("taskVo", baselineTask);

                List<AnnotationTabRowVO> rows = scrnaseqService.findBaselineAnnotationTabRow(baselineTask.getBaselineTask().getTaskId());
                model.addAttribute("rows", rows);
                break;
            case paga:
                PagaTaskDTO pagaTask = scrnaseqService.findPagaTaskDTOById(id);
                model.addAttribute("taskVo", pagaTask);
                break;
            case deg:
                DegTaskDTO degTask = scrnaseqService.findDegTaskDTOById(id);
                model.addAttribute("taskVo", degTask);
                break;
            case wgcna:
                WgcnaTaskDTO wgcnaTask = scrnaseqService.findWgcnaTaskDTOById(id);
                model.addAttribute("taskVo", wgcnaTask);
                break;
            default:
                GenesTaskDTO genesTask = scrnaseqService.findGenesTaskDTOById(id);
                model.addAttribute("taskVo", genesTask);
                break;
        }
        return String.format("scrnaseq/detail/%s-detail", type);
    }

    @RequestMapping("/{id}")
    public String redirectToBase(@PathVariable String id, Model model) {
        GenomicsTaskDTO genomicsTask = scrnaseqService.findGenomicsTaskDTOById(id);
        model.addAttribute("taskVo", genomicsTask);
        return "scrnaseq/detail/genomics-detail";
    }

    @RequestMapping("/genomics/{id}/{chartNo}")
    @ResponseBody
    public CommonResult<Object> genomicsData(@PathVariable("id") String id, @PathVariable("chartNo") Integer chartNo) {
        GenomicsTaskPO task = scrnaseqService.findGenomicsTaskById(id);
        Object res = scrnaseqService.getGenomicsChartData(task, chartNo);
        return success(res);
    }

    @RequestMapping("/baseline/{id}/{chartNo}")
    @ResponseBody
    public CommonResult<Object> baselineData(@PathVariable("id") String id, @PathVariable("chartNo") Integer chartNo) {
        BaselineTaskPO task = scrnaseqService.findBaselineTaskById(id);
        Object res = scrnaseqService.getBaselineChartData(task, chartNo);
        return success(res);
    }

    @RequestMapping("/paga/{id}/{chartNo}")
    @ResponseBody
    public CommonResult<Object> pagaData(@PathVariable("id") String id, @PathVariable("chartNo") Integer chartNo) {
        PagaTaskPO task = scrnaseqService.findPagaTaskById(id);
        Object res = scrnaseqService.getPagaChartData(task, chartNo);
        return success(res);
    }

    @RequestMapping("/deg/{id}/{chartNo}")
    @ResponseBody
    public CommonResult<Object> degData(@PathVariable("id") String id, @PathVariable("chartNo") Integer chartNo) {
        DegTaskPO task = scrnaseqService.findDegTaskById(id);
        Object res = scrnaseqService.getDegChartData(task, chartNo);
        return success(res);
    }

    @RequestMapping("/wgcna/{id}/{charNo}")
    @ResponseBody
    public CommonResult<Object> wgcnaData(@PathVariable("id") String id, @PathVariable("charNo") Integer chartNo) {
        WgcnaTaskPO task = scrnaseqService.findWgcnaTaskById(id);
        Object res = scrnaseqService.getWgcnaChartData(task, chartNo);
        return success(res);
    }

    @RequestMapping("/genes/{id}/{chartNo}")
    @ResponseBody
    public CommonResult<Object> genesData(@PathVariable("id") String id, @PathVariable("chartNo") Integer chartNo) {
        GenesTaskPO task = scrnaseqService.findGenesTaskById(id);
        Object res = scrnaseqService.getGeneChartData(task, chartNo);
        return success(res);
    }

    @RequestMapping("/getGenesStackedViolinPdf")
    public ResponseEntity<byte[]> getGenesStackedViolinPdf(String taskId) {
        return scrnaseqService.getGenesStackedViolinPdf(taskId);
    }

    @RequestMapping("/report")
    public String reportList(CurrentUser currentUser, @ModelAttribute("search") ReportSearchVO searchVO, PageParam pageParam, Model model) {
        PageResult<ReportTaskDTO> page = scrnaseqService.findReportPage(currentUser, searchVO, pageParam);
        model.addAttribute("pageResult", page);

        Map<Integer, String> codeDescMap = ReportTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "scrnaseq/report-list";
    }


    @RequestMapping("/report/form")
    public String addReport() {
        return "scrnaseq/report/report-form";
    }


    @RequestMapping("/report/taskSelect")
    @ResponseBody
    public CommonResult<SelectTaskVO> findTask(ScrnaseqSelectParam param, CurrentUser user) {
        SelectTaskVO vo = scrnaseqService.findTaskByParams(param, user);
        return success(vo);
    }

    @PostMapping("/report/getSelectData")
    public String getSelectData(@Validated ScrnaseqReportTaskParam param, CurrentUser user, Model model) {
        ReportTaskDTO vo = scrnaseqService.getSelectData(user, param);
        model.addAttribute("vo", vo);
        return "scrnaseq/report/report-content";
    }

    @RequestMapping("/report/getGeneNames")
    @ResponseBody
    public CommonResult<Map<String, List<String>>> getGeneNames(ScrnaseqReportTaskParam param) {
        Map<String, List<String>> names = scrnaseqService.getGeneNames(param);
        return success(names);
    }

    @RequestMapping("/report/geneData")
    @ResponseBody
    public CommonResult<List<List<Double>>> geneData(String type, String gene, ScrnaseqReportTaskParam param) {
        List<List<Double>> data = scrnaseqService.getGeneData(type, gene, param);
        return success(data);
    }

    @PostMapping("/report/save")
    @ResponseBody
    public CommonResult<ReportTaskPO> saveReport(@Validated ScrnaseqReportTaskParam param, CurrentUser user) {
        ReportTaskPO task = scrnaseqService.saveReport(user, param);
        return success(task);
    }

    @RequestMapping("/report/deleteTask")
    @ResponseBody
    public CommonResult<ReportTaskPO> deleteReportTask(String id) {
        ReportTaskPO reportTask = scrnaseqService.deleteReportTask(id);
        return success(reportTask);
    }

    @RequestMapping("/report/taskDetail")
    public String reportDetail(String id, Model model) {
        ReportTaskDTO vo = scrnaseqService.findReportTaskDTOById(id);
        model.addAttribute("vo", vo);
        return "scrnaseq/report/report-detail";
    }

    @RequestMapping("/report/download")
    @ResponseBody
    public void downloadReport(String taskId) {
        Response response = scrnaseqService.downloadReportByTaskId(taskId);
        ResponseUtil.download(response);
    }

    @RequestMapping("/common/download")
    @ResponseBody
    public void commonDownload(String code, String runName, @RequestParam(required = false) String sampleName, @RequestParam(required = false) String resName) {
        Response response = scrnaseqService.commonDownload(code, runName, sampleName, resName);
        ResponseUtil.download(response);
    }

    @RequestMapping("/common/downloadLogs")
    @ResponseBody
    public void downloadLogs() {
        Response response = scrnaseqService.downloadLogs();
        ResponseUtil.download(response);
    }

    @RequestMapping("/common/version")
    @ResponseBody
    public String getVersion() {
        return scrnaseqService.getVersion();
    }

}
