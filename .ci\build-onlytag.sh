#!/bin/bash

var_chg () {
    f_out=${2}
    f_in=${1}
    echo "cat ${f_in} | sed -e "s+BUILD_NUMBER+${BUILD_NUMBER}+g" \
                      -e "s+BUILD_ID+${BUILD_ID}+g" \
                      -e "s+BUILD_DATE+${BUILD_DATE}+g" \
                      -e "s+NS_NAME+${NS_NAME}+g"    \
                      -e "s+APP_NAME+${APP_NAME}+g"  \
                      -e "s+DIR_NAME+${DIR_NAME}+g"  \
                      -e "s+SVC_NAME+${SVC_NAME}+g"  \
                      -e "s+DB_NAME+${DB_NAME}+g"  \
                          > ${f_out}"i

    cat ${f_in} | sed -e "s+BUILD_NUMBER+${BUILD_NUMBER}+g" \
                      -e "s+BUILD_ID+${BUILD_ID}+g" \
                      -e "s+BUILD_DATE+`echo ${BUILD_DATE}`+g" \
                      -e "s+NS_NAME+${NS_NAME}+g"    \
                      -e "s+APP_NAME+${APP_NAME}+g"  \
                      -e "s+DIR_NAME+${DIR_NAME}+g"  \
                      -e "s+SVC_NAME+${SVC_NAME}+g"  \
                      -e "s+DB_NAME+${DB_NAME}+g"  \
                          > ${f_out}

}

db_create () {
    db_name=${1}
    mkdir -p /p300-service/${APP_NAME}/${db_name}
    var_chg .ci/db/${db_name}.yaml app_${db_name}.yaml
    echo '---' >> app_db.yaml ; cat app_${db_name}.yaml >> app_db.yaml
}

env | grep "_NAME"
env | grep "_DATE"

var_chg .ci/app_build.yaml app_build.yaml

