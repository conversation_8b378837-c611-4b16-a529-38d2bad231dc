[{"id": "1", "shape": "lane", "site": "start", "position": {"x": -300, "y": 90}, "label": "fastq, fq.gz, bam, txt, xls", "attrs": {"rect2-text": {"text": "Platform"}}}, {"id": "2", "shape": "lane-right", "position": {"x": 0, "y": 90}, "label": "html,JPG", "attrs": {"rect2-text": {"text": "Quality Control"}}}, {"id": "3", "shape": "lane-right", "position": {"x": 170, "y": 90}, "label": "bam", "attrs": {"rect2-text": {"text": "Mapping"}}}, {"id": "4", "shape": "lane-right", "position": {"x": 340, "y": 90}, "label": "xls,txt,tsv,vcf", "attrs": {"rect2-text": {"text": "Quantitative\n& Qualitative"}}}, {"id": "5", "shape": "lane-right", "position": {"x": 510, "y": 90}, "label": "xls,txt,tsv,vcf", "attrs": {"rect2-text": {"text": "Normalizatior\n& Filtration"}}}, {"id": "6", "shape": "lane-right", "position": {"x": 680, "y": 90}, "label": "xls, txt, tsv, vcf, jpg", "attrs": {"rect2-text": {"text": "Basic analysis"}}}, {"id": "7", "shape": "lane-right", "site": "end", "position": {"x": 850, "y": 90}, "label": "png,pdf", "attrs": {"rect2-text": {"text": "Visualization"}}}, {"id": "RNA-Seq", "parent": "1", "shape": "lane-rect-left", "position": {"x": -290, "y": 320}, "data": {"state": ["R1", "R2", "R3", "R4", "R5"]}, "label": "RNA-Seq", "ports": [{"group": "right", "id": "RNA-Seq-right"}, {"group": "left", "id": "RNA-Seq-left"}]}, {"id": "DNA-Seq", "parent": "1", "shape": "lane-rect-left", "position": {"x": -290, "y": 670}, "data": {"state": ["D1", "D2", "D3"]}, "label": "DNA-Seq", "ports": [{"group": "right", "id": "DNA-Seq-right"}, {"group": "left", "id": "DNA-Seq-left"}]}, {"id": "ChIP-Seq", "parent": "1", "shape": "lane-rect-left", "site": "gray", "position": {"x": -290, "y": 830}, "data": {"state": ["E1"]}, "label": "ChIP-Seq", "ports": [{"group": "right", "id": "ChIP-Seq-right"}, {"group": "left", "id": "Ch<PERSON>-<PERSON><PERSON>-left"}]}, {"id": "ATAC-Seq", "parent": "1", "shape": "lane-rect-left", "site": "gray", "position": {"x": -290, "y": 930}, "data": {"state": ["E2"]}, "label": "ATAC-Seq", "ports": [{"group": "right", "id": "ATAC-Seq-right"}, {"group": "left", "id": "ATAC-Seq-left"}]}, {"id": "Methylation", "parent": "1", "shape": "lane-rect-left", "position": {"x": -290, "y": 1080}, "data": {"state": ["M1", "M2"]}, "label": "Methylation", "ports": [{"group": "right", "id": "Methylation-right"}, {"group": "left", "id": "Methylation-left"}]}, {"id": "scRNA-Seq", "parent": "1", "shape": "lane-rect-left", "position": {"x": -290, "y": 1260}, "data": {"state": ["SC1"]}, "label": "scRNA-Seq", "ports": [{"group": "right", "id": "scRNA-Seq-right"}, {"group": "left", "id": "scRNA-Seq-left"}]}, {"id": "stRNA-Seq", "parent": "1", "shape": "lane-rect-left", "position": {"x": -290, "y": 1360}, "data": {"state": ["SP1"]}, "label": "stRNA-Seq", "ports": [{"group": "right", "id": "stRNA-Seq-right"}, {"group": "left", "id": "stRNA-Seq-left"}]}, {"id": "Proteomics", "parent": "1", "shape": "lane-rect-left", "position": {"x": -290, "y": 1445}, "data": {"state": ["P1"]}, "label": "Proteomics", "ports": [{"group": "right", "id": "Proteomics-right"}, {"group": "left", "id": "Proteomics-left"}]}, {"id": "col1-R1", "shape": "lane-polygon", "position": {"x": -150, "y": 160}, "data": {"state": ["R1"], "to": "rnaseq"}, "label": "DEG", "parent": "1", "ports": [{"id": "R1-right", "group": "right"}, {"id": "R1-left", "group": "left"}]}, {"id": "col1-R2", "shape": "lane-polygon", "position": {"x": -150, "y": 240}, "label": "ASE", "data": {"state": ["R2"], "to": "ase"}, "parent": "1", "ports": [{"id": "R2-right", "group": "right"}, {"id": "R2-left", "group": "left"}]}, {"id": "col1-R3", "shape": "lane-polygon", "position": {"x": -150, "y": 320}, "label": "ASE-GPU", "parent": "1", "data": {"state": ["R3"], "to": "paean"}, "ports": [{"id": "R3-left", "group": "left"}, {"id": "R3-right", "group": "right"}]}, {"id": "col1-R4", "shape": "lane-polygon", "position": {"x": -150, "y": 400}, "label": "CircleRNA", "parent": "1", "data": {"state": ["R4"], "to": "circrna"}, "ports": [{"id": "R4-left", "group": "left"}, {"id": "R4-right", "group": "right"}]}, {"id": "col1-R5", "shape": "lane-polygon", "position": {"x": -150, "y": 490}, "label": "SmartSeq", "parent": "1", "data": {"state": ["R5"], "to": "circrna"}, "ports": [{"id": "R5-left", "group": "left"}, {"id": "R5-right", "group": "right"}]}, {"id": "col1-D1", "shape": "lane-polygon", "position": {"x": -150, "y": 590}, "label": "Somatic-SNV", "parent": "1", "data": {"state": ["D1"], "to": "somatic"}, "ports": [{"id": "D1-left", "group": "left"}, {"id": "D1-right", "group": "right"}]}, {"id": "col1-D2", "shape": "lane-polygon", "position": {"x": -150, "y": 670}, "label": "Somatic-CNV", "ports": [{"id": "D2-left", "group": "left"}, {"id": "D2-right", "group": "right"}], "parent": "1", "data": {"state": ["D2"], "to": "somatic-cnvs"}}, {"id": "col1-D3", "shape": "lane-polygon", "position": {"x": -150, "y": 750}, "label": "Germline", "parent": "1", "data": {"state": ["D3"], "to": "germline"}, "ports": [{"id": "D3-left", "group": "left"}, {"id": "D3-right", "group": "right"}]}, {"id": "col1-M1", "shape": "lane-polygon", "position": {"x": -150, "y": 1020}, "label": "WGBS", "parent": "1", "data": {"state": ["M1"], "to": "wgbs"}, "ports": [{"id": "M1-left", "group": "left"}, {"id": "M1-right", "group": "right"}]}, {"id": "col1-M2", "shape": "lane-polygon", "position": {"x": -150, "y": 1144}, "label": "BeadChip", "parent": "1", "data": {"state": ["M2"], "to": "methychip"}, "ports": [{"id": "M2-left", "group": "left"}, {"id": "M2-right", "group": "right"}]}, {"id": "col1-SC1", "shape": "lane-polygon", "position": {"x": -150, "y": 1262}, "label": "10X", "parent": "1", "data": {"state": ["SC1"], "to": "scrnaseq"}, "ports": [{"id": "SC1-left", "group": "left"}, {"id": "SC1-right", "group": "right"}]}, {"id": "col1-SP1", "shape": "lane-polygon", "position": {"x": -150, "y": 1362}, "label": "10X", "parent": "1", "data": {"state": ["SP1"], "to": "strnaseq"}, "ports": [{"id": "SP1-left", "group": "left"}, {"id": "SP1-right", "group": "right"}]}, {"id": "col1-P1", "shape": "lane-polygon", "position": {"x": -150, "y": 1447}, "label": "Proteomics", "parent": "1", "data": {"state": ["P1"], "to": "proteomics"}, "ports": [{"id": "P1-left", "group": "left"}, {"id": "P1-right", "group": "right"}]}, {"id": "col2-NGSQCToolkit", "parent": "2", "shape": "lane-rect", "position": {"x": 40, "y": 330}, "data": {"state": ["R1", "R2", "R4"]}, "label": "NGSQCToolkit", "ports": [{"group": "right", "id": "col2-NGSQCToolkit-right"}, {"group": "left", "id": "col2-NGSQCToolkit-left"}]}, {"id": "col2-Trimmomatic", "parent": "2", "shape": "lane-rect", "position": {"x": 40, "y": 500}, "data": {"state": ["R1", "R2", "R4", "R5", "D1", "D2", "D3", "M1"]}, "label": "Trimmomatic", "ports": [{"group": "right", "id": "col2-Trimm<PERSON><PERSON>-right"}, {"group": "left", "id": "col2-<PERSON><PERSON><PERSON><PERSON>-left"}]}, {"id": "col2-trim_galore", "parent": "2", "shape": "lane-rect", "position": {"x": 40, "y": 880}, "data": {"state": ["E1", "E2"]}, "site": "gray", "label": "trim_galore", "ports": [{"group": "right", "id": "col2-trim_galore-right"}, {"group": "left", "id": "col2-trim_galore-left"}]}, {"id": "col2-ChAMP:champ.QC", "parent": "2", "shape": "lane-rect", "position": {"x": 40, "y": 1150}, "data": {"state": ["M2"]}, "label": "ChAMP:\nchamp.QC", "ports": [{"group": "right", "id": "col2-ChAMP:champ.QC-right"}, {"group": "left", "id": "col2-ChAMP:champ.QC-left"}]}, {"id": "col2-Cell Ranger", "parent": "2", "shape": "lane-rect", "position": {"x": 40, "y": 1268}, "data": {"state": ["SC1"]}, "label": "Cell Ranger", "ports": [{"group": "right", "id": "col2-<PERSON>-right"}, {"group": "left", "id": "col2-<PERSON>-left"}]}, {"id": "col3-HISAT2", "parent": "3", "shape": "lane-rect", "position": {"x": 210, "y": 170}, "data": {"state": ["R1"]}, "label": "HISAT2", "ports": [{"group": "right", "id": "col3-HISAT2-right"}, {"group": "left", "id": "col3-HISAT2-left"}]}, {"id": "col3-STAR", "parent": "3", "shape": "lane-rect", "position": {"x": 210, "y": 250}, "data": {"state": ["R1", "R2", "R4", "R5"]}, "label": "STAR", "ports": [{"group": "right", "id": "col3-STAR-right"}, {"group": "left", "id": "col3-<PERSON><PERSON>-left"}]}, {"id": "col3-RSEM:STAR", "parent": "3", "shape": "lane-rect", "position": {"x": 210, "y": 330}, "data": {"state": ["R1"]}, "label": "RSEM:STAR", "ports": [{"group": "right", "id": "col3-RSEM:STAR-right"}, {"group": "left", "id": "col3-RSEM:STAR-left"}]}, {"id": "col3-MapSplice", "parent": "3", "shape": "lane-rect", "position": {"x": 210, "y": 410}, "data": {"state": ["R2"]}, "site": "gray", "label": "MapSplice", "ports": [{"group": "right", "id": "col3-MapSplice-right"}, {"group": "left", "id": "col3-MapSplice-left"}]}, {"id": "col3-BWA", "parent": "3", "shape": "lane-rect", "position": {"x": 210, "y": 500}, "data": {"state": ["R4", "D1", "D2", "D3"]}, "label": "BWA", "ports": [{"group": "top", "id": "col3-BWA-top"}, {"group": "bottom", "id": "col3-BWA-bottom"}, {"group": "right", "id": "col3-BWA-right"}, {"group": "left", "id": "col3-B<PERSON>-left"}]}, {"id": "col3-<PERSON><PERSON> Duplicates", "parent": "3", "shape": "lane-rect", "position": {"x": 230, "y": 620}, "data": {"state": ["D1", "D2", "D3", "E1", "E2"]}, "label": "Marker\nDuplicates", "ports": [{"group": "top", "id": "col3-<PERSON><PERSON> Du<PERSON>-top"}, {"group": "bottom", "id": "col3-<PERSON><PERSON> Duplicate<PERSON>-bottom"}, {"group": "right", "id": "col3-<PERSON><PERSON>-right"}, {"group": "left", "id": "col3-<PERSON><PERSON>-left"}]}, {"id": "col3-GATK4:BQSR", "parent": "3", "shape": "lane-rect", "position": {"x": 230, "y": 720}, "data": {"state": ["D1", "D2", "D3"]}, "label": "GATK4:BQSR", "ports": [{"group": "top", "id": "col3-GATK4:BQSR-top"}, {"group": "bottom", "id": "col3-GATK4:BQSR-bottom"}, {"group": "right", "id": "col3-GATK4:BQSR-right"}, {"group": "left", "id": "col3-GATK4:BQ<PERSON>-left"}]}, {"id": "col3-<PERSON><PERSON>2", "parent": "3", "shape": "lane-rect", "position": {"x": 210, "y": 820}, "data": {"state": ["E1", "E2", "M1"]}, "label": "Bowtie2", "ports": [{"group": "top", "id": "col3-<PERSON><PERSON>2-top"}, {"group": "bottom", "id": "col3-<PERSON><PERSON>2-bottom"}, {"group": "right", "id": "col3-<PERSON><PERSON>2-right"}, {"group": "left", "id": "col3-<PERSON><PERSON>2-left"}]}, {"id": "col3-Cell Ranger:STAR", "parent": "3", "shape": "lane-rect", "position": {"x": 220, "y": 1268}, "data": {"state": ["SC1"]}, "label": "Cell\nRanger:STAR", "ports": [{"group": "right", "id": "col3-Cell Ranger:STAR-right"}, {"group": "left", "id": "col3-Cell Ranger:<PERSON>AR-left"}]}, {"id": "col4-featureCounts", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 160}, "data": {"state": ["R1", "R5"]}, "label": "featureCounts", "ports": [{"group": "right", "id": "col4-featureCounts-right"}, {"group": "left", "id": "col4-featureCounts-left"}]}, {"id": "col4-HTSeq", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 220}, "data": {"state": ["R1"]}, "label": "HTSeq", "ports": [{"group": "right", "id": "col4-HTSeq-right"}, {"group": "left", "id": "col4-H<PERSON>eq-left"}]}, {"id": "col4-RSEM", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 280}, "data": {"state": ["R1"]}, "label": "RSEM", "ports": [{"group": "right", "id": "col4-RSEM-right"}, {"group": "left", "id": "col4-<PERSON><PERSON>-left"}]}, {"id": "col4-rMATs", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 340}, "data": {"state": ["R2"]}, "label": "rMATs", "ports": [{"group": "right", "id": "col4-rMATs-right"}, {"group": "left", "id": "col4-rMATs-left"}]}, {"id": "col4-<PERSON><PERSON>", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 400}, "data": {"state": ["R3"]}, "label": "<PERSON><PERSON>", "ports": [{"group": "right", "id": "col4-<PERSON><PERSON>-right"}, {"group": "left", "id": "col4-<PERSON><PERSON>-left"}]}, {"id": "col4-CIRCexplorer2", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 460}, "data": {"state": ["R4"]}, "label": "CIRCexplorer2", "ports": [{"group": "right", "id": "col4-CIRCexplorer2-right"}, {"group": "left", "id": "col4-CIRCexplorer2-left"}]}, {"id": "col4-<PERSON><PERSON>", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 520}, "data": {"state": ["R2"]}, "site": "gray", "label": "<PERSON><PERSON>", "ports": [{"group": "right", "id": "col4-<PERSON><PERSON>-right"}, {"group": "left", "id": "col4-<PERSON><PERSON>-left"}]}, {"id": "col4-CIRI", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 580}, "data": {"state": ["R4"]}, "label": "CIRI", "ports": [{"group": "right", "id": "col4-CIRI-right"}, {"group": "left", "id": "col4-C<PERSON><PERSON>-left"}]}, {"id": "col4-GATK4:Mutect2", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 660}, "data": {"state": ["D1"]}, "label": "GATK4:Mutect2", "ports": [{"group": "right", "id": "col4-GATK4:Mutect2-right"}, {"group": "left", "id": "col4-GA<PERSON>K4:<PERSON><PERSON>t2-left"}]}, {"id": "col4-GATK4:CNV", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 720}, "data": {"state": ["D2"]}, "label": "GATK4:CNV", "ports": [{"group": "right", "id": "col4-GATK4:CNV-right"}, {"group": "left", "id": "col4-GATK4:CNV-left"}]}, {"id": "col4-GATK4:HaplotypeCaller", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 780}, "data": {"state": ["D3"]}, "label": "GATK4:\nHaplotypeCaller", "ports": [{"group": "right", "id": "col4-GATK4:HaplotypeCaller-right"}, {"group": "left", "id": "col4-GATK4:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-left"}]}, {"id": "col4-MACS14/2", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 920}, "site": "gray", "data": {"state": ["E1", "E2"]}, "label": "MACS14/2", "ports": [{"group": "right", "id": "col4-MACS14/2-right"}, {"group": "left", "id": "col4-MACS14/2-left"}]}, {"id": "col4-Bismark", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 1010}, "data": {"state": ["M1"]}, "label": "Bismark", "ports": [{"group": "right", "id": "col4-Bismark-right"}, {"group": "left", "id": "col4-Bismark-left"}]}, {"id": "col4-Cell Ranger", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 1268}, "data": {"state": ["SC1"]}, "label": "Cell Ranger", "ports": [{"group": "right", "id": "col4-<PERSON>-right"}, {"group": "left", "id": "col4-<PERSON>-left"}]}, {"id": "col4-Space Ranger", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 1368}, "data": {"state": ["SP1"]}, "label": "Space Ranger", "ports": [{"group": "right", "id": "col4-Space Ranger-right"}, {"group": "left", "id": "col4-Space Ranger-left"}]}, {"id": "col4-MaxQuant", "parent": "4", "shape": "lane-rect", "position": {"x": 380, "y": 1453}, "data": {"state": ["P1"]}, "label": "MaxQuant", "ports": [{"group": "right", "id": "col4-MaxQuant-right"}, {"group": "left", "id": "col4-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col5-GATK4:FilterV<PERSON>ts", "parent": "5", "shape": "lane-rect", "position": {"x": 550, "y": 660}, "data": {"state": ["D1"]}, "label": "GATK4:\nFilterV<PERSON>ts", "ports": [{"group": "right", "id": "col5-GATK4:<PERSON><PERSON><PERSON><PERSON><PERSON>-right"}, {"group": "left", "id": "col5-GATK4:<PERSON><PERSON><PERSON><PERSON><PERSON>-left"}]}, {"id": "col5-GATK4:VQSR", "parent": "5", "shape": "lane-rect", "position": {"x": 550, "y": 750}, "data": {"state": ["D3"]}, "label": "GATK4:VQSR", "ports": [{"group": "right", "id": "col5-GATK4:VQSR-right"}, {"group": "left", "id": "col5-GATK4:VQ<PERSON>-left"}]}, {"id": "col5-GATK4:<PERSON>Filter", "parent": "5", "shape": "lane-rect", "position": {"x": 550, "y": 830}, "data": {"state": ["D3"]}, "label": "GATK4:\nHardF<PERSON>er", "ports": [{"group": "right", "id": "col5-GA<PERSON><PERSON>4:<PERSON><PERSON><PERSON><PERSON>-right"}, {"group": "left", "id": "col5-<PERSON><PERSON><PERSON>4:<PERSON><PERSON><PERSON><PERSON>-left"}]}, {"id": "col5-ChAMP:champ.norm", "parent": "5", "shape": "lane-rect", "position": {"x": 540, "y": 1150}, "data": {"state": ["M2"]}, "label": "ChAMP:\nchamp.norm", "ports": [{"group": "right", "id": "col5-ChAMP:champ.norm-right"}, {"group": "left", "id": "col5-ChAMP:champ.norm-left"}]}, {"id": "col5-<PERSON><PERSON><PERSON>", "parent": "5", "shape": "lane-rect", "position": {"x": 540, "y": 1320}, "data": {"state": ["SC1", "SP1"]}, "label": "<PERSON><PERSON><PERSON>", "ports": [{"group": "right", "id": "col5-<PERSON><PERSON><PERSON>-right"}, {"group": "left", "id": "col5-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col6-DESeq2", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 190}, "data": {"state": ["R1"]}, "label": "DESeq2", "ports": [{"group": "bottom", "id": "col6-DESeq2-bottom"}, {"group": "right", "id": "col6-DESeq2-right"}, {"group": "left", "id": "col6-DESeq2-left"}]}, {"id": "col6-GO/KEGGEnrichment", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 270}, "data": {"state": ["R1", "D1"]}, "label": "GO/KEGG\nEnrichment", "ports": [{"group": "top", "id": "col6-GO/KEGGEnrichment-top"}, {"group": "right", "id": "col6-GO/KEGGEnrichment-right"}, {"group": "left", "id": "col6-GO/KEGGEnrichment-left"}]}, {"id": "col6-MutsigCV", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 440}, "data": {"state": ["D1"]}, "label": "MutsigCV", "ports": [{"group": "right", "id": "col6-<PERSON><PERSON>ig<PERSON><PERSON>-right"}, {"group": "left", "id": "col6-<PERSON><PERSON><PERSON><PERSON><PERSON>-left"}]}, {"id": "col6-MAFTOOLS:oncodriveCLUST", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 510}, "data": {"state": ["D1"]}, "label": "MAFTOOLS:\noncodriveCLUST", "ports": [{"group": "right", "id": "col6-MAFTOOLS:oncodriveCLUST-right"}, {"group": "left", "id": "col6-MAFTOOLS:oncodriveCLUST-left"}]}, {"id": "col6-MAFTOOLS", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 580}, "data": {"state": ["D1"]}, "label": "MAFTOOLS", "ports": [{"group": "right", "id": "col6-MAFTOOLS-right"}, {"group": "left", "id": "col6-MAFTOOLS-left"}]}, {"id": "col6-<PERSON><PERSON><PERSON>", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 660}, "data": {"state": ["D1", "D3"]}, "label": "<PERSON><PERSON><PERSON>", "ports": [{"group": "right", "id": "col6-<PERSON><PERSON><PERSON>-right"}, {"group": "left", "id": "col6-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col6-ABSOLUTE", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 720}, "data": {"state": ["D2"]}, "site": "gray", "label": "ABSOLUTE", "ports": [{"group": "right", "id": "col6-ABSOLUTE-right"}, {"group": "left", "id": "col6-ABSOLUTE-left"}]}, {"id": "col6-HOM<PERSON>", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 860}, "data": {"state": ["E1", "E2"]}, "site": "gray", "label": "HOMER", "ports": [{"group": "right", "id": "col6-<PERSON>OM<PERSON>-right"}, {"group": "left", "id": "col6-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col6-Deeptools", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 920}, "data": {"state": ["E1", "E2"]}, "site": "gray", "label": "Deeptools", "ports": [{"group": "right", "id": "col6-<PERSON><PERSON><PERSON>-right"}, {"group": "left", "id": "col6-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col6-Ch<PERSON><PERSON>ker", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 980}, "data": {"state": ["E1", "E2", "M1"]}, "label": "ChIPseeker", "ports": [{"group": "right", "id": "col6-<PERSON><PERSON><PERSON><PERSON>-right"}, {"group": "left", "id": "col6-<PERSON><PERSON><PERSON><PERSON>-left"}]}, {"id": "col6-DSS", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 1060}, "data": {"state": ["M1"]}, "label": "DSS", "ports": [{"group": "right", "id": "col6-DSS-right"}, {"group": "left", "id": "col6-<PERSON><PERSON>-left"}]}, {"id": "col6-ChAMP:champ.DMP", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 1150}, "data": {"state": ["M2"]}, "label": "ChAMP:\nchamp.DMP", "ports": [{"group": "right", "id": "col6-ChAMP:champ.DMP-right"}, {"group": "left", "id": "col6-ChAMP:champ.DMP-left"}]}, {"id": "col6-Cellassign", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 1210}, "data": {"state": ["SC1"]}, "label": "Cellassign", "ports": [{"group": "right", "id": "col6-Cellassign-right"}, {"group": "left", "id": "col6-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col6-<PERSON><PERSON>", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 1268}, "data": {"state": ["SC1"]}, "label": "<PERSON>s", "ports": [{"group": "right", "id": "col6-<PERSON><PERSON>-right"}, {"group": "left", "id": "col6-<PERSON><PERSON>-left"}]}, {"id": "col6-DEG", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 1330}, "data": {"state": ["SC1", "SP1"]}, "label": "DEG", "ports": [{"group": "right", "id": "col6-DEG-right"}, {"group": "left", "id": "col6-<PERSON><PERSON>-left"}]}, {"id": "col6-Pseudotimetrajectory", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 1390}, "data": {"state": ["SC1"]}, "label": "Pseudotime\ntrajectory", "ports": [{"group": "right", "id": "col6-Pseudotimetrajectory-right"}, {"group": "left", "id": "col6-<PERSON><PERSON><PERSON>timetrajectory-left"}]}, {"id": "col6-WGCNA", "parent": "6", "shape": "lane-rect", "position": {"x": 720, "y": 1450}, "data": {"state": ["SC1"]}, "label": "WGCNA", "ports": [{"group": "right", "id": "col6-WGCNA-right"}, {"group": "left", "id": "col6-W<PERSON><PERSON>-left"}]}, {"id": "col7-Heatmap", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 160}, "data": {"state": ["R1", "D1", "E1", "E2", "M2"]}, "label": "Heatmap", "ports": [{"group": "right", "id": "col7-Heatmap-right"}, {"group": "left", "id": "col7-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col7-VolcanoPlot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 220}, "data": {"state": ["R1", "SC1", "SC2"]}, "label": "VolcanoPlot", "ports": [{"group": "right", "id": "col7-VolcanoPlot-right"}, {"group": "left", "id": "col7-VolcanoPlot-left"}]}, {"id": "col7-MAplot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 280}, "data": {"state": ["R1"]}, "label": "MAplot", "ports": [{"group": "right", "id": "col7-MAplot-right"}, {"group": "left", "id": "col7-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col7-PCA", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 340}, "data": {"state": ["R1", "M2"]}, "label": "PCA", "ports": [{"group": "right", "id": "col7-PCA-right"}, {"group": "left", "id": "col7-<PERSON><PERSON>-left"}]}, {"id": "col7-DotPlot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 400}, "data": {"state": ["D1", "SC1", "SC2"]}, "label": "DotPlot", "ports": [{"group": "right", "id": "col7-Dot<PERSON><PERSON>-right"}, {"group": "left", "id": "col7-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col7-BarPlot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 460}, "data": {"state": ["R1", "D1"]}, "label": "BarPlot", "ports": [{"group": "right", "id": "col7-Bar<PERSON><PERSON>-right"}, {"group": "left", "id": "col7-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col7-Association", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 520}, "data": {"state": ["R1", "D1"]}, "label": "Association", "ports": [{"group": "right", "id": "col7-Association-right"}, {"group": "left", "id": "col7-Association-left"}]}, {"id": "col7-BoxPlot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 580}, "data": {"state": ["R5"]}, "label": "BoxPlot", "ports": [{"group": "right", "id": "col7-BoxPlot-right"}, {"group": "left", "id": "col7-BoxPlot-left"}]}, {"id": "col7-OncoPlot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 640}, "data": {"state": ["D1"]}, "label": "OncoPlot", "ports": [{"group": "right", "id": "col7-OncoPlot-right"}, {"group": "left", "id": "col7-OncoPlot-left"}]}, {"id": "col7-LollipopPlot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 700}, "data": {"state": ["D1"]}, "label": "LollipopPlot", "ports": [{"group": "right", "id": "col7-LollipopPlot-right"}, {"group": "left", "id": "col7-LollipopPlot-left"}]}, {"id": "col7-BubblePlot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 760}, "data": {"state": ["R1", "D1"]}, "label": "BubblePlot", "ports": [{"group": "right", "id": "col7-<PERSON><PERSON>blePlot-right"}, {"group": "left", "id": "col7-<PERSON><PERSON><PERSON><PERSON><PERSON>-left"}]}, {"id": "col7-<PERSON><PERSON><PERSON>", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 860}, "data": {"state": ["E1", "E2"]}, "site": "gray", "label": "<PERSON><PERSON><PERSON>", "ports": [{"group": "right", "id": "col7-<PERSON><PERSON><PERSON>-right"}, {"group": "left", "id": "col7-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col7-Profiling", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 920}, "data": {"state": ["E1", "E2"]}, "site": "gray", "label": "Profiling", "ports": [{"group": "right", "id": "col7-Profiling-right"}, {"group": "left", "id": "col7-Profiling-left"}]}, {"id": "col7-PiePlot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 980}, "data": {"state": ["M1"]}, "label": "PiePlot", "site": "gray", "ports": [{"group": "right", "id": "col7-<PERSON><PERSON><PERSON>-right"}, {"group": "left", "id": "col7-<PERSON><PERSON><PERSON>-left"}]}, {"id": "col7-Circular stacked", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 1040}, "data": {"state": ["M1"]}, "site": "gray", "label": "Circular stacked", "ports": [{"group": "right", "id": "col7-Circular stacked-right"}, {"group": "left", "id": "col7-Circular stacked-left"}]}, {"id": "col7-t-SNE", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 1230}, "data": {"state": ["SC1", "SP1"]}, "label": "t-SNE", "ports": [{"group": "right", "id": "col7-t-SNE-right"}, {"group": "left", "id": "col7-t-SNE-left"}]}, {"id": "col7-ViolinPlot", "parent": "7", "shape": "lane-rect-end", "position": {"x": 890, "y": 1330}, "data": {"state": ["SC1"]}, "label": "ViolinPlot", "ports": [{"group": "right", "id": "col7-Violin<PERSON><PERSON>-right"}, {"group": "left", "id": "col7-<PERSON><PERSON><PERSON>-left"}]}, {"id": "40258ea0-6ec8-47a6-ab4e-6c5594e21d8d", "shape": "lane-edge", "source": {"cell": "RNA-Seq", "port": "RNA-Seq-right"}, "target": {"cell": "col1-R1", "port": "R1-left"}, "vertices": []}, {"id": "433670da-a1e3-4d4c-b9ad-2869dba0ad65", "shape": "lane-edge", "source": {"cell": "RNA-Seq", "port": "RNA-Seq-right"}, "target": {"cell": "col1-R2", "port": "R2-left"}, "vertices": []}, {"id": "73af27ff-2153-4b62-901b-03c72cc7f802", "shape": "lane-edge", "source": {"cell": "RNA-Seq", "port": "RNA-Seq-right"}, "target": {"cell": "col1-R3", "port": "R3-left"}, "vertices": []}, {"id": "032d7884-33df-4a2d-867b-cde9ee1d819a", "shape": "lane-edge", "source": {"cell": "RNA-Seq", "port": "RNA-Seq-right"}, "target": {"cell": "col1-R4", "port": "R4-left"}, "vertices": []}, {"id": "dd58b99d-3dbb-46ac-9fd9-81eb07b9b5e2", "shape": "lane-edge", "source": {"cell": "RNA-Seq", "port": "RNA-Seq-right"}, "target": {"cell": "col1-R5", "port": "R5-left"}, "vertices": []}, {"id": "ba3d0648-af9e-4e97-bbe8-9b34eff0f9c4", "shape": "lane-edge", "source": {"cell": "col1-R4", "port": "R4-right"}, "target": {"cell": "col2-NGSQCToolkit", "port": "col2-NGSQCToolkit-left"}, "vertices": []}, {"id": "0fda2f10-3c30-44ab-ab56-51c1d3ed4683", "shape": "lane-edge", "source": {"cell": "col1-R2", "port": "R2-right"}, "target": {"cell": "col2-NGSQCToolkit", "port": "col2-NGSQCToolkit-left"}, "vertices": []}, {"id": "03311fa1-cbda-449f-9326-12763ae2c2d2", "shape": "lane-edge", "source": {"cell": "col1-R1", "port": "R1-right"}, "target": {"cell": "col2-NGSQCToolkit", "port": "col2-NGSQCToolkit-left"}, "vertices": []}, {"id": "71f0fb7e-2f08-47b8-9271-5db029f69cd4", "shape": "lane-edge", "source": {"cell": "col1-R1", "port": "R1-right"}, "target": {"cell": "col2-Trimmomatic", "port": "col2-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "4833b15d-424f-464e-8a98-f20b5e6b09f6", "shape": "lane-edge", "source": {"cell": "col1-R2", "port": "R2-right"}, "target": {"cell": "col2-Trimmomatic", "port": "col2-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "c8ed4314-86a8-4f8d-aae6-c5da73f985d6", "shape": "lane-edge", "source": {"cell": "col1-R4", "port": "R4-right"}, "target": {"cell": "col2-Trimmomatic", "port": "col2-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "d24a109c-6516-42b1-a1a9-2eb4c0bf8678", "shape": "lane-edge", "source": {"cell": "col1-R5", "port": "R5-right"}, "target": {"cell": "col2-Trimmomatic", "port": "col2-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "0a238e66-44c5-456c-83f4-5df94ef1e957", "shape": "lane-edge", "source": {"cell": "col1-D1", "port": "D1-right"}, "target": {"cell": "col2-Trimmomatic", "port": "col2-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "bd0a3d07-2dd2-45d3-ac61-cf66fe007386", "shape": "lane-edge", "source": {"cell": "col1-D2", "port": "D2-right"}, "target": {"cell": "col2-Trimmomatic", "port": "col2-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "c0e0e5f6-4434-4792-a1bc-8491cca96bc5", "shape": "lane-edge", "source": {"cell": "col1-D3", "port": "D3-right"}, "target": {"cell": "col2-Trimmomatic", "port": "col2-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "2f3af5e2-a10f-4240-b86a-edff5e80a097", "shape": "lane-edge", "source": {"cell": "col1-M1", "port": "M1-right"}, "target": {"cell": "col2-Trimmomatic", "port": "col2-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "27e7498a-6421-46e2-9902-752aebdf8a71", "shape": "lane-edge", "source": {"cell": "DNA-Seq", "port": "DNA-Seq-right"}, "target": {"cell": "col1-D1", "port": "D1-left"}, "vertices": []}, {"id": "a5e95638-d68a-4847-b3b2-15ae406cd26b", "shape": "lane-edge", "source": {"cell": "DNA-Seq", "port": "DNA-Seq-right"}, "target": {"cell": "col1-D2", "port": "D2-left"}, "vertices": []}, {"id": "26cd4ea8-520a-4f5b-98ce-84538b9778a6", "shape": "lane-edge", "source": {"cell": "DNA-Seq", "port": "DNA-Seq-right"}, "target": {"cell": "col1-D3", "port": "D3-left"}, "vertices": []}, {"id": "040e3510-e7e6-4b07-b59d-01b656f9de21", "shape": "lane-edge", "source": {"cell": "scRNA-Seq", "port": "scRNA-Seq-right"}, "target": {"cell": "col1-SC1", "port": "SC1-left"}, "vertices": []}, {"id": "211a0222-f812-4104-bcf7-5e7a3efea8dc", "shape": "lane-edge", "source": {"cell": "stRNA-Seq", "port": "stRNA-Seq-right"}, "target": {"cell": "col1-SP1", "port": "SP1-left"}, "vertices": []}, {"id": "186ec5bd-d69e-4157-b9c5-c1da247e2257", "shape": "lane-edge", "source": {"cell": "col1-P1", "port": "P1-right"}, "target": {"cell": "col4-MaxQuant", "port": "col4-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "d4b0b445-81da-4d3b-b9c6-2e8dd901a201", "shape": "lane-edge", "source": {"cell": "col1-SP1", "port": "SP1-right"}, "target": {"cell": "col4-Space Ranger", "port": "col4-Space Ranger-left"}, "vertices": []}, {"id": "c5168eae-f072-409b-9394-ef52b4fb0871", "shape": "lane-edge", "source": {"cell": "Methylation", "port": "Methylation-right"}, "target": {"cell": "col1-M1", "port": "M1-left"}, "vertices": []}, {"id": "46dbbc6b-8355-42fd-8b93-2ac13c0190e0", "shape": "lane-edge", "source": {"cell": "Methylation", "port": "Methylation-right"}, "target": {"cell": "col1-M2", "port": "M2-left"}, "vertices": []}, {"id": "ad0b1119-5607-41ab-af40-c5c751897ae3", "shape": "lane-edge", "source": {"cell": "col1-R3", "port": "R3-right"}, "target": {"cell": "col4-<PERSON><PERSON>", "port": "col4-<PERSON><PERSON>-left"}, "vertices": [{"x": 53, "y": 368}]}, {"id": "3f5fa740-916a-4910-b014-f008a83d121a", "shape": "lane-edge", "source": {"cell": "col1-M2", "port": "M2-right"}, "target": {"cell": "col2-ChAMP:champ.QC", "port": "col2-ChAMP:champ.QC-left"}, "vertices": []}, {"id": "0d656a98-c494-48ff-a035-5ec85a097945", "shape": "lane-edge", "source": {"cell": "col1-SC1", "port": "SC1-right"}, "target": {"cell": "col2-Cell Ranger", "port": "col2-<PERSON>-left"}, "vertices": []}, {"id": "21ebca49-6b7c-4000-ae54-b212c259ee0c", "shape": "lane-edge", "source": {"cell": "col2-Cell Ranger", "port": "col2-<PERSON>-right"}, "target": {"cell": "col3-Cell Ranger:STAR", "port": "col3-Cell Ranger:<PERSON>AR-left"}, "vertices": []}, {"id": "84d304db-cf86-4726-aacc-be0b2207e918", "shape": "lane-edge", "source": {"cell": "col3-Cell Ranger:STAR", "port": "col3-Cell Ranger:STAR-right"}, "target": {"cell": "col4-Cell Ranger", "port": "col4-<PERSON>-left"}, "vertices": []}, {"id": "f1e6379e-cca3-482c-9fd1-0503c6bf58ef", "shape": "lane-edge", "source": {"cell": "col4-Space Ranger", "port": "col4-Space Ranger-right"}, "target": {"cell": "col5-<PERSON><PERSON><PERSON>", "port": "col5-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "6f4f2cbe-7a38-4ea0-a0dc-a4013ca0376a", "shape": "lane-edge", "source": {"cell": "col4-Cell Ranger", "port": "col4-<PERSON>-right"}, "target": {"cell": "col5-<PERSON><PERSON><PERSON>", "port": "col5-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "b93c8995-4401-44aa-97a6-c87f5d194343", "shape": "lane-edge", "source": {"cell": "col5-<PERSON><PERSON><PERSON>", "port": "col5-<PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col6-<PERSON><PERSON>", "port": "col6-<PERSON><PERSON>-left"}, "vertices": []}, {"id": "e5ba5e1a-222d-4a1f-8b0d-3ac4293eba34", "shape": "lane-edge", "source": {"cell": "col5-<PERSON><PERSON><PERSON>", "port": "col5-<PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col6-DEG", "port": "col6-<PERSON><PERSON>-left"}, "vertices": []}, {"id": "b7a48e09-5e03-4fd7-802b-56dc82f595c8", "shape": "lane-edge", "source": {"cell": "col5-<PERSON><PERSON><PERSON>", "port": "col5-<PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col6-Pseudotimetrajectory", "port": "col6-<PERSON><PERSON><PERSON>timetrajectory-left"}, "vertices": []}, {"id": "224a1c5a-e230-4697-b91d-f7594d3fb987", "shape": "lane-edge", "source": {"cell": "col5-<PERSON><PERSON><PERSON>", "port": "col5-<PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col6-WGCNA", "port": "col6-W<PERSON><PERSON>-left"}, "vertices": []}, {"id": "bd40b6c4-45ec-4a01-b1f6-48e4f9394aa8", "shape": "lane-edge", "source": {"cell": "col6-WGCNA", "port": "col6-WGCNA-right"}, "target": {"cell": "col7-Heatmap", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "0a9a0ebd-e003-4e21-b9eb-f81e2193019a", "shape": "lane-edge", "source": {"cell": "col2-NGSQCToolkit", "port": "col2-NGSQCToolkit-right"}, "target": {"cell": "col3-HISAT2", "port": "col3-HISAT2-left"}, "vertices": []}, {"id": "2b788c41-51df-4498-a8ce-3e51989e46c4", "shape": "lane-edge", "source": {"cell": "col2-Trimmomatic", "port": "col2-Trimm<PERSON><PERSON>-right"}, "target": {"cell": "col3-HISAT2", "port": "col3-HISAT2-left"}, "vertices": []}, {"id": "9c29c8e0-9178-4b03-84d7-3285c4e69ac4", "shape": "lane-edge", "source": {"cell": "col2-NGSQCToolkit", "port": "col2-NGSQCToolkit-right"}, "target": {"cell": "col3-STAR", "port": "col3-<PERSON><PERSON>-left"}, "vertices": []}, {"id": "e2f08bb3-e3b6-41e9-96b1-a64aa50fe3e6", "shape": "lane-edge", "source": {"cell": "col2-Trimmomatic", "port": "col2-Trimm<PERSON><PERSON>-right"}, "target": {"cell": "col3-STAR", "port": "col3-<PERSON><PERSON>-left"}, "vertices": []}, {"id": "b88f83ab-6888-4f6a-8ea8-2151bb6b4448", "shape": "lane-edge", "source": {"cell": "col2-NGSQCToolkit", "port": "col2-NGSQCToolkit-right"}, "target": {"cell": "col3-RSEM:STAR", "port": "col3-RSEM:STAR-left"}, "vertices": []}, {"id": "5d66e55b-ede6-4ebd-9471-6e50416d1bec", "shape": "lane-edge", "source": {"cell": "col2-Trimmomatic", "port": "col2-Trimm<PERSON><PERSON>-right"}, "target": {"cell": "col3-RSEM:STAR", "port": "col3-RSEM:STAR-left"}, "vertices": []}, {"id": "e6ba920c-043f-4b61-962f-1286383dd194", "shape": "lane-edge", "source": {"cell": "col2-NGSQCToolkit", "port": "col2-NGSQCToolkit-right"}, "target": {"cell": "col3-MapSplice", "port": "col3-MapSplice-left"}, "vertices": []}, {"id": "09ca0011-a658-4c95-9331-66067a1bcad7", "shape": "lane-edge", "source": {"cell": "col2-Trimmomatic", "port": "col2-Trimm<PERSON><PERSON>-right"}, "target": {"cell": "col3-MapSplice", "port": "col3-MapSplice-left"}, "vertices": []}, {"id": "ae489250-fe05-4aea-b6b9-b003c7bdc715", "shape": "lane-edge", "source": {"cell": "col2-NGSQCToolkit", "port": "col2-NGSQCToolkit-right"}, "target": {"cell": "col3-BWA", "port": "col3-B<PERSON>-left"}, "vertices": []}, {"id": "67069215-0b0c-432a-a121-b670809575bf", "shape": "lane-edge", "source": {"cell": "col2-Trimmomatic", "port": "col2-Trimm<PERSON><PERSON>-right"}, "target": {"cell": "col3-BWA", "port": "col3-B<PERSON>-left"}, "vertices": []}, {"id": "bbb41410-ae9b-4eb2-9168-9d2878f21d32", "shape": "lane-edge", "source": {"cell": "col3-BWA", "port": "col3-BWA-bottom"}, "target": {"cell": "col3-<PERSON><PERSON> Duplicates", "port": "col3-<PERSON><PERSON> Du<PERSON>-top"}, "vertices": []}, {"id": "94b42b88-b14d-4b49-b940-489f0c55f452", "shape": "lane-edge", "source": {"cell": "col3-<PERSON><PERSON> Duplicates", "port": "col3-<PERSON><PERSON> Duplicate<PERSON>-bottom"}, "target": {"cell": "col3-GATK4:BQSR", "port": "col3-GATK4:BQSR-top"}, "vertices": []}, {"id": "a5ff5347-6fc7-4bc9-a90d-3c47c7fb6645", "shape": "lane-edge", "source": {"cell": "col3-<PERSON><PERSON>2", "port": "col3-<PERSON><PERSON>2-top"}, "target": {"cell": "col3-<PERSON><PERSON> Duplicates", "port": "col3-<PERSON><PERSON>-left"}, "vertices": [{"x": 208, "y": 754}]}, {"id": "8c5f92cc-fcb4-4e5a-bf40-9bcad25a1108", "shape": "lane-edge", "source": {"cell": "ChIP-Seq", "port": "ChIP-Seq-right"}, "target": {"cell": "col2-trim_galore", "port": "col2-trim_galore-left"}, "vertices": []}, {"id": "ab27622f-8615-44f6-88ba-29044c8e274c", "shape": "lane-edge", "source": {"cell": "ATAC-Seq", "port": "ATAC-Seq-right"}, "target": {"cell": "col2-trim_galore", "port": "col2-trim_galore-left"}, "vertices": []}, {"id": "cf617fb7-b618-493c-a421-cad6a1e52efa", "shape": "lane-edge", "source": {"cell": "col2-trim_galore", "port": "col2-trim_galore-right"}, "target": {"cell": "col3-<PERSON><PERSON>2", "port": "col3-<PERSON><PERSON>2-left"}, "vertices": []}, {"id": "18b77492-2393-40a4-95b3-a1143bffaa34", "shape": "lane-edge", "source": {"cell": "col2-Trimmomatic", "port": "col2-Trimm<PERSON><PERSON>-right"}, "target": {"cell": "col3-<PERSON><PERSON>2", "port": "col3-<PERSON><PERSON>2-left"}, "vertices": []}, {"id": "b5bc5678-4094-4b4a-9a0d-5145aa08b6c9", "shape": "lane-edge", "source": {"cell": "col3-<PERSON><PERSON>2", "port": "col3-<PERSON><PERSON>2-right"}, "target": {"cell": "col4-Bismark", "port": "col4-Bismark-left"}, "vertices": []}, {"id": "1753719a-a64c-438b-a75c-ef99e1a2e9ed", "shape": "lane-edge", "source": {"cell": "col4-Bismark", "port": "col4-Bismark-right"}, "target": {"cell": "col6-Ch<PERSON><PERSON>ker", "port": "col6-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "122c3e5d-0e03-4190-8c6e-bc874132424d", "shape": "lane-edge", "source": {"cell": "col4-Bismark", "port": "col4-Bismark-right"}, "target": {"cell": "col6-DSS", "port": "col6-<PERSON><PERSON>-left"}, "vertices": []}, {"id": "712f4cc1-eda5-40df-bb58-b2b26480b98d", "shape": "lane-edge", "source": {"cell": "col4-MACS14/2", "port": "col4-MACS14/2-right"}, "target": {"cell": "col6-HOM<PERSON>", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "14770050-a075-4167-a8ff-880449dba344", "shape": "lane-edge", "source": {"cell": "col4-MACS14/2", "port": "col4-MACS14/2-right"}, "target": {"cell": "col6-Deeptools", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "3b6f7ea8-9df8-4a81-b00f-8385ea5e36c8", "shape": "lane-edge", "source": {"cell": "col4-MACS14/2", "port": "col4-MACS14/2-right"}, "target": {"cell": "col6-Ch<PERSON><PERSON>ker", "port": "col6-<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "bf2a6cad-7377-44d1-b7a8-95a7e076fe13", "shape": "lane-edge", "source": {"cell": "col3-GATK4:BQSR", "port": "col3-GATK4:BQSR-right"}, "target": {"cell": "col4-GATK4:Mutect2", "port": "col4-GA<PERSON>K4:<PERSON><PERSON>t2-left"}, "vertices": []}, {"id": "2124e2af-58ed-4f6e-b024-d5d05c77f3eb", "shape": "lane-edge", "source": {"cell": "col3-GATK4:BQSR", "port": "col3-GATK4:BQSR-right"}, "target": {"cell": "col4-GATK4:CNV", "port": "col4-GATK4:CNV-left"}, "vertices": []}, {"id": "4c84442b-f16b-408b-9303-d8f046f2e3e2", "shape": "lane-edge", "source": {"cell": "col3-GATK4:BQSR", "port": "col3-GATK4:BQSR-right"}, "target": {"cell": "col4-GATK4:HaplotypeCaller", "port": "col4-GATK4:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "74427c0c-4c86-4b5c-be0d-6af8ce35c47d", "shape": "lane-edge", "source": {"cell": "col4-GATK4:Mutect2", "port": "col4-GATK4:Mutect2-right"}, "target": {"cell": "col5-GATK4:FilterV<PERSON>ts", "port": "col5-GATK4:<PERSON><PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "488245da-98f8-4918-a1da-31116b1ab253", "shape": "lane-edge", "source": {"cell": "col4-GATK4:CNV", "port": "col4-GATK4:CNV-right"}, "target": {"cell": "col6-ABSOLUTE", "port": "col6-ABSOLUTE-left"}, "vertices": []}, {"id": "ce08680e-6d39-4fd5-823c-ca985d50da16", "shape": "lane-edge", "source": {"cell": "col4-GATK4:HaplotypeCaller", "port": "col4-GATK4:HaplotypeCaller-right"}, "target": {"cell": "col5-GATK4:VQSR", "port": "col5-GATK4:VQ<PERSON>-left"}, "vertices": []}, {"id": "380908f6-5d7f-42d5-af31-03ca4873b71e", "shape": "lane-edge", "source": {"cell": "col4-GATK4:HaplotypeCaller", "port": "col4-GATK4:HaplotypeCaller-right"}, "target": {"cell": "col5-GATK4:<PERSON>Filter", "port": "col5-<PERSON><PERSON><PERSON>4:<PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "6d32db5a-4cdd-4813-a322-ab87bfd55a41", "shape": "lane-edge", "source": {"cell": "col5-GATK4:<PERSON>Filter", "port": "col5-GA<PERSON><PERSON>4:<PERSON><PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col6-<PERSON><PERSON><PERSON>", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "e3e6ca80-c086-45be-a45d-1cfab432f46e", "shape": "lane-edge", "source": {"cell": "col5-GATK4:VQSR", "port": "col5-GATK4:VQSR-right"}, "target": {"cell": "col6-<PERSON><PERSON><PERSON>", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "94b1e2ad-9726-4473-be81-5df76e970e35", "shape": "lane-edge", "source": {"cell": "col5-GATK4:FilterV<PERSON>ts", "port": "col5-GATK4:<PERSON><PERSON><PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col6-<PERSON><PERSON><PERSON>", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "a8715cbd-6e09-4190-8afb-39da5738753f", "shape": "lane-edge", "source": {"cell": "col6-<PERSON><PERSON><PERSON>", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "target": {"cell": "col6-MAFTOOLS", "port": "col6-MAFTOOLS-left"}, "vertices": [{"x": 711, "y": 632}]}, {"id": "aa4d4e66-53c3-4995-8ea5-1ab6531112fa", "shape": "lane-edge", "source": {"cell": "col6-<PERSON><PERSON><PERSON>", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "target": {"cell": "col6-MAFTOOLS:oncodriveCLUST", "port": "col6-MAFTOOLS:oncodriveCLUST-left"}, "vertices": [{"x": 701, "y": 592}]}, {"id": "420b1b04-c47b-40be-b4c0-f600e565a6de", "shape": "lane-edge", "source": {"cell": "col6-<PERSON><PERSON><PERSON>", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "target": {"cell": "col6-MutsigCV", "port": "col6-<PERSON><PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": [{"x": 701, "y": 582}]}, {"id": "d24f3912-caea-4ed5-8054-8fa2d2838b07", "shape": "lane-edge", "source": {"cell": "col3-HISAT2", "port": "col3-HISAT2-right"}, "target": {"cell": "col4-featureCounts", "port": "col4-featureCounts-left"}, "vertices": []}, {"id": "443d83c0-a112-4c80-8342-7d9d946873a7", "shape": "lane-edge", "source": {"cell": "col3-HISAT2", "port": "col3-HISAT2-right"}, "target": {"cell": "col4-HTSeq", "port": "col4-H<PERSON>eq-left"}, "vertices": []}, {"id": "9b26e874-7225-4fa8-a726-293cb88e40bd", "shape": "lane-edge", "source": {"cell": "col3-STAR", "port": "col3-STAR-right"}, "target": {"cell": "col4-featureCounts", "port": "col4-featureCounts-left"}, "vertices": []}, {"id": "2e4e49d1-11de-423e-85bb-d0427975eb0b", "shape": "lane-edge", "source": {"cell": "col3-STAR", "port": "col3-STAR-right"}, "target": {"cell": "col4-HTSeq", "port": "col4-H<PERSON>eq-left"}, "vertices": []}, {"id": "6dd850fa-376d-435b-a7f0-a0c04db24b33", "shape": "lane-edge", "source": {"cell": "col3-RSEM:STAR", "port": "col3-RSEM:STAR-right"}, "target": {"cell": "col4-RSEM", "port": "col4-<PERSON><PERSON>-left"}, "vertices": []}, {"id": "bf918261-f581-4d47-93f5-08194b4fd2f4", "shape": "lane-edge", "source": {"cell": "col3-STAR", "port": "col3-STAR-right"}, "target": {"cell": "col4-rMATs", "port": "col4-rMATs-left"}, "vertices": []}, {"id": "7e6989e3-346f-46b0-ae1d-b63678815537", "shape": "lane-edge", "source": {"cell": "col3-STAR", "port": "col3-STAR-right"}, "target": {"cell": "col4-CIRCexplorer2", "port": "col4-CIRCexplorer2-left"}, "vertices": []}, {"id": "6995f1c2-ca37-427c-93f8-aff86167f12b", "shape": "lane-edge", "source": {"cell": "col3-MapSplice", "port": "col3-MapSplice-right"}, "target": {"cell": "col4-<PERSON><PERSON>", "port": "col4-<PERSON><PERSON>-left"}, "vertices": []}, {"id": "695aa80d-19af-4309-b925-07f1acad8b4e", "shape": "lane-edge", "source": {"cell": "col3-BWA", "port": "col3-BWA-right"}, "target": {"cell": "col4-CIRI", "port": "col4-C<PERSON><PERSON>-left"}, "vertices": []}, {"id": "8d24308b-b317-4cdb-9f53-64779e871ab6", "shape": "lane-edge", "source": {"cell": "col4-featureCounts", "port": "col4-featureCounts-right"}, "target": {"cell": "col6-DESeq2", "port": "col6-DESeq2-left"}, "vertices": []}, {"id": "8d44e051-e829-46f4-9ceb-660a894bea00", "shape": "lane-edge", "source": {"cell": "col4-HTSeq", "port": "col4-HTSeq-right"}, "target": {"cell": "col6-DESeq2", "port": "col6-DESeq2-left"}, "vertices": []}, {"id": "4b30149a-c444-4784-ab5c-da0b16021d4c", "shape": "lane-edge", "source": {"cell": "col4-RSEM", "port": "col4-RSEM-right"}, "target": {"cell": "col6-DESeq2", "port": "col6-DESeq2-left"}, "vertices": []}, {"id": "a0b717f6-7c76-4e35-9ced-4621e1fd0196", "shape": "lane-edge", "source": {"cell": "col4-featureCounts", "port": "col4-featureCounts-right"}, "target": {"cell": "col7-BoxPlot", "port": "col7-BoxPlot-left"}, "vertices": [{"x": 550, "y": 240}, {"x": 634, "y": 520}, {"x": 812, "y": 555}]}, {"id": "fab9773b-c14e-4251-8cbf-f7d7a68f12dd", "shape": "lane-edge", "source": {"cell": "col6-DESeq2", "port": "col6-DESeq2-bottom"}, "target": {"cell": "col6-GO/KEGGEnrichment", "port": "col6-GO/KEGGEnrichment-top"}, "vertices": []}, {"id": "b0e6a81a-02fb-4f64-a320-42ee3ee235c2", "shape": "lane-edge", "source": {"cell": "col6-<PERSON><PERSON><PERSON>", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "target": {"cell": "col6-GO/KEGGEnrichment", "port": "col6-GO/KEGGEnrichment-left"}, "vertices": [{"x": 682, "y": 510}]}, {"id": "d2547f38-7159-4519-989e-d21afd269a47", "shape": "lane-edge", "source": {"cell": "col2-ChAMP:champ.QC", "port": "col2-ChAMP:champ.QC-right"}, "target": {"cell": "col5-ChAMP:champ.norm", "port": "col5-ChAMP:champ.norm-left"}, "vertices": []}, {"id": "c2124209-2205-4004-9eea-9422bb572c7b", "shape": "lane-edge", "source": {"cell": "col5-ChAMP:champ.norm", "port": "col5-ChAMP:champ.norm-right"}, "target": {"cell": "col6-ChAMP:champ.DMP", "port": "col6-ChAMP:champ.DMP-left"}, "vertices": []}, {"id": "0430bedb-b840-47cd-8a42-b16c1f026ed5", "shape": "lane-edge", "source": {"cell": "col6-ChAMP:champ.DMP", "port": "col6-ChAMP:champ.DMP-right"}, "target": {"cell": "col7-DotPlot", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "1d7f97e8-cdde-4bbc-9aa5-377e60e9a44d", "shape": "lane-edge", "source": {"cell": "col6-DESeq2", "port": "col6-DESeq2-right"}, "target": {"cell": "col7-Heatmap", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "3da10462-4a8c-4346-b4a6-d68e1184ebbc", "shape": "lane-edge", "source": {"cell": "col6-MAFTOOLS:oncodriveCLUST", "port": "col6-MAFTOOLS:oncodriveCLUST-right"}, "target": {"cell": "col7-Heatmap", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "c042897d-0bff-469e-bb4b-76b867ad078c", "shape": "lane-edge", "source": {"cell": "col6-MAFTOOLS", "port": "col6-MAFTOOLS-right"}, "target": {"cell": "col7-Heatmap", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "8aa22600-db40-47a1-bd77-8833deb597b5", "shape": "lane-edge", "source": {"cell": "col6-DEG", "port": "col6-DEG-right"}, "target": {"cell": "col7-Heatmap", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "4bdbb054-4de6-498d-88e5-ef059fe6ed70", "shape": "lane-edge", "source": {"cell": "col6-Deeptools", "port": "col6-<PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col7-Heatmap", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "8e19cbaf-630a-4484-aeca-edaf0a918ad8", "shape": "lane-edge", "source": {"cell": "col6-Pseudotimetrajectory", "port": "col6-Pseudotimetrajectory-right"}, "target": {"cell": "col7-Heatmap", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "7e4fc33a-e980-4d2e-9d61-5a5538776978", "shape": "lane-edge", "source": {"cell": "col6-DESeq2", "port": "col6-DESeq2-right"}, "target": {"cell": "col7-VolcanoPlot", "port": "col7-VolcanoPlot-left"}, "vertices": []}, {"id": "06fe0df3-a629-45d7-a9d0-b0d28d1685ed", "shape": "lane-edge", "source": {"cell": "col6-DEG", "port": "col6-DEG-right"}, "target": {"cell": "col7-VolcanoPlot", "port": "col7-VolcanoPlot-left"}, "vertices": []}, {"id": "f7405818-5e5b-41fe-87d5-2058c8a29b86", "shape": "lane-edge", "source": {"cell": "col6-DESeq2", "port": "col6-DESeq2-right"}, "target": {"cell": "col7-MAplot", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "40f52e3f-5a9e-4dbc-892b-318595d12636", "shape": "lane-edge", "source": {"cell": "col6-DESeq2", "port": "col6-DESeq2-right"}, "target": {"cell": "col7-PCA", "port": "col7-<PERSON><PERSON>-left"}, "vertices": []}, {"id": "db1be60d-afcd-4824-bbd0-523b48a9d71b", "shape": "lane-edge", "source": {"cell": "col6-MAFTOOLS:oncodriveCLUST", "port": "col6-MAFTOOLS:oncodriveCLUST-right"}, "target": {"cell": "col7-DotPlot", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "da296160-cac7-4c70-851c-12e28e9924c8", "shape": "lane-edge", "source": {"cell": "col6-Pseudotimetrajectory", "port": "col6-Pseudotimetrajectory-right"}, "target": {"cell": "col7-DotPlot", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "0ac694bb-6db6-4397-814b-dafaa05b915c", "shape": "lane-edge", "source": {"cell": "col6-GO/KEGGEnrichment", "port": "col6-GO/KEGGEnrichment-right"}, "target": {"cell": "col7-BarPlot", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "339cf685-c279-487f-92cb-acb9adc6bfb5", "shape": "lane-edge", "source": {"cell": "col6-MAFTOOLS", "port": "col6-MAFTOOLS-right"}, "target": {"cell": "col7-BarPlot", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "3d1dc00e-fc1e-4ce5-a058-96d5c88871fd", "shape": "lane-edge", "source": {"cell": "col6-DESeq2", "port": "col6-DESeq2-right"}, "target": {"cell": "col7-Association", "port": "col7-Association-left"}, "vertices": []}, {"id": "68641f8c-3fc3-4189-9ff7-ce1cd8289019", "shape": "lane-edge", "source": {"cell": "col6-MAFTOOLS", "port": "col6-MAFTOOLS-right"}, "target": {"cell": "col7-Association", "port": "col7-Association-left"}, "vertices": []}, {"id": "1a07720d-08ce-4e3d-b675-7f74bfde77f4", "shape": "lane-edge", "source": {"cell": "col6-MAFTOOLS", "port": "col6-MAFTOOLS-right"}, "target": {"cell": "col7-OncoPlot", "port": "col7-OncoPlot-left"}, "vertices": []}, {"id": "9b58cc15-4f13-45d7-8438-633e5f066fad", "shape": "lane-edge", "source": {"cell": "col6-MAFTOOLS", "port": "col6-MAFTOOLS-right"}, "target": {"cell": "col7-LollipopPlot", "port": "col7-LollipopPlot-left"}, "vertices": []}, {"id": "dec27ec3-4875-4d3c-b243-08f4a149dd77", "shape": "lane-edge", "source": {"cell": "col6-GO/KEGGEnrichment", "port": "col6-GO/KEGGEnrichment-right"}, "target": {"cell": "col7-BubblePlot", "port": "col7-<PERSON><PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "d788cdbd-2b76-4ee1-9937-b44ab4cc315f", "shape": "lane-edge", "source": {"cell": "col6-MAFTOOLS:oncodriveCLUST", "port": "col6-MAFTOOLS:oncodriveCLUST-right"}, "target": {"cell": "col7-BubblePlot", "port": "col7-<PERSON><PERSON><PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "fde480e6-dab8-4913-b0d9-51381d5603c7", "shape": "lane-edge", "source": {"cell": "col6-HOM<PERSON>", "port": "col6-<PERSON>OM<PERSON>-right"}, "target": {"cell": "col7-<PERSON><PERSON><PERSON>", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "70d4638b-5215-46dd-8f27-5049ac40867a", "shape": "lane-edge", "source": {"cell": "col6-Deeptools", "port": "col6-<PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col7-Profiling", "port": "col7-Profiling-left"}, "vertices": []}, {"id": "3fecc174-122b-4300-8d55-d85828ecd67f", "shape": "lane-edge", "source": {"cell": "col6-Ch<PERSON><PERSON>ker", "port": "col6-<PERSON><PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col7-PiePlot", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "faabe723-d9e4-45c6-9789-4f0c69e7b07b", "shape": "lane-edge", "source": {"cell": "col6-Ch<PERSON><PERSON>ker", "port": "col6-<PERSON><PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col7-Circular stacked", "port": "col7-Circular stacked-left"}, "vertices": []}, {"id": "fd5abda1-edc9-4469-b7dd-093a9ac584e1", "shape": "lane-edge", "source": {"cell": "col6-Cellassign", "port": "col6-Cellassign-right"}, "target": {"cell": "col7-t-SNE", "port": "col7-t-SNE-left"}, "vertices": []}, {"id": "ac1150f0-8d68-4186-834d-b6bb256f10ca", "shape": "lane-edge", "source": {"cell": "col5-<PERSON><PERSON><PERSON>", "port": "col5-<PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col7-t-SNE", "port": "col7-t-SNE-left"}, "vertices": [{"x": 720, "y": 1320}, {"x": 814, "y": 1310}]}, {"id": "2f644684-5f2f-4d97-a4fb-268fb421c8d1", "shape": "lane-edge", "source": {"cell": "col6-<PERSON><PERSON>", "port": "col6-<PERSON><PERSON>-right"}, "target": {"cell": "col7-ViolinPlot", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "78b23e6a-5504-4b08-a8b8-efb27fa97667", "shape": "lane-edge", "source": {"cell": "col3-<PERSON><PERSON> Duplicates", "port": "col3-<PERSON><PERSON>-right"}, "target": {"cell": "col4-MACS14/2", "port": "col4-MACS14/2-left"}, "vertices": []}, {"id": "b943e53f-5eaa-4a91-812d-872e218a02d7", "shape": "lane-edge", "source": {"cell": "col5-<PERSON><PERSON><PERSON>", "port": "col5-<PERSON><PERSON><PERSON>-right"}, "target": {"cell": "col6-Cellassign", "port": "col6-<PERSON><PERSON><PERSON>-left"}, "vertices": []}, {"id": "84c1eb94-a6f6-490c-a0b5-d858b43bdb0d", "shape": "lane-edge", "source": {"cell": "Proteomics", "port": "Proteomics-right"}, "target": {"cell": "col1-P1", "port": "P1-left"}, "vertices": []}, {"id": "c5310731-3aee-4ae0-a7c9-c7dd7eae95d8", "shape": "lane-edge", "source": {"cell": "col6-ChAMP:champ.DMP", "port": "col6-ChAMP:champ.DMP-right"}, "target": {"cell": "col7-PCA", "port": "col7-<PERSON><PERSON>-left"}, "vertices": []}, {"id": "a7369f96-a1c7-4875-954e-2c7fdda6f71b", "shape": "lane-edge", "source": {"cell": "col6-ChAMP:champ.DMP", "port": "col6-ChAMP:champ.DMP-right"}, "target": {"cell": "col7-Heatmap", "port": "col7-<PERSON><PERSON><PERSON>-left"}, "vertices": []}]