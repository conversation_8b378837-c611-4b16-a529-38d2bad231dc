package cn.ac.picb.vipmap.config;

import cn.ac.picb.vipmap.config.security.RdrProperties;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class UrlConfig {

    private final AppProperties appProperties;
    private final RdrProperties rdrProperties;

    @Bean(name = "urlMap")
    Map<String, String> urlMap() {
        Map<String, String> map = new HashMap<>(6);
        map.put("pdms", appProperties.getPdms());
        map.put("bmdc", appProperties.getBmdc());
        map.put("cas", appProperties.getCas());
        map.put("ftp", appProperties.getFtp());
        map.put("vipmap", appProperties.getVipmap());
        map.put("imac", appProperties.getImac());
        map.put("pheatmap", appProperties.getPheatmap());
        map.put("loginPage", rdrProperties.getRdrLoginPage());
        return map;
    }

    @Bean(name = "modules")
    List<String> modules() {
        return Arrays.stream(StrUtil.split(appProperties.getModules(), ";")).collect(Collectors.toList());
    }
}
