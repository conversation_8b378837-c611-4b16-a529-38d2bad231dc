<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
    <link rel="stylesheet" th:href="@{/css/multi-select.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('proteomics-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Proteomics Analyzer</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/proteomics/form}" class="active">Add Task</a>
                            <a th:href="@{/analysis/proteomics/list}">Task List</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade">
                            <div class="p-2 px-3">
                                <div class="form-custom">
                                    <div class="form-group-box border-top-0 pt-2">
                                        <a href="#coll-1" data-toggle="collapse"
                                           class="h6 text-primary font-weight-bold">Load File</a>
                                        <div class="collapse show" id="coll-1">
                                            <div class="pl-4 pt-2">
                                                <div class="d-flex mb-2">
                                                    <input type="file" class="form-control form-control-sm w-50 mr-2"
                                                           id="excel">
                                                    <button onclick="uploadExcel()" type="button"
                                                            class="btn btn-primary btn-sm mr-2 text-nowrap">Upload
                                                    </button>
                                                    <a th:href="@{/analysis/proteomics/downloadTemplate}"
                                                       class="btn btn-link btn-sm text-nowrap"><i
                                                            class="fa fa-download mr-1"></i>Download Template</a>
                                                </div>

                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-middle font-12">
                                                        <thead class="thead-light">
                                                        <tr>
                                                            <th>File</th>
                                                            <th width="120">Parameter group</th>
                                                            <th width="80">Experiment</th>
                                                            <th width="80">Fraction</th>
                                                            <th width="80">PTM</th>
                                                            <th width="50"></th>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="sample-table">
                                                        <tr>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="showFileModal(this)">Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend">
                                                                      <span class="input-group-text">
                                                                        <em class="seled"></em>
                                                                      </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="number"
                                                                       class="form-control form-control-sm text-center font-12"
                                                                       value="0">
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="number"
                                                                       class="form-control form-control-sm text-center"
                                                                       value="0">
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="number"
                                                                       class="form-control form-control-sm text-center"
                                                                       value="0">
                                                            </td>
                                                            <td class="td-input">
                                                                <select class="form-control form-control-sm select2"
                                                                        style="width: 100%">
                                                                    <option value="false" selected>false</option>
                                                                    <option value="true">true</option>
                                                                </select>
                                                            </td>
                                                            <td class="td-input">
                                                                <i class="fa fa-plus-square text-primary"
                                                                   onclick="addRow(this)"></i>
                                                                <i class="fa fa-minus-square text-muted ml-1"
                                                                   onclick="removeRow(this)"></i>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>

                                        <a href="#coll-2" data-toggle="collapse"
                                           class="h6 text-primary font-weight-bold">Global parameters</a>
                                        <div class="collapse show" id="coll-2">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Database</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6">
                                                        <select name="refDb" class="form-control select2">
                                                            <option th:each="db : ${databases}" th:value="${db}"
                                                                    th:text="${db}"></option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Include
                                                        contaminants</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6">
                                                        <div class="custom-control custom-checkbox custom-control-inline">
                                                            <input name="includeCont" value="true" checked
                                                                   type="checkbox" class="custom-control-input"
                                                                   id="skipIC">
                                                            <label for="skipIC"
                                                                   class="custom-control-label">&nbsp;</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">iBAQ</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6">
                                                        <div class="custom-control custom-checkbox custom-control-inline">
                                                            <input name="ibaq" value="true" type="checkbox"
                                                                   class="custom-control-input" id="skipIQ">
                                                            <label for="skipIQ"
                                                                   class="custom-control-label">&nbsp;</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center offset-xl-2 col-xl-10 p-0"
                                                     id="clf" style="display: none;">
                                                    <label class="col-md-1 col-form-label px-1">Log fit</label>
                                                    <div class="col-md-2">
                                                        <div class="custom-control custom-checkbox custom-control-inline">
                                                            <input name="logFit" value="true" type="checkbox"
                                                                   class="custom-control-input" id="skipLF" checked>
                                                            <label for="skipLF"
                                                                   class="custom-control-label">&nbsp;</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <h6 class="text-primary border-bottom py-2">MS/MS analyzer</h6>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">FTMS
                                                        MS/MS match tolerance / Unit</label>
                                                    <div class="col-xl-3 col-lg-2 col-md-2">
                                                        <div class="input-group">
                                                            <input name="ftms" type="text" class="form-control"
                                                                   value="20">
                                                            <select name="ftmsUnit" class="form-control">
                                                                <option value="true" selected>ppm</option>
                                                                <option value="false">Da</option>
                                                            </select>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">ITMS
                                                        MS/MS match tolerance / Unit</label>
                                                    <div class="col-xl-3 col-lg-2 col-md-2">
                                                        <div class="input-group">
                                                            <input name="itms" type="text" class="form-control"
                                                                   value="0.5">
                                                            <select name="itmsUnit" class="form-control">
                                                                <option value="true">ppm</option>
                                                                <option value="false" selected>Da</option>
                                                            </select>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">TOF
                                                        MS/MS match tolerance / Unit</label>
                                                    <div class="col-xl-3 col-lg-2 col-md-2">
                                                        <div class="input-group">
                                                            <input name="tof" type="text" class="form-control"
                                                                   value="40">
                                                            <select name="tofUnit" class="form-control">
                                                                <option value="true" selected>ppm</option>
                                                                <option value="false">Da</option>
                                                            </select>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Unknown
                                                        MS/MS match tolerance / Unit</label>
                                                    <div class="col-xl-3 col-lg-2 col-md-2">
                                                        <div class="input-group">
                                                            <input name="unknown" type="text" class="form-control"
                                                                   value="20">
                                                            <select name="unknownUnit" class="form-control">
                                                                <option value="true" selected>ppm</option>
                                                                <option value="false">Da</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <h6 class="text-primary border-bottom py-2">Identification</h6>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">PSM
                                                        FDR</label>
                                                    <div class="col-xl-3 col-lg-2 col-md-2">
                                                        <input name="psfFdr" type="text" class="form-control"
                                                               value="0.01">
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Protein
                                                        FDR</label>
                                                    <div class="col-xl-3 col-lg-2 col-md-2">
                                                        <input name="proteinFdr" type="text" class="form-control"
                                                               value="0.01">
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Min.
                                                        peptides</label>
                                                    <div class="col-xl-3 col-lg-2 col-md-2">
                                                        <input name="minPeptides" type="text" class="form-control"
                                                               value="1">
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Min.
                                                        raror + unique peptides</label>
                                                    <div class="col-xl-3 col-lg-2 col-md-2">
                                                        <input name="minRaror" type="text" class="form-control"
                                                               value="1">
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Min.
                                                        unique peptides</label>
                                                    <div class="col-xl-3 col-lg-2 col-md-2">
                                                        <input name="minUnique" type="text" class="form-control"
                                                               value="0">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <a href="#coll-3" data-toggle="collapse"
                                           class="h6 text-primary font-weight-bold">Group parameter</a>
                                        <div class="collapse show" id="coll-3">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-start mb-2">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Type</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6">
                                                        <select name="groupType" class="form-control target-select"
                                                                data-id="type-div">
                                                            <option data-id="type-1" value="Standard" selected>
                                                                Standard
                                                            </option>
                                                            <option data-id="type-2" value="Report ion MS2">Report ion
                                                                MS2
                                                            </option>
                                                            <option data-id="type-3" value="TIMS-DDA">TIMS-DDA</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div id="type-div">
                                                    <div id="type-1" class="desc" style="display:block">
                                                        <div class="form-group row align-items-center offset-xl-2 col-xl-6 p-1">
                                                            <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Multiplicity</label>
                                                            <div class="col-xl-10 col-lg-9 col-md-8 pr-2">
                                                                <select name="multiplicity"
                                                                        class="form-control target-select"
                                                                        data-id="gmm-div">
                                                                    <option data-id="gmm1" value="1" selected>1</option>
                                                                    <option data-id="gmm2" value="2">2</option>
                                                                    <option data-id="gmm3" value="3">3</option>
                                                                </select>
                                                            </div>
                                                        </div>

                                                        <div id="gmm-div">
                                                            <div id="gmm1" class="desc" style="display: block;">
                                                                <div class="form-group row align-items-start offset-xl-2 p-1">
                                                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">Label</label>
                                                                    <div class="col-xl-10 col-lg-9 col-md-8 pr-2">
                                                                        <div class="label-groups-box">
                                                                            <div class="custom-control custom-checkbox custom-control-inline"
                                                                                 th:each="label, sta : ${standardLabels}">
                                                                                <input name="lightLabels"
                                                                                       th:value="${label}"
                                                                                       th:id="${'gmm1-ck' + sta.index}"
                                                                                       value="180" type="checkbox"
                                                                                       class="custom-control-input"
                                                                                       id="gmm1-ck01">
                                                                                <label for="gmm1-ck01"
                                                                                       th:for="${'gmm1-ck' + sta.index}"
                                                                                       th:text="${label}"
                                                                                       class="custom-control-label">180</label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div id="gmm2" class="desc" style="display:none">
                                                                <div class="form-group row align-items-center offset-xl-2 col-xl-6 p-1">
                                                                    <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0">Max.
                                                                        labeled</label>
                                                                    <div class="col-xl-9 col-lg-9 col-md-8 pr-2">
                                                                        <input name="maxLabeled" type="text"
                                                                               class="form-control" value="3">
                                                                    </div>
                                                                </div>
                                                                <div class="form-group row align-items-start offset-xl-2 p-1">
                                                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">Label</label>
                                                                    <div class="col-xl-10 col-lg-9 col-md-8 pr-2">
                                                                        <div class="label-groups-box">
                                                                            <h5 class="font-13 font-weight-bold mb-2">
                                                                                Light labels</h5>
                                                                            <div class="custom-control custom-checkbox custom-control-inline"
                                                                                 th:each="label, sta : ${standardLabels}">
                                                                                <input name="lightLabels"
                                                                                       th:value="${label}"
                                                                                       th:id="${'gmm2-light-ck' + sta.index}"
                                                                                       type="checkbox"
                                                                                       class="custom-control-input">
                                                                                <label for="gmm1-ck01"
                                                                                       th:for="${'gmm2-light-ck' + sta.index}"
                                                                                       th:text="${label}"
                                                                                       class="custom-control-label">180</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="label-groups-box">
                                                                            <h5 class="font-13 font-weight-bold mb-2">
                                                                                Heavy labels</h5>
                                                                            <div class="custom-control custom-checkbox custom-control-inline"
                                                                                 th:each="label, sta : ${standardLabels}">
                                                                                <input name="heavyLabels"
                                                                                       th:value="${label}"
                                                                                       th:id="${'gmm2-heavy-ck' + sta.index}"
                                                                                       type="checkbox"
                                                                                       class="custom-control-input">
                                                                                <label th:for="${'gmm2-heavy-ck' + sta.index}"
                                                                                       th:text="${label}"
                                                                                       class="custom-control-label">180</label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div id="gmm3" class="desc" style="display:none">
                                                                <div class="form-group row align-items-center offset-xl-2 col-xl-6 p-1">
                                                                    <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0">Max.
                                                                        labeled</label>
                                                                    <div class="col-xl-9 col-lg-9 col-md-8 pr-2">
                                                                        <input type="text" class="form-control"
                                                                               value="3">
                                                                    </div>
                                                                </div>
                                                                <div class="form-group row align-items-start offset-xl-2 p-1">
                                                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">Label</label>
                                                                    <div class="col-xl-10 col-lg-9 col-md-8 pr-2">
                                                                        <div class="label-groups-box">
                                                                            <h5 class="font-13 font-weight-bold mb-2">
                                                                                Light labels</h5>
                                                                            <div class="custom-control custom-checkbox custom-control-inline"
                                                                                 th:each="label, sta : ${standardLabels}">
                                                                                <input name="lightLabels"
                                                                                       th:value="${label}"
                                                                                       th:id="${'gmm3-light-ck' + sta.index}"
                                                                                       type="checkbox"
                                                                                       class="custom-control-input">
                                                                                <label th:for="${'gmm3-light-ck' + sta.index}"
                                                                                       th:text="${label}"
                                                                                       class="custom-control-label">180</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="label-groups-box">
                                                                            <h5 class="font-13 font-weight-bold mb-2">
                                                                                Medium labels</h5>
                                                                            <div class="custom-control custom-checkbox custom-control-inline"
                                                                                 th:each="label, sta : ${standardLabels}">
                                                                                <input name="mediumLabels"
                                                                                       th:value="${label}"
                                                                                       th:id="${'gmm3-medium-ck' + sta.index}"
                                                                                       type="checkbox"
                                                                                       class="custom-control-input">
                                                                                <label th:for="${'gmm3-medium-ck' + sta.index}"
                                                                                       th:text="${label}"
                                                                                       class="custom-control-label">180</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="label-groups-box">
                                                                            <h5 class="font-13 font-weight-bold mb-2">
                                                                                Heavy labels</h5>
                                                                            <div class="custom-control custom-checkbox custom-control-inline"
                                                                                 th:each="label, sta : ${standardLabels}">
                                                                                <input name="heavyLabels"
                                                                                       th:value="${label}"
                                                                                       th:id="${'gmm3-heavy-ck' + sta.index}"
                                                                                       type="checkbox"
                                                                                       class="custom-control-input">
                                                                                <label th:for="${'gmm3-heavy-ck' + sta.index}"
                                                                                       th:text="${label}"
                                                                                       class="custom-control-label">180</label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="type-2" class="desc" style="display:none">
                                                        <div class="form-group row align-items-start offset-xl-2 p-1">
                                                            <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Isobaric
                                                                labels</label>
                                                            <div class="col-xl-10 col-lg-9 col-md-8 pr-2">
                                                                <div class="custom-control custom-radio custom-control-inline"
                                                                     th:each="label, sta : ${isobaricLabels}">
                                                                    <input name="isobaricLabels" type="radio"
                                                                           class="custom-control-input"
                                                                           th:value="${label}"
                                                                           th:id="${'iso-' + sta.index}">
                                                                    <label th:for="${'iso-'+ sta.index}"
                                                                           th:text="${label}"
                                                                           class="custom-control-label">4plex</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="type-3" class="desc" style="display:none">
                                                        <div class="form-group row align-items-center offset-xl-2 p-1 mb-0">
                                                            <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0">TIMS
                                                                half width</label>
                                                            <div class="col-xl-6 col-lg-9 col-md-8 pr-2">
                                                                <input name="timsHalfWidth" type="text"
                                                                       class="form-control" value="4">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row align-items-center offset-xl-2 p-1 mb-0">
                                                            <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0">TIMS
                                                                step</label>
                                                            <div class="col-xl-6 col-lg-9 col-md-8 pr-2">
                                                                <input name="timsStep" type="text" class="form-control"
                                                                       value="3">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row align-items-center offset-xl-2 p-1 mb-0">
                                                            <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0">TIMS
                                                                resolution</label>
                                                            <div class="col-xl-6 col-lg-9 col-md-8 pr-2">
                                                                <input name="timsResolution" type="text"
                                                                       class="form-control" value="32000">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row align-items-center offset-xl-2 p-1 mb-0">
                                                            <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0">TIMS
                                                                min ms/ms</label>
                                                            <div class="col-xl-6 col-lg-9 col-md-8 pr-2">
                                                                <input name="timsMin" type="text" class="form-control"
                                                                       value="1.5">
                                                            </div>
                                                        </div>
                                                        <div class="form-group row align-items-center offset-xl-2 p-1 mb-0">
                                                            <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0">TIMS
                                                                remove precursor</label>
                                                            <div class="col-xl-6 col-lg-9 col-md-8 pr-2">
                                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                                    <input name="timsRem" value="true" type="checkbox"
                                                                           class="custom-control-input" id="f02"
                                                                           checked>
                                                                    <label for="f02"
                                                                           class="custom-control-label">&nbsp;</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group row align-items-center offset-xl-2 p-1 mb-0">
                                                            <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0">TIMS
                                                                collapse ms/ms</label>
                                                            <div class="col-xl-6 col-lg-9 col-md-8 pr-2">
                                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                                    <input name="timsColl" value="true" type="checkbox"
                                                                           class="custom-control-input" id="f03"
                                                                           checked>
                                                                    <label for="f03"
                                                                           class="custom-control-label">&nbsp;</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-start mb-2">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Variable
                                                        modifications</label>
                                                    <div class="col-xl-5 col-lg-4 col-md-3">
                                                        <select name="variableMods" class='form-control multi-select'
                                                                multiple='multiple'>
                                                            <option>Acetyl (K)</option>
                                                            <option>Acetyl (N-term)</option>
                                                            <option selected>Acetyl (Protein N-term)</option>
                                                            <option>Amidated (C-term)</option>
                                                            <option>Amidated (Protein C-term)</option>
                                                            <option>Carbamidomethyl (C)</option>
                                                            <option>Carbamyl (N-term)</option>
                                                            <option>Cation:Na (DE)</option>
                                                            <option>Cys-Cys</option>
                                                            <option>Cysteinyl</option>
                                                            <option>Cysteinyl-carbamidomethyl</option>
                                                            <option>Deamidated (N)</option>
                                                            <option>Deamidated (NQ)</option>
                                                            <option>Deamidated 180 (N)</option>
                                                            <option>Dehydrated (ST)</option>
                                                            <option>Delta:H(2)C(2) (N-term)</option>
                                                            <option>Dethiomethyll (M)</option>
                                                            <option>Dimethyl (K)</option>
                                                            <option>Dimethyl (KR)</option>
                                                            <option>Dehydro (C)</option>
                                                            <option>Dioxidation (MW)</option>
                                                            <option>Gln->pyro-Glu</option>
                                                            <option>Glu->pyro-Glu</option>
                                                            <option>GlyGly (K)</option>
                                                            <option>Hydroxyproline</option>
                                                            <option>Methyl (E)</option>
                                                            <option>Methyl (KR)</option>
                                                            <option>Oxidation (HW)</option>
                                                            <option selected>Oxidation (M)</option>
                                                            <option>Phospho (ST)</option>
                                                            <option>Phospho (STY)</option>
                                                            <option>Pro5</option>
                                                            <option>Pro6</option>
                                                            <option>QQTGG (K)</option>
                                                            <option>Sulfation (Y)</option>
                                                            <option>Sulfo (STY)</option>
                                                            <option>Trimethyl (K)</option>
                                                            <option>Trioxidation (C)</option>
                                                            <option>Hex (K)</option>
                                                            <option>Hex(1)HexNAc(1) (ST)</option>
                                                            <option>Hex(5) HexNAc(4) HexNAc(2) (N)</option>
                                                            <option>Hex(5) HexNAc(4) HexNAc(2) Sodium (N)</option>
                                                            <option>HexNAc(ST)</option>
                                                            <option>Thioacyl (DSP)</option>
                                                            <option>Ala->Arg</option>
                                                            <option>Ala->Asn</option>
                                                            <option>Ala->Asp</option>
                                                            <option>Ala->CamCys</option>
                                                            <option>Ala->Cys</option>
                                                            <option>Ala->Gln</option>
                                                            <option>Ala->Glu</option>
                                                            <option>Ala->Gly</option>
                                                            <option>Ala->His</option>
                                                            <option>Ala->Lys</option>
                                                            <option>Ala->Met</option>
                                                            <option>Ala->Phe</option>
                                                            <option>Ala->Pro</option>
                                                            <option>Ala->Ser</option>
                                                            <option>Ala->Thr</option>
                                                            <option>Ala->Trp</option>
                                                            <option>Ala->Tyr</option>
                                                            <option>Ala->Val</option>
                                                            <option>Ala->Xle</option>
                                                            <option>Arg->Ala</option>
                                                            <option>Arg->Asn</option>
                                                            <option>Arg->Asp</option>
                                                            <option>Arg->CamCys</option>
                                                            <option>Arg->Cys</option>
                                                            <option>Arg->Gln</option>
                                                            <option>Arg->Glu</option>
                                                            <option>Arg->Gly</option>
                                                            <option>Arg->His</option>
                                                            <option>Arg->Lys</option>
                                                            <option>Arg->Met</option>
                                                            <option>Arg->Phe</option>
                                                            <option>Arg->Pro</option>
                                                            <option>Arg->Ser</option>
                                                            <option>Arg->Thr</option>
                                                            <option>Arg->Trp</option>
                                                            <option>Arg->Tyr</option>
                                                            <option>Arg->Val</option>
                                                            <option>Arg->Xle</option>
                                                            <option>Asn->Ala</option>
                                                            <option>Asn->Arg</option>
                                                            <option>Asn->Asp</option>
                                                            <option>Asn->CamCys</option>
                                                            <option>Asn->Cys</option>
                                                            <option>Asn->Gln</option>
                                                            <option>Asn->Glu</option>
                                                            <option>Asn->Gly</option>
                                                            <option>Asn->His</option>
                                                            <option>Asn->Lys</option>
                                                            <option>Asn->Met</option>
                                                            <option>Asn->Phe</option>
                                                            <option>Asn->Pro</option>
                                                            <option>Asn->Ser</option>
                                                            <option>Asn->Thr</option>
                                                            <option>Asn->Trp</option>
                                                            <option>Asn->Tyr</option>
                                                            <option>Asn->Val</option>
                                                            <option>Asn->Xle</option>
                                                            <option>Asp->Ala</option>
                                                            <option>Asp->Arg</option>
                                                            <option>Asp->Asn</option>
                                                            <option>Asp->CamCys</option>
                                                            <option>Asp->Cys</option>
                                                            <option>Asp->Gln</option>
                                                            <option>Asp->Glu</option>
                                                            <option>Asp->Gly</option>
                                                            <option>Asp->His</option>
                                                            <option>Asp->Lys</option>
                                                            <option>Asp->Met</option>
                                                            <option>Asp->Phe</option>
                                                            <option>Asp->Pro</option>
                                                            <option>Asp->Ser</option>
                                                            <option>Asp->Thr</option>
                                                            <option>Asp->Trp</option>
                                                            <option>Asp->Tyr</option>
                                                            <option>Asp->Val</option>
                                                            <option>Asp->Xle</option>
                                                            <option>CamCys->Ala</option>
                                                            <option>CamCys->Arg</option>
                                                            <option>CamCys->Asn</option>
                                                            <option>CamCys->Asp</option>
                                                            <option>CamCys->Gln</option>
                                                            <option>CamCys->Glu</option>
                                                            <option>CamCys->Gly</option>
                                                            <option>CamCys->His</option>
                                                            <option>CamCys->Lys</option>
                                                            <option>CamCys->Met</option>
                                                            <option>CamCys->Phe</option>
                                                            <option>CamCys->Pro</option>
                                                            <option>CamCys->Ser</option>
                                                            <option>CamCys->Thr</option>
                                                            <option>CamCys->Trp</option>
                                                            <option>CamCys->Tyr</option>
                                                            <option>CamCys->Val</option>
                                                            <option>CamCys->Xle</option>
                                                            <option>Cys->Ala</option>
                                                            <option>Cys->Arg</option>
                                                            <option>Cys->Asn</option>
                                                            <option>Cys->Asp</option>
                                                            <option>Cys->Gln</option>
                                                            <option>Cys->Glu</option>
                                                            <option>Cys->Gly</option>
                                                            <option>Cys->His</option>
                                                            <option>Cys->Lys</option>
                                                            <option>Cys->Met</option>
                                                            <option>Cys->Phe</option>
                                                            <option>Cys->Pro</option>
                                                            <option>Cys->Ser</option>
                                                            <option>Cys->Thr</option>
                                                            <option>Cys->Trp</option>
                                                            <option>Cys->Tyr</option>
                                                            <option>Cys->Val</option>
                                                            <option>Cys->Xle</option>
                                                            <option>Gln->Ala</option>
                                                            <option>Gln->Arg</option>
                                                            <option>Gln->Asn</option>
                                                            <option>Gln->CamCys</option>
                                                            <option>Gln->Cys</option>
                                                            <option>Gln->Glu</option>
                                                            <option>Gln->Gly</option>
                                                            <option>Gln->His</option>
                                                            <option>Gln->Lys</option>
                                                            <option>Gln->Met</option>
                                                            <option>Gln->Phe</option>
                                                            <option>Gln->Pro</option>
                                                            <option>Gln->Ser</option>
                                                            <option>Gln->Thr</option>
                                                            <option>Gln->Trp</option>
                                                            <option>Gln->Tyr</option>
                                                            <option>Gln->Val</option>
                                                            <option>Gln->Xle</option>
                                                            <option>Glu->Ala</option>
                                                            <option>Glu->Arg</option>
                                                            <option>Glu->Asn</option>
                                                            <option>Glu->CamCys</option>
                                                            <option>Glu->Cys</option>
                                                            <option>Glu->Gln</option>
                                                            <option>Glu->Gly</option>
                                                            <option>Glu->His</option>
                                                            <option>Glu->Lys</option>
                                                            <option>Glu->Met</option>
                                                            <option>Glu->Phe</option>
                                                            <option>Glu->Pro</option>
                                                            <option>Glu->Ser</option>
                                                            <option>Glu->Thr</option>
                                                            <option>Glu->Trp</option>
                                                            <option>Glu->Tyr</option>
                                                            <option>Glu->Val</option>
                                                            <option>Glu->Xle</option>
                                                            <option>Gly->Ala</option>
                                                            <option>Gly->Arg</option>
                                                            <option>Gly->Asn</option>
                                                            <option>Gly->CamCys</option>
                                                            <option>Gly->Cys</option>
                                                            <option>Gly->Gln</option>
                                                            <option>Gly->Glu</option>
                                                            <option>Gly->His</option>
                                                            <option>Gly->Lys</option>
                                                            <option>Gly->Met</option>
                                                            <option>Gly->Phe</option>
                                                            <option>Gly->Pro</option>
                                                            <option>Gly->Ser</option>
                                                            <option>Gly->Thr</option>
                                                            <option>Gly->Trp</option>
                                                            <option>Gly->Tyr</option>
                                                            <option>Gly->Val</option>
                                                            <option>Gly->Xle</option>
                                                            <option>His->Ala</option>
                                                            <option>His->Arg</option>
                                                            <option>His->Asn</option>
                                                            <option>His->Asp</option>
                                                            <option>His->CamCys</option>
                                                            <option>His->Cys</option>
                                                            <option>His->Gln</option>
                                                            <option>His->Glu</option>
                                                            <option>His->Lys</option>
                                                            <option>His->Met</option>
                                                            <option>His->Phe</option>
                                                            <option>His->Pro</option>
                                                            <option>His->Ser</option>
                                                            <option>His->Thr</option>
                                                            <option>His->Trp</option>
                                                            <option>His->Tyr</option>
                                                            <option>His->Val</option>
                                                            <option>His->Xle</option>
                                                            <option>Lys->Ala</option>
                                                            <option>Lys->Arg</option>
                                                            <option>Lys->Asn</option>
                                                            <option>Lys->Asp</option>
                                                            <option>Lys->CamCys</option>
                                                            <option>Lys->Cys</option>
                                                            <option>Lys->Gln</option>
                                                            <option>Lys->Glu</option>
                                                            <option>Lys->Gly</option>
                                                            <option>Lys->His</option>
                                                            <option>Lys->Met</option>
                                                            <option>Lys->Phe</option>
                                                            <option>Lys->Pro</option>
                                                            <option>Lys->Ser</option>
                                                            <option>Lys->Thr</option>
                                                            <option>Lys->Trp</option>
                                                            <option>Lys->Tyr</option>
                                                            <option>Lys->Val</option>
                                                            <option>Lys->Xle</option>
                                                            <option>Met->Ala</option>
                                                            <option>Met->Arg</option>
                                                            <option>Met->Asn</option>
                                                            <option>Met->Asp</option>
                                                            <option>Met->CamCys</option>
                                                            <option>Met->Cys</option>
                                                            <option>Met->Gln</option>
                                                            <option>Met->Glu</option>
                                                            <option>Met->Gly</option>
                                                            <option>Met->His</option>
                                                            <option>Met->Lys</option>
                                                            <option>Met->Phe</option>
                                                            <option>Met->Pro</option>
                                                            <option>Met->Ser</option>
                                                            <option>Met->Thr</option>
                                                            <option>Met->Trp</option>
                                                            <option>Met->Tyr</option>
                                                            <option>Met->Val</option>
                                                            <option>Met->Xle</option>
                                                            <option>Phe->Ala</option>
                                                            <option>Phe->Arg</option>
                                                            <option>Phe->Asn</option>
                                                            <option>Phe->Asp</option>
                                                            <option>Phe->CamCys</option>
                                                            <option>Phe->Cys</option>
                                                            <option>Phe->Gln</option>
                                                            <option>Phe->Glu</option>
                                                            <option>Phe->Gly</option>
                                                            <option>Phe->His</option>
                                                            <option>Phe->Lys</option>
                                                            <option>Phe->Met</option>
                                                            <option>Phe->Pro</option>
                                                            <option>Phe->Ser</option>
                                                            <option>Phe->Thr</option>
                                                            <option>Phe->Trp</option>
                                                            <option>Phe->Tyr</option>
                                                            <option>Phe->Val</option>
                                                            <option>Phe->Xle</option>
                                                            <option>Pro->Ala</option>
                                                            <option>Pro->Arg</option>
                                                            <option>Pro->Asn</option>
                                                            <option>Pro->Asp</option>
                                                            <option>Pro->CamCys</option>
                                                            <option>Pro->Cys</option>
                                                            <option>Pro->Gln</option>
                                                            <option>Pro->Glu</option>
                                                            <option>Pro->Gly</option>
                                                            <option>Pro->His</option>
                                                            <option>Pro->Lys</option>
                                                            <option>Pro->Met</option>
                                                            <option>Pro->Phe</option>
                                                            <option>Pro->Ser</option>
                                                            <option>Pro->Thr</option>
                                                            <option>Pro->Trp</option>
                                                            <option>Pro->Tyr</option>
                                                            <option>Pro->Val</option>
                                                            <option>Pro->Xle</option>
                                                            <option>Ser->Ala</option>
                                                            <option>Ser->Arg</option>
                                                            <option>Ser->Asn</option>
                                                            <option>Ser->Asp</option>
                                                            <option>Ser->CamCys</option>
                                                            <option>Ser->Cys</option>
                                                            <option>Ser->Gln</option>
                                                            <option>Ser->Glu</option>
                                                            <option>Ser->Gly</option>
                                                            <option>Ser->His</option>
                                                            <option>Ser->Lys</option>
                                                            <option>Ser->Met</option>
                                                            <option>Ser->Phe</option>
                                                            <option>Ser->Pro</option>
                                                            <option>Ser->Thr</option>
                                                            <option>Ser->Trp</option>
                                                            <option>Ser->Tyr</option>
                                                            <option>Ser->Val</option>
                                                            <option>Ser->Xle</option>
                                                            <option>Thr->Ala</option>
                                                            <option>Thr->Arg</option>
                                                            <option>Thr->Asn</option>
                                                            <option>Thr->Asp</option>
                                                            <option>Thr->CamCys</option>
                                                            <option>Thr->Cys</option>
                                                            <option>Thr->Gln</option>
                                                            <option>Thr->Glu</option>
                                                            <option>Thr->Gly</option>
                                                            <option>Thr->His</option>
                                                            <option>Thr->Lys</option>
                                                            <option>Thr->Met</option>
                                                            <option>Thr->Phe</option>
                                                            <option>Thr->Pro</option>
                                                            <option>Thr->Ser</option>
                                                            <option>Thr->Trp</option>
                                                            <option>Thr->Tyr</option>
                                                            <option>Thr->Val</option>
                                                            <option>Thr->Xle</option>
                                                            <option>Trp->Ala</option>
                                                            <option>Trp->Arg</option>
                                                            <option>Trp->Asn</option>
                                                            <option>Trp->Asp</option>
                                                            <option>Trp->CamCys</option>
                                                            <option>Trp->Cys</option>
                                                            <option>Trp->Gln</option>
                                                            <option>Trp->Glu</option>
                                                            <option>Trp->Gly</option>
                                                            <option>Trp->His</option>
                                                            <option>Trp->Lys</option>
                                                            <option>Trp->Met</option>
                                                            <option>Trp->Phe</option>
                                                            <option>Trp->Pro</option>
                                                            <option>Trp->Ser</option>
                                                            <option>Trp->Thr</option>
                                                            <option>Trp->Tyr</option>
                                                            <option>Trp->Val</option>
                                                            <option>Trp->Xle</option>
                                                            <option>Tyr->Ala</option>
                                                            <option>Tyr->Arg</option>
                                                            <option>Tyr->Asn</option>
                                                            <option>Tyr->Asp</option>
                                                            <option>Tyr->CamCys</option>
                                                            <option>Tyr->Cys</option>
                                                            <option>Tyr->Gln</option>
                                                            <option>Tyr->Glu</option>
                                                            <option>Tyr->Gly</option>
                                                            <option>Tyr->His</option>
                                                            <option>Tyr->Lys</option>
                                                            <option>Tyr->Met</option>
                                                            <option>Tyr->Phe</option>
                                                            <option>Tyr->Pro</option>
                                                            <option>Tyr->Ser</option>
                                                            <option>Tyr->Thr</option>
                                                            <option>Tyr->Trp</option>
                                                            <option>Tyr->Val</option>
                                                            <option>Tyr->Xle</option>
                                                            <option>Val->Ala</option>
                                                            <option>Val->Arg</option>
                                                            <option>Val->Asn</option>
                                                            <option>Val->Asp</option>
                                                            <option>Val->CamCys</option>
                                                            <option>Val->Cys</option>
                                                            <option>Val->Gln</option>
                                                            <option>Val->Glu</option>
                                                            <option>Val->Gly</option>
                                                            <option>Val->His</option>
                                                            <option>Val->Lys</option>
                                                            <option>Val->Met</option>
                                                            <option>Val->Phe</option>
                                                            <option>Val->Pro</option>
                                                            <option>Val->Ser</option>
                                                            <option>Val->Thr</option>
                                                            <option>Val->Trp</option>
                                                            <option>Val->Tyr</option>
                                                            <option>Val->Xle</option>
                                                            <option>Xle->Ala</option>
                                                            <option>Xle->Arg</option>
                                                            <option>Xle->Asn</option>
                                                            <option>Xle->Asp</option>
                                                            <option>Xle->CamCys</option>
                                                            <option>Xle->Cys</option>
                                                            <option>Xle->Gln</option>
                                                            <option>Xle->Glu</option>
                                                            <option>Xle->Gly</option>
                                                            <option>Xle->His</option>
                                                            <option>Xle->Lys</option>
                                                            <option>Xle->Met</option>
                                                            <option>Xle->Phe</option>
                                                            <option>Xle->Pro</option>
                                                            <option>Xle->Ser</option>
                                                            <option>Xle->Thr</option>
                                                            <option>Xle->Trp</option>
                                                            <option>Xle->Tyr</option>
                                                            <option>Xle->Val</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-start mb-2">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Fixed
                                                        modifications</label>
                                                    <div class="col-xl-5 col-lg-4 col-md-3">
                                                        <select name="fixMods" class='form-control multi-select'
                                                                multiple='multiple'>
                                                            <option>Acetyl (K)</option>
                                                            <option>Acetyl (N-term)</option>
                                                            <option>Acetyl (Protein N-term)</option>
                                                            <option>Amidated (C-term)</option>
                                                            <option>Amidated (Protein C-term)</option>
                                                            <option selected>Carbamidomethyl (C)</option>
                                                            <option>Carbamyl (N-term)</option>
                                                            <option>Cation:Na (DE)</option>
                                                            <option>Cys-Cys</option>
                                                            <option>Cysteinyl</option>
                                                            <option>Cysteinyl-carbamidomethyl</option>
                                                            <option>Deamidated (N)</option>
                                                            <option>Deamidated (NQ)</option>
                                                            <option>Deamidated 180 (N)</option>
                                                            <option>Dehydrated (ST)</option>
                                                            <option>Delta:H(2)C(2) (N-term)</option>
                                                            <option>Dethiomethyll (M)</option>
                                                            <option>Dimethyl (K)</option>
                                                            <option>Dimethyl (KR)</option>
                                                            <option>Dehydro (C)</option>
                                                            <option>Dioxidation (MW)</option>
                                                            <option>Gln->pyro-Glu</option>
                                                            <option>Glu->pyro-Glu</option>
                                                            <option>GlyGly (K)</option>
                                                            <option>Hydroxyproline</option>
                                                            <option>Methyl (E)</option>
                                                            <option>Methyl (KR)</option>
                                                            <option>Oxidation (HW)</option>
                                                            <option>Oxidation (M)</option>
                                                            <option>Phospho (ST)</option>
                                                            <option>Phospho (STY)</option>
                                                            <option>Pro5</option>
                                                            <option>Pro6</option>
                                                            <option>QQTGG (K)</option>
                                                            <option>Sulfation (Y)</option>
                                                            <option>Sulfo (STY)</option>
                                                            <option>Trimethyl (K)</option>
                                                            <option>Trioxidation (C)</option>
                                                            <option>Hex (K)</option>
                                                            <option>Hex(1)HexNAc(1) (ST)</option>
                                                            <option>Hex(5) HexNAc(4) HexNAc(2) (N)</option>
                                                            <option>Hex(5) HexNAc(4) HexNAc(2) Sodium (N)</option>
                                                            <option>HexNAc(ST)</option>
                                                            <option>Thioacyl (DSP)</option>
                                                            <option>Ala->Arg</option>
                                                            <option>Ala->Asn</option>
                                                            <option>Ala->Asp</option>
                                                            <option>Ala->CamCys</option>
                                                            <option>Ala->Cys</option>
                                                            <option>Ala->Gln</option>
                                                            <option>Ala->Glu</option>
                                                            <option>Ala->Gly</option>
                                                            <option>Ala->His</option>
                                                            <option>Ala->Lys</option>
                                                            <option>Ala->Met</option>
                                                            <option>Ala->Phe</option>
                                                            <option>Ala->Pro</option>
                                                            <option>Ala->Ser</option>
                                                            <option>Ala->Thr</option>
                                                            <option>Ala->Trp</option>
                                                            <option>Ala->Tyr</option>
                                                            <option>Ala->Val</option>
                                                            <option>Ala->Xle</option>
                                                            <option>Arg->Ala</option>
                                                            <option>Arg->Asn</option>
                                                            <option>Arg->Asp</option>
                                                            <option>Arg->CamCys</option>
                                                            <option>Arg->Cys</option>
                                                            <option>Arg->Gln</option>
                                                            <option>Arg->Glu</option>
                                                            <option>Arg->Gly</option>
                                                            <option>Arg->His</option>
                                                            <option>Arg->Lys</option>
                                                            <option>Arg->Met</option>
                                                            <option>Arg->Phe</option>
                                                            <option>Arg->Pro</option>
                                                            <option>Arg->Ser</option>
                                                            <option>Arg->Thr</option>
                                                            <option>Arg->Trp</option>
                                                            <option>Arg->Tyr</option>
                                                            <option>Arg->Val</option>
                                                            <option>Arg->Xle</option>
                                                            <option>Asn->Ala</option>
                                                            <option>Asn->Arg</option>
                                                            <option>Asn->Asp</option>
                                                            <option>Asn->CamCys</option>
                                                            <option>Asn->Cys</option>
                                                            <option>Asn->Gln</option>
                                                            <option>Asn->Glu</option>
                                                            <option>Asn->Gly</option>
                                                            <option>Asn->His</option>
                                                            <option>Asn->Lys</option>
                                                            <option>Asn->Met</option>
                                                            <option>Asn->Phe</option>
                                                            <option>Asn->Pro</option>
                                                            <option>Asn->Ser</option>
                                                            <option>Asn->Thr</option>
                                                            <option>Asn->Trp</option>
                                                            <option>Asn->Tyr</option>
                                                            <option>Asn->Val</option>
                                                            <option>Asn->Xle</option>
                                                            <option>Asp->Ala</option>
                                                            <option>Asp->Arg</option>
                                                            <option>Asp->Asn</option>
                                                            <option>Asp->CamCys</option>
                                                            <option>Asp->Cys</option>
                                                            <option>Asp->Gln</option>
                                                            <option>Asp->Glu</option>
                                                            <option>Asp->Gly</option>
                                                            <option>Asp->His</option>
                                                            <option>Asp->Lys</option>
                                                            <option>Asp->Met</option>
                                                            <option>Asp->Phe</option>
                                                            <option>Asp->Pro</option>
                                                            <option>Asp->Ser</option>
                                                            <option>Asp->Thr</option>
                                                            <option>Asp->Trp</option>
                                                            <option>Asp->Tyr</option>
                                                            <option>Asp->Val</option>
                                                            <option>Asp->Xle</option>
                                                            <option>CamCys->Ala</option>
                                                            <option>CamCys->Arg</option>
                                                            <option>CamCys->Asn</option>
                                                            <option>CamCys->Asp</option>
                                                            <option>CamCys->Gln</option>
                                                            <option>CamCys->Glu</option>
                                                            <option>CamCys->Gly</option>
                                                            <option>CamCys->His</option>
                                                            <option>CamCys->Lys</option>
                                                            <option>CamCys->Met</option>
                                                            <option>CamCys->Phe</option>
                                                            <option>CamCys->Pro</option>
                                                            <option>CamCys->Ser</option>
                                                            <option>CamCys->Thr</option>
                                                            <option>CamCys->Trp</option>
                                                            <option>CamCys->Tyr</option>
                                                            <option>CamCys->Val</option>
                                                            <option>CamCys->Xle</option>
                                                            <option>Cys->Ala</option>
                                                            <option>Cys->Arg</option>
                                                            <option>Cys->Asn</option>
                                                            <option>Cys->Asp</option>
                                                            <option>Cys->Gln</option>
                                                            <option>Cys->Glu</option>
                                                            <option>Cys->Gly</option>
                                                            <option>Cys->His</option>
                                                            <option>Cys->Lys</option>
                                                            <option>Cys->Met</option>
                                                            <option>Cys->Phe</option>
                                                            <option>Cys->Pro</option>
                                                            <option>Cys->Ser</option>
                                                            <option>Cys->Thr</option>
                                                            <option>Cys->Trp</option>
                                                            <option>Cys->Tyr</option>
                                                            <option>Cys->Val</option>
                                                            <option>Cys->Xle</option>
                                                            <option>Gln->Ala</option>
                                                            <option>Gln->Arg</option>
                                                            <option>Gln->Asn</option>
                                                            <option>Gln->CamCys</option>
                                                            <option>Gln->Cys</option>
                                                            <option>Gln->Glu</option>
                                                            <option>Gln->Gly</option>
                                                            <option>Gln->His</option>
                                                            <option>Gln->Lys</option>
                                                            <option>Gln->Met</option>
                                                            <option>Gln->Phe</option>
                                                            <option>Gln->Pro</option>
                                                            <option>Gln->Ser</option>
                                                            <option>Gln->Thr</option>
                                                            <option>Gln->Trp</option>
                                                            <option>Gln->Tyr</option>
                                                            <option>Gln->Val</option>
                                                            <option>Gln->Xle</option>
                                                            <option>Glu->Ala</option>
                                                            <option>Glu->Arg</option>
                                                            <option>Glu->Asn</option>
                                                            <option>Glu->CamCys</option>
                                                            <option>Glu->Cys</option>
                                                            <option>Glu->Gln</option>
                                                            <option>Glu->Gly</option>
                                                            <option>Glu->His</option>
                                                            <option>Glu->Lys</option>
                                                            <option>Glu->Met</option>
                                                            <option>Glu->Phe</option>
                                                            <option>Glu->Pro</option>
                                                            <option>Glu->Ser</option>
                                                            <option>Glu->Thr</option>
                                                            <option>Glu->Trp</option>
                                                            <option>Glu->Tyr</option>
                                                            <option>Glu->Val</option>
                                                            <option>Glu->Xle</option>
                                                            <option>Gly->Ala</option>
                                                            <option>Gly->Arg</option>
                                                            <option>Gly->Asn</option>
                                                            <option>Gly->CamCys</option>
                                                            <option>Gly->Cys</option>
                                                            <option>Gly->Gln</option>
                                                            <option>Gly->Glu</option>
                                                            <option>Gly->His</option>
                                                            <option>Gly->Lys</option>
                                                            <option>Gly->Met</option>
                                                            <option>Gly->Phe</option>
                                                            <option>Gly->Pro</option>
                                                            <option>Gly->Ser</option>
                                                            <option>Gly->Thr</option>
                                                            <option>Gly->Trp</option>
                                                            <option>Gly->Tyr</option>
                                                            <option>Gly->Val</option>
                                                            <option>Gly->Xle</option>
                                                            <option>His->Ala</option>
                                                            <option>His->Arg</option>
                                                            <option>His->Asn</option>
                                                            <option>His->Asp</option>
                                                            <option>His->CamCys</option>
                                                            <option>His->Cys</option>
                                                            <option>His->Gln</option>
                                                            <option>His->Glu</option>
                                                            <option>His->Lys</option>
                                                            <option>His->Met</option>
                                                            <option>His->Phe</option>
                                                            <option>His->Pro</option>
                                                            <option>His->Ser</option>
                                                            <option>His->Thr</option>
                                                            <option>His->Trp</option>
                                                            <option>His->Tyr</option>
                                                            <option>His->Val</option>
                                                            <option>His->Xle</option>
                                                            <option>Lys->Ala</option>
                                                            <option>Lys->Arg</option>
                                                            <option>Lys->Asn</option>
                                                            <option>Lys->Asp</option>
                                                            <option>Lys->CamCys</option>
                                                            <option>Lys->Cys</option>
                                                            <option>Lys->Gln</option>
                                                            <option>Lys->Glu</option>
                                                            <option>Lys->Gly</option>
                                                            <option>Lys->His</option>
                                                            <option>Lys->Met</option>
                                                            <option>Lys->Phe</option>
                                                            <option>Lys->Pro</option>
                                                            <option>Lys->Ser</option>
                                                            <option>Lys->Thr</option>
                                                            <option>Lys->Trp</option>
                                                            <option>Lys->Tyr</option>
                                                            <option>Lys->Val</option>
                                                            <option>Lys->Xle</option>
                                                            <option>Met->Ala</option>
                                                            <option>Met->Arg</option>
                                                            <option>Met->Asn</option>
                                                            <option>Met->Asp</option>
                                                            <option>Met->CamCys</option>
                                                            <option>Met->Cys</option>
                                                            <option>Met->Gln</option>
                                                            <option>Met->Glu</option>
                                                            <option>Met->Gly</option>
                                                            <option>Met->His</option>
                                                            <option>Met->Lys</option>
                                                            <option>Met->Phe</option>
                                                            <option>Met->Pro</option>
                                                            <option>Met->Ser</option>
                                                            <option>Met->Thr</option>
                                                            <option>Met->Trp</option>
                                                            <option>Met->Tyr</option>
                                                            <option>Met->Val</option>
                                                            <option>Met->Xle</option>
                                                            <option>Phe->Ala</option>
                                                            <option>Phe->Arg</option>
                                                            <option>Phe->Asn</option>
                                                            <option>Phe->Asp</option>
                                                            <option>Phe->CamCys</option>
                                                            <option>Phe->Cys</option>
                                                            <option>Phe->Gln</option>
                                                            <option>Phe->Glu</option>
                                                            <option>Phe->Gly</option>
                                                            <option>Phe->His</option>
                                                            <option>Phe->Lys</option>
                                                            <option>Phe->Met</option>
                                                            <option>Phe->Pro</option>
                                                            <option>Phe->Ser</option>
                                                            <option>Phe->Thr</option>
                                                            <option>Phe->Trp</option>
                                                            <option>Phe->Tyr</option>
                                                            <option>Phe->Val</option>
                                                            <option>Phe->Xle</option>
                                                            <option>Pro->Ala</option>
                                                            <option>Pro->Arg</option>
                                                            <option>Pro->Asn</option>
                                                            <option>Pro->Asp</option>
                                                            <option>Pro->CamCys</option>
                                                            <option>Pro->Cys</option>
                                                            <option>Pro->Gln</option>
                                                            <option>Pro->Glu</option>
                                                            <option>Pro->Gly</option>
                                                            <option>Pro->His</option>
                                                            <option>Pro->Lys</option>
                                                            <option>Pro->Met</option>
                                                            <option>Pro->Phe</option>
                                                            <option>Pro->Ser</option>
                                                            <option>Pro->Thr</option>
                                                            <option>Pro->Trp</option>
                                                            <option>Pro->Tyr</option>
                                                            <option>Pro->Val</option>
                                                            <option>Pro->Xle</option>
                                                            <option>Ser->Ala</option>
                                                            <option>Ser->Arg</option>
                                                            <option>Ser->Asn</option>
                                                            <option>Ser->Asp</option>
                                                            <option>Ser->CamCys</option>
                                                            <option>Ser->Cys</option>
                                                            <option>Ser->Gln</option>
                                                            <option>Ser->Glu</option>
                                                            <option>Ser->Gly</option>
                                                            <option>Ser->His</option>
                                                            <option>Ser->Lys</option>
                                                            <option>Ser->Met</option>
                                                            <option>Ser->Phe</option>
                                                            <option>Ser->Pro</option>
                                                            <option>Ser->Thr</option>
                                                            <option>Ser->Trp</option>
                                                            <option>Ser->Tyr</option>
                                                            <option>Ser->Val</option>
                                                            <option>Ser->Xle</option>
                                                            <option>Thr->Ala</option>
                                                            <option>Thr->Arg</option>
                                                            <option>Thr->Asn</option>
                                                            <option>Thr->Asp</option>
                                                            <option>Thr->CamCys</option>
                                                            <option>Thr->Cys</option>
                                                            <option>Thr->Gln</option>
                                                            <option>Thr->Glu</option>
                                                            <option>Thr->Gly</option>
                                                            <option>Thr->His</option>
                                                            <option>Thr->Lys</option>
                                                            <option>Thr->Met</option>
                                                            <option>Thr->Phe</option>
                                                            <option>Thr->Pro</option>
                                                            <option>Thr->Ser</option>
                                                            <option>Thr->Trp</option>
                                                            <option>Thr->Tyr</option>
                                                            <option>Thr->Val</option>
                                                            <option>Thr->Xle</option>
                                                            <option>Trp->Ala</option>
                                                            <option>Trp->Arg</option>
                                                            <option>Trp->Asn</option>
                                                            <option>Trp->Asp</option>
                                                            <option>Trp->CamCys</option>
                                                            <option>Trp->Cys</option>
                                                            <option>Trp->Gln</option>
                                                            <option>Trp->Glu</option>
                                                            <option>Trp->Gly</option>
                                                            <option>Trp->His</option>
                                                            <option>Trp->Lys</option>
                                                            <option>Trp->Met</option>
                                                            <option>Trp->Phe</option>
                                                            <option>Trp->Pro</option>
                                                            <option>Trp->Ser</option>
                                                            <option>Trp->Thr</option>
                                                            <option>Trp->Tyr</option>
                                                            <option>Trp->Val</option>
                                                            <option>Trp->Xle</option>
                                                            <option>Tyr->Ala</option>
                                                            <option>Tyr->Arg</option>
                                                            <option>Tyr->Asn</option>
                                                            <option>Tyr->Asp</option>
                                                            <option>Tyr->CamCys</option>
                                                            <option>Tyr->Cys</option>
                                                            <option>Tyr->Gln</option>
                                                            <option>Tyr->Glu</option>
                                                            <option>Tyr->Gly</option>
                                                            <option>Tyr->His</option>
                                                            <option>Tyr->Lys</option>
                                                            <option>Tyr->Met</option>
                                                            <option>Tyr->Phe</option>
                                                            <option>Tyr->Pro</option>
                                                            <option>Tyr->Ser</option>
                                                            <option>Tyr->Thr</option>
                                                            <option>Tyr->Trp</option>
                                                            <option>Tyr->Val</option>
                                                            <option>Tyr->Xle</option>
                                                            <option>Val->Ala</option>
                                                            <option>Val->Arg</option>
                                                            <option>Val->Asn</option>
                                                            <option>Val->Asp</option>
                                                            <option>Val->CamCys</option>
                                                            <option>Val->Cys</option>
                                                            <option>Val->Gln</option>
                                                            <option>Val->Glu</option>
                                                            <option>Val->Gly</option>
                                                            <option>Val->His</option>
                                                            <option>Val->Lys</option>
                                                            <option>Val->Met</option>
                                                            <option>Val->Phe</option>
                                                            <option>Val->Pro</option>
                                                            <option>Val->Ser</option>
                                                            <option>Val->Thr</option>
                                                            <option>Val->Trp</option>
                                                            <option>Val->Tyr</option>
                                                            <option>Val->Xle</option>
                                                            <option>Xle->Ala</option>
                                                            <option>Xle->Arg</option>
                                                            <option>Xle->Asn</option>
                                                            <option>Xle->Asp</option>
                                                            <option>Xle->CamCys</option>
                                                            <option>Xle->Cys</option>
                                                            <option>Xle->Gln</option>
                                                            <option>Xle->Glu</option>
                                                            <option>Xle->Gly</option>
                                                            <option>Xle->His</option>
                                                            <option>Xle->Lys</option>
                                                            <option>Xle->Met</option>
                                                            <option>Xle->Phe</option>
                                                            <option>Xle->Pro</option>
                                                            <option>Xle->Ser</option>
                                                            <option>Xle->Thr</option>
                                                            <option>Xle->Trp</option>
                                                            <option>Xle->Tyr</option>
                                                            <option>Xle->Val</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <a href="#coll-4" data-toggle="collapse"
                                           class="h6 text-primary font-weight-bold">Instrument</a>
                                        <div class="collapse show" id="coll-4">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Instrument
                                                        type</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6">
                                                        <select name="instrumentType" class="form-control">
                                                            <option th:each="type : ${instrumentTypes}"
                                                                    th:value="${type}" th:text="${type}"></option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <a href="#coll-5" data-toggle="collapse"
                                           class="h6 text-primary font-weight-bold">Digestion</a>
                                        <div class="collapse show" id="coll-5">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Digestion
                                                        type</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6">
                                                        <select name="digestionMode" class="form-control target-select"
                                                                data-id="dm-div">
                                                            <option th:each="mode : ${digestionModes}"
                                                                    th:attrappend="data-id=${'dm'+mode.key}"
                                                                    th:value="${mode.value.desc}"
                                                                    th:text="${mode.value.desc}">
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div id="dm-div">
                                                    <div id="dm0"
                                                         class="desc form-group row align-items-center offset-xl-2 col-xl-10 p-1"
                                                         style="display:block">
                                                        <div class="form-group row align-items-start">
                                                            <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Enzyme</label>
                                                            <div class="col-xl-6 col-lg-6 col-md-6">
                                                                <select name="enzyme" class='form-control multi-select'
                                                                        multiple='multiple'>
                                                                    <option>ArgC</option>
                                                                    <option>AspC</option>
                                                                    <option>AspN</option>
                                                                    <option>Chymotrypsin</option>
                                                                    <option>Chymotrypsin+</option>
                                                                    <option>D. P</option>
                                                                    <option>GluC</option>
                                                                    <option>GluN</option>
                                                                    <option>LysC</option>
                                                                    <option>LysC/P</option>
                                                                    <option>LysN</option>
                                                                    <option selected>Trypsin/P</option>
                                                                    <option>Trypsin</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="form-group row align-items-center">
                                                            <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Max
                                                                missed</label>
                                                            <div class="col-xl-6 col-lg-6 col-md-6">
                                                                <input name="maxMissed" type="text" class="form-control"
                                                                       value="2">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="dm1"
                                                         class="desc form-group row align-items-center offset-xl-2 col-xl-10 p-1"
                                                         style="display:none">
                                                        <div class="form-group row align-items-start">
                                                            <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Enzyme</label>
                                                            <div class="col-xl-6 col-lg-6 col-md-6">
                                                                <select class='form-control multi-select'
                                                                        multiple='multiple'>
                                                                    <option>ArgC</option>
                                                                    <option>AspC</option>
                                                                    <option>AspN</option>
                                                                    <option>Chymotrypsin</option>
                                                                    <option>Chymotrypsin+</option>
                                                                    <option>D. P</option>
                                                                    <option>GluC</option>
                                                                    <option>GluN</option>
                                                                    <option>LysC</option>
                                                                    <option>LysC/P</option>
                                                                    <option>LysN</option>
                                                                    <option selected>Trypsin/P</option>
                                                                    <option>Trypsin</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="dm2"
                                                         class="desc form-group row align-items-center offset-xl-2 col-xl-10 p-1"
                                                         style="display:none">
                                                        <div class="form-group row align-items-start">
                                                            <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Enzyme</label>
                                                            <div class="col-xl-6 col-lg-6 col-md-6">
                                                                <select class='form-control multi-select'
                                                                        multiple='multiple'>
                                                                    <option>ArgC</option>
                                                                    <option>AspC</option>
                                                                    <option>AspN</option>
                                                                    <option>Chymotrypsin</option>
                                                                    <option>Chymotrypsin+</option>
                                                                    <option>D. P</option>
                                                                    <option>GluC</option>
                                                                    <option>GluN</option>
                                                                    <option>LysC</option>
                                                                    <option>LysC/P</option>
                                                                    <option>LysN</option>
                                                                    <option selected>Trypsin/P</option>
                                                                    <option>Trypsin</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="dm3"
                                                         class="desc form-group row align-items-center offset-xl-2 col-xl-10 p-1"
                                                         style="display:none">
                                                        <div class="form-group row align-items-start">
                                                            <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Enzyme</label>
                                                            <div class="col-xl-6 col-lg-6 col-md-6">
                                                                <select class='form-control multi-select'
                                                                        multiple='multiple'>
                                                                    <option>ArgC</option>
                                                                    <option>AspC</option>
                                                                    <option>AspN</option>
                                                                    <option>Chymotrypsin</option>
                                                                    <option>Chymotrypsin+</option>
                                                                    <option>D. P</option>
                                                                    <option>GluC</option>
                                                                    <option>GluN</option>
                                                                    <option>LysC</option>
                                                                    <option>LysC/P</option>
                                                                    <option>LysN</option>
                                                                    <option selected>Trypsin/P</option>
                                                                    <option>Trypsin</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="dm4"
                                                         class="desc form-group row align-items-center offset-xl-2 col-xl-10 p-1"
                                                         style="display:none">
                                                    </div>
                                                    <div id="dm5"
                                                         class="desc form-group row align-items-center offset-xl-2 col-xl-10 p-1"
                                                         style="display:none">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mb-3">
                                <a href="javascript:void(0)" onclick="submitForm(this)"
                                   class="btn btn-outline-primary btn-custom">
                                    <span>Submit</span><i class="fa fa-long-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                    </div>
                </div>
            </main>
        </div>
    </div>
    <div id="file-modal"></div>
</div>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/file-modal3.js}"></script>
    <script th:src="@{/js/jquery.multi-select.js}"></script>

    <script>
      $("#file-modal").fileModal('/analysis/fileTree')

      $("select.target-select").on('change', function () {
        var targetDiv = $(this).data('id')
        var targetId = $(this).find("option:selected").data('id')
        $("#" + targetDiv).find("div.desc").hide()
        $("#" + targetId).show()

        $("#" + targetId).find('select.target-select').trigger('change')
      })

      $('.multi-select').multiSelect({})
      $('.input-daterange').datepicker({
        format: "yyyy/mm/dd",
        toggleActive: true,
        autoclose: true,
        todayHighlight: true
      })

      $("#skipIQ").click(function () {
        if ($(this).is(":checked")) {
          $("#clf").show()
        } else {
          $("#clf").hide()
        }
      })

      var _selectBtn
      $("#file-modal").on('__SELECT_FILES__', function (e, data) {
        var nodes = data.nodes || []
        if (nodes.length === 0) {
          return
        }
        if (nodes.length > 1) {
          layer.msg("only one file or dir can be selected")
          return
        }

        var html = []
        $.each(nodes, function (i, node) {
          var filePath = node.path
          var fileName = node.name
          var fileSize = node.size

          html.push('<b class="text-primary width-400" data-toggle="tooltip" data-placement="left"  title="' + filePath + '">' + filePath + '')
          html.push('<input type="hidden" value="' + filePath + '">')
          html.push('<input type="hidden" value="' + fileName + '">')
          html.push('<input type="hidden" value="' + fileSize + '">')
          html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
          html.push('</b>')
        })
        $(_selectBtn).parent().next().find('em.seled:first').html(html.join(''))
        $('[data-toggle="tooltip"]').tooltip()
      })

      function showFileModal (_this) {
        _selectBtn = _this

        var selectIds = []
        $(_this).parents("td:first").find("em").find("b.text-primary").each(function () {
          selectIds.push($(this).find("input[type=hidden]:eq(0)").val())
        })
        $("#file-modal").trigger('__SHOW_SELECT_FILES_MODAL__', {
          selectIds: selectIds,
          allowFolder: function () {
            return true
          },
          func: function (data) {
            return !data.name || /.*\.wiff/.test(data.name) || /.*\.mzxml/.test(data.name) || /.*\.raw/.test(data.name) || /.*\.uimf/.test(data.name) || /.*\.d/.test(data.name)
          }
        })
      }

      function addRow (_this) {
        var trClone = $(_this).parents("tbody:first").find("tr").eq(0).clone(true)
        trClone.find('em.seled').html("")
        $(_this).parents("tbody:first").append(trClone)
      }

      function removeRow (_this) {
        if ($(_this).parents("tbody:first").find("tr").length < 2) {
          return
        }
        $(_this).parents("tr:first").remove()
      }

      function removeFile (_this) {
        $(_this).parent().parent().remove()
      }

      function validate () {
        // TODO
        return true
      }

      function submitForm (_this) {
        var res = validate()
        if (!res) {
          return
        }

        var formData = new FormData()
        $("#sample-table").find("tr").each(function (index) {
          $(this).find("td:eq(0)").find('b.text-primary').each(function (i) {
            formData.append("inputs[" + index + "].file.path", $(this).find("input[type=hidden]:eq(0)").val())
            formData.append("inputs[" + index + "].file.name", $(this).find("input[type=hidden]:eq(1)").val())
            formData.append("inputs[" + index + "].file.size", $(this).find("input[type=hidden]:eq(2)").val())
          })

          formData.append("inputs[" + index + "].group", $(this).find("td:eq(1)").find('input').val())
          formData.append("inputs[" + index + "].experiment", $(this).find("td:eq(2)").find('input').val())
          formData.append("inputs[" + index + "].fraction", $(this).find("td:eq(3)").find('input').val())
          formData.append("inputs[" + index + "].ptm", $(this).find("td:eq(4)").find('select').val())
        })

        var refDb = $("select[name='refDb']").val() || ''
        var includeCont = $("input[name='includeCont']:checked").val() || ''
        var ibaq = $("input[name='ibaq']:checked").val() || ''
        var logFit = $("input[name='logFit']:checked").val() || ''
        formData.append("refDb", refDb)
        formData.append("includeCont", includeCont)
        formData.append("ibaq", ibaq)
        formData.append("logFit", logFit)

        var ftms = $("input[name='ftms']").val() || ''
        var ftmsUnit = $("select[name='ftmsUnit']").val() || ''
        var itms = $("input[name='itms']").val() || ''
        var itmsUnit = $("select[name='itmsUnit']").val() || ''
        var tof = $("input[name='tof']").val() || ''
        var tofUnit = $("select[name='tofUnit']").val() || ''
        var unknown = $("input[name='unknown']").val() || ''
        var unknownUnit = $("select[name='unknownUnit']").val() || ''

        formData.append("ftms", ftms)
        formData.append("ftmsUnit", ftmsUnit)
        formData.append("itms", itms)
        formData.append("itmsUnit", itmsUnit)
        formData.append("tof", tof)
        formData.append("tofUnit", tofUnit)
        formData.append("unknown", unknown)
        formData.append("unknownUnit", unknownUnit)

        var psfFdr = $("input[name='psfFdr']").val() || ''
        var proteinFdr = $("input[name='proteinFdr']").val() || ''
        var minPeptides = $("input[name='minPeptides']").val() || ''
        var minRaror = $("input[name='minRaror']").val() || ''
        var minUnique = $("input[name='minUnique']").val() || ''

        formData.append("psfFdr", psfFdr)
        formData.append("proteinFdr", proteinFdr)
        formData.append("minPeptides", minPeptides)
        formData.append("minRaror", minRaror)
        formData.append("minUnique", minUnique)

        var groupType = $("select[name='groupType']").val()
        formData.append("groupType", groupType)
        switch (groupType) {
          case "Standard":
            setStandard(formData)
            break
          case "Report ion MS2":
            formData.append("isobaricLabels", $(":radio[name='isobaricLabels']").val())
            break
          case "TIMS-DDA":
            setTimsDda(formData)
            break
          default:
            break
        }

        var variableMods = $("select[name='variableMods']").val() || ''
        formData.append("variableMods", variableMods)

        var fixMods = $("select[name='fixMods']").val() || ''
        formData.append("fixMods", fixMods)

        var instrumentType = $("select[name='instrumentType']").val() || ''
        formData.append("instrumentType", instrumentType)

        var digestionMode = $("select[name='digestionMode']").val() || ''
        formData.append("digestionMode", digestionMode)

        if (digestionMode !== 'Unspecific' && digestionMode !== 'No digestion') {
          var dmId = $("select[name='digestionMode']").find('option:selected').data('id')
          var enzymes = $("#" + dmId).find("select[name='enzyme']").val()
          formData.append("enzymes", enzymes)
        }

        if (digestionMode === 'Specific') {
          var maxMissed = $("#dm0").find("input[name='maxMissed']").val()
          formData.append("maxMissed", maxMissed)
        }

        if ($(_this).data('loading') == 'true') {
          return
        }
        $(_this).data('loading', 'true')

        $.ajax({
          url: '/analysis/proteomics/createTask',
          dataType: 'json',
          type: 'post',
          processData: false,
          contentType: false,
          data: formData,
          success: function (result) {
            if (result.code == 200) {
              layer.msg('submit success')
              var id = result.data
              setTimeout(function () {
                var _context_path = $("meta[name='_context_path']").attr("content")
                window.location.href = $.trim(_context_path) + '/analysis/proteomics/list?taskId=' + id
              }, 2000)
            }
          },
          complete: function () {
            $(_this).data('loading', 'false')
          }
        })

        function setStandard (formData) {
          var multiplicity = $("select[name='multiplicity']").val()
          formData.append('multiplicity', multiplicity)
          var maxLabeled = 0

          if (multiplicity === "1") {
            $("#gmm1").find("input[name='lightLabels']:checked").each(function () {
              formData.append('lightLabels', $(this).val())
            })
          } else if (multiplicity === "2") {
            maxLabeled = $("#gmm2").find("input[name='maxLabeled']").val()

            $("#gmm2").find("input[name='lightLabels']:checked").each(function () {
              formData.append('lightLabels', $(this).val())
            })
            $("#gmm2").find("input[name='heavyLabels']:checked").each(function () {
              formData.append('heavyLabels', $(this).val())
            })
          } else {
            maxLabeled = $("#gmm3").find("input[name='maxLabeled']").val()

            $("#gmm3").find("input[name='lightLabels']:checked").each(function () {
              formData.append('lightLabels', $(this).val())
            })
            $("#gmm3").find("input[name='mediumLabels']:checked").each(function () {
              formData.append('mediumLabels', $(this).val())
            })
            $("#gmm3").find("input[name='heavyLabels']:checked").each(function () {
              formData.append('heavyLabels', $(this).val())
            })
          }
          formData.append('maxLabeled', maxLabeled)
        }

        function setTimsDda (formData) {
          var timsHalfWidth = $("input[name='timsHalfWidth']").val() || ''
          var timsStep = $("input[name='timsStep']").val() || ''
          var timsResolution = $("input[name='timsResolution']").val() || ''
          var timsMin = $("input[name='timsMin']").val() || ''
          var timsRem = $("input[name='timsRem']:checked").val() || 'false'
          var timsColl = $("input[name='timsColl']:checked").val() || 'false'

          formData.append("timsHalfWidth", timsHalfWidth)
          formData.append("timsStep", timsStep)
          formData.append("timsResolution", timsResolution)
          formData.append("timsMin", timsMin)
          formData.append("timsRem", timsRem)
          formData.append("timsColl", timsColl)
        }
      }

      function uploadExcel () {
        if ($("#excel").val() === '') {
          layer.msg('please select a file')
          return
        }
        var formData = new FormData()
        formData.append('file', $('#excel')[0].files[0])
        $.ajax({
          url: '/analysis/proteomics/uploadTemplate',
          data: formData,
          dataType: 'json',
          type: 'post',
          async: false,
          processData: false,
          contentType: false,
          success: function (result) {
            if (result.success) {
              var data = result.data || []
              if (data.length === 0) {
                layer.msg("no data")
                return
              }
              var trs = []
              $.each(data, function (idx, item) {
                var html = ['<tr>']
                html.push('<td class="td-input">')
                html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>')
                html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">')
                obtainTr(html, item.file)
                html.push('</em></span></div></td>')

                html.push('<td class="td-input"><input type="number" class="form-control text-center" value="' + item.group + '"></td>')
                html.push('<td class="td-input"><input type="number" class="form-control text-center" value="' + item.experiment + '"></td>')
                html.push('<td class="td-input"><input type="number" class="form-control text-center" value="' + item.fraction + '"></td>')

                html.push('<td class="td-input">')
                html.push('<select class="form-control form-control-sm select2" style="width: 100%">')
                html.push('<option value="false"' + (item.ptm ? '' : 'checked') + '>False</option>')
                html.push('<option value="true"' + (item.ptm ? 'checked' : '') + '>True</option>')
                html.push('</select></td>')
                html.push('<td><i class="fa fa-plus-square text-primary" onclick="addRow(this)"></i><i class="fa fa-minus-square text-muted ml-1" onclick="removeRow(this)"></i></td>')
                trs.push(html.join(''))
              })
              $("#sample-table").html(trs.join(''))
              layer.msg("The data is imported successfully. Please check whether the table data result is correct")
            } else {
              layer.msg(result.message)
            }
          }
        })
      }

      function obtainTr (html, node) {
        if (!node) {
          return
        }
        var filePath = node.path
        var fileName = node.name
        var fileSize = node.size

        html.push('<b class="text-primary width-400" data-toggle="tooltip" data-placement="left"  title="' + filePath + '">' + filePath + '')
        html.push('<input type="hidden" value="' + filePath + '">')
        html.push('<input type="hidden" value="' + fileName + '">')
        html.push('<input type="hidden" value="' + fileSize + '">')
        html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
        html.push('</b>')
      }
    </script>
</th:block>
</html>
