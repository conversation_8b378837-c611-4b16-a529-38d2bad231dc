package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.scrnaseq.vo.*;
import cn.ac.picb.vipmap.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ScrnaseqMapper {

    ScrnaseqMapper INSTANCE = Mappers.getMapper(ScrnaseqMapper.class);

    GenomicsTaskParamVO convertToGenomicsTaskParamVO(ScrnaseqGenomicsTaskParam vo);

    TaskQueryVO convertToTaskQuery(ScrnaseqTaskSearchVO vo);

    BaselineTaskParamVO convertToBaselineTaskParamVO(ScrnaseqBaselineTaskParam param);

    PagaTaskParamVO convertToPagaTaskParamVO(ScrnaseqPagaTaskParam param);

    DegTaskParamVO convertToDegTaskParamVO(ScrnaseqDegTaskParam param);

    WgcnaTaskParamVo convertToWgcnaTaskParamVO(ScrnaseqWgcnaTaskParam param);

    GenesTaskParamVO convertToGenesTaskParamVO(ScrnaseqGenesTaskParam param);

    ReportQueryVO convertToReportQuery(ReportSearchVO vo);

    ReportTaskParamVO convertToReportTaskParamVO(ScrnaseqReportTaskParam param);

    ReportTaskCreateParamVO convertToReportTaskCreateParamVO(ScrnaseqReportTaskParam param);

    SelectParamVO convertToSelectParamVO(ScrnaseqSelectParam param);

    SelectedTaskVO convertToSelectedTaskVO(ScrnaseqReportTaskParam param);
}
