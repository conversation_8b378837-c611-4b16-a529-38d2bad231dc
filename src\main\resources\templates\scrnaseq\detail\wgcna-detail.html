<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">WGCNA</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <th:block th:switch="${taskVo.wgcnaTask.status}">
                        <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                    aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Prepared</p>
                        </div>
                        <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="2">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                    aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Data prepared</p>
                        </div>
                        <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="3">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                    aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis done</p>
                        </div>
                        <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                    aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis Error</p>
                        </div>
                    </th:block>
                    <div class="form-group-box">
                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                        <div class="collapse show" id="coll-1">
                            <div class="pl-4 pt-2">
                                <div class="result-box">
                                    <div class="table-responsive mt-2">
                                        <table class="table table-bordered table-sm table-center table-middle mb-1">
                                            <thead>
                                            <tr class="thead-light">
                                                <th>Cluster baseline ID</th>
                                                <th>Start time</th>
                                                <th>Status time</th>
                                                <th>Consuming</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td th:text="${taskVo.baselineTask.taskId}"></td>
                                                <td th:text="${#dates.format(taskVo.baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${#dates.format(taskVo.baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${taskVo.baselineTask.useTime}">26m55s</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>


                                    <h6 class="border-bottom pb-2 m-2">Gene ontology</h6>
                                    <div class="form-group row align-items-center m-0">
                                        <label
                                                class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                        <div class="col-xl-3 col-lg-3 col-md-3">
                                            <span class="text-primary" th:if="${taskVo.wgcnaTask.species} == 'human'">Homo sapiens (human)</span>
                                            <span class="text-primary" th:if="${taskVo.wgcnaTask.species} == 'mouse'">Mus musculus（mouse）</span>
                                        </div>
                                        <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                        <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary">Fisher’s
                                                    exact test </span></div>

                                    </div>

                                    <h6 class="border-bottom pb-2 m-2" style="margin-top: 30px;">Differential
                                        Analysis</h6>
                                    <div class="form-group row align-items-center m-0">
                                        <div class="col-lg-3">
                                            <label>Power</label>
                                            <span class="text-primary" style="margin-left: 20px;"
                                                  th:text="${taskVo.wgcnaTask.power}">15</span>
                                        </div>
                                        <div class="col-lg-3">
                                            <label>netWorkType</label>
                                            <span class="text-primary" style="margin-left: 20px;"
                                                  th:text="${taskVo.wgcnaTask.networkType}">signed</span>
                                        </div>
                                        <div class="col-lg-3">
                                            <label class="col-form-label pr-0">minModuleSize</label>
                                            <span class="text-primary" style="margin-left: 20px;"
                                                  th:text="${taskVo.wgcnaTask.minModuleSize}">10</span>
                                        </div>
                                        <div class="col-lg-3">
                                            <label>mergeCutHeight</label>
                                            <span class="text-primary" style="margin-left: 20px;"
                                                  th:text="${taskVo.wgcnaTask.mergeCutHeight}">0.2</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group-box" th:if="${taskVo.wgcnaTask.status == 2}">
                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                        <div class="collapse show" id="coll-2">
                            <div class="tool-content">
                                <div class="row" style="margin-top: 50px;height: 400px;">
                                    <div class="col-lg-12">
                                        <div id="chart-14" style="width: 100%;height: 400px"></div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row" style="margin-top: 50px;height: 500px;">
                                    <div class="col-lg-12">
                                        <div id="chart-17" style="width: 100%;height: 500px"></div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row" style="margin-top: 100px;height: 600px;">
                                    <div class="col-lg-12">
                                        <div id="chart-15" style="width: 100%;height: 600px"></div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row" style="margin-top: 100px;height: 600px;">
                                    <div class="col-lg-12">
                                        <div id="chart-16-box"></div>
                                        <div id="chart-16" style="width: 100%;height: 600px"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-box">
                    <div class="tool-title">Reference</div>
                    <div class="tool-content">
                        <ul class="r-list">
                            <li>1. BMC bioinformatics, 2008, WGCNA: an R package for weighted correlation network
                                analysis
                            </li>
                            <li>2. Cell discovery, 2019, Twin-peak temporal regulation during human neocortical
                                development
                            </li>
                            <li>3. Elife, 2020, Co-expression analysis reveals interpretable gene modules controlled
                                by trans-acting genetic variants
                            </li>
                            <li>4. Scientific reports, 2021, Weighted gene co-expression network analysis of the
                                salt-responsive transcriptomes reveals novel hub genes in green halophytic
                                microalgae Dunaliella salina
                            </li>

                        </ul>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>
    <script>
      $(document).ready(function () {
        initChart14();
        initChart17();
        initChart15();
        initChart16();
      })

      function initChart17() {
          if (!document.getElementById('chart-17')) {
              return
          }
          let chart = echarts.init(document.getElementById('chart-17'))
          chart.clear()

          $.ajax({
              url: '/analysis/scrnaseq/wgcna/[[${taskVo.wgcnaTask.id}]]/17',
              async: false,
              beforeSend: function () {
                  $("#chart-17").next().remove()
                  $("#chart-17").show()
                  chart.showLoading()
              },
              complete: function () {
                  chart.hideLoading()
              },
              success: function (res) {
                  if (res.code != 200) {
                      $("#chart-17").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                      $("#chart-17").hide()
                      return
                  }
                  let {indexMap, geneMap, treeStr} = res.data

                  function sum(arr) {
                      return arr.reduce(function (prev, curr, idx, arr) {
                          return prev + curr;
                      });
                  }
                  function parse_newick(s) {
                      let ancestors = [], tree = {}, subtree = {};
                      let tokens = s.split(/\s*(;|\(|\)|,|:)\s*/);
                      for (let i = 0; i < tokens.length; i++) {
                          let token = tokens[i];
                          switch (token) {
                              case '(': // new children
                                  subtree = {};
                                  tree.children = [subtree];
                                  ancestors.push(tree);
                                  tree = subtree;
                                  break;
                              case ',': // another branch
                                  subtree = {};
                                  ancestors[ancestors.length - 1].children.push(subtree);
                                  tree = subtree;
                                  break;
                              case ')': // optional name next
                                  tree = ancestors.pop();
                                  break;
                              case ':': // optional length next
                                  break;
                              default:
                                  let c = tokens[i - 1];
                                  if (['(', ')', ','].indexOf(c) !== -1) {
                                      if (token.startsWith('"') && token.startsWith('"')) {
                                          token = token.substr(1, token.length - 2)
                                      }

                                      if (token != null && token != '') {
                                          tree.index = token;
                                          tree.name = indexMap[tree.index]
                                          tree.color = geneMap[tree.name]
                                      }
                                  } else if (c === ':') {
                                      tree.height = parseFloat(token);
                                  }
                          }
                      }
                      return tree;
                  }

                  let tree = parse_newick(treeStr);
                  tree.height = 1;

                  let nodes = [], lines = [], minY = 1;

                  function getInternalNode(d) {
                      const l = []
                      d.forEach((it) => {
                          let x = it.x
                          if (x === undefined) {
                              if (Array.isArray(it.children) && it.children.length > 1) {
                                  x = getInternalNode(it.children)
                              }
                          }
                          l.push(x)
                      })
                      return sum(l) / l.length
                  }
                  function genLeafNode(tree) {
                      if (tree.children === undefined) {
                          tree.x = nodes.length;
                          tree.leaf = true;
                          nodes.push({index: tree.index, name: tree.name, height: tree.height, color: tree.color})
                      }
                      if (Array.isArray(tree.children) && tree.children.length > 1) {
                          tree.children.forEach(it => {
                              genLeafNode(it)
                          })
                          tree.x = getInternalNode(tree.children)
                      }
                  }
                  genLeafNode(tree)

                  function genTreeCoords(childs, parentX, parentH, result) {
                      childs.forEach(item => {
                          let itemY = item.leaf ? item.height : parentH - item.height;
                          if (itemY < minY) {
                              minY = itemY
                          }
                          const coords = [
                              [parentX + 0.5, parentH],
                              [item.x + 0.5, parentH],
                              [item.x + 0.5, itemY]
                          ]
                          result.push({
                              coords: coords
                          })

                          if (item.children != null && item.children.length > 0) {
                              genTreeCoords(item.children, item.x, itemY, result)
                          }
                      })
                  }
                  genTreeCoords(tree.children, tree.x, tree.height, lines)

                  let barData = [];
                  for (let i = 0; i < nodes.length; i++) {
                      barData.push({
                          node: nodes[i],
                          value: [i, 1],
                          itemStyle: {
                              color: nodes[i].color,
                          }
                      })
                  }

                  chart.setOption({
                      grid: [
                          {
                              top: '10%',
                              height: '62%',
                              left: '100px',
                              right: '50px'
                          }, {
                              top: '75%',
                              bottom: '40px',
                              left: '100px',
                              right: '50px'
                              // height: '20%'
                          }
                      ],
                      toolbox: {
                          show: true,
                          feature: {
                              saveAsImage: {}
                          }
                      },
                      title: {
                          text: 'Cluster Dendrogram',
                          left: 'center'
                      },
                      tooltip: {
                          trigger: 'axis',
                          axisPointer: {
                              type: 'cross',
                              snap: true,
                              label: {
                                  show: false,
                                  precision: 0,
                              }
                          },
                          formatter: function (param) {
                              let node = param[0].data.node;
                              return `${param[0].marker} ${node.name}: ${node.height}`
                              // return `${param[0].marker} ${JSON.stringify(node)}`
                          }
                      },
                      axisPointer: {
                          link: { xAxisIndex: 'all' },
                      },
                      dataZoom: [
                          {
                              type: 'slider',
                              xAxisIndex: [0, 1],
                              labelPrecision: 0,
                              minValueSpan: 1,
                              showDetail: false,
                          },
                          {
                              type: 'slider',
                              yAxisIndex: [0],
                              // xAxisIndex: [0, 1],
                              orient: 'vertical',
                              showDetail: false,
                          }
                      ],
                      xAxis: [
                          {
                              gridIndex: 0,
                              type: 'value',
                              min: 0,
                              max: nodes.length,
                              axisLine: {show: false},
                              axisTick: {show: false},
                              axisLabel: {show: false},
                              splitLine: {show: false},
                          },
                          {
                              gridIndex: 1,
                              type: 'category',
                              axisLine: {show: false},
                              axisTick: {show: false},
                              axisLabel: {show: false},
                              splitLine: {show: false},
                          }
                      ],
                      yAxis: [
                          {
                              gridIndex: 0,
                              type: 'value',
                              inverse: false,
                              scale: true,
                              min: Math.floor(minY / 0.05) * 0.05,
                              max: 1,
                              name: 'Height',
                              nameLocation: 'center',
                              nameGap: 70,
                              nameTextStyle: {
                                  size: 12,
                                  fontWeight: 'bold',
                              },
                              axisLabel: {
                                  formatter: function (value) {
                                      return value.toFixed(2)
                                  }
                              },
                              // axisLine: {show: false},
                              // axisTick: {show: false},
                              splitLine: {show: false},
                          }, {
                              gridIndex: 1,
                              type: 'value',
                              name: 'Module colors',
                              nameLocation: 'center',
                              nameGap: 10,
                              nameRotate: 0,
                              nameTextStyle: {
                                  size: 12,
                                  fontWeight: 'bold',
                              },
                              axisLine: {show: false},
                              axisTick: {show: false},
                              axisLabel: {show: false},
                              splitLine: {show: false},
                          }
                      ],
                      series: [
                          {
                              type: 'lines',
                              xAxisIndex: 0,
                              yAxisIndex: 0,
                              coordinateSystem: 'cartesian2d',
                              polyline: true,
                              data: lines,
                              silent: true,
                              large: true,    // 大数据渲染优化
                              lineStyle: {
                                  type: 'solid',
                                  color: '#000'
                              }
                          },
                          {
                              type: 'bar',
                              xAxisIndex: 1,
                              yAxisIndex: 1,
                              barWidth: '100%',
                              clip: true,
                              silent: true,
                              data: barData,
                          }
                      ]
                  })
              }
          })
      }

      function initChart14 () {
        if (!document.getElementById('chart-14')) {
          return
        }
        var power = echarts.init(document.getElementById('chart-14'))
        power.clear()
        var result
        //$.ajaxSettings.async = false;
        $.ajax({
          url: '/analysis/scrnaseq/wgcna/[[${taskVo.wgcnaTask.id}]]/14',
          async: false,
          beforeSend: function () {
            $("#chart-14").next().remove()
            $("#chart-14").show()
            power.showLoading()
          },
          complete: function () {
            power.hideLoading()
          },
          success: function (res) {
            if (res.code != 200) {
              $("#chart-14").after('<div class="bg-light text-muted p-3">无分析结果</div>')
              $("#chart-14").hide()
              return
            }
            result = res.data
          }
        })
        var show1 = []
        var show2 = []
        for (let i = 0; i < result.powerNumber.length; i++) {
          show1.push([result.powerNumber[i], result.correlation[i]])
          show2.push([result.powerNumber[i], result.connectivity[i]])
        }
        var dataAll = [show1, show2]

        option = {
          toolbox: {
            show: true,
            feature: {
              saveAsImage: {}
            }
          },
          grid: [
            { left: '7%', top: '7%', width: '38%', bottom: '100px' },
            { right: '7%', top: '7%', width: '38%', bottom: '100px' }

          ],
          tooltip: {
            formatter: '{a}: ({c})'
          },
          title: [
            {
              text: 'Scale independence',
              left: '15%',
              textStyle: {
                lineHeight: 12,
              }
            },
            {
              text: 'Mean connectivity',
              right: '17%',
              textStyle: {
                lineHeight: 12,
              }
            }
          ],
          xAxis: [
            {
              gridIndex: 0,
              name: 'soft Threshold(power)',
              nameLocation: 'center',
              nameGap: 50,
              nameTextStyle: {
                size: 12,
                fontWeight: 'bold',
              },
              min: 0
            },
            {
              gridIndex: 1,
              name: 'soft Threshold(power)',
              nameLocation: 'center',
              nameGap: 50,
              nameTextStyle: {
                size: 12,
                fontWeight: 'bold',
              },
              min: 0
            },
          ],
          yAxis: [
            {
              gridIndex: 0,
              name: 'Scale Free Topology Model Fit,signed R^2',
              nameLocation: 'center',
              nameGap: 50,
              nameTextStyle: {
                fontWeight: 'bold',
              },
              namePotate: 90,

            },
            {
              gridIndex: 1,
              name: 'Mean Connectivity',
              nameLocation: 'center',
              nameGap: 50,
              nameTextStyle: {
                fontWeight: 'bold',
              },
              namePotate: 90,
              axisLine: {
                show: false,
                width: 5
              }
            }
          ],
          series: [
            {
              name: 'Scale independence',
              type: 'scatter',
              xAxisIndex: 0,
              yAxisIndex: 0,
              data: dataAll[0],
            },
            {
              name: 'Mean connectivity',
              type: 'scatter',
              xAxisIndex: 1,
              yAxisIndex: 1,
              data: dataAll[1],
            }
          ]
        }
        power.setOption(option)
      }

      function initChart15 () {
        if (!document.getElementById('chart-15')) {
          return
        }
        var dom = document.getElementById("chart-15")
        var myChart = echarts.init(dom)

        myChart.clear()
        var result
        $.ajax({
          url: '/analysis/scrnaseq/wgcna/[[${taskVo.wgcnaTask.id}]]/15',
          async: false,
          beforeSend: function () {
            $("#chart-15").next().remove()
            $("#chart-15").show()
            myChart.showLoading()
          },
          complete: function () {
            myChart.hideLoading()
          },
          success: function (res) {
            if (res.code != 200) {
              $("#chart-15").after('<div class="bg-light text-muted p-3">无分析结果</div>')
              $("#chart-15").hide()
              return
            }
            result = res.data
          }
        })

        const hours = result.x
        const days = result.y
        const data = result.data

        var sd = data.map(function (item) {
          return [item[1], item[0], item[2] || '-']
        })

        option = {
          toolbox: {
            show: true,
            feature: {
              saveAsImage: {}
            }
          },
          title: {
            text: 'Correlation between module and cell type',
            left: 'center',
            textStyle: {
              lineHeight: 12,
            }
          },

          tooltip: {
            position: 'top'
          },
          grid: {
            bottom: '100px',
            top: '10%'
          },
          xAxis: {
            type: 'category',
            data: hours,
            splitArea: {
              show: true
            },
            axisLabel: {
              interval: 0,
              rotate: 90
            }
          },
          yAxis: {
            type: 'category',
            data: days,
            splitArea: {
              show: true
            },
            axisLabel: {
              interval: 0
            },
            inverse: true
          },
          visualMap: {
            min: 0,
            max: 1,
            dimension: 2,
            precision: 8,
            text: ['1', '0'],
            //calculable: true,
            orient: 'vertical',
            left: 'right',
            //bottom: '15%',
            top: '15%',
            inRange: {
              color: ['#5398ea', '#eedb93', '#ea4242']
            }
          },
          series: [{
            type: 'heatmap',
            data: sd,
          }]
        }

        myChart.setOption(option)
      }

      function initChart16 () {
        if (!document.getElementById('chart-16')) {
          return
        }
        var dom = document.getElementById("chart-16")
        var myChart = echarts.init(dom, null, { width: 800, height: 500 })
        myChart.clear()
        var result
        $.ajax({
          url: '/analysis/scrnaseq/wgcna/[[${taskVo.wgcnaTask.id}]]/16',
          async: false,
          beforeSend: function () {
            $("#chart-16").next().remove()
            $("#chart-16").show()
            myChart.showLoading()
          },
          complete: function () {
            myChart.hideLoading()
          },
          success: function (res) {
            if (res.code != 200) {
              $("#chart-16").after('<div class="bg-light text-muted p-3">无分析结果</div>')
              $("#chart-16").hide()
              return
            }
            result = res.data
          }
        })

        const dataBJ = result.data
        let max1 = 0
        let max2 = 0
        for (i = 0; i < dataBJ.length; i++) {
          if (dataBJ[i][2] > max1) {
            max1 = dataBJ[i][2]
          }
          if (dataBJ[i][3] > max2) {
            max2 = dataBJ[i][3]
          }
        }
        const itemStyle = {
          opacity: 0.8,
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowColor: 'rgb(9,8,8)'
        }
        option = {
          // toolbox: {
          //     show: true,
          //     feature: {
          //         saveAsImage: {}
          //     }
          // },
          title: {
            text: 'Gene ontology analysis',
            left: 'center'
          },

          grid: {
            left: '50%',
            show: true
          },
          tooltip: {
            backgroundColor: 'rgb(89,89,218)',
            formatter: function (param) {
              var value = param.value
              // prettier-ignore
              return 'Cluster：' + value[0] + '<br>' +
                'Description：' + value[1] + '<br>' +
                'GeneRatio：' + value[2] + '<br>' +
                'p.adjust：' + value[3] + '<br>'
            }
          },
          xAxis: {
            type: 'category',
            name: 'Cluster',
            nameLocation: 'center',
            boundaryGap: false,
            nameGap: 30,
            nameTextStyle: {
              fontSize: 13,
            },
            //max: 31,
            splitLine: {
              show: true
            }
          },
          yAxis: {
            type: 'category',
            boundaryGap: false,
            offset: 10,
            splitLine: {
              show: true
            },
          },
          visualMap: [
            {
              type: 'piecewise',
              left: 'right',
              top: '10%',
              dimension: 2,
              precision: 2,
              min: 0,
              max: 0.25,
              text: ['GeneRatio'],
              pieces: [
                {
                  gt: 0.20,
                  lte: 0.25,
                  symbol: 'circle',
                  symbolSize: 20
                },
                {
                  gt: 0.15,
                  lte: 0.20,
                  symbol: 'circle',
                  symbolSize: 17
                },
                {
                  gt: 0.10,
                  lte: 0.15,
                  symbol: 'circle',
                  symbolSize: 15
                },
                {
                  gt: 0.05,
                  lte: 0.10,
                  symbol: 'circle',
                  symbolSize: 12
                },
                {
                  gt: 0,
                  lte: 0.05,
                  symbol: 'circle',
                  symbolSize: 10,
                },
              ],
              itemWidth: 0,
              hoverLink: false,
              inRange: {
                symbol: 'circle',
                symbolSize: [5, 30],
              },
            },
            {
              left: 'right',
              bottom: '5%',
              dimension: 3,
              precision: 8,
              min: 0,
              max: max2,
              itemHeight: 120,
              text: ['p.adjust'],
              textGap: 30,
              inRange: {
                color: ['#f90020', '#ba009e', '#2809fc']
              },
            }
          ],
          series: [
            {
              type: 'scatter',
              itemStyle: itemStyle,
              data: dataBJ
            }
          ]
        }
        intoScatter()
        myChart.setOption(option)
      }

      function intoScatter () {
        const html =
          '<div class="d-flex justify-content-between"\n' +
          '                                             style="height: 100px;position: absolute;left: 760px; top: 80px;">\n' +
          '                                            <div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 10px; height: 10px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 12px; height: 12px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 15px; height: 15px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 17px; height: 17px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 20px; height: 20px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                            </div>\n' +
          '                                            <div class="ml-2">\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 10px; height: 10px;">0.05</div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 12px; height: 12px;margin-top: 30%">0.10</div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 15px; height: 15px;margin-top: 30%">0.15</div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 17px; height: 17px;margin-top: 40%">0.20</div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 20px; height: 20px;margin-top: 30%">0.25</div>\n' +
          '                                                </div>\n' +
          '                                            </div>\n'
        var box = document.getElementById("chart-16-box")
        box.innerHTML = html
      }
    </script>
</th:block>
</html>
