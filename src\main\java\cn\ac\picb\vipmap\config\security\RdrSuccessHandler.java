package cn.ac.picb.vipmap.config.security;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * VipMap登录成功处理器
 * 参考RdrSuccessHandler实现，处理登录成功后的逻辑
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
public class RdrSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

    private RdrProperties rdrProperties;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException {
        log.info("用户登录成功");

        // 获取用户详情
        RdrUserDetailsService.CustomSecurityUser userDetails = (RdrUserDetailsService.CustomSecurityUser) authentication.getPrincipal();

        // 记录登录日志（可以根据需要扩展）
        log.info("用户 {} ({}) 登录成功", userDetails.getName(), userDetails.getId());

        // 重定向到客户端页面
        // 对于前后不分离的项目，可以重定向到主页或者使用默认的成功处理逻辑
        String targetUrl = determineTargetUrl(request, response);
        if (targetUrl == null) {
            targetUrl = "/home"; // 默认重定向到主页
        }

        getRedirectStrategy().sendRedirect(request, response, targetUrl);
    }
}
