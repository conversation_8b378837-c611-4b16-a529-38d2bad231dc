package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnasmartseq.dto.ScrnaSmartseqTaskDTO;
import cn.ac.picb.scrnasmartseq.po.ScrnaSmartseqTaskPO;
import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskInput;
import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskParamVO;
import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskQueryVO;
import cn.ac.picb.vipmap.client.ScrnaSmartseqServiceClient;
import feign.Response;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaSmartseqServiceClientFallback implements ScrnaSmartseqServiceClient {

    private final static String SERVER_NAME = "scrnasmartseq-service";


    @Override
    public CommonResult<PageResult<ScrnaSmartseqTaskPO>> findTaskPage(ScrnaSmartseqTaskQueryVO query) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ScrnaSmartseqTaskPO> saveTask(ScrnaSmartseqTaskParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ScrnaSmartseqTaskPO> findById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ScrnaSmartseqTaskDTO> findDetailById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ScrnaSmartseqTaskPO> deleteById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadResult(String s, String s1) {
        return null;
    }


    @Override
    public Response downloadTemplateExcel() {
        return null;
    }

    @Override
    public CommonResult<List<ScrnaSmartseqTaskInput>> uploadDataExcel(String s, MultipartFile multipartFile) {
        return serverError(SERVER_NAME);
    }
}
