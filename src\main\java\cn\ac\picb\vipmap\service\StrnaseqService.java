package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.strnaseq.dto.StrnaseqDTO;
import cn.ac.picb.strnaseq.po.StrnaseqTaskPO;
import cn.ac.picb.strnaseq.vo.ChartParamVO;
import cn.ac.picb.strnaseq.vo.StrnaseqTaskParamVO;
import cn.ac.picb.strnaseq.vo.StrnaseqTaskQueryVO;
import cn.ac.picb.vipmap.client.StrnaseqServiceClient;
import cn.ac.picb.vipmap.mapper.StrnaseqMapper;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.StrnaseqTaskParam;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/3/2 11:05
 */
@Service
@RequiredArgsConstructor
public class StrnaseqService {

    private final StrnaseqServiceClient strnaseqServiceClient;

    public StrnaseqTaskPO save(CurrentUser user, StrnaseqTaskParam param) {
        StrnaseqTaskParamVO vo = StrnaseqMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<StrnaseqTaskPO> result = strnaseqServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public PageResult<StrnaseqTaskPO> findPage(CurrentUser user, StrnaseqTaskQueryVO queryVO, PageParam pageParam) {
        queryVO.setUserId(user.getId());
        queryVO.setPage(pageParam.getPage());
        queryVO.setSize(pageParam.getSize());
        CommonResult<PageResult<StrnaseqTaskPO>> result = strnaseqServiceClient.findTaskPage(queryVO);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<StrnaseqTaskPO> result = strnaseqServiceClient.deleteTask(id);
        result.checkError();
    }

    public StrnaseqDTO findTaskDtoById(String id) {
        CommonResult<StrnaseqDTO> result = strnaseqServiceClient.findTaskDtoById(id);
        result.checkError();
        return result.getData();
    }

    public ResponseEntity<byte[]> getImage(String taskId, String path) {
        return strnaseqServiceClient.getImage(taskId, path);
    }

    public Response downloadFile(String id, String path) {
        return strnaseqServiceClient.downloadFile(id, path, "");
    }

    public Object getChartData(ChartParamVO vo) {
        CommonResult<Object> result = strnaseqServiceClient.getChartData(vo);
        result.checkError();
        return result.getData();
    }
}
