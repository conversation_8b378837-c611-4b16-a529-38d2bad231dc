package cn.ac.picb.vipmap;

import cn.ac.picb.vipmap.repository.CasUserRepository;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@SpringBootTest
class VipmapAppApplicationTests {

    @Autowired
    private CasUserRepository casUserRepository;

    @Test
    void contextLoads() {

        ArrayList<Object> objects = new ArrayList<>();
        objects.add(123);
        objects.add(123);
        ArrayList<Object> objects2 = new ArrayList<>();
        objects2.add("12");
        objects2.add("aaa");
        objects.addAll(objects2);
        System.out.println(objects);
    }

    @Test
    void readJson() {
        String jsonStr = FileUtil.readString("C:\\Users\\<USER>\\Desktop\\aa.json", StandardCharsets.UTF_8);
        List<List<String>> data = JSON.parseObject(jsonStr, new TypeReference<List<List<String>>>() {
        });
        Map<String, CurrentUser> userMap = data.stream().map(li -> {
            CurrentUser user = new CurrentUser();
            user.setId(li.get(7).split("/")[0]);
            user.setUsername(li.get(0));
            user.setName(li.get(1));
            return user;
        }).collect(Collectors.toMap(CurrentUser::getId, Function.identity()));

        List<String> list = FileUtil.readLines("C:\\Users\\<USER>\\Desktop\\userId.txt", StandardCharsets.UTF_8);
        List<CurrentUser> collect = list.stream().map(x -> {
            return userMap.get(x);
        }).collect(Collectors.toList());
        // casUserRepository.deleteAll();
        for (CurrentUser user : collect) {
            System.out.println(user);
            if (user != null) {
                casUserRepository.saveAndFlush(user);
            }
        }
        System.out.println(collect);
    }

}
