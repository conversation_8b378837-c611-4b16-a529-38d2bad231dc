package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.rnaseq.dto.RnaseqTaskDTO;
import cn.ac.picb.rnaseq.po.RnaseqTaskPO;
import cn.ac.picb.rnaseq.vo.*;
import cn.ac.picb.vipmap.client.RnaseqServiceClient;
import cn.ac.picb.vipmap.mapper.RnaseqMapper;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.RnaseqTaskParam;
import cn.ac.picb.vipmap.vo.RnaseqTaskSearchVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RnaseqService {

    private final RnaseqServiceClient rnaseqServiceClient;

    public PageResult<RnaseqTaskPO> findPage(CurrentUser user, RnaseqTaskSearchVO search, PageParam pageParam) {
        RnaseqTaskQueryVO queryVO = RnaseqMapper.INSTANCE.convert(search);
        queryVO.setUserId(user.getId());
        queryVO.setPage(pageParam.getPage());
        queryVO.setSize(pageParam.getSize());
        CommonResult<PageResult<RnaseqTaskPO>> result = rnaseqServiceClient.findTaskPage(queryVO);
        result.checkError();
        return result.getData();
    }

    public RnaseqTaskPO createTask(CurrentUser user, RnaseqTaskParam param) {
        RnaseqTaskParamVO vo = RnaseqMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<RnaseqTaskPO> result = rnaseqServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<RnaseqTaskPO> result = rnaseqServiceClient.deleteById(id);
        result.checkError();
    }

    public Response downloadResult(String taskId, Integer step, String displayName) {
        return rnaseqServiceClient.downloadResult(taskId, step, displayName);
    }

    public RnaseqTaskDTO findTaskVO(String id) {
        CommonResult<RnaseqTaskDTO> result = rnaseqServiceClient.findDetailById(id);
        result.checkError();
        return result.getData();
    }

    public Object getChartData(ChartParamVO param) {
        CommonResult<Object> result = rnaseqServiceClient.chartData(param);
        result.checkError();
        return result.getData();
    }

    public Response downloadTemplate() {
        return rnaseqServiceClient.downloadTemplateExcel();
    }

    public List<RnaseqTaskInput> uploadTemplate(MultipartFile file, CurrentUser user) {
        final CommonResult<List<RnaseqTaskInput>> result = rnaseqServiceClient.uploadDataExcel(user.getUsername(), file);
        result.checkError();
        return result.getData();
    }

    public List<SyncToNodeFileVo> getToNodeList(String taskId, CurrentUser user) {
        CommonResult<List<SyncToNodeFileVo>> result = rnaseqServiceClient.getToNodeList(taskId, user.getUsername());
        result.checkError();
        return result.getData();
    }

    public void syncToNode(RnaseqSyncToNodeVO dto, CurrentUser user) {
        dto.setUsername(user.getUsername());
        dto.setUserId(user.getId());
        CommonResult result = rnaseqServiceClient.syncToNode(dto);
        result.checkError();
    }
}
