package cn.ac.picb.vipmap.config.security;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * RDR登录配置属性类
 * 完全参考样例代码RdrProperties创建
 *
 * <AUTHOR>
 */
@Component
@Data
public class RdrProperties {

    /**
     * rdr的登录页面
     */
    @Value("${rdr.login_page}")
    private String rdrLoginPage;

    /**
     * rdr的后端api
     */
    @Value("${rdr.api.url}")
    private String rdrApiUrl;

    /**
     * rdr获取根据token获取用户链接的url
     */
    @Value("${rdr.api.get_user_info_by_token}")
    private String rdrApiGetUserInfoByToken;

    /**
     * rdr校验token是否有效
     */
    @Value("${rdr.api.check_user_login}")
    private String rdrApiCheckUserLogin;

    /**
     * rdr根据用户名密码获取用户信息的url
     */
    @Value("${rdr.api.get_user_info_by_user_name_and_password}")
    private String rdrApiGetUserInfoByUserNameAndPassword;

    /**
     * rdr的用户信息注销的接口
     */
    @Value("${rdr.api.logout_url}")
    private String rdrApiLogoutUrl;
}
