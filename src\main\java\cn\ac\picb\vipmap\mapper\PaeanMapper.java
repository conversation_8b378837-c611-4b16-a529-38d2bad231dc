package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.paean.vo.PaeanTaskParamVO;
import cn.ac.picb.paean.vo.PaeanTaskQueryVO;
import cn.ac.picb.vipmap.vo.PaeanSearchVO;
import cn.ac.picb.vipmap.vo.PaeanTaskParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface PaeanMapper {

    PaeanMapper INSTANCE = Mappers.getMapper(PaeanMapper.class);

    PaeanTaskQueryVO convert(PaeanSearchVO searchVO);

    PaeanTaskParamVO convertToVO(PaeanTaskParam param);
}
