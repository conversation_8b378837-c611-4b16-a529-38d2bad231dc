package cn.ac.picb.vipmap.vo;

import cn.ac.picb.somatic.vo.SomaticFileVO;
import cn.ac.picb.somatic.vo.SomaticTaskInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SomaticCnvsTaskParam {

    @NotBlank
    private String taskName;

    /**
     * 输入文件类型 fastq或者bam
     */
    @NotBlank
    private String uploadtype;

    private List<SomaticTaskInput> input;

    private String qcMethod;

    private String refVersion;

    private String mappingMethod;

    @NotBlank
    private String gatkVersion;

    @NotNull
    @Size(min = 1)
    private List<String> ponNames;

    /**
     * self、NONE。
     * 不包含default
     */
    private String exonBed;

    private SomaticFileVO exonFileVo;


}

