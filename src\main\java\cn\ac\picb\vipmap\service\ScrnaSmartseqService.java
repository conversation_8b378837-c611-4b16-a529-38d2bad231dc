package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnasmartseq.dto.ScrnaSmartseqTaskDTO;
import cn.ac.picb.scrnasmartseq.po.ScrnaSmartseqTaskPO;
import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskInput;
import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskParamVO;
import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskQueryVO;
import cn.ac.picb.vipmap.client.ScrnaSmartseqServiceClient;
import cn.ac.picb.vipmap.mapper.ScrnaSmartseqMapper;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.ScrnaSmartseqTaskParam;
import cn.ac.picb.vipmap.vo.ScrnaSmartseqTaskSearchVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ScrnaSmartseqService {

    private final ScrnaSmartseqServiceClient scrnaSmartseqServiceClient;

    public PageResult<ScrnaSmartseqTaskPO> findPage(CurrentUser user, ScrnaSmartseqTaskSearchVO search, PageParam pageParam) {
        ScrnaSmartseqTaskQueryVO queryVO = ScrnaSmartseqMapper.INSTANCE.convert(search);
        queryVO.setUserId(user.getId());
        queryVO.setPage(pageParam.getPage());
        queryVO.setSize(pageParam.getSize());
        CommonResult<PageResult<ScrnaSmartseqTaskPO>> result = scrnaSmartseqServiceClient.findTaskPage(queryVO);
        result.checkError();
        return result.getData();
    }

    public ScrnaSmartseqTaskPO createTask(CurrentUser user, ScrnaSmartseqTaskParam param) {
        ScrnaSmartseqTaskParamVO vo = ScrnaSmartseqMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<ScrnaSmartseqTaskPO> result = scrnaSmartseqServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }
//    public Response downloadTemplate() {
//        return scrnasmartseqServiceClient.downloadTemplateExcel();
//    }

    public void deleteTask(String id) {
        CommonResult<ScrnaSmartseqTaskPO> result = scrnaSmartseqServiceClient.deleteById(id);
        result.checkError();
    }

    public Response downloadResult(String taskId, String displayName) {
        return scrnaSmartseqServiceClient.downloadResult(taskId, displayName);
    }

    public ScrnaSmartseqTaskDTO findTaskVO(String id) {
        CommonResult<ScrnaSmartseqTaskDTO> result = scrnaSmartseqServiceClient.findDetailById(id);
        result.checkError();
        return result.getData();
    }

    public Response downloadTemplate() {
        return scrnaSmartseqServiceClient.downloadTemplateExcel();
    }

    public List<ScrnaSmartseqTaskInput> uploadTemplate(MultipartFile file, CurrentUser user) {
        final CommonResult<List<ScrnaSmartseqTaskInput>> result = scrnaSmartseqServiceClient.uploadDataExcel(user.getUsername(), file);
        result.checkError();
        return result.getData();
    }
}
