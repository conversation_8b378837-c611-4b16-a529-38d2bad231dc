package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnaseq.dto.GenomicsTaskDTO;
import cn.ac.picb.scrnaseq.po.GenomicsTaskPO;
import cn.ac.picb.scrnaseq.vo.GenomicsTaskParamVO;
import cn.ac.picb.scrnaseq.vo.TaskQueryVO;
import cn.ac.picb.vipmap.client.ScrnaseqGenomicsServiceClient;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaseqGenomicsServiceClientFallback implements ScrnaseqGenomicsServiceClient {

    private final static String SERVER_NAME = "scrnaseq-service";

    @Override
    public CommonResult<PageResult<GenomicsTaskPO>> findGenomicsTaskPage(TaskQueryVO taskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<GenomicsTaskPO>> findcompleteGenomicsTask(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GenomicsTaskPO> saveGenomicsTask(GenomicsTaskParamVO genomicsTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GenomicsTaskPO> findGenomicsById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GenomicsTaskDTO> findGenomicsDtoById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GenomicsTaskPO> deleteGenomicsById(String s) {
        return serverError(SERVER_NAME);
    }
}
