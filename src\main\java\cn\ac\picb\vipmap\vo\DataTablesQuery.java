package cn.ac.picb.vipmap.vo;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.io.Serializable;

import static org.springframework.data.domain.Sort.Direction.DESC;

@Data
public class DataTablesQuery implements Serializable {

    /**
     * datatable 传入参数，防止重复点击是 ajax 展示数据与分页信息匹配不上
     */
    private Integer draw = 0;
    /**
     * 从第几条数据开始
     */
    private Integer start = 0;
    /**
     * 每页数量
     */
    private Integer length;
    private String orderColumn;
    private String orderDirection;

    public Pageable getPageable() {
        length = length == null || length <= 0 ? Integer.MAX_VALUE : length;
        if (StrUtil.isBlank(orderColumn)) {
            return PageRequest.of(start / length, length);
        }
        if (StrUtil.equalsIgnoreCase(orderDirection, DESC.name())) {
            return PageRequest.of(start / length, length, Sort.by(Sort.Order.desc(orderColumn)));
        }
        return PageRequest.of(start / length, length, Sort.by(Sort.Order.asc(orderColumn)));
    }
}
