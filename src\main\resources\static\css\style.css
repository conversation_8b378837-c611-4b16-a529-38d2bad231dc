:root {
    --primary: #0066cc;
    --primary-rgb: 0, 102, 204;
}

body {
    font-size: 14px;
    font-family: Arial;
    background-color: #f5f5f5;
}

ul,
li {
    list-style: none;
    padding: 0px;
    margin: 0px;
}

img {
    max-width: 100%;
}

header {
    background-color: #fff;
    box-shadow: 0 2px 2px rgba(0, 0, 0, .05);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 5;
}

header .page-user .btn {
    padding: 2px 8px;
    line-height: 20px;
}

header .page-user .btn-link {
    color: var(--gray);
}

header .navbar {
    position: relative;
    padding: 0 3rem;
}

header .navbar-nav {
    flex-grow: 1;
    margin: -1px 0;
}

header .navbar-expand-lg .navbar-nav .nav-link {
    font-size: 1rem;
    padding: 0.75rem .5rem;
    color: var(--dark);
    transition: all 0.3s;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

header .navbar-expand-lg .navbar-nav .nav-link:before {
    display: block;
    content: '';
    position: absolute;
    left: 50%;
    margin-left: -6px;
    bottom: -6px;
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent var(--primary) transparent;
    transition: all 0.4s;
}

header .navbar-expand-lg .navbar-nav .nav-link:hover,
header .navbar-expand-lg .navbar-nav .nav-item.active .nav-link {
    color: var(--primary);
    border-bottom-color: var(--primary);
}

header .navbar-expand-lg .navbar-nav .nav-link:hover:before,
header .navbar-expand-lg .navbar-nav .nav-item.active .nav-link:before {
    bottom: 0px;
}

header .navbar-expand-lg .navbar-nav .nav-item.active .nav-link {
    font-weight: bold;
}

header .menu {
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}

header .menu .navbar li {
    position: relative;
}

header .menu .navbar li .dropdown-menu {
    display: block;
    padding: 10px 10px 5px;
    box-shadow: 0 0 13px rgba(0, 0, 0, 0.1);
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    visibility: hidden;
    transition: 0.3s;
    margin-top: 20px;
}

.circle {
    border: 1px solid #000000;
    border-radius: 50%;
    margin: auto;
    background-color: #000000;
}

header .menu .navbar li .dropdown-menu:before {
    display: block;
    content: '';
    position: absolute;
    top: -14px;
    left: 50%;
    margin-left: -8px;
    width: 0;
    height: 0;
    border-width: 8px;
    border-style: solid;
    border-color: transparent transparent #fff transparent;
}

header .menu .navbar li:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    margin-top: 0;
}

header .menu .navbar li .dropdown-menu li a {
    font-size: 13px;
    white-space: nowrap;
    display: block;
    text-align: center;
    padding: 4px 6px;
    border-radius: 2px;
    color: #152a3e;
    transition: all 0.3s;
    font-weight: normal;
    text-decoration: none;
}

header .menu .navbar li .dropdown-menu li a:hover {
    color: #fff;
    background-color: var(--primary);
}

header .menu .navbar li .dropdown-menu li a.active {
    color: var(--white);
    background-color: var(--primary);
}

.logo {
    padding: 10px 0;
}

.logo-text {
    color: var(--gray);
}

.logo-text:hover {
    text-decoration: none;
}

.logo-text b {
    font-size: 32px;
    font-weight: 900;
    color: var(--primary);
    letter-spacing: 2px;
}

/* 平台切换导航样式 */
.platform-nav {
    position: relative;
}

.platform-nav .nav-pills {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 4px;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.platform-nav .nav-pills .nav-link {
    color: #666;
    background-color: transparent;
    border: none;
    border-radius: 20px;
    padding: 10px 20px;
    margin: 0 2px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    white-space: nowrap;
}

.platform-nav .nav-pills .nav-link:hover {
    color: var(--primary);
    background-color: rgba(var(--primary-rgb), 0.1);
    text-decoration: none;
    transform: translateY(-1px);
}

.platform-nav .nav-pills .nav-link.active {
    color: var(--white);
    background-color: var(--primary);
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.4);
    transform: translateY(-1px);
}

.platform-nav .nav-pills .nav-link.active:hover {
    color: var(--white);
    background-color: var(--primary);
    transform: translateY(-1px);
}

.platform-nav .nav-pills .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.platform-nav .nav-pills .nav-link:hover::before {
    opacity: 1;
}

.container-fulid {
    max-width: 1300px;
    margin: auto;
}

.header-user {
    display: flex;
}

.header-user li {
    padding: .5rem 1rem;
    position: relative;
}

.header-user li + li:before {
    display: block;
    content: '';
    position: absolute;
    width: 1px;
    height: 16px;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background-color: #ddd;
}

.header-user a {
    color: var(--gray);
    transition: 0.2s;
}

.header-user a:hover {
    text-decoration: none;
    color: var(--primary);
}

.header-user li .dropdown-menu {
    padding: 5px;
}

.header-user li .dropdown-menu a {
    font-size: .8125rem;
    padding: .25rem .5rem;
}

.page-content {
    padding: 180px 0 20px;
}

.page-content main,
.page-content nav {
    background-color: #fff;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    position: relative;
    display: block;
    min-height: 500px;
    padding: 25px 20px;
}

.page-content .side-bar {
    flex: 0 0 250px;
    padding-right: 35px;
    max-width: 250px;
}

.page-content .side-bar h5 i.fa {
    margin-right: 6px;
}


.page-content main {
    margin-left: 10px;
    padding-left: 50px;
    padding-right: 30px;
    flex: 0 0 calc(100% - 260px);
    max-width: calc(100% - 260px);
}

.page-content main:before,
.page-content main:after {
    position: absolute;
    display: block;
    content: '';
    left: -35px;
    width: 60px;
    height: 60px;
}

.page-content main:before {
    top: 50px;
    background-image: url("../images/page-axis.png");
}

.page-content main:after {
    bottom: 50px;
    background-image: url("../images/page-axis.png");
}

.side-bar a,
.side-bar li {
    position: relative;
}

.side-bar a {
    line-height: 2;
}

.side-bar a:hover {
    text-decoration: none;
}

.side-bar .level-1 {
    padding-left: 31px;
}

.side-bar .level-2 {
    padding-left: 15px;
}

.side-bar .level-1 > li {
    margin-bottom: 3px;
}

.side-bar .level-1 > li > a {
    font-size: 16px;
    color: var(--gray);
}

.side-bar .level-1 > li > a[data-toggle="collapse"].collapsed,
.side-bar .level-2 > li > a[data-toggle="collapse"].collapsed,
.side-bar .level-2 > li > a {
    color: var(--gray);
}

.side-bar .level-1 > li > a[data-toggle="collapse"].collapsed:hover,
.side-bar .level-1 > li > a[data-toggle="collapse"],
.side-bar .level-2 > li > a[data-toggle="collapse"],
.side-bar .level-1 > li > a:hover,
.side-bar .level-1 > li.active > a,
.side-bar .level-2 > li > a:hover,
.side-bar .level-2 > li.active > a {
    color: #007bff;
}

.side-bar .level-1 > li.active > a,
.side-bar .level-2 > li.active > a {
    font-weight: bold;
}

.side-bar .level-2 > li.pl-2 > a {
    line-height: 1.6;
    display: inline-block;
    padding: 3px 0;
}

.side-bar a[data-toggle="collapse"]:before {
    position: absolute;
    display: block;
    font: normal normal normal 14px/1 FontAwesome;
    content: "\f105";
    left: -15px;
    top: 1px;
    transition: all 0.2s;
    transform: rotate(90deg);
}

.side-bar a[data-toggle="collapse"].collapsed:before {
    transform: rotate(0deg);
}

.side-bar .level-1 > li.active > a:before {
    position: absolute;
    display: block;
    font: normal normal normal 14px/1 FontAwesome;
    content: "\f00c";
    top: 1px;
}

.side-bar .level-1 > li.active > a:before {
    left: -22px;
}

.side-bar .level-2 > li.active > a:before {
    left: -33px;
}

.nav-step {
    margin-bottom: 20px;
}

.nav-step li {
    position: relative;
}

.nav-step li:before {
    display: block;
    content: '';
    position: absolute;
    left: -9px;
    width: 15px;
    height: 30px;
    overflow: hidden;
    background-image: url("../images/step-arrow-right.png");
    background-position: 0 0;
    top: 50%;
    transform: translateY(-50%);
}

.nav-step li.actived:before,
.nav-step li.active:before {
    background-position-x: -15px;
}

.nav-step li:first-child:before {
    display: none;
}

.nav-step a {
    display: block;
    text-align: center;
    border: 2px solid #ddd;
    background-color: #ddd;
    color: var(--gray);
    font-size: 18px;
    padding: 6px;
    border-radius: 21px;
}

.nav-step a:hover {
    text-decoration: none;
}

.nav-step li.active a {
    background-color: #fff;
    color: var(--primary);
    font-weight: bold;
    border-color: var(--primary);
}

.nav-step li.actived a {
    color: #fff;
    background-color: var(--primary);
    font-weight: bold;
    border-color: var(--primary);
}

.btn-custom {
    display: inline-flex;
    border-radius: 19px;
    padding: 5px 5px 5px 15px;
    align-items: center;
    font-size: 16px !important;
}

.btn-custom i.fa {
    background-color: var(--primary);
    color: #fff;
    display: inline-block;
    width: 26px;
    height: 26px;
    line-height: 26px;
    border-radius: 13px;
    margin-left: 10px;
    transition: all 0.2s;
}

.btn-custom:hover i.fa {
    background-color: #fff;
    color: var(--primary);
}

.form-group-title {
    color: var(--gray);
    padding-left: 15px;
    padding-right: 15px;
}

.form-group-content {
    flex-grow: 1;
    padding-left: 15px;
    padding-right: 15px;
}

.custom-control-label {
    cursor: pointer;
    line-height: 24px;
}

.form-custom .custom-select,
.form-custom .btn,
.form-custom .form-control {
    font-size: 14px;
}

.form-custom .form-group {
    margin-bottom: 10px;
}

.form-custom .form-group label {
    margin-bottom: 0;
}

.table-middle td {
    vertical-align: middle;
}

.td-input {
    padding: 0 !important;
}

.td-input.px-15 {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.td-input .custom-select,
.td-input .form-control {
    border-color: transparent;
}

.td-input .input-group.input-group-sm {
    background: #fff;
    flex-wrap: nowrap;
}

.td-input .input-group .input-group-prepend:last-child {
    flex-grow: 1;
}

.td-input .btn-sm,
.td-input .input-group.input-group-sm .btn-sm {
    border-radius: 4px;
    font-size: .8125rem;
    padding: .25rem .2rem;
    margin-top: 2px;
    margin-bottom: 2px;
}

.td-input .input-group.input-group-sm .input-group-text {
    border: 0;
    padding: 0;
    background-color: #fff;
}

.td-input .input-group.input-group-sm .seled {
    font-style: normal;
    font-size: .75rem;
    display: flex;
    flex-wrap: wrap;
}

.td-input .input-group.input-group-sm .seled b {
    font-weight: 500;
    width: 80px;
    display: inline-block;
    vertical-align: bottom;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: left;
    position: relative;
    padding-left: 5px;
    line-height: 29px;
    padding-right: 20px;
    background: #e9f3ff;
    margin-left: 5px;
    border-radius: 4px;
    margin-top: 2px;
    margin-bottom: 2px;
}

.td-input .input-group.input-group-sm .seled b.lg {
    width: 200px;
}

.td-input .input-group.input-group-sm .seled b span {
    position: absolute;
    top: 0px;
    right: 5px;
}

.td-input .select2-container--default .select2-selection--single {
    border-color: transparent;
}

.table-center th,
.table-center td {
    text-align: center;
}

.form-group-box {
    display: block;
    border-top: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5;
    padding: 12px 0;
    margin-bottom: 12px;
}

.form-group-box + .form-group-box {
    border-top: none;
}

.form-group-box a[data-toggle="collapse"] {
    position: relative;
    padding-left: 22px;
}

.form-group-box a[data-toggle="collapse"]:hover {
    text-decoration: none;
}

.form-group-box a[data-toggle="collapse"]:before {
    position: absolute;
    display: block;
    content: "\f068";
    font: normal normal normal 14px/1 FontAwesome;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

.form-group-box a[data-toggle="collapse"].collapsed:before {
    content: "\f067";
}

.form-group-box .group-inner {
    width: 300px;
    display: inline-flex;
}

.form-group-box .group-inner .input-group-append {
    margin: 0;
}

.form-group-box .group-inner .input-group > .form-control:not(:last-child),
.form-group-box .group-inner .input-group > .form-control:not(:first-child),
.tool-content .form-group .select2-container--default .select2-selection--single {
    border-radius: .25rem;
}

.form-group-box .group-inner .input-group-append .input-group-text {
    background: transparent;
    border: none;
}

.form-group-box .form-group .RunName {
    border: transparent;
    display: inline-flex;
}

.form-group-box .basic-name .input-name {
    margin-left: 5px;
    width: 200px;
    display: inline-flex;
}

.tool-content .form-group select.form-control-sel {
    height: 35px;
    font-size: 14px;
}

.result-box h5 {
    border-bottom: 1px solid #ddd;
    color: var(--dark);
}

.result-box h5 span {
    display: inline-block;
    border-bottom: 2px solid var(--dark);
    padding-bottom: 6px;
    margin-bottom: -1px;
}

.tool-box {
    display: block;
    margin-bottom: 15px;
}

.tool-box .tool-title {
    position: relative;
    font-size: 20px;
    padding: 5px 15px;
    background-color: #eee;
    border-radius: 4px;
    color: var(--primary);
    overflow: hidden;
}

.tool-box .tool-title:after {
    display: block;
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--primary);
    left: 0;
    top: 50%;
    transform: translate(-50%, -50%);
}

.tool-box .tool-content {
    padding: 15px;
}

.table .select2-container .select2-selection--single {
    border-radius: 0;
    height: 36px;
}

.table .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
}

.table tbody + tbody {
    border-top: none;
}

.r-list li {
    color: var(--gray);
    line-height: 1.8;
}

.uc-sidebar {
    padding: 0;
}

.user-photo {
    display: block;
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: 5px solid #6c757d;
    padding: 5px;
    background-color: #eee;
    margin-bottom: 1.5rem;
    margin-top: 1.25rem;
}

.user-photo .img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    overflow: hidden;
}

.user-photo .img img {
    width: 100%;
    height: 100%;
}

.user-photo .upload-img {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #343a40;
    color: #fff;
    text-align: center;
    line-height: 40px;
    right: 8px;
    bottom: 8px;
}

.user-photo .upload-img input {
    border: 0;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    cursor: pointer;
}

.user-nav li {
    margin-bottom: .5rem;
    border-bottom: 1px dashed #ddd;
}

.user-nav a {
    display: block;
    color: #6c757d;
    padding: 0.25rem 0 0.5rem;
    position: relative;
    font-size: 16px;
}

.user-nav a:hover {
    text-decoration: none;
}

.user-nav a:before {
    font: normal normal normal 14px/1 FontAwesome;
    display: block;
    content: '\f178';
    position: absolute;
    right: 20px;
    top: 12px;
    opacity: 0;
    transition: all 0.3s;
}

.user-nav li a:hover:before,
.user-nav li.active a:before {
    right: 0;
    opacity: 1;
}

.user-nav li.active a {
    color: #007bff;
    font-weight: bold;
}

.user-nav li > ul {
    padding-left: 10px;
    padding-bottom: 10px;
}

.user-nav li > ul > li {
    border: none;
    margin: 0;
}

.user-nav li > ul > li > a {
    font-size: 14px;
    padding: 0.25rem 0;
}

.user-nav li > ul > li > a:before {
    font: normal normal normal 12px/1 FontAwesome;
    display: block;
    content: '\f105';
    position: absolute;
    left: -20px;
    top: 8px;
    opacity: 0;
    transition: all 0.3s;
}

.user-nav li > ul > li > a:hover {
    color: #007bff;
}

.user-nav li > ul > li > a:hover:before {
    color: #007bff;
    left: -10px;
}

.uc-content {
    flex-grow: 1;
    padding: 0 0 0 2rem;
    font-size: 16px;
}

.card-custom {
    border-radius: 10px;
    border-width: 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.bg-gray td {
    background-color: #eceff1;
    color: var(--gray)
}

.as {
    background-color: #eceff1;
    border-radius: 4px;
    padding: 5px 8px;
    font-size: 14px;
    font-weight: bold;
    color: var(--gray);
}

.as.as-running {
    background-color: #d3e8ff;
    color: var(--primary);
}

.ap-info {
    line-height: 1.2;
}

.ap-info small {
    display: block;
}

.user-analysis-box {
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    font-size: 14px;
    margin-bottom: 15px;
}

.user-analysis-box .ua-header {
    display: flex;
    justify-content: space-between;
    padding: 8px 10px;
    background-color: #f5f5f5;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.user-analysis-box .ua-title {
    font-size: 13px;
    color: var(--gray);
}

.user-analysis-box .ua-title span {
    color: var(--gray);
}

.user-analysis-box .ua-title span.ua-datetime {
    margin-right: 20px;
}

.user-analysis-box .ua-title span.ua-num {
    color: var(--dark);
}

.user-analysis-box .ua-option a {
    color: var(--gray);
    transition: all 0.2s;
}

.user-analysis-box .ua-option a:hover {
    text-decoration: none;
    color: var(--primary);
}

.user-analysis-box .ua-content {
    display: flex;
    padding: 5px;
    color: var(--gray);
}

.user-analysis-box-title {
    display: flex;
    color: var(--gray);
    background-color: #eee;
    font-size: 13px;
    margin-bottom: 10px;
    border-radius: 6px;
    padding: 5px;
}

.uc-content .run-id,
.uc-content .pipeline,
.uc-content .create-time,
.uc-content .status,
.uc-content .summary {
    padding: 5px 10px;
}

.uc-content .run-id {
    flex: 0 0 110px;
    color: var(--dark);
}

.uc-content .pipeline {
    flex: 0 0 160px;
}

.uc-content .create-time {
    flex: 0 0 95px;
}

.uc-content .status {
    flex: 0 0 90px;
}

.uc-content .summary {
}

.uc-content .create-time,
.uc-content .status {
    text-align: center;
}

.uc-content .card-header-tabs-basic,
.ana-content .card-header-tabs-basic {
    padding-top: 0;
    padding-bottom: 0;
    display: flex;
}

.ana-content .card-header-tabs-basic {
    padding: .25rem 1rem 0;
    justify-content: space-between;
}

.ana-content .card-header-tabs-basic .nav-tabs-elem {
    padding: 4px 0 8px;
    margin-bottom: -1px;
}

.ana-content .card-header-tabs-basic .nav-tabs-elem ~ div > a {
    color: #fff;
    font-size: 0.8125rem;
    font-weight: 500;
    padding: .25rem .5rem;
}

.ana-content .card-header-tabs-basic .nav-tabs-elem ~ div > a.text-primary:hover {
    color: #fff !important;
}

.uc-content .card-header-tabs-basic a,
.ana-content .card-header-tabs-basic a {
    padding: 10px 10px 9px;
    color: #7c8a9d;
    font-weight: 600;
    font-size: 15px;
    text-decoration: none;
}

.uc-content .card-header-tabs-basic .active,
.ana-content .card-header-tabs-basic .active {
    color: #364c67;
    border-bottom: 2px solid #6774df;
}

.uc-content .list-group .tab-pane .card-header,
.ana-content .list-group .tab-pane .card-header {
    background: transparent;
    padding: 10px 20px;
}

.ana-content {
    margin-bottom: 1rem;
    position: relative;
}

.ana-content .tab-content .tab-pane {
    position: relative;
}

.ana-content .tab-content .tab-pane .action-new {
    right: 5px;
    top: 5px;
    position: absolute;
    z-index: 1;
}

.uc-content .list-group .tab-pane .card-header .card-title {
    font-size: 14px;
    margin: 0;
    text-decoration: none;
}

.uc-content .list-group .tab-pane .card-header a[data-toggle=collapse] {
    display: block;
}

.uc-content .list-group .tab-pane .card-header a[data-toggle=collapse].collapsed:before {
    content: "\f107"
}

.uc-content .list-group .tab-pane .card-header a[data-toggle=collapse]:before {
    content: "\f106";
    display: block;
    float: right;
    font-family: 'FontAwesome';
    font-size: 20px;
    line-height: 1;
    text-align: right;
}

.ua-content .pipeline,
.ua-content .create-time,
.ua-content .status,
.ua-content .summary {
    border-left: 1px dashed #ddd;
}

.analysis-box {
    border-radius: 10px;
    border: 1px solid #dee2e6;
    margin-bottom: 15px;
    overflow: hidden;
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--gray);
    padding: 8px 15px;
    background-color: #f5f5f5;
}

.analysis-content {
    padding: 10px 15px;
}

.status-box {
    display: block;
    text-align: center;
    position: relative;
    font-size: 14px;
    margin-top: .5rem;
}

.status-box .status-icon {
    font-size: 28px;
    width: 60px;
    line-height: 52px;
    text-align: center;
    border-radius: 50%;
    position: relative;
    background-color: #fff;
    z-index: 2;
    border: 4px solid #fff;
    display: inline-block;
}

.status-box .status-icon .icon {
    fill: var(--gray);
    max-width: 30px;
    max-height: 30px;
}

.status-box p {
    line-height: 1.2;
    margin-top: 10px;
}

.status-box p span {
    display: block;
    margin-bottom: 6px;
    color: var(--dark)
}

.status-box p small {
    color: var(--gray);
}

.status-box:before,
.status-box:after {
    display: block;
    content: '';
    position: absolute;
    top: 28px;
    width: 50%;
    height: 4px;
    background-color: #ddd;
}

.status-box:before {
    left: 0;
}

.status-box:after {
    right: 0;
}

.col:first-child .status-box:before,
.col:last-child .status-box:after {
    display: none;
}

.status-box.actived .status-icon .icon {
    fill: var(--success);
}

.status-box.active .status-icon {
    background-color: var(--success);
}

.status-box.active .status-icon .icon {
    fill: #fff;
}

.status-box.actived:before,
.status-box.actived:after {
    background-color: var(--success);
}

.status-box.active:before {
    background-color: var(--success);
}

.login-box,
.reg-box {
    width: 420px;
    max-width: 100%;
    padding: 1.5rem 3rem;
    border: 0;
    border-radius: 10px;
    margin: 0 auto;
}

.reg-box {
    width: 800px;
}

.account-input-box {
    position: relative;
}

.account-input-box .verification-img {
    position: absolute;
    width: 80px;
    height: 40px;
    top: 50%;
    margin-top: -19px;
    right: 10px;
}

.account-input-box .verification-img img {
    width: 100%;
    height: 100%;
}

.account-input-box > i.fa {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1.125rem;
    width: 22px;
    text-align: center;
    display: inline-block;
}

.account-input-box .form-control {
    padding-right: 2rem;
    background: none;
    padding-top: .6rem;
    padding-bottom: .6rem;
    height: auto;
}

.account-input-box span {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 10px;
    background-color: #fff;
    padding-left: 5px;
    padding-right: 5px;
    color: #999;
    transition: all 0.3s;
}

.account-input-box.active span,
.account-input-box.focus span {
    top: 0;
}

.account-input-box.focus span {
    color: #007bff;
}

.account-input-box .form-control:focus {
    outline: none;
    box-shadow: none;
}

.form-custom-1 .col-form-label {
    color: #6c757d;
}

.form-custom-1 .form-control {
    border-width: 2px;
    border-color: #e6eaef;
    font-size: 1rem;
}

.form-custom-1 select.form-control {
    padding-left: 6px;
    padding-right: 6px;
}

.form-custom-1 .input-group-text {
    border: 2px solid #e6eaef;
}

.form-custom-1 .range .input-group-text {
    border-left: 0;
    border-right: 0;
    background: none;
    padding-left: 2px;
    padding-right: 2px;
    color: #bfcdde;
}

.form-custom-1 .range .form-control {
    text-align: center;
    padding-left: 5px;
    padding-right: 5px;
}

.form-custom-1 .range .form-control:first-child {
    border-right: 0;
}

.form-custom-1 .range .form-control:last-child {
    border-left: 0;
}

.btn-reg {
    border-radius: 50%;
    width: 100px;
    height: 100px;
    text-align: center;
    line-height: 80px;
    padding: 10px;
    font-size: 1.5rem;
}

.select2-container--default .select2-selection--single {
    min-height: 32px;
    height: 100%;
    /* border-radius: .25rem 0 0 .25rem; */
}

.select2-dropdown,
.select2-container--default .select2-search--dropdown .select2-search__field,
.select2-container--default .select2-selection--single {
    border-color: #ced4da;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 33px;
    color: var(--dark);
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 50%;
    transform: translateY(-50%);
}

.num-box {
    display: flex;
    border-radius: 3px;
    border: 1px solid #e5e5e5;
    align-items: center;
    margin-right: 10px;
}

.num-box span {
    background-color: #eee;
    padding: 7px 12px;
}

.num-box b {
    font-size: 16px;
    padding: 5px 15px;
    color: #007bff;
}

.font-12 {
    font-size: .75rem !important;
}

.font-13 {
    font-size: .8125rem !important;
}

.font-14 {
    font-size: .875rem !important;
}

.font-16 {
    font-size: 1rem;
    font-size: 1rem;
}

.width-80 {
    width: 80px !important;
}

.width-90 {
    width: 90px !important;
}

.width-100 {
    width: 100px !important;
}

.width-120 {
    width: 120px !important;
}

.width-150 {
    width: 150px !important;
}

.width-250 {
    width: 250px !important;
}

.width-300 {
    width: 300px !important;
}

.width-400 {
    max-width: 400px !important;
    width: auto !important;
}

.wd-73 {
    width: 73px !important;
}

.input-filetext {
    font-size: 13px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 250px;
}

.form-task {
    margin-bottom: .5rem;
    position: relative;
}

.form-task .input-daterange .form-control.form-control-sm {
    font-size: 12px;
    height: calc(1.5em + .5rem + 5px);
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .5rem;
    width: 100px;
}

.form-task .input-group-append {
    margin-left: -1px;
    margin-right: -1px;
}

.form-task .input-daterange .input-group-text {
    padding: .1rem .3rem;
}

.form-task .btn-primary {
    margin-left: 1rem;
}

.form-task .select2-container--default .select2-selection--single {
    min-height: 30px;
    border-radius: .25rem;
}

.form-task .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 29px;
}

.datepicker {
    z-index: 9999;
}

.datepicker > div {
    font-size: .725rem;
}

.table-nowrap th,
.table-nowrap td {
    white-space: nowrap;
}

.table.table-nowrap td,
.table.table-nowrap th {
    vertical-align: middle;
}

.table-nowrap .action a {
    margin: 0 3px;
}

.tooltip-inner {
    font-size: 12px;
}

.alert.alert-wth-icon {
    padding-left: 65px;
    position: relative;
}

.table .btn-group > a {
    margin: 0 .25rem;
}

.table .btn-group .dropdown-menu {
    font-size: .8125rem;
}

.bootstrap-tagsinput {
    line-height: 30px;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    box-shadow: none;
    width: 100%;
}

.bootstrap-tagsinput .tag {
    padding: 6px;
    border-radius: 2px;
}

.bootstrap-tagsinput .label {
    border-radius: 4px;
    font-size: .8125rem;
    padding: 4px 7px;
    margin-right: 5px;
    font-weight: 400;
    color: #fff !important;
}

.bootstrap-tagsinput .label-info {
    background-color: #007bff;
}

table.treetable {
    border-color: #dee2e6;
}

table.treetable thead tr th,
table.treetable tbody tr td {
    padding: .5rem;
    vertical-align: middle;
}

table.treetable .form-check-input {
    margin-top: 0;
}

.table.treetable tbody tr td {
    word-wrap: break-word;
    word-break: break-all;
}

.info-box {
    background: #fbfcff;
    font-size: 13px;
    padding: 10px 15px;
    border: 1px solid #f3f3f3;
    border-radius: 3px;
    margin-bottom: 15px;
}

#tree {
    min-height: 300px;
    max-height: 500px;
    overflow: auto;
}

#tree .dropdown-menu {
    min-width: 120px;
}

#tree .dropdown-menu .dropdown-item {
    font-size: 13px;
}

#tree span.fancytree-node {
    min-height: 26px;
}

.upload-sd {
    right: 5px;
    bottom: 60px;
    position: fixed;
}

.upload-sd .my-sd {
    background: url(../images/pic-sd.png) no-repeat;
    background-size: cover;
    display: block;
    width: 98px;
    height: 116px;
    text-indent: -9999em;
}

.alert-with-icon {
    padding-left: 60px;
    position: relative;
}

.alert-with-icon .alert-icon-wrap {
    display: block;
    text-align: center;
    padding-top: 12px;
    height: 100%;
    width: 65px;
    left: 0;
    top: 0;
    position: absolute;
}

.type-icon01 {
    background: url(../images/type-01.png) no-repeat;
}

.type-icon02 {
    background: url(../images/type-02.png) no-repeat;
}

.type-icon03 {
    background: url(../images/type-03.png) no-repeat;
}

.type-icon04 {
    background: url(../images/type-04.png) no-repeat;
}

.type-icon01,
.type-icon02,
.type-icon03,
.type-icon04 {
    background-size: cover;
}

.desc {
    display: none;
}

.tag-title {
    display: inline-block;
    width: 95px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tag-item {
    display: inline-block;
    line-height: 24px;
    padding: 0 60px 0 5px;
    border: 1px solid #ddd;
    position: relative;
    width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 5px;
    border-radius: 4px;
    font-size: 13px;
}

.tag-item em {
    position: absolute;
    background-color: #ddd;
    right: 0;
    top: 0;
    font-size: 12px;
    padding: 0 5px;
    font-style: normal;
}

.tag-coll {
    color: var(--secondary);
    font-size: 1rem;
}

.detail-info li {
    display: flex;
    margin-bottom: .5rem;
}

.detail-info li span {
    display: block;
    flex: 0 0 135px;
    max-width: 135px;
    line-height: 22px;
}

.detail-info li span.sm {
    display: block;
    flex: 0 0 90px;
    max-width: 90px;
}

.detail-info li span.auto {
    display: block;
    flex: none;
    max-width: none;
}

.detail-info li div {
    flex-grow: 1;
    line-height: 22px;
}

.tabs-ana .nav.nav-pills {
    margin: 15px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #e5e5e5;
}

.text-intro {
    background: #f5f9fd;
    border-left: 5px solid #90bce9;
    border-radius: .25rem;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.text-intro h5 {
    font-size: 1.125rem;
    line-height: 2;
    margin-bottom: 0;
}

.glyph-icon:before {
    font-size: 1.25rem;
    color: var(--primary);
    margin-left: 0;
}

.contact-info {
    padding: 0 0 0 1rem;
}

.contact-info li {
    display: flex;
}

.contact-info li div + div {
    padding-left: 1rem;
}

.contact-info h5 {
    color: #364459;
    font-size: 1.125rem;
    margin: 0.3125rem 0;
}

.contact-info p {
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 0.3125rem;
}

.card-guide {
    border-color: transparent;
    background-color: #F5F9FD;
}

.header-login {
    position: relative;
}

.header-login .btn-light {
    background-color: #f5f5f5;
    border-color: #f1f1f1;
}

.login-card {
    position: absolute;
    right: 0;
    top: 100%;
    box-shadow: 0 3px 10px rgba(0, 0, 0, .1);
    width: 240px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 15px;
    opacity: 0;
    transition: all .3s;
    visibility: hidden;
    z-index: 99;
}

.header-login:hover .login-card {
    opacity: 1;
    visibility: visible;
    margin-top: 10px;
}

.login-card .login-info {
    display: flex;
    align-items: center;
    padding: 20px 15px;
    background-color: #e9eef5;
}

header .login-card .login-info .account {
    background-color: #fff;
}

header .login-card .login-info h5 {
    margin: 0;
    font-size: 15px;
    color: var(--dark);
}

.login-card .login-content {
    padding: 15px;
}

.lc-box {
    display: block;
    padding: 6px 10px;
    color: var(--dark);
    transition: all .3s;
    position: relative;
    margin-bottom: 5px;
    background-color: #f5f5f5;
    border-radius: 6px;
}

.lc-box:before {
    display: block;
    font: normal normal normal 14px/1 FontAwesome;
    content: "\f0c1";
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    color: var(--primary);
    position: absolute;
    transition: all 0.3s;
    opacity: 0;
}

.lc-box:hover {
    color: var(--primary);
    text-decoration: none;
    background-color: #e9f3ff;
}

.lc-box:hover:before {
    opacity: 1;
}

.lc-box i.fa {
    opacity: 0.6;
    display: inline-block;
    width: 16px;
}

.label-groups-box {
    background: #f9f9f9;
    border-radius: .25rem;
    padding: .25rem;
}

.label-groups-box + .label-groups-box {
    margin-top: .25rem;
}


/*scrna修改时加入的样式 start*/

.dataTables_info {
    display: inline-block;
}

.dataTables_length {
    display: inline-block;
    margin-left: 70px;
}


.dataTables_length > label > select {
    width: 60px !important;
}

.dataTables_paginate {
    display: inline-block;
    float: right;
}

.text-center {
    margin-bottom: 10px;
}

.ml {
    margin-left: 20px;
}

.form-group-box .form-group .RunName {
    border: transparent;
    display: inline-flex;
}

.basic-name span {
    margin-right: 10px;
}

.input-name {
    margin-left: 5px;
}

.center {
    left: 32%;
}

.min-width {
    min-width: 914px;
}

.h-31 {
    height: 31px;
}

.max-width-97 {
    max-width: 97px;
}

.width-345 {
    width: 345px;
}

.ml--16 {
    margin-left: -16px;
}

.ml--10 {
    margin-left: -10px;
}

.ml--8 {
    margin-left: -8px;
}

.min-width-400 {
    min-width: 400px;
}

.white-space {
    white-space: nowrap;
}

.width-160 {
    width: 160px;
}

#tree-Analysis {
    min-height: 230px;
    max-height: 500px;
    overflow: auto;
}

.ptb-2 {
    padding: 0 20px;
}

.hide {
    overflow: hidden;
    display: none;
}

.fw {
    font-weight: bold;
}

.not-bb {
    border-bottom: transparent;
}

#tree-BA, #tree-CD, #tree-PT, #tree-DE, #tree-GD {
    margin-left: -30px;
    width: 210px;
}

#tree-Analysis, #tree-tr {
    margin-left: -15px;
    width: 350px;
}

.font-10 {
    font-size: 10px;
}

#tree-all .fancytree-title, #tree-report .fancytree-title {
    margin-left: -18px;
    min-height: 0;
}

#tree-Analysis ul.fancytree-container ul,
#tree-tr ul.fancytree-container ul,
#tree-BA ul.fancytree-container ul,
#tree-CD ul.fancytree-container ul,
#tree-PT ul.fancytree-container ul,
#tree-DE ul.fancytree-container ul,
#tree-GD ul.fancytree-container ul {
    margin-left: 24px;
}

table.treetable thead tr th {
    font-weight: 700;
    text-align: center;
}

#tree-BA span.fancytree-icon,
#tree-CD span.fancytree-icon,
#tree-PT span.fancytree-icon,
#tree-GD span.fancytree-icon,
#tree-DE span.fancytree-icon,
#tree-all span.fancytree-icon,
#tree-report span.fancytree-icon {
    background-position: 16px 0;
}

.pl-3 {
    padding-left: .25rem;
}

.width-145 {
    width: 145px;
}

.ml--50 {
    margin-left: -50px;
}

.ml-123 {
    margin-left: 123px;
}

.ml--25 {
    margin-left: -25px;
}

.ml--39 {
    margin-left: -39px;
}

.width-135 {
    width: 135px;
}

.ml--105 {
    margin-left: -105px;
}

.br {
    border-radius: 5px;
}

.float-f {
    float: left;
}

.treeTitle {
    width: 1058px;
    margin-left: 8px;
    margin-right: 0;
}

.treeTitle .col-md-3,
.treeTitle .col-xl-3,
.treeTitle .col-lg-3 {
    flex: 0 0 28%;
    max-width: 28%;
}

.treeTitle .col-md-2,
.treeTitle .col-xl-2,
.treeTitle .col-lg-2 {
    flex: 0 0 12.666667%;
    max-width: 12.666667%;
}

.treeTitle .col-md-1,
.treeTitle .col-xl-1,
.treeTitle .col-lg-1 {
    flex: 0 0 9.333333%;
    max-width: 9.333333%;
}

.mL {
    margin-left: 1.5rem !important;
}

.mtb--17 {
    margin-top: -17px;
    margin-bottom: -17px;
}

.ml--18 {
    margin-left: -18px;
}

.chart-box {
    padding: 10px 15px;
    background-color: #fafafa;
    border: 1px solid #f1f1f1;
    border-radius: 4px;
    margin-bottom: 15px;
}

.chart-td {
    position: relative;
}

.chart-download {
    position: absolute;
    right: 0;
    top: 55%;
    transform: translateY(-50%);
}

.chart-title {
    font-size: 18px;
}

.nav-group {
    margin-left: 10px;
}

.nav-select {
    line-height: 20px;
    background-color: #ddd;
    border-radius: 4px;
    padding: .5rem 1rem;
}

.nav-select:hover {
    cursor: pointer;
    background-color: #007BFF;
    color: #fff !important;
}

.nav-select.active {
    background-color: #007BFF;
    color: #fff !important;
}

.p1 {
    position: relative;
    left: -200px;
    top: 25px;
}

.p2 {
    position: relative;
    top: 25px;
    left: 40px;
}

.p3 {
    position: relative;
    top: 25px;
    left: 290px;
}

.loading-overlay {
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.layui-layer-content {
    word-break: break-word !important;
}

/*.fancytree-status-merged {*/
/*    text-align: center !important;*/
/*}*/

/*end*/



