package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.scrnaseq.dto.WgcnaTaskDTO;
import cn.ac.picb.scrnaseq.po.WgcnaTaskPO;
import cn.ac.picb.scrnaseq.vo.WgcnaTaskParamVo;
import cn.ac.picb.vipmap.client.ScrnaseqWgcnaServiceClient;
import org.springframework.stereotype.Component;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaseqWgcnaServiceClientFallback implements ScrnaseqWgcnaServiceClient {

    private final static String SERVER_NAME = "scrnaseq-service";


    @Override
    public CommonResult<WgcnaTaskPO> saveWgcnaTask(WgcnaTaskParamVo vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<WgcnaTaskDTO> findWgcnaById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<WgcnaTaskPO> findWgcnaTaskById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<WgcnaTaskPO> deleteWgcnaTaskById(String id) {
        return serverError(SERVER_NAME);
    }
}
