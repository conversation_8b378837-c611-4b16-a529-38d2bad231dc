<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-deg-introduction')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Introduction</h4>

                <div class="tool-box">
                    <div class="tool-title">Summary</div>
                    <div class="tool-content">
                        <p class="text-muted">RNA-seq，also known as transcriptomic sequencing, mainly uses
                            high-throughput sequencing to reveal the expression level of RNA in a biosample, and further
                            analyze the transcriptional change.</p>
                        <p class="text-muted">The pipeline can automatically analyze RNA-seq sequencing data and
                            produces a series of visual figures and tables of results. In this pipeline, both human and
                            mouse species are supported. The pipeline consists of five analysis steps. Firstly,
                            Trimmomatic or NGS QC Toolkit software was used for quality control. For reads after quality
                            control, STAR or HISAT2 software is used for mapping. Then, a gene expression matrix can be
                            obtained by using featureCounts, HTSeq or RSEM software. According to the group information
                            of samples, DESeq2 is used to obtain the differential genes between groups. Finally, for the
                            differential genes identified by DESeq2, the pipeline will perform GO and KEGG functional
                            enrichment analysis by using clusterProfiler.</p>
                        <p class="text-muted">The schematic diagram of RNA-seq pipeline is as follows:</p>
                        <p class="text-center">
                            <img th:src="@{/images/analysis.png}" alt="">
                        </p>
                        <h6>Software and references:</h6>
                        <ul>
                            <li><b>Trimmomatic: </b>Trimmomatic: a flexible trimmer for Illumina sequence data.</li>
                            <li><b>NGS QC Toolkit: </b>NGS QC Toolkit: A Toolkit for Quality Control of Next Generation
                                Sequencing Data.
                            </li>
                            <li><b>STAR: </b>STAR: ultrafast universal RNA-seq aligner.</li>
                            <li><b>HISAT2: </b>Graph-based genome alignment and genotyping with HISAT2 and
                                HISAT-genotype.
                            </li>
                            <li><b>featureCounts: </b>featureCounts: an efficient general purpose program for assigning
                                sequence reads to genomic features.
                            </li>
                            <li><b>HTSeq: </b>HTSeq--a Python framework to work with high-throughput sequencing data.
                            </li>
                            <li><b>RSEM: </b>RSEM: accurate transcript quantification from RNA-Seq data with or without
                                a reference genome.
                            </li>
                            <li><b>DESeq2: </b>Moderated estimation of fold change and dispersion for RNA-Seq data with
                                DESeq2.
                            </li>
                            <li><b>clusterProfiler: </b>clusterProfiler: an R package for comparing biological themes
                                among gene clusters.
                            </li>
                        </ul>
                    </div>

                </div>
                <div class="text-center">
                    <a th:href="@{/analysis/rnaseq/form}" class="btn btn-outline-primary btn-custom"><span>Next</span><i
                            class="fa fa-long-arrow-right"></i></a>
                </div>

            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
</th:block>
</html>
