<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}" th:with="task=${vo.task.task}, detailVO = ${vo.detailVO}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('dnaseq-somatic-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">WES/WGS-somatic</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <!--/*
                    <div style="display: none;" th:text="${task?.errMsg}"></div>
                    */-->
                </div>
                <th:block th:switch="${task.status}">
                    <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis Error</p>
                    </div>

                    <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Prepared</p>
                    </div>
                    <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="7">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis done</p>
                    </div>

                    <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Data prepared</p>
                    </div>
                </th:block>

                <ul class="detail-info row mb-0">
                    <li class="col-xl-3 col-lg-3"><span class="text-muted sm">Task ID </span>
                        <div class="text-primary" th:text="${task.batch}">20200722001</div>
                    </li>
                    <li class="col-xl-9 col-lg-9"><span class="text-muted sm">Task Name </span>
                        <div class="text-primary" th:text="${task.taskName}">20200722001</div>
                    </li>
                    <li class="col-xl-3 col-lg-3"><span class="text-muted sm">Run ID </span>
                        <div class="text-primary" th:text="${task.taskId}">20200722001</div>
                    </li>
                    <li class="col-xl-9 col-lg-9"><span class="text-muted sm">Run Name </span>
                        <div class="text-primary" th:text="${task.runName}">20200722001</div>
                    </li>
                    <li class="col-xl-3 col-lg-3"><span class="text-muted sm">Start Time</span>
                        <div class="text-primary font-13" th:text="${#dates.format(task.createTime,'yyyy-MM-dd HH:mm:ss')}">2020-11-17 16:35:25</div>
                    </li>
                    <li class="col-xl-9 col-lg-9"><span class="text-muted sm">Consuming</span>
                        <div class="text-primary font-13" th:text="${task.useTime}">17小时3分</div>
                    </li>
                </ul>

                <!--/*
                <ul class="detail-info row mb-2">
                    <li class="col-xl-4 col-lg-6"><span class="text-muted sm">TaskNo：</span>
                        <div th:text="${task.taskId}">AA20200722001</div>
                    </li>
                    <li class="col-xl-4 col-lg-6"><span class="text-muted sm">Batch：</span>
                        <div th:text="${task.batch}">AA20200722001</div>
                    </li>
                    <li class="col-xl-4 col-lg-6"><span class="text-muted sm">StartTime：</span>
                        <div th:text="${#dates.format(task.createTime,'yyyy-MM-dd HH:mm:ss')}">2020-07-22</div>
                    </li>
                    <th:block th:if="${task.status== 7}">
                        <li class="col-xl-4 col-lg-6"><span class="text-muted sm">Consuming：</span>
                            <div th:text="${task.useTime}">42分</div>
                        </li>
                    </th:block>
                </ul>
                */-->

                <div class="form-group-box">
                    <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                    <div class="collapse show" id="coll-1">
                        <div class="pl-4 pt-2">
                            <div class="result-box">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th>RunName</th>
                                            <th>R1(tumor)</th>
                                            <th>R2(tumor)</th>
                                            <th>R1(normal)</th>
                                            <th>R2(normal)</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td th:text="${task.runName}">2151050411</td>
                                            <td th:text="${vo.task.FFastqTumor == null ? '-' : vo.task.FFastqTumor.path}">-</td>
                                            <td th:text="${vo.task.RFastqTumor == null ? '-' : vo.task.RFastqTumor.path}">-</td>
                                            <td th:text="${vo.task.FFastqNormal == null ? '-' : vo.task.FFastqNormal.path}">-</td>
                                            <td th:text="${vo.task.RFastqNormal == null ? '-' : vo.task.RFastqNormal.path}">-</td>
                                        </tr>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Quality Control</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary">Trimmomatic</span></div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary">Homo sapiens</span></div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Version</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary">hg38(GRCh38)</span></div>
                                </div>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${#strings.isEmpty(task.mappingMethod) ? '-' : 'BWA'}">BWA</span>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Somatic Mutation Calling</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary" th:text="${#strings.isEmpty(task.varianceMethod) ? '-' : 'GATK4'}">GATK4</span></div>
                                </div>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Panel of Normal(PON)</label>
                                    <th:block th:if="${task.pon == 'self'}">
                                        <div class="col-xl-9 col-lg-9 col-md-8">
                                            <span class="text-primary" style="word-wrap: break-word;word-break: normal;" th:text="${task.ponList}">Public Panel</span>
                                        </div>
                                    </th:block>
                                    <th:block th:unless="${task.pon == 'self'}">
                                        <div class="col-xl-9 col-lg-9 col-md-8">
                                            <span class="text-primary" th:text="${task.pon == 'default' ? 'Public Panel' : 'None'}">Public Panel</span>
                                        </div>
                                    </th:block>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Statistics</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Regions file</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${task.exonBed == 'default'}">Protein Coding Regions(CCDS)</span>
                                        <span class="text-primary" th:if="${task.exonBed == 'NONE'}">WGS</span>
                                        <span class="text-primary" th:if="${task.exonBed == 'self'}" th:text="${task.exonFileVo.name}"></span>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Annotation</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary" th:if="${task.annoMethod == 'ANNOVAR'}">ANNOVAR</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group-box" th:if="${task.status == 7}">
                    <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                    <div class="collapse show" id="coll-2">
                        <div class="pl-4 pt-2">
                            <div class="result-box">
                                <h6 class="border-bottom pb-2 m-0 mb-2">Quality Control</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th width="300">FileName</th>
                                            <th>Total_reads</th>
                                            <th>Total_bases</th>
                                            <th>Sequence_length</th>
                                            <th>GC_pct</th>
                                            <th>Q20_pct</th>
                                            <th>Q30_pct</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="row : ${detailVO.qcSummaryVos}">
                                            <td th:text="${row.fileName}"></td>
                                            <td th:text="${row.totalReads}"></td>
                                            <td th:text="${row.totalBases}"></td>
                                            <td th:text="${row.sequenceLength}"></td>
                                            <td th:text="${row.gcPct}"></td>
                                            <td th:text="${row.q20Pct}"></td>
                                            <td th:text="${row.q30Pct}"></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="form-group row align-items-center">
                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">FileName</label>
                                    <div class="col-xl-3 col-lg-3 col-md-8">
                                        <select class="form-control form-control-sm" id="file-name" onchange="changeFileName(this)">
                                            <option th:each="name,sta : ${detailVO.fileNames}" th:value="${name}" th:text="${name}" th:selected="${sta.first}">Choose..</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-md-6">
                                        <h6 class="font-weight-bold text-center text-muted">Base Quality</h6>
                                        <div style="height:300px" id="bq-img"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="font-weight-bold text-center text-muted">GC Content</h6>
                                        <div style="height:300px" id="gc-img"></div>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0 mb-2">Mapping</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th>Name</th>
                                            <th>Mapped_Reads</th>
                                            <th>Properly_Mapped_Reads</th>
                                            <th>Average_Depth</th>
                                            <th>Coverage</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="row: ${detailVO.mappingSummaryVos}">
                                            <td th:text="${row.name}">test_PE.normal</td>
                                            <td th:text="${row.mappedReads}">test_PE.normal</td>
                                            <td th:text="${row.properlyMappedReads}">test_PE.normal</td>
                                            <td th:text="${row.averageDepth}">test_PE.normal</td>
                                            <td th:text="${row.coverage}">test_PE.normal</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="form-group row align-items-center">
                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">Name</label>
                                    <div class="col-xl-3 col-lg-3 col-md-8">
                                        <select class="form-control form-control-sm" id="name" onchange="changeName(this)">
                                            <option th:each="name,sta : ${detailVO.names}" th:value="${name}" th:text="${name}" th:selected="${sta.first}">Choose..</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-md-12">
                                        <h6 class="font-weight-bold text-center text-muted">Genome Fraction Coverage</h6>
                                        <div style="height:300px" class="text-center" id="gfc-img"></div>
                                    </div>
                                </div>
                            </div>
                            <h6 class="border-bottom pb-2 m-0 mb-2">Somatic Mutations</h6>
                            <p>
                                <a th:href="@{/analysis/somatic/downloadVcf(taskId=${task.taskId},runName=${task.runName})}"><i class="fa fa-download"></i> vcf-format file of mutations</a><br>
                                <a th:href="@{/analysis/somatic/downloadAnnotatedFile(taskId=${task.taskId},runName=${task.runName})}"><i class="fa fa-download"></i> Annotated file by ANNOVAR</a>
                            </p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<th:block layout:fragment="custom-script">
    <script th:if="${task.status == 7}">
        $(document).ready(function () {
            var name = $("#name").val();
            $("#gfc-img").html('<img height="300" src="[[@{/analysis/somatic/gfcImg}]]?taskId=[[${task.taskId}]]&name=' + name + '">')

            var filename = $("#file-name").val();
            $("#bq-img").html('<img height="300" src="[[@{/analysis/somatic/baseQualityImg}]]?taskId=[[${task.taskId}]]&name=' + filename + '">')
            $("#gc-img").html('<img height="300" src="[[@{/analysis/somatic/gcContentImg}]]?taskId=[[${task.taskId}]]&name=' + filename + '">')
        })

        function changeFileName(_this) {
            var filename = $("#file-name").val();
            $("#bq-img").html('<img height="300" src="[[@{/analysis/somatic/baseQualityImg}]]?taskId=[[${task.taskId}]]&name=' + filename + '">');
            $("#gc-img").html('<img height="300" src="[[@{/analysis/somatic/gcContentImg}]]?taskId=[[${task.taskId}]]&name=' + filename + '">');
        }

        function changeName(_this) {
            var name = $(_this).val();
            $("#gfc-img").html('<img height="300" src="[[@{/analysis/somatic/gfcImg}]]?taskId=[[${task.taskId}]]&name=' + name + '">')
        }
    </script>
</th:block>
</html>
