<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
</th:block>
<div class="page-content" layout:fragment="content">

    <div class="container-fulid">
        <div class="row mb-3">
            <div class="col-5 mb-3">
                <div class="card">
                    <div class="card-body d-flex align-items-center">
                        <div class="d-flex " style="flex: 1">
                            <div>
                                <svg t="1689591861839" class="icon" viewBox="0 0 1070 1024" version="1.1"
                                     xmlns="http://www.w3.org/2000/svg" p-id="3680" width="60" height="60">
                                    <path d="M951.215686 883.45098c-10.039216 0-18.823529-8.784314-18.823529-18.823529 0-112.941176-62.745098-214.588235-163.137255-267.294118-5.019608-2.509804-8.784314-7.529412-10.039216-13.803921s1.254902-12.54902 6.27451-16.313726c40.156863-37.647059 61.490196-87.843137 61.490196-143.058823 0-107.921569-87.843137-195.764706-195.764706-195.764706s-195.764706 87.843137-195.764706 195.764706c0 53.960784 22.588235 105.411765 61.490196 143.058823 5.019608 3.764706 6.27451 10.039216 6.27451 16.313726s-5.019608 11.294118-10.039215 13.803921c-100.392157 52.705882-163.137255 154.352941-163.137255 267.294118 0 10.039216-8.784314 18.823529-18.82353 18.823529s-18.823529-8.784314-18.823529-18.823529c0-119.215686 61.490196-227.137255 161.882353-288.627451-36.392157-42.666667-56.470588-95.372549-56.470588-151.843137 0-129.254902 105.411765-233.411765 233.411764-233.411765s233.411765 105.411765 233.411765 233.411765c0 56.470588-20.078431 109.176471-56.470588 151.843137 100.392157 61.490196 161.882353 169.411765 161.882353 288.627451 0 11.294118-8.784314 18.823529-18.82353 18.823529zM120.470588 833.254902c-10.039216 0-18.823529-8.784314-18.823529-18.823529 0-119.215686 61.490196-227.137255 161.882353-288.627451-36.392157-42.666667-56.470588-95.372549-56.470588-151.843138 0-129.254902 105.411765-233.411765 233.411764-233.411764 10.039216 0 18.823529 8.784314 18.82353 18.823529s-8.784314 18.823529-18.82353 18.823529c-107.921569 0-195.764706 87.843137-195.764706 195.764706 0 53.960784 22.588235 105.411765 61.490196 143.058824 5.019608 3.764706 6.27451 10.039216 6.27451 16.313725s-5.019608 11.294118-10.039215 13.803922c-100.392157 52.705882-163.137255 154.352941-163.137255 267.294118 0 10.039216-8.784314 18.823529-18.82353 18.823529z"
                                          fill="#0B3155" p-id="3681"></path>
                                    <path d="M951.215686 883.45098c-10.039216 0-18.823529-8.784314-18.823529-18.823529 0-112.941176-62.745098-214.588235-163.137255-267.294118-5.019608-2.509804-8.784314-7.529412-10.039216-13.803921s1.254902-12.54902 6.27451-16.313726c40.156863-37.647059 61.490196-87.843137 61.490196-143.058823 0-107.921569-87.843137-195.764706-195.764706-195.764706s-195.764706 87.843137-195.764706 195.764706c0 53.960784 22.588235 105.411765 61.490196 143.058823 5.019608 3.764706 6.27451 10.039216 6.27451 16.313726s-5.019608 11.294118-10.039215 13.803921c-100.392157 52.705882-163.137255 154.352941-163.137255 267.294118 0 10.039216-8.784314 18.823529-18.82353 18.823529s-18.823529-8.784314-18.823529-18.823529c0-119.215686 61.490196-227.137255 161.882353-288.627451-36.392157-42.666667-56.470588-95.372549-56.470588-151.843137 0-129.254902 105.411765-233.411765 233.411764-233.411765s233.411765 105.411765 233.411765 233.411765c0 56.470588-20.078431 109.176471-56.470588 151.843137 100.392157 61.490196 161.882353 169.411765 161.882353 288.627451 0 11.294118-8.784314 18.823529-18.82353 18.823529z"
                                          fill="#0B3155" p-id="3682"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h5 class="card-title white-space" style="width: 100px">
                                    用户数
                                </h5>
                                <p class="card-text" th:text="${userNum}+'个'">50 个</p>
                            </div>
                        </div>

                        <div style="flex: 2">
                            <div id="chart-1" style="height: 250px;width: 100%">1</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-7 mb-3">
                <div class="card">
                    <div class="card-body d-flex align-items-center">
                        <div class="d-flex " style="flex: 1">
                            <div>
                                <svg t="1689591918250" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                     xmlns="http://www.w3.org/2000/svg" p-id="4712" width="60" height="60">
                                    <path d="M273.516444 720.303186M340.568583 78.060867c-2.416026 0-4.832052 0-7.248078 0-77.80197 15.773242-152.999626 40.686654-228.52781 64.547085-19.779486 6.248308-34.299177 16.156982-39.655162 36.928051 0 195.542558 0 391.085116 0 586.626651 0 51.314713 35.542494 47.747467 62.645782 38.232766 60.922535-21.3871 122.989125-39.778982 185.294147-56.814983 21.378913-5.845125 28.54308-14.023388 28.430516-36.573987C340.458065 500.028341 340.700589 289.044092 340.568583 78.060867zM306.673611 698.373735c0.037862 16.012695 1.050935 14.07046-14.84408 18.568914-57.623395 16.309454-114.826212 34.222429-171.692361 53.028751-23.037692 7.618515-20.162202 6.952342-20.006659-13.293771 0.704035-91.724051 0.308015-183.457312 0.308015-275.187503 0-89.316212 0.692778-178.641633-0.503467-267.941472-0.287549-21.442358-0.880043-34.015721 20.824281-40.033785 56.555064-15.681144 112.455211-33.760918 168.426991-51.482535 16.471137-5.214768 17.585517-1.223874 17.527189 18.721387C306.165028 326.626036 306.229496 512.500397 306.673611 698.373735zM956.393229 137.572262c-0.119727-38.271652-19.436678-51.261501-56.199976-40.225143-64.25442 19.288299-127.960347 40.545438-192.691627 58.033741-22.617114 6.110161-27.191292 16.473183-27.082822 37.600363 0.784876 152.046928 0.420579 304.099996 0.420579 456.152041 0 51.939953 0 96.717785 0 151.822824 0 18.859533 14.83794 19.831674 18.212804 18.786878 82.148975-25.438369 153.135726-49.813522 232.099149-74.495668 20.87954-6.526647 25.482371-23.648606 25.486464-42.912345C956.675662 514.079358 956.980607 325.824787 956.393229 137.572262zM921.699056 689.49553c0.052189 17.59575-2.471284 24.680099-19.775392 29.582759-57.623395 16.324804-104.516402 31.425733-161.428599 50.122561-20.527522 6.743588-24.577768 3.364631-24.411993-16.285918 0.783853-92.939739 0.349971-179.74885 0.348947-272.694729-0.001023-89.324398 0.678452-170.189124-0.49528-259.498172-0.281409-21.444405 5.245468-31.875988 27.124777-37.747719 55.532781-14.900362 100.242052-32.157397 154.930605-49.972135 21.562085-7.023974 23.798009 4.609994 23.725354 26.339902C921.097352 342.816786 921.152611 506.018775 921.699056 689.49553zM618.33277 154.36267c-71.625294-19.732413-142.648884-41.828664-213.237569-65.040319-23.733541-7.804756-31.518854-5.482875-31.405267 21.555945 0.838088 200.439078 0.688685 400.884296 0.092098 601.325421-0.060375 20.190855 5.695722 28.993335 26.347065 34.795481 69.223594 19.445888 137.552819 42.063001 206.313879 63.175855 41.10621 12.620435 41.23924 12.375865 41.255613-29.225625 0.036839-96.599082 0.011256-193.198164 0.011256-289.797245 0-99.014084-0.661056-198.035332 0.48914-297.036114C648.469138 170.888042 641.717364 160.805406 618.33277 154.36267zM586.325799 769.071913c-55.403844-19.399839-100.632955-35.893488-157.215648-51.635007-16.243963-4.51892-21.097504-14.716166-21.068851-30.435173 0.332575-184.810123 0.473791-359.562169-0.140193-544.370245-0.071631-21.650089 2.341325-17.769712 20.410865-12.008498 57.103556 18.203594 102.728686 37.371142 160.600745 52.768831 22.350031 5.946432 24.02723 10.653641 23.898293 29.481452-0.621147 90.589205-0.279363 181.185572-0.279363 271.77887 0 89.385796-0.807389 172.815951 0.479931 262.183328C613.409644 774.573207 613.004415 778.412652 586.325799 769.071913z"
                                          fill="#272636" p-id="4713"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h5 class="card-title white-space">任务总数</h5>
                                <p class="card-text" th:text="${all}+'个'">1000 个</p>
                            </div>
                        </div>
                        <div style="flex: 3">
                            <div id="chart-3" style="height: 250px;width: 100%">1</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-5">
                <div class="card">
                    <div class="card-body d-flex align-items-center">
                        <div class="d-flex " style="flex: 1">
                            <div>
                                <svg t="1689589737194" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                     xmlns="http://www.w3.org/2000/svg" p-id="4330" width="60" height="60">
                                    <path d="M675.328 117.717333A425.429333 425.429333 0 0 0 512 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667c0-56.746667-11.093333-112-32.384-163.328a21.333333 21.333333 0 0 0-39.402667 16.341333A382.762667 382.762667 0 0 1 896 512c0 212.074667-171.925333 384-384 384S128 724.074667 128 512 299.925333 128 512 128c51.114667 0 100.8 9.984 146.986667 29.12a21.333333 21.333333 0 0 0 16.341333-39.402667z m-213.333333 468.608l-105.664-105.642666a21.248 21.248 0 0 0-30.122667 0.042666c-8.32 8.32-8.213333 21.973333-0.064 30.101334l120.810667 120.832a21.248 21.248 0 0 0 30.122666-0.085334l211.157334-211.157333a21.290667 21.290667 0 0 0 0-30.186667 21.397333 21.397333 0 0 0-30.250667 0.106667l-196.010667 195.989333z"
                                          fill="#3D3D3D" p-id="4331"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h5 class="card-title white-space">成功任务数</h5>
                                <p class="card-text" th:text="${complete}+'个'">300 个</p>
                            </div>
                        </div>
                        <div style="flex: 2">
                            <div id="chart-2" style="height: 250px;width: 100%">1</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-7">
                <div class="card">
                    <div class="card-body d-flex align-items-center">
                        <div class="d-flex " style="flex: 1;">
                            <div>
                                <svg t="1689592052413" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                     xmlns="http://www.w3.org/2000/svg" p-id="10322" width="60" height="60">
                                    <path d="M736.232934 118.192202h-448.922521L62.848129 506.970675 287.309389 895.749148h448.922521l224.46126-388.778473L736.232934 118.192202zM714.634073 858.33945H308.90825l-202.862399-351.368775L308.909274 155.602924h405.725823l202.862399 351.368775L714.634073 858.33945z"
                                          fill="" p-id="10323"></path>
                                    <path d="M702.027997 467.579759c-1.821492 0-3.613291 0.121842-5.377446 0.334811l-57.531092-89.131266c9.021454-11.03134 14.444974-25.115907 14.444974-40.445518 0-35.285136-28.706673-63.992833-63.992833-63.992833s-63.992833 28.707697-63.992833 63.992833c0 20.231974 9.448414 38.289216 24.153455 50.023965L432.361176 623.290191a57.620171 57.620171 0 0 0-6.604061-0.392148 57.306862 57.306862 0 0 0-12.382869 1.360744l-56.37615-88.752428a44.318876 44.318876 0 0 0 6.635801-23.387588c0-24.597821-19.941191-44.539012-44.539012-44.539012s-44.539012 19.941191-44.539011 44.539012 19.941191 44.539012 44.539011 44.539011c1.710912 0 3.397252-0.106484 5.057994-0.293855l56.384341 88.764715c-7.465148 9.655239-11.918025 21.757563-11.918025 34.907322 0 31.556146 25.581775 57.137921 57.13792 57.137921s57.137921-25.581775 57.137921-57.137921c0-15.254867-5.989729-29.102916-15.731998-39.349961l119.124962-238.438319a63.816725 63.816725 0 0 0 20.238117-2.214664l57.425632 88.967444a44.329115 44.329115 0 0 0-6.462764 23.117283c0 24.597821 19.941191 44.539012 44.539012 44.539011s44.539012-19.941191 44.539011-44.539011c0-24.596797-19.941191-44.537988-44.539011-44.537988zM564.48641 338.33881c0-13.831667 11.2525-25.08519 25.08519-25.08519s25.08519 11.253524 25.08519 25.08519-11.2525 25.08519-25.08519 25.085191-25.08519-11.253524-25.08519-25.085191z"
                                          fill="" p-id="10324"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h5 class="card-title white-space">总数据量</h5>
                                <p class="card-text" th:text="${totalDisk}">2 TB</p>
                            </div>
                        </div>
                        <div style="flex: 3">
                            <div id="chart-4" style="height: 250px;width: 100%">1</div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="card card-custom">
            <div class="card-body">
                <div class="d-flex flex-column">
                    <div class="border rounded ana-content">
                        <div class="list-group tab-content">
                            <div class="tab-pane active show fade">
                                <div id="search-form"
                                     class="form-inline form-task mt-2">

                                    <div class="d-flex">
                                        <label class="mx-2 font-12">Analysis Type</label>
                                        <input name="analysisType" type="text"
                                               class="form-control form-control-sm width-100">
                                        <label class="mx-2 font-12">Task ID</label>
                                        <input name="taskId" type="text"
                                               class="form-control form-control-sm width-100">
                                        <label class="mx-2 font-12">User Name</label>
                                        <input name="username" type="text"
                                               class="form-control form-control-sm width-100">
                                        <label class="mx-2 font-12">Start time</label>
                                        <div class="input-daterange input-group">
                                            <input autocomplete="off" type="text" class="form-control form-control-sm"
                                                   name="start"
                                            />
                                            <div class="input-group-append">
                                                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                            </div>
                                            <input autocomplete="off" type="text" class="form-control form-control-sm"
                                                   name="end"
                                            />
                                            <div class="input-group-append">
                                                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex ">
                                        <button onclick="filterTask()" class="btn btn-primary btn-sm _submit_btn">Search
                                        </button>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table id="list-table"
                                           class="table table-hover w-100 table-striped table-nowrap font-12 m-0">
                                        <thead>
                                        <!--                                        <tr>-->
                                        <!--                                            <th scope="col">Analysis Type</th>-->
                                        <!--                                            <th scope="col">Task ID</th>-->
                                        <!--                                            <th scope="col">User Name</th>-->
                                        <!--                                            <th scope="col">Input Size</th>-->
                                        <!--                                            <th scope="col">Start time</th>-->
                                        <!--                                            <th scope="col">Status</th>-->
                                        <!--                                            <th scope="col">Status time</th>-->
                                        <!--                                            <th scope="col">Consuming</th>-->
                                        <!--                                            <th scope="col">Action</th>-->
                                        <!--                                        </tr>-->
                                        </thead>
                                        <tbody>
                                        <th:block th:unless="${#lists.isEmpty(list)}">
                                            <tr th:each="task : ${list}">
                                                <td th:text="${task.analysisType}">200302151050411</td>
                                                <td th:text="${task.taskId}">R22041100001</td>
                                                <td th:text="${task.userId}">vipmap_test</td>
                                                <td th:text="${task.readableInputDataSize}">vipmap_test</td>
                                                <td th:text="${#dates.format(task.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:switch="${task.status}">
                                                    <th:block
                                                            th:case="'Analysis done'">
                                                        <span class="text-dark"><i class="fa fa-circle"></i> [[${task.status}]]</span>
                                                    </th:block>
                                                    <th:block
                                                            th:case="'Analysis Error'">
                                                        <span class="text-danger"><i class="fa fa-circle"></i> [[${task.status}]]</span>
                                                    </th:block>
                                                    <th:block th:case="*">
                                                        <span class="text-success"><i class="fa fa-circle"></i> [[${task.status}]]</span>
                                                    </th:block>
                                                </td>
                                                <td th:text="${#dates.format(task.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${task.useTime}">26m55s</td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a th:href="@{/analysis/{url}/{id}(id=${task.id},url=${task.url})}"
                                                           class="text-primary" data-toggle="tooltip" title="View">
                                                            <i class="fa fa-eye font-14"></i>
                                                        </a>
                                                        <a href="javascript:void(0);" class="text-danger"
                                                           data-toggle="tooltip"
                                                           th:onclick="deleteTask([[${task.url}]],[[${task.id}]])"
                                                           title="Delete">
                                                            <i class="fa fa-times font-14"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        </th:block>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="pt-1 mb-2">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script th:src="@{/js/echarts.min.js}"></script>
    <script>
        $(document).ready(function () {
            $('.input-daterange').datepicker({
                format: 'yyyy-mm-dd',
                toggleActive: true,
                autoclose: true,
                todayHighlight: true
            })
        })

        // let table = $('#list-table').DataTable({
        //     searching: true,
        //     ordering: false,
        //     dom: 'trilp',
        //     retrieve: true,
        //     lengthChange: false,
        //     autoWidth: false,
        //     displayLength: 20,
        //     // columnDefs: [
        //     //     {"width": "3%", "targets": 0},
        //     //     {"width": "9%", "targets": 1},
        //     //     {"width": "11%", "targets": 2},
        //     //     {"width": "10%", "targets": 3},
        //     //     {"width": "10%", "targets": 4},
        //     //     {"width": "10%", "targets": 5},
        //     //     {"width": "10%", "targets": 6},
        //     //     {"width": "10%", "targets": 7},
        //     //     {"width": "9%", "targets": 8},
        //     //     {"width": "9%", "targets": 9},
        //     //     {"width": "9%", "targets": 10},
        //     // ],
        //     fnDrawCallback: function () {
        //         setTableStyle(this)
        //     }
        // })

        let table = $('#list-table').DataTable({
            lengthChange: true,
            searching: false,
            ordering: false,
            autoWidth: true,
            scrollCollapse: true,
            // fixedColumns: {
            //     leftColumns: 0,
            //     rightColumns: 1
            // },
            lengthMenu: [10, 20, 50, 100],
            displayLength: 20,
            dom: 'trilp',
            serverSide: true,
            columns: [
                {
                    title: 'Analysis Type',
                    data: 'analysisType'
                },
                {
                    title: 'Task ID',
                    data: 'taskId'
                },
                {
                    title: 'User Name',
                    data: 'userId'
                },
                {
                    title: 'Input Data Size',
                    data: 'readableInputDataSize'
                },
                {
                    title: 'Start time',
                    data: 'createTime'
                },
                {
                    title: 'Status',
                    data: 'status',
                    render: function (data, type, row, meta) {
                        let clzStyle = 'text-success'
                        if (row.status == 'Analysis done') {
                            clzStyle = 'text-dark'
                        }
                        if (row.status.indexOf("Error") !== -1) {
                            clzStyle = 'text-danger'
                        }
                        return `<span class="${clzStyle}"><i class="fa fa-circle"></i>${row.status}</span>`
                    }
                },
                {
                    title: 'Status time',
                    data: 'updateTime'
                },
                {
                    title: 'Consuming',
                    data: 'useTime'
                },
                {
                    title: 'Action',
                    data: '',
                    render: function (data, type, row, meta) {
                        var _context_path = $("meta[name='_context_path']").attr("content")
                        let see = `<a href="${_context_path}/analysis/${row.url}/${row.id}"
                                                        target="_blank"
                                                           class="text-primary" data-toggle="tooltip" title="View">
                                                            <i class="fa fa-eye font-14"></i>
                                                        </a>`
                        if (row.analysisType == 'scRNA-Seq_10X') {
                            see = `<a href="${_context_path}/analysis/${row.url}/taskDetail?type=genomics&id=${row.id}"
                                                        target="_blank"
                                                           class="text-primary" data-toggle="tooltip" title="View">
                                                            <i class="fa fa-eye font-14"></i>
                                                        </a>`
                        }

                        return ` <div class="btn-group">${see}

                                                        <a href="javascript:void(0);" class="text-danger"
                                                           data-toggle="tooltip"
                                                           onclick="deleteTask('${row.url}','${row.id}')"
                                                           title="Delete">
                                                            <i class="fa fa-times font-14"></i>
                                                        </a>
                                                    </div>`
                    }
                },
            ],
            ajax: {
                url: '/admin/getTableData',
                type: 'POST',
                cache: false,
                traditional: true,
                data: function (data, setting) {
                    /*data.type = 'Indication'
                    data.keyword = keyword*/
                    data.analysisType = $('input[name="analysisType"]').val().trim();
                    data.taskId = $('input[name="taskId"]').val().trim();
                    data.username = $('input[name="username"]').val().trim();
                    data.startTime = $('input[name="start"]').val().trim();
                    data.endTime = $('input[name="end"]').val().trim();
                }
            },
            initComplete: function (settings, json) {
                // layer.close(initLoadIndex);
            }
        })


        function filterTask() {
            table.ajax.reload()
        }

        function deleteTask(url, id) {
            layer.confirm('<p class="text-center">确定删除吗？</p>', {btn: ['确认', '取消']}, function () {
                var loadLayerIndex
                $.ajax({
                    url: `/analysis/${url}/deleteTask`,
                    data: {'id': id},
                    dataType: 'json',
                    async: false,
                    method: 'post',
                    beforeSend: function () {
                        loadLayerIndex = layer.load(1, {
                            shade: [0.1, '#fff'] //0.1透明度的白色背景
                        })
                    },
                    success: function (result) {
                        if (result.code === 200) {
                            layer.msg('删除成功', {time: 500}, function () {
                                location.reload()
                            })
                        } else {
                            layer.alert(result.message, {icon: 2})
                        }
                    },
                    complete: function () {
                        layer.close(loadLayerIndex)
                    }
                })
            })
        }

        function initChart1() {
            // 初始化 ECharts 实例
            let chart = echarts.init(document.getElementById('chart-1'));

            // 配置图表选项
            chart.showLoading()
            $.ajax({
                url: '/admin/chartData',
                data: {
                    'chartNo': 1,
                },
                success: function (result) {
                    //console.log(result)
                    if (result.error) {
                        return
                    }
                    let data = result.data
                    chart.hideLoading();
                    // 填入数据到上面空模板

                    chart.setOption(
                        {
                            tooltip: {
                                trigger: 'item'
                            },
                            grid: {
                                right: "0%",
                                left: "15%",
                                bottom: "60",
                                top: "30"
                            },
                            dataset: {
                                source: data
                            },
                            color: ["#4472C4", "#ED7D31", "#A5A5A5"],
                            legend: {
                                top: '90%',
                                left: 'center'
                            },
                            labelLine: {
                                show: true,
                                length: 0
                            },
                            series: [
                                {
                                    type: 'pie',
                                    datasetIndex: 0,
                                    radius: '60%',
                                }
                            ]
                        })
                }
            })

        }

        function initChart2() {
            // 初始化 ECharts 实例
            let chart = echarts.init(document.getElementById('chart-2'));

            chart.showLoading()
            $.ajax({
                url: '/admin/chartData',
                data: {
                    'chartNo': 2,
                },
                success: function (result) {
                    //console.log(result)
                    if (result.error) {
                        return
                    }
                    let data = result.data
                    chart.hideLoading();

                    chart.setOption(
                        {
                            tooltip: {
                                trigger: 'item'
                            },
                            grid: {
                                right: "0%",
                                left: "15%",
                                bottom: "60",
                                top: "30"
                            },
                            legend: {
                                top: '90%',
                                left: 'center'
                            },
                            dataset: {
                                source: data
                            },
                            color: ["#4472C4", "#ED7D31", "#A5A5A5"],
                            labelLine: {
                                show: true,
                                length: 0
                            },

                            series: [
                                {
                                    type: 'pie',
                                    radius: '60%',
                                    datasetIndex: 0,
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 10,
                                            shadowOffsetX: 0,
                                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                                        }
                                    }
                                }
                            ]
                        })
                }
            })

        }

        function initChart3() {

            function fillArray(arr, start, length) {
                // 创建一个长度为 length 填充 0 的数组
                const result = Array(length).fill('');

                // 将 arr 的元素复制到 result 中
                for (let i = 0; i < arr.length; i++) {
                    const index = start + i;
                    if (index >= length) {
                        break;
                    }
                    result[index] = arr[i];
                }
                return result;
            }

            // 初始化 ECharts 实例
            let chart = echarts.init(document.getElementById('chart-3'));
            let colorMap = {
                R: '#4472C4',
                D: '#F4B183',
                M: '#A9D18E',
                SC: '#FFD966',
                SP: "#fc8452",
                P: '#AFABAB',
            }

            // 使用刚指定的配置项和数据显示图表
            chart.showLoading()
            $.ajax({
                url: '/admin/chartData',
                data: {
                    'chartNo': 3,
                },
                success: function (result) {
                    // console.log('3', result)
                    if (result.error) {
                        return
                    }
                    let data = result.data
                    let type = Object.keys(data);
                    // console.log(type);
                    let length = 0;
                    let xAxisData = []
                    let seriesData = []
                    let start = 0;
                    type.forEach(it => {
                        length += data[it].length
                        data[it].forEach(x => {
                            xAxisData.push(x.name)
                        })
                    })
                    type.forEach(it => {
                        let arr = data[it].map(x => x.value);
                        seriesData.push({
                            name: it,
                            type: 'bar',
                            stack: 'Total',
                            data: fillArray(arr, start, length)
                        })
                        start += data[it].length
                    })
                    // console.log(length, xAxisData, seriesData)
                    chart.hideLoading();
                    // // 填入数据到上面空模板
                    // let chartData = []
                    // // let xAxisData = data.map(it => it.name)
                    // data.forEach(it => {
                    //     let colo = ''
                    //     if (it["group"].startsWith("R")) {
                    //         colo = colorMap["R"]
                    //     }
                    //     if (it["group"].startsWith("D")) {
                    //         colo = colorMap["D"]
                    //     }
                    //     if (it["group"].startsWith("M")) {
                    //         colo = colorMap["M"]
                    //     }
                    //     if (it["group"].startsWith("SC")) {
                    //         colo = colorMap["SC"]
                    //     }
                    //     if (it["group"].startsWith("SP")) {
                    //         colo = colorMap["SP"]
                    //     }
                    //     if (it["group"].startsWith("P")) {
                    //         colo = colorMap["P"]
                    //     }
                    //     let item = {
                    //         value: it.value,
                    //         itemStyle: {
                    //             color: colo
                    //         }
                    //     }
                    //     chartData.push(item)
                    // })
                    // console.log(xAxisData, chartData)
                    chart.setOption({
                        tooltip: {},
                        legend: {},
                        grid: {
                            right: "0%",
                            left: "10%",
                            bottom: "60",
                            top: "30"
                        },
                        xAxis: {
                            type: 'category',
                            data: xAxisData,
                            axisLabel: {
                                rotate: 45,
                                fontSize: 9
                            }
                        },
                        color: [
                            '#4472C4',
                            '#F4B183',
                            '#A9D18E',
                            '#FFD966',
                            "#fc8452",
                            '#AFABAB',
                        ],
                        yAxis: {
                            splitLine: {
                                show: false
                            }
                        },
                        series: seriesData
                    })
                },
            })
        }

        function initChart4() {
            // 初始化 ECharts 实例
            let chart = echarts.init(document.getElementById('chart-4'));

            // 使用刚指定的配置项和数据显示图表
            chart.showLoading()
            $.ajax({
                url: '/admin/chartData',
                data: {
                    'chartNo': 4,
                },
                success: function (result) {
                    //console.log(4, result)
                    if (result.error) {
                        return
                    }
                    let data = result.data
                    chart.hideLoading();
                    // 填入数据到上面空模板
                    let legendData = Array.from(Object.keys(data))
                    let xAxisData = Object.keys(data[legendData[0]]);
                    let seriesData = []
                    legendData.forEach(it => {
                        seriesData.push({
                            name: it,
                            type: 'line',
                            symbol: 'circle',
                            data: Object.values(data[it])
                        })
                    })
                    //console.log(legendData, xAxisData, seriesData)
                    let option = {
                        tooltip: {
                            trigger: 'axis'
                        },
                        grid: {
                            right: "2%",
                            left: "10%",
                            bottom: "70",
                            top: "30"
                        },
                        legend: {
                            show: true,
                            top: '90%',
                            left: 'center',
                            data: legendData
                        },
                        color: [
                            '#4472C4',
                            '#F4B183',
                            '#A9D18E',
                            '#FFD966',
                            "#fc8452",
                            '#AFABAB',
                        ],
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: xAxisData,
                            axisLabel: {
                                rotate: 75,
                                fontSize: 9
                            }
                        },
                        yAxis: {
                            type: 'value',
                            splitLine: {
                                show: false
                            },
                            name: "单位（GB）"
                        },
                        series: seriesData
                    };
                    chart.setOption(option)
                },
            })
        }

        initChart1();
        initChart2();
        initChart3();
        initChart4();

    </script>
</th:block>
</html>
