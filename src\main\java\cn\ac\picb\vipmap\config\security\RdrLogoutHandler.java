package cn.ac.picb.vipmap.config.security;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.LogoutHandler;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * VipMap登出处理器
 * 参考RdrLogoutHandler实现
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class RdrLogoutHandler implements LogoutHandler {

    private RdrProperties rdrProperties;

    @Override
    public void logout(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        // 访问远程登出接口
        String token = RdrUserDetailsService.obtainToken(request, response);
        if (StrUtil.isNotBlank(token)) {
            String logoutUrl = rdrProperties.getRdrApiLogoutUrl() + "?token=" + token;
            try {
                String body = HttpUtil.createRequest(Method.DELETE, logoutUrl)
                        .header("Authorization", token)
                        .execute()
                        .body();
                log.debug("远程登出响应: {}", body);
            } catch (Exception e) {
                log.error("调用远程登出接口失败", e);
            }
        }

        // 清除Cookie
        Cookie cookie = new Cookie("Admin-Token", null);
        cookie.setMaxAge(0);
        cookie.setPath("/");
        response.addCookie(cookie);

        // 清除security中的登录用户
        SecurityContextHolder.clearContext();

        log.info("用户登出");
    }
}
