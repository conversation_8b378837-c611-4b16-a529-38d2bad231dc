<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">DEG & GSVA</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <th:block th:switch="${taskVo.degTask.status}">
                        <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Prepared</p>
                        </div>
                        <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="2">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis done</p>
                        </div>
                        <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis Error</p>
                        </div>
                    </th:block>
                    <div class="form-group-box">
                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                        <div class="collapse show" id="coll-1">
                            <div class="pl-4 pt-2">
                                <div class="result-box">
                                    <div class="table-responsive mt-2">
                                        <table class="table table-bordered table-sm table-center table-middle mb-1">
                                            <thead>
                                            <tr class="thead-light">
                                                <th>Cluster Baseline ID</th>
                                                <th>Start time</th>
                                                <th>Status time</th>
                                                <th>Consuming</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td th:text="${taskVo.baselineTask.taskId}"></td>
                                                <td th:text="${#dates.format(taskVo.baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${#dates.format(taskVo.baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${taskVo.baselineTask.useTime}">26m55s</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="form-group row align-items-baseline m-0" th:if="${taskVo.degTask.degMode == 'single'}">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Compare cluster</label>
                                        <div class="col-xl-2 col-lg-2 col-md-4">
                                            <span class="text-primary" th:text="${taskVo.degTask.cluster1}">Cluster_1</span>
                                        </div>
                                    </div>
                                    <div class="form-group row align-items-baseline m-0" th:if="${taskVo.degTask.degMode == 'multi'}">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Compare cluster</label>
                                        <div class="col-xl-2 col-lg-2 col-md-4" th:with="cluster1s=${#strings.listSplit(taskVo.degTask.cluster1, ';')}">
                                            <th:block th:each="cluster1, sta : ${cluster1s}">
                                                <span class="text-primary" th:text="${cluster1}">Cluster_1</span><br th:unless="${sta.last}">
                                            </th:block>
                                        </div>
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">with cluster</label>
                                        <div class="col-xl-2 col-lg-2 col-md-4" th:with="cluster2s=${#strings.listSplit(taskVo.degTask.cluster2, ';')}">
                                            <th:block th:each="cluster2, sta : ${cluster2s}">
                                                <span class="text-primary" th:text="${cluster2}">Cluster_1</span><br th:unless="${sta.last}">
                                            </th:block>
                                        </div>
                                    </div>

                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Fold change(log2)</label>
                                        <div class="col-xl-2 col-lg-2 col-md-4">
                                            <span class="text-primary" th:text="${taskVo.degTask.cutfc}">0.05</span>
                                        </div>
                                        <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">FDR</label>
                                        <div class="col-xl-2 col-lg-2 col-md-4">
                                            <span class="text-primary" th:text="${taskVo.degTask.cutp}">0.05</span>
                                        </div>
                                    </div>
                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Database</label>
                                        <div class="col-xl-2 col-lg-2 col-md-4">
                                            <span class="text-primary" th:text="${taskVo.degTask.annoDb}">REACTOME</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group-box" th:if="${taskVo.degTask.status == 2}">
                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                        <div class="collapse show" id="coll-2">
                            <div class="tool-content">
                                <div class="row position-relative">
                                    <div class="col-lg-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">Volcano Plot</h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='volcano',runName =${taskVo.degTask.taskId})}">-->
                                            <!--                                                <i class="fa fa-download text-primary"></i>-->
                                            <!--                                            </a>-->
                                        </div>
                                        <div id="chart-10" style="width: 100%;height: 500px"></div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">Gene Set Enrichment</h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='Gene_set',runName =${taskVo.degTask.taskId}, fdr=${taskVo.degTask.cutp}, foldChange=${taskVo.degTask.cutfc})}">-->
                                            <!--                                                <i class="fa fa-download text-primary"></i>-->
                                            <!--                                            </a>-->
                                        </div>
                                        <div id="chart-11" style="width: 100%;height: 500px"></div>
                                    </div>
                                    <div id="tip" style="position: absolute;background: rgba(0,0,0,0.5); border-radius: 5px;padding: 5px;z-index: 1;color: #fff; display: none"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tool-box">
                    <div class="tool-title">Reference</div>
                    <div class="tool-content">
                        <ul class="r-list">
                            <li>1. Cell,2017, Landscape of Infiltrating T Cells in Liver Cancer Revealed by Single-Cell
                                Sequencing
                            </li>
                            <li>2. cell,2019, Neutrophils Driving Unconventional T Cells Mediate Resistance against
                                Murine Sarcomas and Selected Human Tumors
                            </li>
                        </ul>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>
    <script>
        $(document).ready(function () {
            initChart10();
            initChart11();
        })


        function initChart10() {
            if (!document.getElementById('chart-10')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-10'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/deg/[[${taskVo.degTask.id}]]/10',
                    beforeSend: function () {
                        $("#chart-10").next().remove();
                        $("#chart-10").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-10").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-10").hide();
                            return;
                        }
                        var data = result.data;
                        var nodes = data.data, cutp = data.cutp, cutfc = data.cutfc;
                        var nodeData = nodes.map(function (item) {
                            return {
                                value: [item.x, item.y, item.name],
                                itemStyle: {
                                    color: item.color
                                }
                            }
                        })
                        var points = {
                            type: 'scatter',
                            data: nodeData,
                            symbolSize: 6,
                        }
                        var line = {
                            type: 'line',
                            data: [],
                            markLine: {
                                data: [
                                    {
                                        name: 'x=Fold change(log2)',
                                        symbol: 'none',
                                        xAxis: cutfc,
                                        lineStyle: {
                                            color: '#ff0000'
                                        }
                                    },
                                    {
                                        name: 'x=-Fold change(log2)',
                                        symbol: 'none',
                                        xAxis: -cutfc,
                                        lineStyle: {
                                            color: '#ff0000'
                                        }
                                    },
                                    {
                                        name: 'y=-log10(FDR)',
                                        symbol: 'none',
                                        yAxis: -Math.log10(cutp),
                                        lineStyle: {
                                            color: '#ff0000'
                                        }
                                    }
                                ]
                            }
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            xAxis: {
                                name: 'log2(Fold change)',
                                nameGap: 25,
                                nameLocation: 'center',
                            },
                            yAxis: {
                                name: '-log10(FDR)',
                                nameGap: 35,
                                nameLocation: 'center',
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                containLabel: true
                            },
                            tooltip: {
                                formatter: function (params) {
                                    return params.data.value[2]
                                }
                            },
                            series: [
                                points,
                                line
                            ]
                        });
                    }
                })
            }
        }

        function initChart11() {
            if (!document.getElementById('chart-11')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-11'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                        url: '/analysis/scrnaseq/deg/[[${taskVo.degTask.id}]]/11',
                        beforeSend: function () {
                            $("#chart-11").next().remove();
                            $("#chart-11").show();
                            myChart.showLoading();
                        },
                        complete: function () {
                            myChart.hideLoading();
                        },
                        success: function (result) {
                            if (result.error) {
                                $("#chart-11").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                                $("#chart-11").hide();
                                return;
                            }
                            var data = result.data;
                            var xData = data.xData, yData = data.yData, sData = data.data

                            var seriesData = sData.map(function (item) {
                                return [item[1], item[0], item[2] || '-'];
                            });

                            myChart.setOption({
                                toolbox: {
                                    show: true,
                                    feature: {
                                        saveAsImage: {}
                                    }
                                },
                                tooltip: {
                                    position: 'top'
                                },
                                animation: false,
                                grid: {
                                    height: '50%',
                                    left: '30%'
                                },
                                xAxis: {
                                    type: 'category',
                                    data: xData,
                                    splitArea: {
                                        show: true
                                    },
                                    axisLabel: {
                                        interval: 0,
                                        rotate: 90
                                    },
                                },
                                yAxis: {
                                    type: 'category',
                                    data: yData,
                                    splitArea: {
                                        show: true
                                    },
                                    axisLabel: {
                                        interval: 0,
                                        fontSize: 10,
                                        formatter: function (params) {
                                            if (params.length > 40) {
                                                return params.substring(0, 40) + '...';
                                            } else {
                                                return params;
                                            }
                                        }
                                    },
                                    triggerEvent: true
                                },
                                visualMap: {
                                    min: -1,
                                    max: 1,
                                    dimension: 2,
                                    precision: 3,
                                    calculable: true,
                                    orient: 'horizontal',
                                    left: 'center',
                                    bottom: '15%',
                                    inRange: {
                                        color: ['#053061', '#2fbce2', '#f5f5f5', '#e7783c', '#69001f']
                                    }
                                },
                                series: [{
                                    type: 'heatmap',
                                    data: seriesData,
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 10,
                                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                                        }
                                    }
                                }]
                            })

                            myChart.on('mouseover', function (params) {
                                if (params.componentType === 'yAxis') {
                                    var tt = $('#tip');
                                    tt.html(params.value);
                                    tt.css('left', params.event.event.offsetX + 30);
                                    tt.css('top', params.event.event.offsetY + 550);
                                    tt.show();
                                }
                            })
                            myChart.on('mouseout', function () {
                                $('#tip').hide();
                            })
                        }
                    }
                )
            }
        }

    </script>
</th:block>
</html>
