package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.rnaseq.dto.RnaseqTaskDTO;
import cn.ac.picb.rnaseq.enums.RnaseqTaskStatus;
import cn.ac.picb.rnaseq.po.RnaseqTaskPO;
import cn.ac.picb.rnaseq.vo.ChartParamVO;
import cn.ac.picb.rnaseq.vo.RnaseqSyncToNodeVO;
import cn.ac.picb.rnaseq.vo.RnaseqTaskInput;
import cn.ac.picb.rnaseq.vo.SyncToNodeFileVo;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.RnaseqService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.RnaseqTaskParam;
import cn.ac.picb.vipmap.vo.RnaseqTaskSearchVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/analysis/rnaseq")
@RequiredArgsConstructor
public class RnaseqController {

    private final RnaseqService rnaseqService;
    private final AppProperties appProperties;

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") RnaseqTaskSearchVO search, PageParam pageParam, Model model) {
        PageResult<RnaseqTaskPO> pageResult = rnaseqService.findPage(user, search, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = RnaseqTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "rnaseq/list";
    }

    @RequestMapping("/form")
    public String form() {
        return "rnaseq/form";
    }

    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated RnaseqTaskParam param) {
//        return null;
        RnaseqTaskPO task = rnaseqService.createTask(user, param);
        return success(task.getTaskId());
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        rnaseqService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/download")
    @ResponseBody
    public void downloadResult(String taskId, Integer step) {
        Response response = rnaseqService.downloadResult(taskId, step, "");
        ResponseUtil.download(response);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        RnaseqTaskDTO vo = rnaseqService.findTaskVO(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = RnaseqTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "rnaseq/detail";
    }

    @RequestMapping("/chartData")
    @ResponseBody
    public CommonResult<Object> chartData(@Validated ChartParamVO param) {
        Object data = rnaseqService.getChartData(param);
        return success(data);
    }

    @RequestMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemplate() {
        Response response = rnaseqService.downloadTemplate();
        ResponseUtil.download(response);
    }

    @RequestMapping("/uploadTemplate")
    @ResponseBody
    public CommonResult<List<RnaseqTaskInput>> uploadTemplate(CurrentUser user, MultipartFile file) {
        List<RnaseqTaskInput> vos = rnaseqService.uploadTemplate(file, user);
        return success(vos);
    }

    @RequestMapping("/getSyncToNodeFileList")
    @ResponseBody
    public CommonResult<List<SyncToNodeFileVo>> getSyncToNodeFileList(@RequestParam String taskId, CurrentUser user) {
        List<SyncToNodeFileVo> vos = rnaseqService.getToNodeList(taskId, user);
        return success(vos);
    }

    @RequestMapping("/syncToNode")
    @ResponseBody
    public CommonResult<Boolean> syncToNode(@RequestBody RnaseqSyncToNodeVO dto, CurrentUser user) {
        rnaseqService.syncToNode(dto, user);
        return success(true);
    }
}
