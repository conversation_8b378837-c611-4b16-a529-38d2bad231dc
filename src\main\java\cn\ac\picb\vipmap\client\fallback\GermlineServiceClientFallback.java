package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.germline.dto.GermlineTaskDTO;
import cn.ac.picb.germline.po.GermlineTaskPO;
import cn.ac.picb.germline.vo.*;
import cn.ac.picb.vipmap.client.GermlineServiceClient;
import feign.Response;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class GermlineServiceClientFallback implements GermlineServiceClient {

    private final static String SERVER_NAME = "file-service";

    @Override
    public CommonResult<PageResult<GermlineTaskDTO>> findTaskPage(GermlineTaskQueryVO query) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GermlineTaskPO> saveTask(GermlineTaskParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GermlineTaskPO> findById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GermlineTaskDTO> findDetailById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GermlineTaskPO> deleteById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GermlineTaskQcDataVO> findQcData(String taskId, String runName) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GermlineTaskMappingDataVO> findMappingData(String taskId) {
        return serverError(SERVER_NAME);
    }

    @Override
    public ResponseEntity<byte[]> getBaseQualityImg(String taskId, String runName) {
        return null;
    }

    @Override
    public ResponseEntity<byte[]> getGcContentImg(String taskId, String runName) {
        return null;
    }

    @Override
    public ResponseEntity<byte[]> getGenomeFractionCoverageImg(String taskId, String name) {
        return null;
    }

    @Override
    public Response downloadVcf(String taskId, String type, String displayName) {
        return null;
    }

    @Override
    public Response downloadAnnotatedFile(String taskId, String type, String displayName) {
        return null;
    }

    @Override
    public Response downloadResult(String taskId, String displayName) {
        return null;
    }

    @Override
    public Response downloadGermlineTemplate() {
        return null;
    }

    @Override
    public CommonResult<List<GermlineValidateResultVO>> uploadGermlineTemplate(MultipartFile file, String username) {
        return serverError(SERVER_NAME);
    }
}
