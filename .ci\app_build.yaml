apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: analysis
    tier: tomcat
    type: web
    level: vipmap
  name: vipmap
  namespace: analysis
spec:
  revisionHistoryLimit: 10 
  replicas: 1
  selector:
    matchLabels:
      app: analysis
      tier: tomcat
      type: web
      level: vipmap
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: analysis
        tier: tomcat
        type: web
        level: vipmap
    spec:
      hostname: vipmap-app
      containers:
      - image: dev.biosino.org/analysis/APP_NAME:vBUILD_ID-BUILD_DATE
        imagePullPolicy: Always
        name: vipmap
        ports:
        - containerPort: 8080
          protocol: TCP
        resources:
          limits:
            cpu: "4"
            memory: 8Gi
          requests:
            cpu: "2"
            memory: 2Gi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /data/logs
          name: rootdir
          subPath: vipmap/logs
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      volumes:
      - name: rootdir
        persistentVolumeClaim:
          claimName: analysis
