package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.methychip.vo.MethyChipTaskParamVO;
import cn.ac.picb.methychip.vo.MethyChipTaskQueryVO;
import cn.ac.picb.vipmap.vo.MethyChipTaskParam;
import cn.ac.picb.vipmap.vo.MethyChipTaskSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:14
 */
@Mapper
public interface MethyChipMapper {
    MethyChipMapper INSTANCE = Mappers.getMapper(MethyChipMapper.class);


    MethyChipTaskParamVO convertToVO(MethyChipTaskParam param);

    MethyChipTaskQueryVO convert(MethyChipTaskSearchVO search);
}
