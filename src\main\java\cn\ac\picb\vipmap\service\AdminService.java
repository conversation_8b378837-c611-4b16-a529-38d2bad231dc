package cn.ac.picb.vipmap.service;

import cn.ac.picb.ase.enums.AseTaskStatus;
import cn.ac.picb.circrna.enums.CircrnaTaskStatus;
import cn.ac.picb.germline.enums.GermlineTaskStatus;
import cn.ac.picb.methychip.enums.MethyChipTaskStatus;
import cn.ac.picb.paean.enums.PaeanTaskStatus;
import cn.ac.picb.proteomics.enums.ProteomicsTaskStatus;
import cn.ac.picb.rnaseq.enums.RnaseqTaskStatus;
import cn.ac.picb.scrnaseq.enums.GenomicTaskStatus;
import cn.ac.picb.scrnasmartseq.enums.ScrnaSmartseqTaskStatus;
import cn.ac.picb.somatic.enums.SomaticTaskStatus;
import cn.ac.picb.strnaseq.enums.StrnaseqTaskStatus;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.repository.CasUserRepository;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.TaskSearchVO;
import cn.ac.picb.vipmap.vo.TaskVO;
import cn.ac.picb.wgbs.enums.WgbsTaskStatus;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2023/7/18
 */
@Service
@RequiredArgsConstructor
public class AdminService {

    private final JdbcTemplate jdbcTemplate;
    private final AppProperties appProperties;
    private final CasUserRepository casUserRepository;

    private static final String deg = "DEG";
    private static final String ase = "ASE";
    private static final String circrna = "CircleRNA";
    private static final String paean = "ASE-GPU";
    private static final String scrnaseq = "scRNA-Seq";
    private static final String scrnasmartseq = "SmartSeq";
    private static final String strnaseq = "stRNA-Seq";
    private static final String somatic = "Somatic-SNV";
    private static final String somatic_cnvs = "Somatic-CNV";
    private static final String germline = "Germline";
    private static final String methychip = "BeadChip";
    private static final String wgbs = "WGBS";
    private static final String proteomics = "Proteomics";

    public Page<TaskVO> findData(TaskSearchVO query) {
        String modules = appProperties.getModules();

        String[] split = modules.split(";");

        ArrayList<String> sqlList = new ArrayList<>();
        if (ArrayUtil.contains(split, "rnaseq")) {
            sqlList.add(getSql(deg, "rnaseq", "rna_seq_task"));
        }
        if (ArrayUtil.contains(split, "ase")) {
            sqlList.add(getSql(ase, "ase", "ase_task"));
        }
        if (ArrayUtil.contains(split, "circrna")) {
            sqlList.add(getSql(circrna, "circrna", "circrna_task"));
        }
        if (ArrayUtil.contains(split, "paean")) {
            sqlList.add(getSql(paean, "paean", "paean_task"));
        }
        if (ArrayUtil.contains(split, "scrnaseq")) {
            sqlList.add(getSql(scrnaseq, "scrnaseq", "scrnaseq_genomics_task"));
        }
        if (ArrayUtil.contains(split, "scrnasmartseq")) {
            sqlList.add(getSql(scrnasmartseq, "scrnaSmartseq", "scrna_smartseq_task"));
        }
        if (ArrayUtil.contains(split, "strnaseq")) {
            sqlList.add(getSql(strnaseq, "strnaseq", "strnaseq_task"));
        }
        if (ArrayUtil.contains(split, "somatic")) {
            sqlList.add(getSql(somatic, "somatic", "somatic_task"));
            sqlList.add(getSql(somatic_cnvs, "somatic-cnvs", "somatic_cnvs_task"));
        }
        if (ArrayUtil.contains(split, "germline")) {
            sqlList.add(getSql(germline, "germline", "germline_task"));
        }
        if (ArrayUtil.contains(split, "methychip")) {
            sqlList.add(getSql(methychip, "methychip", "methy_beadchip_task"));
        }
        if (ArrayUtil.contains(split, "wgbs")) {
            sqlList.add(getSql(wgbs, "wgbs", "wgbs_task"));
        }
        if (ArrayUtil.contains(split, "proteomics")) {
            sqlList.add(getSql(proteomics, "proteomics", "proteomics_task"));
        }

        String unionSql = StrUtil.format("select m.analysis_type,m.url,m.id,m.task_id, case when u.user_name is null then m.user_id else u.user_name end as user_id ,m.input_data_size,m.status,m.create_time,m.update_time,m.use_time from ({}) as m LEFT JOIN cas_user AS u ON m.user_id = u.id", CollUtil.join(sqlList, " UNION ALL "));

        ArrayList<String> conditionList = new ArrayList<>();
        // 条件
        if (StrUtil.isNotBlank(query.getAnalysisType())) {
            conditionList.add(StrUtil.format("subQuery.analysis_type like '%{}%'", query.getAnalysisType()));
        }
        if (StrUtil.isNotBlank(query.getTaskId())) {
            conditionList.add(StrUtil.format("subQuery.task_id like '%{}%'", query.getTaskId()));
        }
        if (StrUtil.isNotBlank(query.getUsername())) {
            conditionList.add(StrUtil.format("subQuery.user_id like '%{}%'", query.getUsername()));
        }
        if (query.getStartTime() != null) {
            Date date = DateUtil.endOfDay(query.getStartTime()).toSqlDate();
            conditionList.add(StrUtil.format("subQuery.create_time >= '{}'", date));
        }
        if (query.getEndTime() != null) {
            Date date = DateUtil.endOfDay(query.getEndTime()).toSqlDate();
            conditionList.add(StrUtil.format("subQuery.create_time <= '{}'", date));
        }
        conditionList.add("subQuery.status != '0'");
        // 分页
        String finalSql = StrUtil.format("select * from ({}) as subQuery  where  {} ORDER BY create_time desc limit {} offset {}", unionSql, CollUtil.join(conditionList, " and "), query.getLength(), query.getStart());

        Integer totalCount = jdbcTemplate.queryForObject(StrUtil.format("select count(*) from ({}) as subQuery where {}", unionSql, CollUtil.join(conditionList, " and ")), Integer.class);

        List<TaskVO> result = jdbcTemplate.query(finalSql, new RowMapper() {
            @Override
            public Object mapRow(ResultSet rs, int i) throws SQLException {
                TaskVO taskVO = new TaskVO();
                taskVO.setAnalysisType(rs.getString("analysis_type"));
                taskVO.setUrl(rs.getString("url"));
                taskVO.setId(rs.getString("id"));
                taskVO.setTaskId(rs.getString("task_id"));
                taskVO.setUserId(rs.getString("user_id"));
                taskVO.setInputDataSize(rs.getLong("input_data_size"));
                taskVO.setStatus(rs.getString("status"));
                taskVO.setCreateTime(rs.getTimestamp("create_time"));
                taskVO.setUpdateTime(rs.getTimestamp("update_time"));
                taskVO.setUseTime(rs.getString("use_time"));
                return taskVO;
            }
        });
        for (TaskVO x : result) {
            x.setReadableInputDataSize(FileUtil.readableFileSize(x.getInputDataSize() != null ? x.getInputDataSize() : 0));
            switch (x.getAnalysisType()) {
                case deg:
                    x.setStatus(RnaseqTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case ase:
                    x.setStatus(AseTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case circrna:
                    x.setStatus(CircrnaTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case paean:
                    x.setStatus(PaeanTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case scrnaseq:
                    x.setStatus(GenomicTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case scrnasmartseq:
                    x.setStatus(ScrnaSmartseqTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case strnaseq:
                    x.setStatus(StrnaseqTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case somatic:
                    x.setStatus(SomaticTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case somatic_cnvs:
                    x.setStatus(SomaticTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case germline:
                    x.setStatus(GermlineTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case methychip:
                    x.setStatus(MethyChipTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case wgbs:
                    x.setStatus(WgbsTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                case proteomics:
                    x.setStatus(ProteomicsTaskStatus.statusMap(appProperties.getLocale()).get(Integer.parseInt(x.getStatus())));
                    break;
                default:
                    break;
            }
        }

        return new PageImpl<>(result, query.getPageable(), totalCount != null ? totalCount : 0);
    }


    private String getSql(String analysisType, String url, String tableName) {
        String sqlTemplate = "select '{}' as analysis_type,'{}' as url,id,task_id,user_id,create_time,status,update_time,use_time,input_data_size FROM {}";
        return StrUtil.format(sqlTemplate, analysisType, url, tableName);
    }

    public Long countUser() {
        return casUserRepository.count();
    }

    public Integer countAllTask() {
        String modules = appProperties.getModules();

        String[] split = modules.split(";");
        ArrayList<String> sqlList = new ArrayList<>();
        if (ArrayUtil.contains(split, "rnaseq")) {
            sqlList.add(getCountSql("rna_seq_task"));
        }
        if (ArrayUtil.contains(split, "ase")) {
            sqlList.add(getCountSql("ase_task"));
        }
        if (ArrayUtil.contains(split, "circrna")) {
            sqlList.add(getCountSql("circrna_task"));
        }
        if (ArrayUtil.contains(split, "paean")) {
            sqlList.add(getCountSql("paean_task"));
        }
        if (ArrayUtil.contains(split, "scrnaseq")) {
            sqlList.add(getCountSql("scrnaseq_genomics_task"));
        }
        if (ArrayUtil.contains(split, "scrnasmartseq")) {
            sqlList.add(getCountSql("scrna_smartseq_task"));
        }
        if (ArrayUtil.contains(split, "strnaseq")) {
            sqlList.add(getCountSql("strnaseq_task"));
        }
        if (ArrayUtil.contains(split, "somatic")) {
            sqlList.add(getCountSql("somatic_task"));
            sqlList.add(getCountSql("somatic_cnvs_task"));
        }
        if (ArrayUtil.contains(split, "germline")) {
            sqlList.add(getCountSql("germline_task"));
        }
        if (ArrayUtil.contains(split, "methychip")) {
            sqlList.add(getCountSql("methy_beadchip_task"));
        }
        if (ArrayUtil.contains(split, "wgbs")) {
            sqlList.add(getCountSql("wgbs_task"));
        }
        if (ArrayUtil.contains(split, "proteomics")) {
            sqlList.add(getCountSql("proteomics_task"));
        }
        String unionSql = CollUtil.join(sqlList, " UNION ALL ");
        String finalSql = StrUtil.format("select count(*) from ({}) as subQuery", unionSql);
        Integer count = jdbcTemplate.queryForObject(finalSql, Integer.class);
        return count;
    }

    public Integer countCompleteTask() {
        String modules = appProperties.getModules();

        String[] split = modules.split(";");
        ArrayList<String> sqlList = new ArrayList<>();
        if (ArrayUtil.contains(split, "rnaseq")) {
            sqlList.add(getStatusSql("rna_seq_task", RnaseqTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "ase")) {
            sqlList.add(getStatusSql("ase_task", AseTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "circrna")) {
            sqlList.add(getStatusSql("circrna_task", CircrnaTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "paean")) {
            sqlList.add(getStatusSql("paean_task", PaeanTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "scrnaseq")) {
            sqlList.add(getStatusSql("scrnaseq_genomics_task", GenomicTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "scrnasmartseq")) {
            sqlList.add(getStatusSql("scrna_smartseq_task", ScrnaSmartseqTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "strnaseq")) {
            sqlList.add(getStatusSql("strnaseq_task", StrnaseqTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "somatic")) {
            sqlList.add(getStatusSql("somatic_task", SomaticTaskStatus.complete.getCode()));
            sqlList.add(getStatusSql("somatic_cnvs_task", SomaticTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "germline")) {
            sqlList.add(getStatusSql("germline_task", GermlineTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "methychip")) {
            sqlList.add(getStatusSql("methy_beadchip_task", MethyChipTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "wgbs")) {
            sqlList.add(getStatusSql("wgbs_task", WgbsTaskStatus.complete.getCode()));
        }
        if (ArrayUtil.contains(split, "proteomics")) {
            sqlList.add(getStatusSql("proteomics_task", ProteomicsTaskStatus.complete.getCode()));
        }
        String unionSql = CollUtil.join(sqlList, " UNION ALL ");
        String finalSql = StrUtil.format("select count(*) from ({}) as subQuery", unionSql);
        Integer count = jdbcTemplate.queryForObject(finalSql, Integer.class);
        return count;
    }

    public Integer countErrorTask() {
        String modules = appProperties.getModules();

        String[] split = modules.split(";");
        ArrayList<String> sqlList = new ArrayList<>();
        if (ArrayUtil.contains(split, "rnaseq")) {
            sqlList.add(getStatusSql("rna_seq_task", RnaseqTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "ase")) {
            sqlList.add(getStatusSql("ase_task", AseTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "circrna")) {
            sqlList.add(getStatusSql("circrna_task", CircrnaTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "paean")) {
            sqlList.add(getStatusSql("paean_task", PaeanTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "scrnaseq")) {
            sqlList.add(getStatusSql("scrnaseq_genomics_task", GenomicTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "scrnasmartseq")) {
            sqlList.add(getStatusSql("scrna_smartseq_task", ScrnaSmartseqTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "strnaseq")) {
            sqlList.add(getStatusSql("strnaseq_task", StrnaseqTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "somatic")) {
            sqlList.add(getStatusSql("somatic_task", SomaticTaskStatus.error.getCode()));
            sqlList.add(getStatusSql("somatic_cnvs_task", SomaticTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "germline")) {
            sqlList.add(getStatusSql("germline_task", GermlineTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "methychip")) {
            sqlList.add(getStatusSql("methy_beadchip_task", MethyChipTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "wgbs")) {
            sqlList.add(getStatusSql("wgbs_task", WgbsTaskStatus.error.getCode()));
        }
        if (ArrayUtil.contains(split, "proteomics")) {
            sqlList.add(getStatusSql("proteomics_task", ProteomicsTaskStatus.error.getCode()));
        }
        String unionSql = CollUtil.join(sqlList, " UNION ALL ");
        String finalSql = StrUtil.format("select count(*) from ({}) as subQuery", unionSql);
        Integer count = jdbcTemplate.queryForObject(finalSql, Integer.class);
        return count;
    }

    public String countDiskSize() {
        String modules = appProperties.getModules();

        String[] split = modules.split(";");
        ArrayList<String> sqlList = new ArrayList<>();
        if (ArrayUtil.contains(split, "rnaseq")) {
            sqlList.add(getCountSql("rna_seq_task"));
        }
        if (ArrayUtil.contains(split, "ase")) {
            sqlList.add(getCountSql("ase_task"));
        }
        if (ArrayUtil.contains(split, "circrna")) {
            sqlList.add(getCountSql("circrna_task"));
        }
        if (ArrayUtil.contains(split, "paean")) {
            sqlList.add(getCountSql("paean_task"));
        }
        if (ArrayUtil.contains(split, "scrnaseq")) {
            sqlList.add(getCountSql("scrnaseq_genomics_task"));
        }
        if (ArrayUtil.contains(split, "scrnasmartseq")) {
            sqlList.add(getCountSql("scrna_smartseq_task"));
        }
        if (ArrayUtil.contains(split, "strnaseq")) {
            sqlList.add(getCountSql("strnaseq_task"));
        }
        if (ArrayUtil.contains(split, "somatic")) {
            sqlList.add(getCountSql("somatic_task"));
            sqlList.add(getCountSql("somatic_cnvs_task"));
        }
        if (ArrayUtil.contains(split, "germline")) {
            sqlList.add(getCountSql("germline_task"));
        }
        if (ArrayUtil.contains(split, "methychip")) {
            sqlList.add(getCountSql("methy_beadchip_task"));
        }
        if (ArrayUtil.contains(split, "wgbs")) {
            sqlList.add(getCountSql("wgbs_task"));
        }
        if (ArrayUtil.contains(split, "proteomics")) {
            sqlList.add(getCountSql("proteomics_task"));
        }
        String unionSql = CollUtil.join(sqlList, " UNION ALL ");
        String finalSql = StrUtil.format("select sum(input_data_size) from ({}) as subQuery", unionSql);
        Long count = jdbcTemplate.queryForObject(finalSql, Long.class);
        return FileUtil.readableFileSize(count);
    }

    private String getCountSql(String tableName) {
        String sqlTemplate = "select input_data_size FROM {} where status !='0'";
        return StrUtil.format(sqlTemplate, tableName);
    }

    private String getStatusSql(String tableName, Object status) {
        String sqlTemplate = "select id FROM {} where status = '{}'";
        return StrUtil.format(sqlTemplate, tableName, status);
    }

    public String addAdmin(String username, String token) {
        if (!token.equalsIgnoreCase("vipmap_test_add_admin")) {
            return "token错误";
        }
        Optional<CurrentUser> optional = casUserRepository.findFirstByUsername(username);
        if (!optional.isPresent()) {
            return "用户不存在";
        }
        CurrentUser user = optional.get();
        user.setRole("ADMIN");
        casUserRepository.save(user);
        return StrUtil.format("给 {} 添加 ADMIN 角色成功", username);
    }

    public Object chartData(Integer chartNo) {
        Object result = null;
        switch (chartNo) {
            case 1:
                result = chartData1();
                break;
            case 2:
                result = chartData2();
                break;
            case 3:
                result = chartData3();
                break;
            case 4:
                result = chartData4();
                break;
            default:
                break;
        }
        return result;
    }

    private Object chartData1() {
        List<TaskVO> vo = getAllTaskVO();
        List<String> result = vo.stream().map(x -> x.getUserId()).distinct().collect(Collectors.toList());
        long innerUser = result.stream().filter(x -> x.endsWith("@picb.ac.cn") || x.endsWith("@sibs.ac.cn") || x.endsWith("@sinh.ac.cn") || x.endsWith("vipmap_test")).count();
        long outerUser = result.size() - innerUser;
        List<List<Object>> data = new ArrayList<>();
        data.add(CollUtil.newArrayList("所内", innerUser));
        data.add(CollUtil.newArrayList("所外", outerUser));
        return data;
    }

    private Object chartData2() {
        Integer all = countAllTask();
        Integer err = countErrorTask();
        Integer complete = countCompleteTask();
        int run = all - complete - err;
        List<List<Object>> data = new ArrayList<>();
        data.add(CollUtil.newArrayList("正在运行", run));
        data.add(CollUtil.newArrayList("报错", err));
        data.add(CollUtil.newArrayList("已完成", complete));
        return data;
    }

    private Object chartData3() {
        List<TaskVO> vo = getAllTaskVO();

        Map<String, Long> map =
                vo.stream()
                        .collect(Collectors.groupingBy(TaskVO::getAnalysisType, LinkedHashMap::new, Collectors.counting()));
        ArrayList<Chart3Data> data = new ArrayList<>();
        data.add(new Chart3Data("DEG", "R", map.get(deg)));
        data.add(new Chart3Data("ASE", "R", map.get(ase)));
        data.add(new Chart3Data("ASE-GPU", "R", map.get(paean)));
        data.add(new Chart3Data("CircleRNA", "R", map.get(circrna)));
        data.add(new Chart3Data("SmartSeq", "R", map.get(scrnasmartseq)));
        data.add(new Chart3Data("Somatic-SNV", "D", map.get(somatic)));
        data.add(new Chart3Data("Somatic-CNV", "D", map.get(somatic_cnvs)));
        data.add(new Chart3Data("Germline", "D", map.get(germline)));
        data.add(new Chart3Data("WGBS", "M", map.get(wgbs)));
        data.add(new Chart3Data("BeadChip", "M", map.get(methychip)));
        data.add(new Chart3Data("scRNA-Seq", "SC", map.get(scrnaseq)));
        data.add(new Chart3Data("stRNA-Seq", "SP", map.get(strnaseq)));
        data.add(new Chart3Data("Proteomics", "P", map.get(proteomics)));
        Map<String, List<Chart3Data>> result = data.stream().collect(Collectors.groupingBy(Chart3Data::getGroup, LinkedHashMap::new, Collectors.toList()));
        return result;
    }

    private Object chartData4() {
        List<String> abbr = CollUtil.newArrayList("R", "D", "M", "SC", "SP", "P");
        List<TaskVO> vo = getAllTaskVO().stream()
                .map(x -> {
                    if (StrUtil.equalsAnyIgnoreCase(x.getAnalysisType(), deg, ase, circrna, paean, scrnasmartseq)) {
                        x.setAnalysisType("R");
                    }
                    if (StrUtil.equalsAnyIgnoreCase(x.getAnalysisType(), somatic, somatic_cnvs, germline)) {
                        x.setAnalysisType("D");
                    }
                    if (StrUtil.equalsAnyIgnoreCase(x.getAnalysisType(), methychip, wgbs)) {
                        x.setAnalysisType("M");
                    }
                    if (StrUtil.equalsAnyIgnoreCase(x.getAnalysisType(), scrnaseq)) {
                        x.setAnalysisType("SC");
                    }
                    if (StrUtil.equalsAnyIgnoreCase(x.getAnalysisType(), strnaseq)) {
                        x.setAnalysisType("SP");
                    }
                    if (StrUtil.equalsAnyIgnoreCase(x.getAnalysisType(), proteomics)) {
                        x.setAnalysisType("P");
                    }

                    return x;
                }).filter(x -> StrUtil.equalsAny(x.getAnalysisType(), "R", "D", "M", "SC", "SP", "P"))
                .sorted(Comparator.comparing(TaskVO::getCreateTime)).collect(Collectors.toList());


        // 求出每个季度的数据量
        Map<String, Map<String, Long>> map = new LinkedHashMap<>();
        for (String s : abbr) {
            map.put(s, null);
        }
        TreeSet<String> xVal = new TreeSet<>();
        for (TaskVO taskVO : vo) {
            Date createTime = taskVO.getCreateTime();
            int year = DateUtil.year(createTime);
            int quarter = DateUtil.quarter(createTime);
            xVal.add(year + "Q" + quarter);
        }

        for (TaskVO taskVO : vo) {
            String outKey = taskVO.getAnalysisType();
            Map<String, Long> value = map.get(outKey);
            if (value == null) {
                value = new TreeMap<>();
                for (String s : xVal) {
                    value.put(s, 0L);
                }
            }
            Date createTime = taskVO.getCreateTime();
            int year = DateUtil.year(createTime);
            int quarter = DateUtil.quarter(createTime);
            String innerKey = year + "Q" + quarter;
            Long innerValue = value.get(innerKey);
            if (innerValue == null) {
                innerValue = 0L;
            } else {
                innerValue = innerValue + taskVO.getInputDataSize();
            }
            value.put(innerKey, innerValue);

            map.put(outKey, value);
        }

        // 求出每个季度累计之前季度的数据量
        Map<String, Map<String, Long>> newMap = new LinkedHashMap<>();
        for (Map.Entry<String, Map<String, Long>> entry : map.entrySet()) {
            String outKey = entry.getKey();
            Map<String, Long> outValue = entry.getValue();
            Map<String, Long> newValue = new TreeMap<>();
            for (Map.Entry<String, Long> innerEntry : outValue.entrySet()) {
                String innerKey = innerEntry.getKey();
                newValue.put(innerKey, get(outValue, innerKey) + innerEntry.getValue());
            }
            newMap.put(outKey, newValue);
        }

        // 把数据单位转为GB
        Map<String, Map<String, String>> resultMap = new LinkedHashMap<>();
        for (Map.Entry<String, Map<String, Long>> entry : newMap.entrySet()) {

            Map<String, String> newValue = new TreeMap<>();
            for (Map.Entry<String, Long> innerEntry : entry.getValue().entrySet()) {
                String innerKey = innerEntry.getKey();
                newValue.put(innerKey, NumberUtil.roundStr(innerEntry.getValue() / 1024 / 1024 / 1024, 0));
            }
            resultMap.put(entry.getKey(), newValue);
        }

        return resultMap;
    }

    private Long get(Map<String, Long> value, String innerKey) {
        long count = 0L;

        for (Map.Entry<String, Long> entry : value.entrySet()) {
            int i = entry.getKey().compareTo(innerKey);
            if (i < 0) {
                count = count + entry.getValue();
            }
        }
        return count;
    }

    private List<TaskVO> getAllTaskVO() {
        String modules = appProperties.getModules();

        String[] split = modules.split(";");

        ArrayList<String> sqlList = new ArrayList<>();
        if (ArrayUtil.contains(split, "rnaseq")) {
            sqlList.add(getSql(deg, "rnaseq", "rna_seq_task"));
        }
        if (ArrayUtil.contains(split, "ase")) {
            sqlList.add(getSql(ase, "ase", "ase_task"));
        }
        if (ArrayUtil.contains(split, "circrna")) {
            sqlList.add(getSql(circrna, "circrna", "circrna_task"));
        }
        if (ArrayUtil.contains(split, "paean")) {
            sqlList.add(getSql(paean, "paean", "paean_task"));
        }
        if (ArrayUtil.contains(split, "scrnaseq")) {
            sqlList.add(getSql(scrnaseq, "scrnaseq", "scrnaseq_genomics_task"));
        }
        if (ArrayUtil.contains(split, "scrnasmartseq")) {
            sqlList.add(getSql(scrnasmartseq, "scrnaSmartseq", "scrna_smartseq_task"));
        }
        if (ArrayUtil.contains(split, "strnaseq")) {
            sqlList.add(getSql(strnaseq, "strnaseq", "strnaseq_task"));
        }
        if (ArrayUtil.contains(split, "somatic")) {
            sqlList.add(getSql(somatic, "somatic", "somatic_task"));
            sqlList.add(getSql(somatic_cnvs, "somatic-cnvs", "somatic_cnvs_task"));
        }
        if (ArrayUtil.contains(split, "germline")) {
            sqlList.add(getSql(germline, "germline", "germline_task"));
        }
        if (ArrayUtil.contains(split, "methychip")) {
            sqlList.add(getSql(methychip, "methychip", "methy_beadchip_task"));
        }
        if (ArrayUtil.contains(split, "wgbs")) {
            sqlList.add(getSql(wgbs, "wgbs", "wgbs_task"));
        }
        if (ArrayUtil.contains(split, "proteomics")) {
            sqlList.add(getSql(proteomics, "proteomics", "proteomics_task"));
        }

        String unionSql = StrUtil.format("select m.analysis_type,m.url,m.id,m.task_id, case when u.user_name is null then m.user_id else u.user_name end as user_id ,m.input_data_size,m.status,m.create_time,m.update_time,m.use_time from ({}) as m LEFT JOIN cas_user AS u ON m.user_id = u.id", CollUtil.join(sqlList, " UNION ALL "));

        String finalSql = StrUtil.format("select * from ({}) as subQuery where subQuery.status != '0'", unionSql);

        List<TaskVO> result = jdbcTemplate.query(finalSql, new RowMapper() {
            @Override
            public Object mapRow(ResultSet rs, int i) throws SQLException {
                TaskVO taskVO = new TaskVO();
                taskVO.setAnalysisType(rs.getString("analysis_type"));
                taskVO.setUrl(rs.getString("url"));
                taskVO.setId(rs.getString("id"));
                taskVO.setTaskId(rs.getString("task_id"));
                taskVO.setUserId(rs.getString("user_id"));
                taskVO.setInputDataSize(rs.getLong("input_data_size"));
                taskVO.setStatus(rs.getString("status"));
                taskVO.setCreateTime(rs.getTimestamp("create_time"));
                taskVO.setUpdateTime(rs.getTimestamp("update_time"));
                taskVO.setUseTime(rs.getString("use_time"));
                return taskVO;
            }
        });
        return result;
    }

    @Data
    @AllArgsConstructor
    class Chart3Data {
        private String name;
        private String group;
        private Long value;
    }
}
