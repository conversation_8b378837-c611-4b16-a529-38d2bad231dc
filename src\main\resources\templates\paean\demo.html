<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-paean-demo')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Paean</h4>
                <h5 class="text-primary">Instructions</h5>
                <div class="row mb-3">
                    <div class="col-lg-4">
                        <p>
                            <span class="badge badge-primary">Step 0.</span> <br>Upload your <b>BAM</b> files with <<b>SUBMIT DATA</b>>.<br>
                            <span class="badge badge-primary">Step 1.</span> <br>Click <<b>Start Analysis</b>> from the left column and select one or several BAMs from
                            your uploaded data.<br>
                            <span class="badge badge-primary">Step 2.</span> <br>If your BAM consists of pair-end reads, choose <<b>Pair-end</b>>, else choose <<b>Single-end</b>><br>
                            <span class="badge badge-primary">Step 3.</span> <br>We recommend you to use default <b>Paean</b>’s annotations which we prepared for the <b>Hg38</b>
                            reference genome.<br>
                            <span class="badge badge-primary">Step 4.</span> <br>Click <<b>Start Your Paean job!</b>> to start your <b>Paean</b> tasks.<br>
                            <span class="badge badge-primary">Step 5.</span> <br>Switch to <<b>Task List</b>> tab and inspect your task states. If your tasks were done,
                            you could view their results.<br>
                            <span class="badge badge-primary">Step 6.</span> <br>Download your results.
                        </p>
                    </div>
                    <div class="col-md-8">
                        <img th:src="@{/images/paean-help-1.png}" alt="">
                        <img th:src="@{/images/paean-help-3.png}" alt="">
                    </div>
                </div>
                <h5 class="text-primary">Appendix</h5>
                <div class="d-flex flex-column">
                    <div>Here we present a <b>Paean</b> demo which analyzed a 30GB BAM. You could generate this BAM by mapping origin SRR file from <b>SRR56344</b> (<a
                            href="https://trace.ncbi.nlm.nih.gov/Traces/sra/?run=SRR536344" target="_blank">https://trace.ncbi.nlm.nih.gov/Traces/sra/?run=SRR536344</a>) to the <b>Hg38</b> reference genome. We analyzed this input with <<b>Pair-end</b>> mode and default <b>Paean</b>’s annotations.
                        You can download our pre-computed result with the following link(<a th:href="@{/analysis/paean/downloadDemo}"><i class="fa fa-download"></i></a>).
                    </div>
                </div>
                <hr>
                <div class="text-center"><a th:href="@{/analysis/paean/form}" class="btn btn-secondary">Return</a>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
</th:block>
</html>
