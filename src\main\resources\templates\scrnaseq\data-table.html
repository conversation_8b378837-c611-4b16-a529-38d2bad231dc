<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/all}">Tree</a>
                            <a th:href="@{/analysis/scrnaseq/filter}" class="active">Data list</a>
                            <a th:href="@{/analysis/scrnaseq/report}">Report list</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade">
                            <form th:action="@{/analysis/scrnaseq/filter}" id="search-form" class="form-inline form-task mt-2">
                                <div class="d-flex">
                                    <label class="mx-2 font-12">Type</label>
                                    <select name="type" class="custom-select custom-select-sm">
                                        <option selected="">Select</option>
                                        <option th:selected="${search.type == 'genomics'}" value="genomics">10X Genomics</option>
                                        <option th:selected="${search.type == 'baseline'}" value="baseline">Cluster baseline</option>
                                        <option th:selected="${search.type == 'paga'}" value="paga">PAGA & Pseudotime trajectory</option>
                                        <option th:selected="${search.type == 'deg'}" value="deg">DEG & GSVA</option>
                                        <option th:selected="${search.type == 'genes'}" value="genes">Genes</option>
                                    </select>
                                    <label class="mx-2 font-12">Task ID</label>
                                    <input name="taskId" type="text" class="form-control form-control-sm width-100">
                                    <label class="mx-2 font-12">Time</label>
                                    <div class="input-daterange input-group">
                                        <input autocomplete="off" type="text" class="form-control form-control-sm" name="start"
                                               th:value="${search.start==null?'':#calendars.format(search.start,'yyyy-MM-dd')}"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                        </div>
                                        <input autocomplete="off" type="text" class="form-control form-control-sm" name="end"
                                               th:value="${search.end==null?'':#calendars.format(search.end,'yyyy-MM-dd')}"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex mx-auto">
                                    <button type="submit" class="btn btn-primary btn-sm m-2 _submit_btn">Search</button>
                                </div>
                            </form>
                            <div th:unless="${#strings.isEmpty(search.type)}" class="table-responsive" th:switch="${search.type}">
                                <table th:case="'genomics'" class="table table-hover table-striped table-nowrap font-12 m-0">
                                    <thead>
                                    <tr>
                                        <th scope="col">Task ID</th>
                                        <th scope="col">Start time</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Status time</th>
                                        <th scope="col">Consuming</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <th:block th:unless="${pageResult.content.isEmpty()}">
                                        <tr th:each="item : ${pageResult.content}">
                                            <td th:text="${item.genomicsTask.taskId}">200302151050411</td>
                                            <td th:text="${#dates.format(item.genomicsTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td>
                                                <th:block th:switch="${item.genomicsTask.status}">
                                                    <th:block th:case="-1">
                                                        <span class="text-danger"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.genomicsTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="1">
                                                        <span class="text-info"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.genomicsTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="2">
                                                        <span class="text-info"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.genomicsTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="3">
                                                        <span class="text-success"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.genomicsTask.status)}]]</span>
                                                    </th:block>
                                                </th:block>
                                            </td>
                                            <td th:text="${#dates.format(item.genomicsTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:text="${item.genomicsTask.useTime}">26m55s</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a th:href="@{/analysis/scrnaseq/taskDetail(id=${item.genomicsTask.id},type='genomics')}"
                                                       class="text-primary" data-toggle="tooltip" title="View">
                                                        <i class="fa fa-eye font-14"></i>
                                                    </a>
                                                    <a href="javascript:void(0);" class="text-danger" data-toggle="tooltip"
                                                       th:onclick="deleteTask('genomics',[[${item.genomicsTask.id}]])" title="Delete">
                                                        <i class="fa fa-times font-14"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </th:block>
                                    <th:block th:if="${pageResult.content.isEmpty()}">
                                        <tr>
                                            <td colspan="6">no data</td>
                                        </tr>
                                    </th:block>
                                    </tbody>
                                </table>
                                <table th:case="'baseline'" class="table table-hover table-striped table-nowrap font-12 m-0">
                                    <thead>
                                    <tr>
                                        <th scope="col">Task ID</th>
                                        <th scope="col">Start time</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Status time</th>
                                        <th scope="col">Consuming</th>
                                        <th scope="col">scRNA-seq ID</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <th:block th:unless="${pageResult.content.isEmpty()}">
                                        <tr th:each="item : ${pageResult.content}">
                                            <td th:text="${item.baselineTask.taskId}"></td>
                                            <td th:text="${#dates.format(item.baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td>
                                                <th:block th:switch="${item.baselineTask.status}">
                                                    <th:block th:case="-1">
                                                        <span class="text-danger"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.baselineTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="1">
                                                        <span class="text-info"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.baselineTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="2">
                                                        <span class="text-success"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.baselineTask.status)}]]</span>
                                                    </th:block>
                                                </th:block>
                                            </td>
                                            <td th:text="${#dates.format(item.baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:text="${item.baselineTask.useTime}"></td>
                                            <td><a th:href="@{/analysis/scrnaseq/taskDetail(id=${item.genomicsTask.id},type='genomics')}">[[${item.genomicsTask.taskId}]]</a></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a th:href="@{/analysis/scrnaseq/taskDetail(id=${item.baselineTask.id},type='baseline')}"
                                                       class="text-primary" data-toggle="tooltip" title="View">
                                                        <i class="fa fa-eye font-14"></i>
                                                    </a>
                                                    <a href="javascript:void(0);" class="text-danger" data-toggle="tooltip"
                                                       th:onclick="deleteTask('baseline',[[${item.baselineTask.id}]])" title="Delete">
                                                        <i class="fa fa-times font-14"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </th:block>
                                    <th:block th:if="${pageResult.content.isEmpty()}">
                                        <tr>
                                            <td colspan="7">no data</td>
                                        </tr>
                                    </th:block>
                                    </tbody>
                                </table>
                                <table th:case="'paga'" class="table table-hover table-striped table-nowrap font-12 m-0">
                                    <thead>
                                    <tr>
                                        <th scope="col">Task ID</th>
                                        <th scope="col">Start time</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Status time</th>
                                        <th scope="col">Consuming</th>
                                        <th scope="col">Cluster Baseline ID</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <th:block th:unless="${pageResult.content.isEmpty()}">
                                        <tr th:each="item : ${pageResult.content}">
                                            <td th:text="${item.pagaTask.taskId}"></td>
                                            <td th:text="${#dates.format(item.pagaTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td>
                                                <th:block th:switch="${item.pagaTask.status}">
                                                    <th:block th:case="-1">
                                                        <span class="text-danger"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.pagaTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="1">
                                                        <span class="text-info"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.pagaTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="2">
                                                        <span class="text-success"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.pagaTask.status)}]]</span>
                                                    </th:block>
                                                </th:block>
                                            </td>
                                            <td th:text="${#dates.format(item.pagaTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:text="${item.pagaTask.useTime}"></td>
                                            <td><a th:href="@{/analysis/scrnaseq/taskDetail(id=${item.baselineTask.id},type='baseline')}">[[${item.baselineTask.taskId}]]</a></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a th:href="@{/analysis/scrnaseq/taskDetail(id=${item.pagaTask.id}, type='paga')}"
                                                       class="text-primary" data-toggle="tooltip" title="View">
                                                        <i class="fa fa-eye font-14"></i>
                                                    </a>
                                                    <a href="javascript:void(0);" class="text-danger" data-toggle="tooltip"
                                                       th:onclick="deleteTask('paga',[[${item.pagaTask.id}]])" title="Delete">
                                                        <i class="fa fa-times font-14"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </th:block>
                                    <th:block th:if="${pageResult.content.isEmpty()}">
                                        <tr>
                                            <td colspan="7">no data</td>
                                        </tr>
                                    </th:block>
                                    </tbody>
                                </table>
                                <table th:case="'deg'" class="table table-hover table-striped table-nowrap font-12 m-0">
                                    <thead>
                                    <tr>
                                        <th scope="col">Task ID</th>
                                        <th scope="col">Start time</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Status time</th>
                                        <th scope="col">Consuming</th>
                                        <th scope="col">Cluster Baseline ID</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <th:block th:unless="${pageResult.content.isEmpty()}">
                                        <tr th:each="item : ${pageResult.content}">
                                            <td th:text="${item.degTask.taskId}"></td>
                                            <td th:text="${#dates.format(item.degTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td>
                                                <th:block th:switch="${item.degTask.status}">
                                                    <th:block th:case="-1">
                                                        <span class="text-danger"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.degTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="1">
                                                        <span class="text-info"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.degTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="2">
                                                        <span class="text-success"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.degTask.status)}]]</span>
                                                    </th:block>
                                                </th:block>
                                            </td>
                                            <td th:text="${#dates.format(item.degTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:text="${item.degTask.useTime}"></td>
                                            <td><a th:href="@{/analysis/scrnaseq/taskDetail(id=${item.baselineTask.id},type='baseline')}">[[${item.baselineTask.taskId}]]</a></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a th:href="@{/analysis/scrnaseq/taskDetail(id=${item.degTask.id}, type='deg')}"
                                                       class="text-primary" data-toggle="tooltip" title="View">
                                                        <i class="fa fa-eye font-14"></i>
                                                    </a>
                                                    <a href="javascript:void(0);" class="text-danger" data-toggle="tooltip"
                                                       th:onclick="deleteTask('deg',[[${item.degTask.id}]])" title="Delete">
                                                        <i class="fa fa-times font-14"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </th:block>
                                    <th:block th:if="${pageResult.content.isEmpty()}">
                                        <tr>
                                            <td colspan="7">no data</td>
                                        </tr>
                                    </th:block>
                                    </tbody>
                                </table>
                                <table th:case="'genes'" class="table table-hover table-striped table-nowrap font-12 m-0">
                                    <thead>
                                    <tr>
                                        <th scope="col">Task ID</th>
                                        <th scope="col">Start time</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Status time</th>
                                        <th scope="col">Consuming</th>
                                        <th scope="col">Cluster Baseline ID</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <th:block th:unless="${pageResult.content.isEmpty()}">
                                        <tr th:each="item : ${pageResult.content}">
                                            <td th:text="${item.genesTask.taskId}"></td>
                                            <td th:text="${#dates.format(item.genesTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td>
                                                <th:block th:switch="${item.genesTask.status}">
                                                    <th:block th:case="-1">
                                                        <span class="text-danger"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.genesTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="1">
                                                        <span class="text-info"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.genesTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="2">
                                                        <span class="text-success"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.genesTask.status)}]]</span>
                                                    </th:block>
                                                </th:block>
                                            </td>
                                            <td th:text="${#dates.format(item.genesTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:text="${item.genesTask.useTime}"></td>
                                            <td><a th:href="@{/analysis/scrnaseq/taskDetail(id=${item.baselineTask.id},type='baseline')}">[[${item.baselineTask.taskId}]]</a></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a th:href="@{/analysis/scrnaseq/taskDetail(id=${item.genesTask.id}, type='genes')}"
                                                       class="text-primary" data-toggle="tooltip" title="View">
                                                        <i class="fa fa-eye font-14"></i>
                                                    </a>
                                                    <a href="javascript:void(0);" class="text-danger" data-toggle="tooltip"
                                                       th:onclick="deleteTask('paga',[[${item.genesTask.id}]])" title="Delete">
                                                        <i class="fa fa-times font-14"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </th:block>
                                    <th:block th:if="${pageResult.content.isEmpty()}">
                                        <tr>
                                            <td colspan="7">no data</td>
                                        </tr>
                                    </th:block>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pt-1 mb-2">
                                <div th:replace="~{base/pageable}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script>
        $(document).ready(function () {
            $('.input-daterange').datepicker({
                format: "yyyy-mm-dd",
                toggleActive: true,
                autoclose: true,
                todayHighlight: true
            });
        })

        function deleteTask(type, id) {
            layer.confirm('<p class="text-center">确定删除吗？</p>', {btn: ['确认', '取消']}, function () {
                var loadLayerIndex;
                $.ajax({
                    url: "/analysis/scrnaseq/deleteTask",
                    data: {"id": id, "type": type},
                    dataType: 'json',
                    async: false,
                    method: 'post',
                    beforeSend: function () {
                        loadLayerIndex = layer.load(1, {
                            shade: [0.1, '#fff'] //0.1透明度的白色背景
                        });
                    },
                    success: function (result) {
                        if (result.success) {
                            layer.msg("Deletion Succeeded", {time: 500}, function () {
                                location.reload();
                            });
                        } else {
                            layer.alert(result.message, {icon: 2});
                        }
                    },
                    complete: function () {
                        layer.close(loadLayerIndex);
                    }
                });
            });
        }
    </script>
</th:block>
</html>
