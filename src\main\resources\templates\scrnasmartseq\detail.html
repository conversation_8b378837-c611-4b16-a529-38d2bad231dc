<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-scrnasmartseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Smart-Seq</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                </div>
                <th:block th:switch="${vo.task.status}">
                    <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Analysis Error</p>
                    </div>

                    <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Prepared</p>
                    </div>
                    <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="7">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Analysis done</p>
                    </div>

                    <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Data prepared</p>
                    </div>
                </th:block>

                <ul class="detail-info row mb-2">
                    <li class="col-xl-4 col-lg-6"><span class="text-muted">TaskID：</span>
                        <div th:text="${vo.task.taskId}">AA20200722001</div>
                    </li>
                    <li class="col-xl-4 col-lg-6"><span class="text-muted">StartTime：</span>
                        <div th:text="${#dates.format(vo.task.createTime,'yyyy-MM-dd HH:mm:ss')}">2020-07-22</div>
                    </li>

                    <th:block th:if="${vo.task.status== 7}">
                        <li class="col-xl-4 col-lg-6"><span class="text-muted">Consuming：</span>
                            <div th:text="${vo.task.useTime}">分</div>
                        </li>
                    </th:block>
                </ul>
                <div class="form-group-box">
                    <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                    <div class="collapse show" id="coll-1">
                        <div class="pt-2 pl-2">
                            <div class="result-box">
                                <h6 class="border-bottom pb-2 m-0">Select Sample</h6>
                                <div class="table-responsive mb-3">
                                    <table class="table table-bordered table-sm table-middle mb-0">
                                        <thead class="thead-light">
                                        <tr>
                                            <td width="200" class="text-center text-nowrap">Sample name</td>
                                            <td class="text-center">Select file</td>
                                            <td width="200" class="text-center text-nowrap">Group</td>
                                            <td width="150" class="text-center text-nowrap">Batch</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="input : ${vo.inputs}">
                                            <td class="text-center" th:text="${input.sample}"></td>
                                            <td class="text-center">
                                                <div class="d-flex justify-content-center align-items-center"
                                                     th:each="file : ${input.files}">
                                                    [[${file.name}]]<span class="badge badge-secondary ml-1"><em
                                                        th:text="${file.size}">412.96K</em></span>
                                                </div>
                                            </td>
                                            <td class="text-center" th:text="${input.group}"></td>
                                            <td class="text-center" th:text="${input.batch}"></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Quality Control</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method: </label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary"
                                                                                  th:text="${vo.task.qcMethod}">Trimmomatic</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${vo.task.species == 'human'}">Homo sapiens（human）</span>
                                        <span class="text-primary" th:if="${vo.task.species == 'mouse'}">Mus musculus（mouse）</span>
                                    </div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Version：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${vo.task.specVersion}">GRCh38</span>
                                    </div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${vo.task.mappingMethod}">STAR</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Counting</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${vo.task.countingMethod}">STAR</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group-box" th:if="${vo.task.status == 7}"><!--th:if="${vo.task.status == 7}"-->
                    <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                    <div class="collapsed show" id="coll-2">
                        <div class="pt-2 pl-3">
                            <div>
                                <span class="text-primary h6">Expression Matrix</span>
                                <a th:href="@{/analysis/scrnaSmartseq/download(taskId=${vo.task.taskId})}"
                                   class="btn btn-link btn-sm text-nowrap ml-3"><i
                                        class="fa fa-download mr-1"></i>Download</a>
                            </div>
                            <div>
                                <span class="text-primary h6">Statistics</span>
                                <div class="text-center">
                                    <h6 class="text-center">Distribution of Gene Expression</h6>
                                    <img  th:src="@{/images/result.png}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <a th:href="@{/analysis/scrnaSmartseq/list}" class="btn btn-outline-secondary"><span>Back</span></a>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>
</th:block>
</html>
