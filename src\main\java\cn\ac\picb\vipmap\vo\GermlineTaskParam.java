package cn.ac.picb.vipmap.vo;

import cn.ac.picb.germline.vo.GermlineTaskInput;
import cn.ac.picb.somatic.vo.SomaticFileVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GermlineTaskParam {

    @NotEmpty
    private List<GermlineTaskInput> input;

    @NotBlank
    private String qcMethod;

    @NotBlank
    private String refVersion;

    @NotBlank
    private String mappingMethod;

    @NotBlank
    private String varianceMethod;

    @NotBlank
    private String filteredMethod;

    @NotBlank
    private String exonBed;

    private SomaticFileVO exonFileVo;

    @NotBlank
    private String annoMethod;
}
