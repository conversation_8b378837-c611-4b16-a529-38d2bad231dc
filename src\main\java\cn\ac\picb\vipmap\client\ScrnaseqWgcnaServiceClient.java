package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnaseq.client.ScrnaseqWgcnaServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqWgcnaServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "scrnaseq-service", contextId = "wgcna", fallback = ScrnaseqWgcnaServiceClientFallback.class)
public interface ScrnaseqWgcnaServiceClient extends ScrnaseqWgcnaServiceApi {
}
