<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/bootstrap-tagsinput.css}">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq_10X</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/all}" class="active">Tree</a>
                            <a th:href="@{/analysis/scrnaseq/filter}">Data list</a>
                            <a th:href="@{/analysis/scrnaseq/report}">Report list</a>
                        </div>
                        <div>
                            <a href="javascript:void(0)" onclick="newTask('genomics', '')" class="btn btn-primary btn-sm">New task</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade" id="tabs-a01">
                            <div class="p-2">
                                <div id="tree" class="fancytree-colorize-hover fancytree-fade-expander"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="form-content" style="display: none"></div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script>
        $("#tree").fancytree({
            checkbox: false,
            selectMode: 3,
            source: {
                url: '/analysis/scrnaseq/treeData',
                data: {type: ''}
            },
            lazyLoad: function (event, data) {
                var node = data.node;
                data.result = {
                    url: "/analysis/scrnaseq/treeData",
                    data: {type: node.data.type, parentId: node.data.id}
                }
            },
            renderTitle: function (event, tree) {
                return generateTitle(tree.node);
            },
            toggleEffect: {
                effect: "blind",
                options: {
                    direction: "vertical",
                    scale: "box"
                },
                duration: 200
            }
        });

        function generateTitle(node) {
            if (!node || !node.data || !node.data.nodeType) {
                return '';
            }
            var data = node.data;
            var nodeType = data.nodeType;
            var html = [];

            var map = {
                '-1': "<span class='text-danger'><i class='fa fa-circle'></i>Error</span>&nbsp;&nbsp;",
                '0': '<span class="text-info"><i class="fa fa-circle"></i>Deleted</span>&nbsp;&nbsp;',
                '1': '<span class="text-info"><i class="fa fa-circle"></i>Prepared</span>&nbsp;&nbsp;',
                '2': '<span class="text-info"><i class="fa fa-circle"></i>Import data</span>&nbsp;&nbsp;',
                '3': '<span class="text-success"><i class="fa fa-circle"></i>Analysis done</span>&nbsp;&nbsp;'
            };

            var statusMap = {
                '-1': "<span class='text-danger'><i class='fa fa-circle'></i>Error</span>&nbsp;&nbsp;",
                '0': '<span class="text-info"><i class="fa fa-circle"></i>Deleted</span>&nbsp;&nbsp;',
                '1': '<span class="text-info"><i class="fa fa-circle"></i>Prepared</span>&nbsp;&nbsp;',
                '2': '<span class="text-success"><i class="fa fa-circle"></i>Analysis done</span>&nbsp;&nbsp;',
            }

            switch (nodeType) {
                case "genomics":
                    html.push('10X Genomics &nbsp;&nbsp;<span class="badge badge-primary font-weight-normal">' + data.taskId + '</span>&nbsp;&nbsp;');
                    html.push(map[data.status]);
                    if (data.status === 4) {
                        html.push('<a href="javascript:void(0)" onclick="newTask(\'baseline\', \'' + data.id + '\')" class="text-info" title="Cluster baseline"><i class="fa fa-sliders font-14"></i></a>&nbsp;&nbsp;');
                    }
                    html.push('<a href="javascript:void(0)" onclick="viewTask(\'genomics\', \'' + data.id + '\')" class="text-primary" title="View"><i class="fa fa-eye font-14"></i></a>&nbsp;&nbsp;');
                    html.push("<a href='javascript:void(0);' onclick='deleteTask(\"genomics\", \"" + data.id + "\")' class='text-danger' title='Delete'><i class='fa fa-times font-14'></i></a>");
                    break;
                case "baseline":
                    html.push("baseline &nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>" + data.taskId + "</span>&nbsp;&nbsp;");
                    html.push(statusMap[data.status]);
                    html.push("<a href='javascript:void(0);' class='text-info dropdown-toggle' data-toggle='dropdown' title='More'><i class='fa fa-sliders font-14'></i></a>");

                    if (data.status === 2) {
                        html.push("<div class='dropdown-menu dropdown-menu-right'>");
                        html.push("<a class='dropdown-item' href='javascript:void(0)' onclick='newTask(\"paga\", \"" + data.id + "\")'>PAGA & Pseudotime trajectory</a>");
                        html.push("<a class='dropdown-item' href='javascript:void(0)' onclick='newTask(\"deg\", \"" + data.id + "\")'> DEG & GSVA </a>");
                        html.push("<a class='dropdown-item' href='javascript:void(0)' onclick='newTask(\"genes\", \"" + data.id + "\")'>Genes</a>");
                        html.push("</div>&nbsp;&nbsp;");
                    }

                    html.push("<a href='javascript:void(0)' onclick='viewTask(\"baseline\", \"" + data.id + "\")' class='text-primary' title='View'><i class='fa fa-eye font-14'></i></a>&nbsp;&nbsp;");
                    html.push("<a href='javascript:void(0);' onclick='deleteTask(\"baseline\", \"" + data.id + "\")' class='text-danger' title='Delete'><i class='fa fa-times font-14'></i></a>");
                    break;
                case "paga":
                    html.push("Graph and trajectory &nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>" + data.taskId + "</span>&nbsp;&nbsp;");
                    html.push(statusMap[data.status]);
                    html.push("<a href='javascript:void(0);' onclick='viewTask(\"paga\", \"" + data.id + "\")' class='text-primary' title='View'><i class='fa fa-eye font-14'></i></a>&nbsp;&nbsp;");
                    html.push("<a href='javascript:void(0);' onclick='deleteTask(\"paga\", \"" + data.id + "\")' class='text-danger' title='Delete'><i class='fa fa-times font-14'></i></a>");
                    break;
                case "deg":
                    html.push("DEG and enrichment &nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>" + data.taskId + "</span>&nbsp;&nbsp;");
                    html.push(statusMap[data.status]);
                    html.push("<a href='javascript:void(0);' onclick='viewTask(\"deg\", \"" + data.id + "\")'  class='text-primary' title='View'><i class='fa fa-eye font-14'></i></a>&nbsp;&nbsp;");
                    html.push("<a href='javascript:void(0);' onclick='deleteTask(\"deg\", \"" + data.id + "\")' class='text-danger'  title='Delete'><i class='fa fa-times font-14'></i></a>");
                    break;
                case "genes":
                    html.push("Expression and correlation &nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>" + data.taskId + "</span>&nbsp;&nbsp;");
                    html.push(statusMap[data.status]);
                    html.push("<a href='javascript:void(0);' onclick='viewTask(\"genes\", \"" + data.id + "\")' class='text-primary' title='View'><i class='fa fa-eye font-14'></i></a>&nbsp;&nbsp;");
                    html.push("<a href='javascript:void(0);' onclick='deleteTask(\"genes\", \"" + data.id + "\")' class='text-danger'  title='Delete'><i class='fa fa-times font-14'></i></a>");
                    break;
                default:
                    html.push(node.title);
                    break;
            }
            return html.join('');
        }

        $("#tree").tooltip({
            content: function () {
                return $(this).attr("title");
            }
        });

        function newTask(type, parentId) {
            $.ajax({
                url: '/analysis/scrnaseq/form',
                data: {
                    type: type,
                    parentId: parentId
                },
                success: function (res) {
                    $("#form-content").html(res);
                    if (($("#username").length > 0) && ($("#password").length > 0)) {
                        window.location.reload();
                    }
                    $("#form-content").show();
                }
            })
        }

        function viewTask(type, id) {
            var _context_path = $("meta[name='_context_path']").attr("content");
            var url = $.trim(_context_path) + '/analysis/scrnaseq/taskDetail?type=' + type + "&id=" + id;
            window.open(url)
        }

        function deleteTask(type, id) {
            layer.confirm('<p class="text-center">Are you sure you want to delete it？</p>', {btn: ['确认', '取消']}, function () {
                var loadLayerIndex;
                $.ajax({
                    url: "/analysis/scrnaseq/deleteTask",
                    data: {"id": id, "type": type},
                    dataType: 'json',
                    async: false,
                    method: 'post',
                    beforeSend: function () {
                        loadLayerIndex = layer.load(1, {
                            shade: [0.1, '#fff'] //0.1透明度的白色背景
                        });
                    },
                    success: function (result) {
                        if (result.success) {
                            layer.msg("Deleted success", {time: 500}, function () {
                                var tree = $.ui.fancytree.getTree("#tree");
                                tree.getActiveNode().remove();
                            });
                        } else {
                            layer.alert(result.message, {icon: 2});
                        }
                    },
                    complete: function () {
                        layer.close(loadLayerIndex);
                    }
                });
            });
        }

        function addNewNode(node) {
            console.info(node);
            var tree = $.ui.fancytree.getTree("#tree");
            var type = node.data.type || '';

            var parentNode;
            switch (type) {
                case "genomics":
                    parentNode = tree.getRootNode();
                    break;
                case "baseline":
                    parentNode = tree.findFirst(function (item) {
                        console.info(item);
                        return item.data.id === node.data.parentId;
                    });
                    break;
                case "paga":
                    parentNode = tree.findFirst(function (item) {
                        return item.data.id === node.data.parentId && item.data.nodeType === 'folder' && item.data.type === 'paga';
                    });
                    break;
                case "deg":
                    parentNode = tree.findFirst(function (item) {
                        return item.data.id === node.data.parentId && item.data.nodeType === 'folder' && item.data.type === 'deg';
                    });
                    break;
                case "genes":
                    parentNode = tree.findFirst(function (item) {
                        return item.data.id === node.data.parentId && item.data.nodeType === 'folder' && item.data.type === 'genes';
                    });
                    break;
                default:
                    break;
            }
            if (parentNode) {
                parentNode.addChildren(node);
            }
            layer.msg('submit success');
            $("#form-content").hide();
            $("#form-content").html('');
        }
    </script>
</th:block>
</html>
