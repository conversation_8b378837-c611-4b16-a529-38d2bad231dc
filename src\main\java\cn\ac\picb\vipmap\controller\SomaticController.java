package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.somatic.dto.SomaticTaskDTO;
import cn.ac.picb.somatic.enums.SomaticTaskStatus;
import cn.ac.picb.somatic.vo.SomaticTaskIdVO;
import cn.ac.picb.somatic.vo.SomaticValidateResultVO;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.SomaticService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.SomaticTaskParam;
import cn.ac.picb.vipmap.vo.SomaticTaskSearchVO;
import cn.ac.picb.vipmap.vo.SomaticTaskVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/analysis/somatic")
@RequiredArgsConstructor
public class SomaticController {

    private final SomaticService somaticService;
    private final AppProperties appProperties;

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") SomaticTaskSearchVO queryVO, @ModelAttribute PageParam pageParam, Model model) {
        PageResult<SomaticTaskDTO> pageResult = somaticService.findPage(user, queryVO, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = SomaticTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);

        SomaticTaskIdVO somaticTaskIdVO = somaticService.findSomaticIdInfo(user.getId());
        model.addAttribute("somaticTaskIdVO", somaticTaskIdVO);
        return "somatic/list";
    }

    @RequestMapping("/form")
    public String form() {
        return "somatic/form";
    }

    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated SomaticTaskParam param) {
        String taskId = somaticService.createTask(user, param);
        return success(taskId);
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        somaticService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        SomaticTaskVO vo = somaticService.findTaskVO(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = SomaticTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "somatic/detail";
    }

    @RequestMapping("/baseQualityImg")
    public ResponseEntity<byte[]> getBaseQualityImg(String taskId, String name) {
        return somaticService.getBaseQualityImg(taskId, name);
    }

    @RequestMapping("/gcContentImg")
    @SneakyThrows
    public ResponseEntity<byte[]> getGcContentImg(String taskId, String name) {
        return somaticService.getGcContentImg(taskId, name);
    }

    @RequestMapping("/gfcImg")
    @SneakyThrows
    public ResponseEntity<byte[]> getGfcImg(String taskId, String name) {
        return somaticService.getGenomeFractionCoverageImg(taskId, name);
    }

    @RequestMapping("/downloadVcf")
    @ResponseBody
    @SneakyThrows
    public void downloadVcf(String taskId, String runName) {
        Response response = somaticService.downloadVcf(taskId, runName);
        ResponseUtil.download(response);
    }

    @RequestMapping("/downloadAnnotatedFile")
    @ResponseBody
    @SneakyThrows
    public void downloadAnnotatedFile(String taskId, String runName) {
        Response response = somaticService.downloadAnnotatedFile(taskId, runName);
        ResponseUtil.download(response);
    }

    @RequestMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemplate() {
        Response response = somaticService.downloadTemplate();
        ResponseUtil.download(response);
    }

    @RequestMapping("/uploadTemplate")
    @ResponseBody
    public CommonResult<List<SomaticValidateResultVO>> uploadTemplate(CurrentUser user, MultipartFile file) {
        List<SomaticValidateResultVO> vos = somaticService.uploadTemplate(file, user);
        return success(vos);
    }
}
