package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.DownloadUtils;
import cn.ac.picb.file.vo.TreeNode;
import cn.ac.picb.vipmap.service.FileService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.hutool.core.io.IoUtil;
import lombok.SneakyThrows;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
public class IndexController {

    private final FileService indexService;

    public IndexController(FileService indexService) {
        this.indexService = indexService;
    }

    @RequestMapping("/")
    public String index() {
        return "redirect:/home";
    }

    @RequestMapping("/home")
    public String home() {
        return "index";
    }

    @RequestMapping("/scrnaseq")
    public String scrnaseq() {
        return "scrnaseq/introduction";
    }

    @RequestMapping("/ase")
    public String ase() {
        return "ase/introduction";
    }

    @RequestMapping("/circrna")
    public String circrna() {
        return "circrna/introduction";
    }

    @RequestMapping("/scrnasmartseq")
    public String scrnasmartseq() {
        return "scrnasmartseq/introduction";
    }

    @RequestMapping("/paean")
    public String paean() {
        return "paean/introduction";
    }

    @RequestMapping("/rnaseq")
    public String rnaseq() {
        return "rnaseq/introduction";
    }

    @RequestMapping("/somatic")
    public String somatic() {
        return "somatic/introduction";
    }

    @RequestMapping("/somatic-cnvs")
    public String somaticCnvs() {
        return "somatic-cnvs/introduction";
    }

    @RequestMapping("/germline")
    public String germline() {
        return "germline/introduction";
    }

    @RequestMapping("/proteomics")
    public String proteomics() {
        return "proteomics/introduction";
    }

    @RequestMapping("/strnaseq")
    public String strnaseq() {
        return "strnaseq/introduction";
    }

    @RequestMapping("/methychip")
    public String methychip() {
        return "methychip/introduction";
    }

    @RequestMapping("/wgbs")
    public String wgbs() {
        return "wgbs/introduction";
    }

    @RequestMapping("/analysis/fileTree")
    @ResponseBody
    public List<TreeNode> fileTreeNodes(CurrentUser user, String parentPath) {
        return indexService.findFileTreeNodes(user, parentPath);
    }

    @RequestMapping("/guidelines")
    public String guidelines() {
        return "guidelines";
    }

    @RequestMapping("/contactUs")
    public String contactUs() {
        return "contactUs";
    }

    @RequestMapping("/download/scrnaseq")
    @SneakyThrows
    public void scrnaseq(HttpServletRequest request, HttpServletResponse response) {
        ClassPathResource resource = new ClassPathResource("guidelines/ViPMAP_guideline-scRNA_Seq-20201106.pdf");
        InputStream is = resource.getInputStream();
        byte[] bytes = IoUtil.readBytes(is);
        DownloadUtils.download(request, response, bytes, "ViPMAP_guideline-scRNA_Seq-20201106.pdf");
    }

    @RequestMapping("/download/somatic")
    @SneakyThrows
    public void somatic(HttpServletRequest request, HttpServletResponse response) {
        ClassPathResource resource = new ClassPathResource("guidelines/ViPMAP_guideline-somatic-SNV-basic-202107.v1.0.pdf");
        InputStream is = resource.getInputStream();
        byte[] bytes = IoUtil.readBytes(is);
        DownloadUtils.download(request, response, bytes, "ViPMAP_guideline-somatic-SNV-basic-202107.v1.0.pdf");
    }

}
