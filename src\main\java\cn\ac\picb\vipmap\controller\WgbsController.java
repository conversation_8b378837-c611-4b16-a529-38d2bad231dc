package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.WgbsService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.WgbsChartFilter;
import cn.ac.picb.vipmap.vo.WgbsTaskParam;
import cn.ac.picb.vipmap.vo.WgbsTaskSearchVO;
import cn.ac.picb.wgbs.dto.WgbsTaskDTO;
import cn.ac.picb.wgbs.enums.WgbsTaskStatus;
import cn.ac.picb.wgbs.po.WgbsTaskPO;
import cn.ac.picb.wgbs.vo.WgbsTaskInput;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR> Li
 */
@Controller
@RequestMapping("/analysis/wgbs")
@RequiredArgsConstructor
public class WgbsController {

    private final WgbsService wgbsService;
    private final AppProperties appProperties;

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") WgbsTaskSearchVO search, PageParam pageParam, Model model) {
        PageResult<WgbsTaskPO> pageResult = wgbsService.findPage(user, search, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = WgbsTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "wgbs/list";
    }

    @RequestMapping("/form")
    public String form() {
        return "wgbs/form";
    }

    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated WgbsTaskParam param) {
        WgbsTaskPO task = wgbsService.createTask(user, param);
        return success(task.getTaskId());
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        wgbsService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/download")
    @ResponseBody
    public void downloadResult(String taskId, Integer step) {
        Response response = wgbsService.downloadResult(taskId, step, "");
        ResponseUtil.download(response);
    }

    @RequestMapping("/downloadChart")
    @ResponseBody
    public void downloadChart(WgbsChartFilter filter) {
        Response response = wgbsService.downloadChart(filter);
        ResponseUtil.download(response);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        WgbsTaskDTO vo = wgbsService.findTaskVO(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = WgbsTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "wgbs/detail";
    }


    @RequestMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemplate() {
        Response response = wgbsService.downloadTemplate();
        ResponseUtil.download(response);
    }

    @RequestMapping("/uploadTemplate")
    @ResponseBody
    public CommonResult<List<WgbsTaskInput>> uploadTemplate(CurrentUser user, MultipartFile file) {
        List<WgbsTaskInput> vos = wgbsService.uploadTemplate(file, user);
        return success(vos);
    }

    @RequestMapping("/{taskId}/{chartNo}")
    @ResponseBody
    public CommonResult<Object> chartData(@PathVariable("taskId") String taskId, @PathVariable("chartNo") Integer chartNo, WgbsChartFilter filter) {
        Object data = wgbsService.getChartData(taskId, chartNo, filter);
        return success(data);
    }

}
