package cn.ac.picb.vipmap.controller;


import cn.ac.picb.circrna.enums.CircrnaTaskStatus;
import cn.ac.picb.circrna.po.CircrnaTaskPO;
import cn.ac.picb.circrna.vo.CircrnaTaskInput;
import cn.ac.picb.circrna.vo.CircrnaTaskVO;
import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.CircrnaService;
import cn.ac.picb.vipmap.vo.CircrnaTaskParam;
import cn.ac.picb.vipmap.vo.CircrnaTaskSearchVO;
import cn.ac.picb.vipmap.vo.CurrentUser;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR> Li
 * @date 2022/3/1 11:31
 */
@Controller
@RequestMapping("/analysis/circrna")
@RequiredArgsConstructor
public class CircrnaController {

    private final CircrnaService circrnaService;
    private final AppProperties appProperties;

    /**
     * 转跳到ase任务创建页面
     */
    @RequestMapping("/form")
    public String form() {
        return "circrna/form";
    }

    /**
     * 下载模板文件
     */
    @RequestMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemplate() {
        Response response = circrnaService.downloadTemplate();
        ResponseUtil.download(response);
    }

    /**
     * 解析上传的文件
     */
    @RequestMapping("/uploadTemplate")
    @ResponseBody
    public CommonResult<List<CircrnaTaskInput>> uploadTemplate(CurrentUser user, MultipartFile file) {
        List<CircrnaTaskInput> vos = circrnaService.uploadTemplate(file, user);
        return success(vos);
    }

    /**
     * 创建ase任务
     */
    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated CircrnaTaskParam param) {
        CircrnaTaskPO task = circrnaService.createTask(user, param);
        return success(task.getTaskId());
    }

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") CircrnaTaskSearchVO search, PageParam pageParam, Model model) {
        PageResult<CircrnaTaskPO> pageResult = circrnaService.findPage(user, search, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = CircrnaTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "circrna/list";
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        circrnaService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        CircrnaTaskVO vo = circrnaService.findTaskVO(id);
        model.addAttribute("vo", vo);
        Map<Integer, String> codeDescMap = CircrnaTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "circrna/detail";
    }

    @RequestMapping("/download")
    @ResponseBody
    public void downloadResult(String taskId) {
        Response response = circrnaService.downloadResult(taskId, "");
        ResponseUtil.download(response);
    }

}
