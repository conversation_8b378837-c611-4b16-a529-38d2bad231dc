<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('methylation-beadchip-analysis')}"></div>

            <main>
                <h4 class="border-bottom pb-2 mb-3">Methylation BeadChip</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/methychip/form}" class="active">Add Task</a>
                            <a th:href="@{/analysis/methychip/list}">Task List</a>
                        </div>
                    </div>
                    <form id="form">
                        <div class="list-group tab-content">
                            <div class="tab-pane active show fade">
                                <div class="p-2">
                                    <div class="form-group row align-items-center mb-2">
                                        <label class="col-xl-1 text-center col-lg-3 col-md-4 col-form-label pr-0 mr-2 pl-0 ml-2">Task
                                            Name</label>
                                        <div class="col-xl-10 col-lg-9 col-md-8">
                                            <input class="form-control width-300 input-name validate[required]"
                                                   name="taskName"
                                                   type="text"
                                                   onkeyup="value=value.replace(/[^A-Za-z0-9_\s]/ig,'')">
                                        </div>
                                    </div>
                                    <div class="form-group-box">

                                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Select
                                            Sample
                                        </a>
                                        <a href="javascript:void(0);" class="ml-2 h5 text-danger"
                                           data-container="body" data-html="true"
                                           data-trigger="focus" data-toggle="popover"
                                           data-placement="right"
                                           data-content="Required fields: Data_Directory, Sample_Name, Sample_Group, Sentrix_ID, Sentrix_Position."><i
                                                class="fa fa-question-circle"></i></a>

                                        <div class="collapse show" id="coll-1">
                                            <div class="pl-4 pt-2">
                                                <label>Select your input data by fill the following table or upload an
                                                    Excel file that includes the attributes</label>
                                                <div class="d-flex mb-2">
                                                    <input type="file" multiple name="" id="excel"
                                                           class="form-control form-control-sm w-50 mr-2">
                                                    <button type="button" onclick="uploadExcel()"
                                                            class="btn btn-primary btn-sm mr-2 text-nowrap">Upload
                                                    </button>
                                                    <a th:href="@{/analysis/methychip/downloadTemplate}"
                                                       class="btn btn-link btn-sm text-nowrap"><i
                                                            class="fa fa-download mr-1"></i>Download template</a>
                                                </div>
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-middle mb-0">
                                                        <thead class="thead-light">
                                                        <tr>
                                                            <td style="min-width: 150px" class="text-center">
                                                                Data_Directory<span
                                                                    class="text-danger">*<a
                                                                    href="javascript:void(0);"
                                                                    class="text-danger"
                                                                    data-container="body" data-html="true"
                                                                    data-trigger="focus" data-toggle="popover"
                                                                    data-placement="right"
                                                                    data-content="Directory of IDAT data in File Manager System. The name of files in this directory should be [Sentrix_ID]_[Sentrix_Position]_Grn.idat and [Sentrix_ID]_[Sentrix_Position]_Red.idat"><i
                                                                    class="fa fa-question-circle"></i></a></span></td>
                                                            <td style="min-width: 130px" class="text-center">
                                                                Sample_Name<span
                                                                    class="text-danger"><b>*</b></span>
                                                            </td>
                                                            <td style="min-width: 130px" class="text-center">
                                                                Sample_Plate
                                                            </td>
                                                            <td style="min-width: 130px" class="text-center">
                                                                Sample_Group<span
                                                                    class="text-danger"><b>*</b></span>
                                                            </td>
                                                            <td style="min-width: 130px" class="text-center">Pool_ID
                                                            </td>
                                                            <td style="min-width: 130px" class="text-center">Project
                                                            </td>
                                                            <td style="min-width: 130px" class="text-center">
                                                                Sample_Well
                                                            </td>
                                                            <td style="min-width: 130px" class="text-center">
                                                                Sentrix_ID<span
                                                                    class="text-danger"><b>*</b></span>
                                                            </td>
                                                            <td style="min-width: 130px" class="text-center">
                                                                Sentrix_Position<span
                                                                    class="text-danger"><b>*</b></span>
                                                            </td>
                                                            <td style="min-width: 100px"></td>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="sample-table">
                                                        <tr>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="showFileModal(this)">Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend">
                                                                  <span class="input-group-text">
                                                                    <em class="seled"></em>
                                                                  </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center rounded-0 "
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center rounded-0 "
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center group rounded-0 "
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center rounded-0 "
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center rounded-0 "
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center rounded-0 "
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center rounded-0  "
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center rounded-0 "
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td>
                                                                <button type="button" class="btn btn-primary btn-sm"
                                                                        onclick="addRow(this)"><i
                                                                        class="fa fa-plus"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-secondary btn-sm"
                                                                        onclick="removeRow(this)"><i
                                                                        class="fa fa-minus"></i></button>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div><span class="h5 text-danger ml-1 mr-3"><b>*</b></span>Required
                                                    fields
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Microarray Type
                                        </a>
                                        <div class="collapse show" id="coll-2">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">Type</label>
                                                    <div class="col-xl-11 col-lg-9 col-md-8">
                                                        <div class="col-6 custom-control custom-radio custom-control-inline">
                                                            <input type="radio"
                                                                   class="custom-control-input validate[required]"
                                                                   id="450K" name="arrayType" value="450K"
                                                                   checked>
                                                            <label for="450K"
                                                                   class="custom-control-label">Infinium
                                                                HumanMethylation450 BeadChip(450K)</label>
                                                        </div>
                                                        <div class="col-5 custom-control custom-radio custom-control-inline">
                                                            <input type="radio"
                                                                   class="custom-control-input validate[required]"
                                                                   id="EPIC" name="arrayType"
                                                                   value="EPIC">
                                                            <label for="EPIC"
                                                                   class="custom-control-label">Infinium MethylationEPIC
                                                                BeadChip(850K)</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-3" data-toggle="collapse"
                                           class="h5 text-primary">Normalization</a>
                                        <div class="collapse show" id="coll-3">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <div class="col-2 custom-control custom-radio custom-control-inline">
                                                            <input type="radio"
                                                                   class="custom-control-input validate[required]"
                                                                   id="BMIQ"
                                                                   name="normMethod" value="BMIQ" checked>
                                                            <label for="BMIQ" class="custom-control-label">BMIQ</label>
                                                        </div>
                                                        <div class="col-2 custom-control custom-radio custom-control-inline">
                                                            <input type="radio"
                                                                   class="custom-control-input validate[required]"
                                                                   id="SWAN"
                                                                   name="normMethod" value="SWAN">
                                                            <label for="SWAN" class="custom-control-label">SWAN</label>
                                                        </div>
                                                        <div class="col-2 custom-control custom-radio custom-control-inline">
                                                            <input type="radio"
                                                                   class="custom-control-input validate[required]"
                                                                   id="PBC"
                                                                   name="normMethod" value="PBC">
                                                            <label for="PBC" class="custom-control-label">PBC</label>
                                                        </div>
                                                        <div class="col-3 custom-control custom-radio custom-control-inline">
                                                            <input type="radio"
                                                                   class="custom-control-input validate[required]"
                                                                   id="FunctionalNormalize"
                                                                   name="normMethod" value="FunctionalNormalize">
                                                            <label for="FunctionalNormalize"
                                                                   class="custom-control-label">FunctionalNormalize</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-xl-10 col-lg-9 col-md-8"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-4" data-toggle="collapse" class="h5 text-primary">Differentially
                                            Methodlated Probes</a>
                                        <div class="collapse show" id="coll-4">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center">
                                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">p.adjust
                                                        < </label>
                                                    <div class="col-xl-4 col-lg-4 col-md-5">
                                                        <input type="number"
                                                               class="form-control validate[required,custom[number],min[0],max[1]]"
                                                               name="adjPVal"
                                                               value="0.05">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center my-3">
                                        <a href="javascript:void(0)" onclick="submitForm(this)"
                                           class="btn btn-outline-primary btn-custom"><span>Submit</span><i
                                                class="fa fa-long-arrow-right"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
    <div id="file-modal"></div>
</div>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-zh_CN.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>

    <script>
      $('#file-modal').fileModal('/analysis/fileTree', 3)

      $(document).ready(function () {
        $('#species').on('change', function () {
          var group = $('#species').val()
          if (group == 'human') {
            $('#specVersion').html('<option name="GRCh38" value="hg38" selected>GRCh38</option>')
          } else {
            $('#specVersion').html('<option name="GRCm38" value="mm10" selected>GRCm38</option>')
          }
        })

        $('.group').autocomplete({ source: [] })

        $('.group').on('change', function () {
          $('.group').each(function () {
            var group = $(this).val()
            if (/^[0-9]*$/.test(group)) {
              layer.msg('Group cannot use pure numbers')
              return
            }
          })
        })
        // Microarray Type与Normalization联动约束
        $('input[name="arrayType"]').on('change', function () {
          if (this.value === '450K') {
            $('#FunctionalNormalize').attr('disabled', false)
          }

          if (this.value === 'EPIC') {
            $('#FunctionalNormalize').removeAttr('checked')
            $('#FunctionalNormalize').attr('disabled', true)
            $('#BMIQ').attr('checked', 'checked')
            $('#BMIQ').trigger('click')
          }
        })

        validate()
      })

      function validate () {
        $('.input-int').on('change', function () {
          var value = $.trim($(this).val())
          if (value === '') {
            return
          }
          if (!/^[1-9]+[0-9]*]*$/.test(value)) {
            layer.msg('please input number')
            $(this).val('')
          }
        })

        $('.input-double').on('change', function () {
          var value = $.trim($(this).val())
          if (value === '') {
            return
          }
          if (!/^[0-9]+.?[0-9]*]*$/.test(value)) {
            layer.msg('please input number')
            $(this).val('')
          }
        })
      }

      function bindGroupChange () {

        $('.group').on('change', function () {
          $('.group').each(function () {
            var group = $(this).val()
            if (/^[0-9]*$/.test(group)) {
              layer.msg('Group cannot use pure numbers')
              return
            }
          })
          initAutocomplete()
        })
      }

      function initAutocomplete () {
        var instance = $('.group').autocomplete('instance')
        if (instance) {
          instance.destroy()
        }
        var groupOptions = []

        $('.group').autocomplete({ source: groupOptions })
      }

      function addRow (_this) {
        // $('.group').autocomplete('destroy')
        var clone = $(_this).parents('tr:first').clone()
        clone.find('td:eq(0)').find('input:eq(0)').val('')
        clone.find('td:eq(1)').find('input:eq(0)').val('')
        clone.find('td:eq(2)').find('input:eq(0)').val('')
        clone.find('td:eq(3)').find('input:eq(0)').val('')
        clone.find('td:eq(4)').find('input:eq(0)').val('')
        clone.find('td:eq(5)').find('input:eq(0)').val('')
        clone.find('td:eq(6)').find('input:eq(0)').val('')
        clone.find('td:eq(7)').find('input:eq(0)').val('')
        clone.find('td:eq(8)').find('input:eq(0)').val('')
        clone.find('em.seled').html('')
        $(_this).parents('tbody').append(clone)

        // initAutocomplete()
        bindGroupChange()
        validate()
      }

      function removeRow (_this) {
        var rows = $(_this).parents('tbody').find('tr')

        var rowDiv = $(_this).parents('tr')
        if (rows.length > 1) {
          rowDiv.remove()
        } else {
          rowDiv.find('input.sample').val('')
          rowDiv.find('input.group').val('1')
          rowDiv.find('div.select-file').html('')
        }
      }

      var _selectBtn

      function showFileModal (_this) {
        _selectBtn = _this

        var selectIds = []
        $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
          selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
        })
        $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
          selectIds: selectIds,
          allowFolder: function () {
            return true
          },
          func: function (data) {
            return data.folder
          }
        })
      }

      $('#file-modal').on('__SELECT_FILES__', function (e, data) {
        var nodes = data.nodes || []
        if (nodes.length === 0) {
          return
        }
        if (nodes.length > 1) {
          layer.msg('Up to 1 Data Directory in "Select"')
          return
        }

        var html = []
        $.each(nodes, function (i, node) {
          var filePath = node.path
          var fileName = node.name
          var fileSize = node.size
          var isFolder = node.folder

          html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '')
          html.push('<input type="hidden" value="' + filePath + '">')
          html.push('<input type="hidden" value="' + fileName + '">')
          html.push('<input type="hidden" value="' + fileSize + '">')
          html.push('<input type="hidden" value="' + isFolder + '">')
          html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
          html.push('</b>')
        })

        $(_selectBtn).parent().next().find('em.seled:first').html(html.join(''))
        $('[data-toggle="tooltip"]').tooltip()
      })

      function removeFile (_this) {
        $(_this).parent().parent().remove()
      }

      function validateRequired () {

        let dataDirectoryFlag = true
        let sampleNameFlag = true
        let sampleGroupFlag = true
        let sentrixIdFlag = true
        let sentrixPositionFlag = true

        let dataDirectoryArr = []
        let sampleNameArr = []
        let samplePlateArr = []
        let sampleGroupArr = []
        let poolIdArr = []
        let sampleWellArr = []
        let sentrixIdArr = []
        let sentrixPositionArr = []

        var rows = $('#sample-table').find('tr')
        $.each(rows, function (i, item) {
          var dataDirectory = $(item).find('td:eq(0)').find('b.text-primary').find('input:eq(0)').val()
          console.log(dataDirectory)
          var sampleName = $(item).find('td:eq(1)').find('input:eq(0)').val()

          // var samplePlate = $(item).find('td:eq(2)').find('input:eq(0)').val()

          var sampleGroup = $(item).find('td:eq(3)').find('input:eq(0)').val()

          // var poolId = $(item).find('td:eq(4)').find('input:eq(0)').val()

          // var project = $(item).find('td:eq(5)').find('input:eq(0)').val()

          // var sampleWell = $(item).find('td:eq(6)').find('input:eq(0)').val()

          var sentrixId = $(item).find('td:eq(7)').find('input:eq(0)').val()

          var sentrixPosition = $(item).find('td:eq(8)').find('input:eq(0)').val()
          if ($.trim(dataDirectory) === '') {
            dataDirectoryFlag = false
            return false
          }
          dataDirectoryArr.push(dataDirectory)
          if ($.trim(sampleName) === '') {
            sampleNameFlag = false
            return false
          }
          sampleNameArr.push(sampleName)
          if ($.trim(sampleGroup) === '' || /^[0-9]*$/.test(sampleGroup)) {
            sampleGroupFlag = false
            return false
          }
          sampleGroupArr.push(sampleGroup)
          if ($.trim(sentrixId) === '') {
            sentrixIdFlag = false
            return false
          }
          sentrixIdArr.push(sentrixId)
          if ($.trim(sentrixPosition) === '') {
            sentrixPositionFlag = false
            return false
          }
          sentrixPositionArr.push(sentrixPosition)

        })
        // console.log(dataDirectoryFlag, dataDirectoryArr)
        if (!dataDirectoryFlag || dataDirectoryArr.length === 0) {
          layer.msg('data directory can not be empty')
          return false
        }
        if (!sampleNameFlag || sampleNameArr.length === 0) {
          layer.msg('sample name can not be empty')
          return false
        }
        if (!sampleGroupFlag || sampleGroupArr.length === 0) {
          layer.msg('sample group can not be empty or use pure numbers')
          return false
        }
        if (!sentrixIdFlag || sentrixIdArr.length === 0) {
          layer.msg('sentrix id can not be empty')
          return false
        }
        if (!sentrixPositionFlag || sentrixPositionArr.length === 0) {
          layer.msg('sentrix position can not be empty')
          return false
        }
        if (isRepeat(sampleNameArr)) {
          layer.msg('Please check the sample name, the name within the same task needs to be uniquely.')
          return false
        }
        let sampleGroupSet = new Set(sampleGroupArr)
        if (sampleGroupSet < 2) {
          layer.msg('The number of groups should be greater than 2.')
          return false
        }

        return true
      }

      function submitForm (_this) {
        if (!$('#form').validationEngine('validate')) {
          return
        }
        if (!validateRequired()) {
          return
        }

        var formData = new FormData()

        var rows = $('#sample-table').find('tr')
        $.each(rows, function (i, item) {
          var dataDirectory = $(item).find('td:eq(0)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].dataDirectory.path', dataDirectory)
          formData.append('inputs[' + i + '].dataDirectory.name', $(item).find('td:eq(0)').find('input:eq(1)').val())
          formData.append('inputs[' + i + '].dataDirectory.size', $(item).find('td:eq(0)').find('input:eq(2)').val())
          formData.append('inputs[' + i + '].dataDirectory.folder', $(item).find('td:eq(0)').find('input:eq(3)').val())

          var sampleName = $(item).find('td:eq(1)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].sampleName', sampleName)

          var samplePlate = $(item).find('td:eq(2)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].samplePlate', samplePlate)

          var sampleGroup = $(item).find('td:eq(3)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].sampleGroup', sampleGroup)

          var poolId = $(item).find('td:eq(4)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].poolId', poolId)

          var project = $(item).find('td:eq(5)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].project', project)

          var sampleWell = $(item).find('td:eq(6)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].sampleWell', sampleWell)

          var sentrixId = $(item).find('td:eq(7)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].sentrixId', sentrixId)

          var sentrixPosition = $(item).find('td:eq(8)').find('input:eq(0)').val()
          formData.append('inputs[' + i + '].sentrixPosition', sentrixPosition)

        })

        var taskName = $('input[name=\'taskName\']').val()
        var arrayType = $('input[name=\'arrayType\']:checked').val() || ''
        var normMethod = $('input[name=\'normMethod\']:checked').val() || ''
        var adjPVal = $('input[name=\'adjPVal\']').val() || ''

        formData.append('taskName', taskName)
        formData.append('arrayType', arrayType)
        formData.append('normMethod', normMethod)
        formData.append('adjPVal', adjPVal)

        // formData.forEach((value, key) => {
        //   console.log(key, value)
        // })
        if ($(_this).data('loading') == 'true') {
          return
        }
        $(_this).data('loading', 'true')

        $.ajax({
          url: '/analysis/methychip/createTask',
          dataType: 'json',
          type: 'post',
          processData: false,
          contentType: false,
          data: formData,
          success: function (result) {
            if (result.code == 200) {
              layer.msg('submit success')
              var id = result.data
              setTimeout(function () {
                var _context_path = $('meta[name=\'_context_path\']').attr('content')
                window.location.href = $.trim(_context_path) + '/analysis/methychip/list?taskId=' + id
              }, 2000)
            }
          },
          complete: function () {
            $(_this).data('loading', 'false')
          }
        })
      }

      function uploadExcel () {
        if ($('#excel').val() === '') {
          layer.msg('please select a file')
          return
        }
        var formData = new FormData()
        formData.append('file', $('#excel')[0].files[0])
        $.ajax({
          url: '/analysis/methychip/uploadTemplate',
          data: formData,
          dataType: 'json',
          type: 'post',
          async: false,
          processData: false,
          contentType: false,
          success: function (result) {
            if (result.success) {
              var data = result.data || []
              if (data.length === 0) {
                layer.msg('no data')
                return
              }
              // console.log(result)
              var trs = []
              $.each(data, function (idx, item) {
                var html = ['<tr>']
                html.push('<td class="td-input">')
                html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>')
                html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">')
                obtainTr(html, item.dataDirectory)
                html.push('</em></span></div></div></td>')
                html.push(`<td class="td-input"><input type="text" value="${item.sampleName || ''}" class="form-control text-center rounded-0 " onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>`)
                html.push(`<td class="td-input"><input type="text" value="${item.samplePlate || ''}" class="form-control text-center rounded-0 " onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>`)
                html.push(`<td class="td-input"><input type="text" value="${item.sampleGroup || ''}" class="form-control text-center rounded-0 group" onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>`)
                html.push(`<td class="td-input"><input type="text" value="${item.poolId || ''}" class="form-control text-center rounded-0 " onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>`)
                html.push(`<td class="td-input"><input type="text" value="${item.project || ''}" class="form-control text-center rounded-0 " onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>`)
                html.push(`<td class="td-input"><input type="text" value="${item.sampleWell || ''}" class="form-control text-center rounded-0 " onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>`)
                html.push(`<td class="td-input"><input type="text" value="${item.sentrixId || ''}" class="form-control text-center rounded-0 " onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>`)
                html.push(`<td class="td-input"><input type="text" value="${item.sentrixPosition || ''}" class="form-control text-center rounded-0 " onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>`)
                html.push(`<td>
                               <button type="button" class="btn btn-primary btn-sm" onclick="addRow(this)"><i class="fa fa-plus"></i>
                               </button>
                               <button type="button" class="btn btn-secondary btn-sm" onclick="removeRow(this)"><i class="fa fa-minus"></i></button>
                           </td>`)
                trs.push(html.join(''))
              })
              $('#sample-table').html(trs.join(''))
              bindGroupChange()
              // layer.msg("The data is imported successfully. Please check whether the table data result is correct")
            } else {
              layer.msg(result.message)
            }
          }
        })
      }

      function obtainTr (html, node) {
        if (!node) {
          return
        }
        var filePath = node.path
        var fileName = node.name
        var fileSize = node.size
        html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '')
        html.push('<input type="hidden" value="' + filePath + '">')
        html.push('<input type="hidden" value="' + fileName + '">')
        html.push('<input type="hidden" value="' + fileSize + '">')
        html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
        html.push('</b>')
      }

    </script>
</th:block>
</html>
