package cn.ac.picb.vipmap.vo;

import cn.ac.picb.rnaseq.vo.RnaseqTaskComparison;
import cn.ac.picb.rnaseq.vo.RnaseqTaskInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RnaseqTaskParam {
    @NotBlank
    private String taskName;
    @NotNull
    private List<RnaseqTaskInput> inputs;

    // qc

    @NotBlank
    private String qcMethod;

    // mapping

    @NotBlank
    private String species;

    @NotBlank
    private String specVersion;

    @NotBlank
    private String mappingMethod;

    // counting

    @NotBlank
    private String countingMethod;

    // differential analysis

    @NotNull
    private List<RnaseqTaskComparison> comparisons;

    @NotNull
    private Double log2FoldChange;

    @NotNull
    private Double pAdjust;

    @NotNull
    private Double pValueCutOff;

    @NotNull
    private Double qValueCutOff;

}
