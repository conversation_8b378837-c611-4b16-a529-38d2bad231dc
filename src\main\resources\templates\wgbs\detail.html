<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('methylation-wgbs-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">WGBS</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                </div>
                <th:block th:switch="${vo.task.status}">
                    <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <span class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Analysis Error

                        </span><span th:if="${vo.task.errMsg!=null}">
                             : [[${vo.task.errMsg}]]
                            </span>
                    </div>
                    <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Prepared</p>
                    </div>
                    <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="4">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Analysis done</p>
                    </div>
                    <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Data prepared</p>
                    </div>
                </th:block>

                <ul class="detail-info row mb-2">
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Task ID：</span>
                        <div th:text="${vo.task.taskId}">WGBS23031600005</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Task Name：</span>
                        <div th:text="${vo.task.taskName}">test</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Start Time：</span>
                        <div th:text="${#dates.format(vo.task.createTime,'yyyy-MM-dd HH:mm:ss')}">2020-07-22</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Consuming：</span>
                        <div th:text="${vo.task.useTime}">42分</div>
                    </li>
                </ul>
                <div class="form-group-box">
                    <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                    <div class="collapse show" id="coll-1">
                        <div class="pt-2 pl-2">
                            <div class="result-box">
                                <h6 class="border-bottom pb-2 m-0">Select Sample</h6>
                                <div class="table-responsive mb-3">
                                    <table class="table table-bordered table-sm table-middle mb-0">
                                        <thead class="thead-light">
                                        <tr>
                                            <td width="200" class="text-center text-nowrap">Sample name</td>
                                            <td class="text-center">Select file</td>
                                            <td width="200" class="text-center text-nowrap">Group</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="input : ${vo.inputs}">
                                            <td class="text-center" th:text="${input.sample}"></td>
                                            <td class="text-center">
                                                <div class="d-flex justify-content-center align-items-center"
                                                     th:each="file : ${input.files}">
                                                    [[${file.name}]]<span class="badge badge-secondary ml-1"><em
                                                        th:text="${file.size}">412.96K</em></span>
                                                </div>
                                            </td>
                                            <td class="text-center" th:text="${input.group}"></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${vo.task.species}">human</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Extraction of methylation information</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Thread：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary">10</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Differential Analysis</h6>
                                <div class="form-group row m-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 pr-0">Comparison：</label>
                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm mb-2 w-auto text-center">
                                                <thead>
                                                <tr>
                                                    <th>control</th>
                                                    <th>patient</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td th:text="${vo?.task?.control}">control</td>
                                                    <td th:text="${vo?.task?.patient}">patient</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">P value：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${vo.task.pvalue}">0.05</span>
                                    </div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Delta：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${vo.task.delta}">0.1</span>
                                    </div>
                                </div>
                                <div th:if="${vo.task?.pvalueCutoff!=null}">
                                    <h6 class="border-bottom pb-2 m-0">GO functional enrichment analysis</h6>
                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">p-value
                                            cutoff：</label>
                                        <div class="col-xl-3 col-lg-3 col-md-3">
                                            <span class="text-primary" th:text="${vo.task.pvalueCutoff}">0.05</span>
                                        </div>
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">q-value
                                            cutoff：</label>
                                        <div class="col-xl-3 col-lg-3 col-md-3">
                                            <span class="text-primary" th:text="${vo.task.qvalueCutoff}">0.1</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group-box" th:if="${vo.task.status == 4 && vo.task.pvalueCutoff==null}">
                    <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                    <div class="collapse show" id="coll-2">
                        <ul class="detail-info row ml-2 mt-3 mb-2">
                            <li class="col-4"><p class="h6">DMR&DML download：</p>
                                <div style="cursor: pointer">
                                    <a th:href="@{/analysis/wgbs/download(taskId=${vo.task.taskId},step=1)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                            <li class="col-4"><p class="h6">Bismark report download：</p>
                                <div style=" cursor: pointer">
                                    <a th:href="@{/analysis/wgbs/download(taskId=${vo.task.taskId},step=2)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="form-group-box" th:if="${vo.task.status == 4 && vo.task.pvalueCutoff!=null}">
                    <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                    <div class="collapse show" id="coll-2">
                        <ul class="detail-info row ml-2 mt-3 mb-2">
                            <li class="col-12"><p class="h6">Quality Control download：</p>
                                <div style="cursor: pointer">
                                    <a class="text-primary"
                                       th:href="@{/analysis/wgbs/download(taskId=${vo.task.taskId},step=3)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                            <li class="col-12"><p class="h6">Bismark report download：</p>
                                <div style="cursor: pointer">
                                    <a class="text-primary"
                                       th:href="@{/analysis/wgbs/download(taskId=${vo.task.taskId},step=2)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                            <li class="col-12"><p class="h6">Extraction results of methylation information download：</p>
                                <div style="cursor: pointer">
                                    <a class="text-primary"
                                       th:href="@{/analysis/wgbs/download(taskId=${vo.task.taskId},step=4)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                            <li class="col-12"><p class="h6">Differential Analysis and GO enrichment download：</p>
                                <div style="cursor: pointer">
                                    <a class="text-primary"
                                       th:href="@{/analysis/wgbs/download(taskId=${vo.task.taskId},step=1)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="text-center">
                        <!--1-->
                        <div class="chart-box">
                            <div class="chart-td">
                                <span class="chart-title mr-2 mr-2">Mean methylation levels of the three contexts</span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary" id="chart-01-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=1)}"
                                       style="cursor: pointer"><i class="fa fa-download mr-1"></i></a>
                                </div>
                            </div>
                            <div class="row" id="chart-01-div"></div>
                            <div id="chart-01" style="height: 340px;width: 100%"></div>
                        </div>
                        <!-- 2-->
                        <div class="mb-5 chart-box">
                            <div class="chart-td">
                                <span class="chart-title mr-2">Mean methylation levels of the three contexts in the
                                     <select style="width: 100px" id="chart-02-group"
                                             class="custom-select custom-select">
                                    <option selected value="control">Control</option>
                                    <option value="patient">Patient</option>
                                </select> group</span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary"
                                       id="chart-02-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=2,group='control')}"
                                       style="cursor: pointer"><i class="fa fa-download mr-1"></i></a>
                                </div>
                                <!--<div class="btn-group chart-download">
                                    <ul class="nav results-tabs">
                                        <li class="nav-item nav-group">
                                            <a class="nav-select active" data-metadata="control"
                                               onclick="changeLabel(this, 'chart-02-group')"
                                            >Control</a>
                                        </li>
                                        <li class="nav-item nav-group">
                                            <a class="nav-select" data-metadata="patient"
                                               onclick="changeLabel(this, 'chart-02-group')"
                                            >Patient</a>
                                        </li>
                                    </ul>
                                </div>-->
                            </div>
                            <!--<input type="hidden" id="chart-02-group" value="control">-->
                            <div id="chart-02" style="height: 630px;width: 100%"></div>
                        </div>
                        <div class="mb-5 chart-box">
                            <div class="chart-td">
                                <span class="chart-title mr-2">Methylation levels of genomic elements in <select
                                        selected style="width: 80px"
                                        id="chart-03-context"
                                        class="custom-select custom-select">
                                    <option selected value="CpG">CpG</option>
                                    <option value="CHG">CHG</option>
                                    <option value="CHH">CHH</option>
                                </select> context</span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary" id="chart-03-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=3,context='CpG')}"
                                       style="cursor: pointer"><i class="fa fa-download mr-1"></i></a>
                                </div>
                                <!--<div class="btn-group chart-download">
                                    <ul class="nav results-tabs">
                                        <li class="nav-item nav-group">
                                            <a class="nav-select active" data-metadata="CHH"
                                               onclick="changeLabel(this, 'chart-03-context')"
                                            >CHH</a>
                                        </li>
                                        <li class="nav-item nav-group">
                                            <a class="nav-select" data-metadata="CHG"
                                               onclick="changeLabel(this, 'chart-03-context')"
                                            >CHG</a>
                                        </li>
                                        <li class="nav-item nav-group">
                                            <a class="nav-select" data-metadata="CpG"
                                               onclick="changeLabel(this, 'chart-03-context')"
                                            >CpG</a>
                                        </li>
                                    </ul>
                                </div>-->
                            </div>
                            <!-- <input type="hidden" id="chart-03-context" value="CHH">-->
                            <div id="chart-03" style="height: 300px;width: 100%"></div>
                        </div>
                        <div class="mb-5 chart-box">
                            <div class="chart-td">
                                <span class="chart-title mr-2">Methylation levels along the chromosome in the
                                    <select style="width: 100px" id="chart-04-group"
                                            class="custom-select custom-select">
                                    <option selected value="control">Control</option>
                                    <option value="patient">Patient</option>
                                </select> group</span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary"
                                       id="chart-04-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=4,group='control')}"
                                       style="cursor: pointer"><i class="fa fa-download mr-1"></i></a>
                                </div>
                                <!--<div class="btn-group chart-download">
                                    <ul class="nav results-tabs">
                                        <li class="nav-item nav-group">
                                            <a class="nav-select active" data-metadata="control"
                                               onclick="changeLabel(this, 'chart-04-group')"
                                            >Control</a>
                                        </li>
                                        <li class="nav-item nav-group">
                                            <a class="nav-select" data-metadata="patient"
                                               onclick="changeLabel(this, 'chart-04-group')"
                                            >Patient</a>
                                        </li>
                                    </ul>
                                </div>-->
                            </div>
                            <!--<input type="hidden" id="chart-04-group" value="control">-->
                            <div class="loading-overlay align-items-center justify-content-center"
                                 style="height: 600px">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden"></span>
                                </div>
                            </div>
                            <div id="chart-04-control"></div>
                            <div id="chart-04-patient" style="display: none;"></div>
                        </div>
                        <div class="mb-5 chart-box">
                            <div class="chart-td">
                                <span class="chart-title mr-2">PCA results of different grouped samples</span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary" id="chart-05-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=5)}"
                                       style="cursor: pointer"><i class="fa fa-download mr-1"></i></a>
                                </div>
                            </div>
                            <div id="chart-05" style="height: 500px;width:600px;margin: 0 auto"></div>
                        </div>
                        <div class="mb-5 chart-box">
                            <div class="chart-td">
                                <span class="chart-title mr-2">Correlation analysis of the
                                    <select selected style="width: 80px"
                                            id="chart-06-context"
                                            class="custom-select custom-select">
                                    <option value="cg">CpG</option>
                                    <option value="chg">CHG</option>
                                    <option value="chh">CHH</option>
                                </select> methylation levels between samples</span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary" id="chart-06-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=6,context='chh')}"
                                       style="cursor: pointer"><i
                                            class="fa fa-download mr-1"></i></a>
                                </div>
                                <!--<div class="btn-group chart-download">
                                    <ul class="nav results-tabs">
                                        <li class="nav-item nav-group">
                                            <a class="nav-select active" data-metadata="chh"
                                               onclick="changeLabel(this, 'chart-06-context')"
                                            >CHH</a>
                                        </li>
                                        <li class="nav-item nav-group">
                                            <a class="nav-select " data-metadata="cg"
                                               onclick="changeLabel(this, 'chart-06-context')"
                                            >CpG</a>
                                        </li>
                                        <li class="nav-item nav-group">
                                            <a class="nav-select" data-metadata="chg"
                                               onclick="changeLabel(this, 'chart-06-context')"
                                            >CHG</a>
                                        </li>
                                    </ul>
                                </div>-->
                            </div>
                            <!--<input type="hidden" id="chart-06-context" value="cg">-->
                            <div id="chart-06" style="height: 500px;width:600px;margin: 0 auto"></div>
                        </div>
                        <div class="chart-box">
                            <div class="chart-td">
                                <span class="chart-title mr-2">Results of identification of Hyper-DMCs and Hypo-DMCs</span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary" id="chart-07-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=7)}"
                                       style="cursor: pointer"><i
                                            class="fa fa-download mr-1"></i></a>
                                </div>
                            </div>
                            <div id="chart-07" style="height: 300px;width: 100%"></div>
                        </div>
                        <!-- chart-box-->
                        <div class="chart-box">
                            <div class="chart-td">
                                 <span class="chart-title mr-2">Results of identification of Hyper-DMRs and Hypo-DMRs
                                 </span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary" id="chart-08-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=8)}"
                                       style="cursor: pointer"><i
                                            class="fa fa-download mr-1"></i></a>
                                </div>
                            </div>
                            <div id="chart-08" style="height: 300px;width: 100%"></div>
                        </div>
                        <div class="chart-box">
                            <div class="chart-td">
                                <span class="chart-title mr-2">Genome distribution of genes covered by
                                    <select style="width: 130px" id="chart-09-context"
                                            class="custom-select custom-select">
                                    <option selected value="ALL">All contexts</option>
                                    <option value="CpG">CpG context</option>
                                    <option value="CHG">CHG context</option>
                                    <option value="CHH">CHH context</option>
                                </select>  corresponding to
                                    <select style="width: 130px" id="chart-09-dmrs" class="custom-select custom-select">
                                    <option selected value="all">All DMRs</option>
                                    <option value="hyper">Hyper-DMRs</option>
                                    <option value="hypo">Hypo-DMRs</option>
                                </select>
                                 </span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary" id="chart-09-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=9,dmrs='all',context='ALL')}"><i
                                            class="fa fa-download mr-1"></i></a>
                                </div>
                            </div>
                            <!--<div class="form-group mb-2">
                                <label class="mx-2 font-16">DMRs</label>
                                <select style="width: 130px" id="chart-09-dmrs" class="custom-select custom-select">
                                    <option selected value="all">All DMRs</option>
                                    <option value="hyper">Hyper-DMRs</option>
                                    <option value="hypo">Hypo-DMRs</option>
                                </select>
                                <label class="mx-2 font-16">Context</label>
                                <select style="width: 130px" id="chart-09-context" class="custom-select custom-select">
                                    <option selected value="ALL">All contexts</option>
                                    <option value="CpG">CpG context</option>
                                    <option value="CHG">CHG context</option>
                                    <option value="CHH">CHH context</option>
                                </select>
                            </div>-->
                            <div id="chart-09" style="height: 300px;width: 100%"></div>
                        </div>
                        <div class="chart-box">
                            <div class="chart-td">
                                <span class="chart-title mr-2">GO enrichment results of genes covered by
                                    <select style="width: 130px" id="chart-10-dmrs" class="custom-select custom-select">
                                    <option selected value="all">All DMRs</option>
                                    <option value="hyper">Hyper-DMRs</option>
                                    <option value="hypo">Hypo-DMRs</option>
                                </select>  corresponding to
                                   <select style="width: 130px" id="chart-10-context"
                                           class="custom-select custom-select">
                                    <option selected value="ALL">All contexts</option>
                                    <option value="CpG">CpG context</option>
                                    <option value="CHG">CHG context</option>
                                    <option value="CHH">CHH context</option>
                                </select>
                                 </span>
                                <div class="chart-download" style="cursor: pointer">
                                    <a class="text-primary" id="chart-10-a"
                                       th:href="@{/analysis/wgbs/downloadChart(taskId=${vo.task.taskId},chartNo=10,dmrs='all',context='ALL')}"><i
                                            class="fa fa-download mr-1"></i></a>
                                </div>
                            </div>
                            <!--<div class="form-group mb-2">
                                <label class="mx-2 font-16">DMRs</label>
                                <select style="width: 130px" id="chart-10-dmrs" class="custom-select custom-select">
                                    <option selected value="all">All DMRs</option>
                                    <option value="hyper">Hyper-DMRs</option>
                                    <option value="hypo">Hypo-DMRs</option>
                                </select>
                                <label class="mx-2 font-16">Context</label>
                                <select style="width: 130px" id="chart-10-context" class="custom-select custom-select">
                                    <option selected value="ALL">All contexts</option>
                                    <option value="CpG">CpG context</option>
                                    <option value="CHG">CHG context</option>
                                    <option value="CHH">CHH context</option>
                                </select>
                            </div>-->
                            <div id="chart-10" style="height: 800px;width: 100%"></div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>
    <script th:src="@{/js/lib/jquery.js}"></script>
    <script th:src="@{/js/lib/d3.js}"></script>
    <script th:src="@{/js/lib/snp.js}"></script>
    <script th:src="@{/data/human.hg19.js}"></script>
    <script th:src="@{/data/mouse.mm9.js}"></script>
    <script th:if="${vo.task?.pvalueCutoff!=null&&vo.task.status==4}">
        let taskId = "[[${vo.task.taskId}]]"
        let species = "[[${vo.task.species}]]";

        function refreshUrl(id) {
            let arr = id.split('-');
            let context = $(`#${id}-context`).val();
            let group = $(`#${id}-group`).val();
            let dmrs = $(`#${id}-dmrs`).val();
            let params = []
            if (context) {
                params.push('context=' + context)
            }
            if (group) {
                params.push('group=' + group)
            }
            if (dmrs) {
                params.push('dmrs=' + dmrs)
            }
            let downloadUrl = `${_context_path}/analysis/wgbs/downloadChart?taskId=${taskId}&chartNo=${Number.parseInt(arr[1])}&${params.join('&')}`
            $('#' + id + '-a').attr('href', downloadUrl)
        }

        function changeLabel(_this, id) {
            $("#" + id).val($(_this).data('metadata'));
            $("#" + id + "-span").text($(_this)[0].text);
            // console.log($(_this).data('metadata'))
            $(_this).parents('ul:first').find("a.nav-select").removeClass('active');
            $(_this).addClass('active');
            $("#" + id).trigger('change');
        }


        function initChart01() {

            drawChart();

            function drawChart() {
                $.ajax({
                    url: `/analysis/wgbs/${taskId}/1`,
                    beforeSend: function () {
                        $("#chart-01-div").next().remove();
                        $("#chart-01").show();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-01-div").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-01-div").hide();
                            return;
                        }
                        let color = ['#4DBBD5', '#E64B35']
                        var data = result.data;
                        // console.log(data)
                        let options = []
                        let context = ['CpG', 'CHG', 'CHH']
                        for (let i = 0; i < 3; i++) {
                            let title = data[3][i][1];
                            // console.log('title', title)
                            let axisData = data[i].map(item => item[0]);
                            let boxData = data[i].map(item => {
                                let index = data[i].indexOf(item);
                                return {
                                    value: [item[1], item[2], item[3], item[4], item[5]],
                                    itemStyle: {
                                        color: color[index],
                                        borderColor: color[index]
                                    }
                                }
                            });

                            // console.log(axisData, boxData)
                            let option = {
                                title: [
                                    {
                                        text: context[i],
                                        subtext: 'Wilcoxon p=' + title,
                                        left: 'center',
                                    }
                                ],
                                tooltip: {},
                                grid: [
                                    {
                                        left: '20%',
                                        top: '15%',
                                        bottom: '15%'
                                    },
                                ],
                                xAxis: {
                                    type: 'category',
                                    data: axisData,
                                    name: 'Group',
                                    nameGap: 20,
                                    nameLocation: 'center',
                                    nameTextStyle: {
                                        fontWeight: 'bold'
                                    },
                                },
                                yAxis: {
                                    type: 'value',
                                    name: 'Mean methylation levels',
                                    nameGap: 40,
                                    nameLocation: 'center',
                                    nameTextStyle: {
                                        fontWeight: 'bold'
                                    },
                                    axisLine: {
                                        show: true,
                                    },
                                    axisTick: {
                                        show: true,
                                    },
                                    splitLine: {show: false}
                                },
                                series: [
                                    {
                                        name: 'boxplot',
                                        type: 'boxplot',
                                        axisData: axisData,
                                        data: boxData,
                                        tooltip: {
                                            formatter: function (param) {
                                                // console.log(param)
                                                return [
                                                    'upper: ' + parseFloat(param.value[5]).toFixed(3),
                                                    'Q3: ' + parseFloat(param.value[4]).toFixed(3),
                                                    'median: ' + parseFloat(param.value[3]).toFixed(3),
                                                    'Q1: ' + parseFloat(param.value[2]).toFixed(3),
                                                    'lower: ' + parseFloat(param.value[1]).toFixed(3)
                                                ].join('<br/>');
                                            }
                                        }
                                    },
                                ]
                            }
                            options.push(option)
                        }
                        // console.log(options)
                        options.forEach(function (option, index) {
                            $("#chart-01-div").append('<div class="col-md-4 py-1"><div class="text-center"><div style="height: 340px;" id="chart-01-' + index + '"></div></div></div>');
                            var myChart = echarts.init(document.getElementById('chart-01-' + index));
                            myChart.setOption(option);
                            window.onresize = () => {
                                myChart.resize()
                            }
                        })
                    }
                })
            }
        }


        function initChart02() {
            if (!document.getElementById('chart-02')) {
                return;
            }

            let myChart = echarts.init(document.getElementById('chart-02'));

            $("#chart-02-group").on('change', function () {
                refreshUrl('chart-02')
                drawChart(myChart);
            });

            drawChart(myChart);

            function drawChart(myChart) {
                myChart.clear();

                $.ajax({
                    url: `/analysis/wgbs/${taskId}/2`,
                    data: {
                        group: $("#chart-02-group").val()
                    },
                    beforeSend: function () {
                        $("#chart-02").next().remove();
                        $("#chart-02").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-02").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-02").hide();
                            return;
                        }
                        let data = result.data;
                        $("#chart-02").html("<img height='630' src='data:image/svg+xml+png;base64," + data + "'>")
                    }
                })
            }
        }

        function initChart03() {
            if (!document.getElementById('chart-03')) {
                return;
            }

            let myChart = echarts.init(document.getElementById('chart-03'));
            window.onresize = () => {
                myChart.resize()
            }

            $("#chart-03-context").on('change', function () {
                refreshUrl('chart-03')
                drawChart(myChart);
            });

            drawChart(myChart);

            function drawChart(myChart) {
                myChart.clear();
                let context = $("#chart-03-context").val();

                $.ajax({
                    url: `/analysis/wgbs/${taskId}/3`,
                    data: {
                        context: context
                    },
                    beforeSend: function () {
                        $("#chart-03").next().remove();
                        $("#chart-03").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-03").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-03").hide();
                            return;
                        }
                        var data = result.data;
                        let xAxisData = Object.keys(data);
                        let series = []
                        xAxisData.forEach(it => {
                            series.push({
                                name: it,
                                symbol: 'none',
                                data: data[it].map(i => i[3]),
                                type: 'line'
                            },)
                        })

                        // console.log(data)

                        var option = {
                            color: ['#00478A', '#D62727'],
                            legend: {
                                type: 'scroll',
                                orient: 'vertical',
                                right: 10,
                                top: 100
                            },
                            xAxis: [
                                {
                                    name: context,
                                    nameLocation: 'middle',
                                    nameGap: '2',
                                    nameTextStyle: {
                                        // backgroundColor: '#D9D9D9',
                                        padding: [4, 360, 2, 360],
                                        borderType: 'solid',
                                        borderWidth: '1',
                                        borderColor: 'black'
                                    },
                                    position: 'top',
                                    type: 'category',
                                    axisTick: {
                                        show: false
                                    },
                                    axisLine: {
                                        show: false
                                    },
                                    axisLabel: {
                                        show: false
                                    }
                                },
                                {
                                    type: 'category',
                                    position: 'bottom',
                                    data: ['upstream', 'body', 'downstream'],
                                    splitLine: {
                                        show: true,
                                        interval: 0, // 控制刻度分隔线的显示间隔
                                        lineStyle: {
                                            color: '#505050',
                                            type: 'dashed'

                                        }
                                    },
                                    axisTick: {
                                        show: false
                                    }
                                }
                            ],
                            yAxis: {
                                type: 'value',
                                scale: true,
                                axisLine: {
                                    show: true
                                },
                                axisTick: {
                                    show: true
                                },
                                splitLine: {
                                    show: false
                                }
                            },
                            series: series
                        };
                        // 使用配置项和数据渲染图表
                        myChart.setOption(option);
                    }
                })
            }
        }

        function initChart04() {

            $("#chart-04-group").on('change', function () {
                refreshUrl('chart-04')
                let val = $("#chart-04-group").val();
                if (val === 'control') {
                    $('#chart-04-control').show()
                    $('#chart-04-patient').hide()
                } else {
                    $('#chart-04-control').hide()
                    $('#chart-04-patient').show()
                }
            });

            drawChart('control');

            setTimeout(() => {
                $('.loading-overlay').hide();
            }, 3000)

            drawChart('patient');

            function renderChart(data, id) {
                $('.loading-overlay').show();
                let NGCircosGenome = species === 'mouse' ? [
                    [//mouse mm9
                        ["1", 197195432],
                        ["2", 181748087],
                        ["3", 159599783],
                        ["4", 155630120],
                        ["5", 152537259],
                        ["6", 149517037],
                        ["7", 152524553],
                        ["8", 131738871],
                        ["9", 124076172],
                        ["10", 129993255],
                        ["11", 121843856],
                        ["12", 121257530],
                        ["13", 120284312],
                        ["14", 125194864],
                        ["15", 103494974],
                        ["16", 98319150],
                        ["17", 95272651],
                        ["18", 90772031],
                        ["19", 61342430]
                    ]
                ] : [
                    [
                        ["1", 249250621],
                        ["2", 243199373],
                        ["3", 198022430],
                        ["4", 191154276],
                        ["5", 180915260],
                        ["6", 171115067],
                        ["7", 159138663],
                        ["8", 146364022],
                        ["9", 141213431],
                        ["10", 135534747],
                        ["11", 135006516],
                        ["12", 133851895],
                        ["13", 115169878],
                        ["14", 107349540],
                        ["15", 102531392],
                        ["16", 90354753],
                        ["17", 81195210],
                        ["18", 78077248],
                        ["19", 59128983],
                        ["20", 63025520],
                        ["21", 48129895],
                        ["22", 51304566]
                    ]
                ]
                var BACKGROUND01 = ["BACKGROUND01", {
                    BginnerRadius: 235,
                    BgouterRadius: 185,
                    BgFillColor: "#fff",
                    BgborderColor: "#000",
                    BgborderSize: 0.3
                }];
                var BACKGROUND02 = ["BACKGROUND02", {
                    BginnerRadius: 180,
                    BgouterRadius: 130,
                    BgFillColor: "#fff",
                    BgborderColor: "#000",
                    BgborderSize: 0.3
                }];
                var BACKGROUND03 = ["BACKGROUND03", {
                    BginnerRadius: 125,
                    BgouterRadius: 75,
                    BgFillColor: "#fff",
                    BgborderColor: "#000",
                    BgborderSize: 0.3
                }];
                var TEXT01 = ["TEXT01", {
                    x: -35,
                    y: 5,
                    textSize: 20,
                    textWeight: "bold",
                    textColor: "black",
                    textOpacity: 1.0,
                    text: id
                }];
                let arc = species === 'mouse' ? ARC_mm9 : ARC_hg19

                let data1 = data['cg']
                let data2 = data['chg']
                let data3 = data['chh']
                data1 = ["SNP04_gwascatalog",
                    {
                        "compareGroup": 1,
                        "maxRadius": 233,
                        "minRadius": 187,
                        "SNPFillColor": "#9400D3",
                        "PointType": "circle",
                    }, data1.map(it => {
                        it.color = '#53BBCE'
                        it.des = 'CpG'
                        return it;
                    })]
                data2 = ["SNP04_gwascatalog",
                    {
                        "compareGroup": 1,
                        "maxRadius": 178,
                        "minRadius": 132,
                        "SNPFillColor": "#9400D3",
                        "PointType": "circle",
                    }, data2.map(it => {
                        it.color = '#E74936'
                        it.des = 'CHG'
                        return it;
                    })]
                data3 = ["SNP04_gwascatalog",
                    {
                        "compareGroup": 1,
                        "maxRadius": 123,
                        "minRadius": 77,
                        "SNPFillColor": "#9400D3",
                        "PointType": "circle",
                    }, data3.map(it => {
                        it.color = '#E5A732'
                        it.des = 'CHH'
                        return it;
                    })]

                // console.log(data1, data2, data3)


                NGCircos01 = new NGCircos(TEXT01, arc, data1, data2, data3, BACKGROUND01, BACKGROUND02, BACKGROUND03, NGCircosGenome, {
                    //Main configuration
                    target: 'chart-04-' + id,
                    svgWidth: 700,
                    svgHeight: 600,
                    svgClassName: "NGCircos",
                    chrPad: 0.04,
                    innerRadius: 230,
                    outerRadius: 260,
                    SNPMouseOverDisplay: true,
                    SNPMouseOverCircleOpacity: 1.0,
                    SNPMouseOverTooltipsBorderWidth: 1,
                    SNPMouseOutDisplay: true,
                    SNPMouseOutColor: "none",
                    SNPMouseOutCircleSize: "none",
                    SNPMouseOutCircleOpacity: 1.0,
                });
                console.time("渲染时间")
                NGCircos01.draw_genome(NGCircos01.genomeLength, 0);
                NGCircos01.draw_genome(NGCircos01.genomeLength2, 1); // NGCircos2.js callback second time
                console.timeEnd("渲染时间")

                $('.loading-overlay').hide();

            }

            function drawChart(group) {

                $.ajax({
                    url: `/analysis/wgbs/${taskId}/4`,
                    data: {
                        group: group
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-04").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-04").hide();
                            return;
                        }
                        let data = result.data;
                        renderChart(data, group);
                        // $("#chart-04").html("<img height='630' src='data:image/svg+xml+png;base64," + data + "'>")
                    }
                })
            }
        }


        function initChart05() {
            if (!document.getElementById('chart-05')) {
                return
            }
            let myChart = echarts.init(document.getElementById('chart-05'), 'roma')
            window.onresize = () => {
                myChart.resize()
            }
            drawChart(myChart)

            function drawChart(myChart) {
                $.ajax({
                    url: `/analysis/wgbs/${taskId}/5`,
                    success: function (result) {
                        // console.log(result)
                        if (result.error) {
                            $("#chart-05").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-05").hide();
                            return;
                        }
                        let data = result.data;
                        // console.log('chart05', data)
                        let series = [], legendData = []
                        let flag = false
                        for (let key in data) {
                            legendData.push(key)
                            data[key] = data[key].map(function (it) {
                                it.value = [it.x, it.y]
                                if (Math.abs(it.x) < 0.000001 || Math.abs(it.y) < 0.000001) {
                                    flag = true;
                                }
                                // console.log(flag)
                                return it
                            })
                            series.push({
                                name: key,
                                type: 'scatter',
                                symbol: 'diamond',
                                symbolSize: 10,
                                data: data[key]
                            })
                        }
                        myChart.setOption({
                            legend: {
                                data: legendData,
                                type: 'scroll',
                            },
                            grid: {
                                top: 30,
                                bottom: 40,
                                containLabel: true,
                                show: true,
                                borderWidth: '1',
                                borderColor: 'black'
                            },
                            tooltip: {
                                formatter: function (params) {
                                    return 'group: ' + params.data.group
                                        + '<br> sample: ' + params.data.name + '<br>'
                                        + 'PC1: ' + params.data.x.toFixed(3) + '<br>'
                                        + 'PC2: ' + params.data.y.toFixed(3)
                                }
                            },
                            xAxis: {
                                type: 'value',
                                name: 'PC1',
                                nameLocation: 'center',
                                nameGap: 35,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                }
                            },
                            yAxis: {
                                type: 'value',
                                name: 'PC2',
                                nameLocation: 'center',
                                nameRotate: 90,
                                nameGap: flag ? 80 : 50,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                axisLabel: flag ? {
                                    formatter: function (value, index) {
                                        return value.toExponential(2); // 将数值转换为科学计数法表示，保留两位小数
                                    }
                                } : {},
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                }
                            },
                            series: series
                        })

                    }
                })
            }

        }

        function initChart06() {
            if (!document.getElementById('chart-06')) {
                return;
            }

            let myChart = echarts.init(document.getElementById('chart-06'));
            window.onresize = () => {
                myChart.resize()
            }

            $("#chart-06-context").on('change', function () {
                refreshUrl('chart-06')
                drawChart(myChart);
            });

            drawChart(myChart);

            function drawChart(myChart) {
                myChart.clear();
                let context = $("#chart-06-context").val();

                $.ajax({
                    url: `/analysis/wgbs/${taskId}/6`,
                    data: {
                        context: context
                    },
                    beforeSend: function () {
                        $("#chart-06").next().remove();
                        $("#chart-06").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-06").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-06").hide();
                            return;
                        }
                        var data = result.data;

                        var sd = data.data.map(function (item) {
                            return [item[1], item[0], item[2] || '-'];
                        });

                        myChart.setOption({
                            grid: {
                                left: '2%',
                                right: '25%',
                                top: '7%',
                                bottom: '6%',
                                containLabel: true
                            },
                            xAxis: {
                                z: 999,
                                type: 'category',
                                data: data.x,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0,
                                    rotate: 30
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                }
                            },
                            tooltip: {
                                trigger: 'item',
                                axisPointer: {
                                    type: 'shadow'
                                },
                                formatter: function (params) {
                                    // console.log(params)
                                    return `${params.marker} ${params.name}-${data.y[params.data[1]]}: ${parseFloat(params.data[2]).toFixed(3)}`
                                }
                            },
                            yAxis: {
                                z: 999,
                                type: 'category',
                                data: data.y,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0
                                },
                                axisLine: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                },
                                splitLine: {
                                    show: true
                                },
                                inverse: true
                            },
                            visualMap: {
                                min: -1.0,
                                max: 1.0,
                                dimension: 2,
                                precision: 3,
                                inRange: {
                                    color: ['#4D29FF', '#A87FFF', 'white', '#FFAC93', '#FF0000']
                                },
                                calculable: true,
                                orient: 'vertical',
                                right: '0',
                                top: 'center'
                            },
                            series: [{
                                type: 'heatmap',
                                data: sd
                            }]
                        })
                    }
                })
            }
        }


        function initChart07() {
            var myChart = echarts.init(document.getElementById('chart-07'));
            window.onresize = () => {
                myChart.resize()
            }

            // 配置图表的选项和数据
            var option = {
                color: ['#F8766D', '#00BFC4'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'vertical',
                    right: 10,
                    top: 120,
                    bottom: 20
                },
                grid: {
                    left: '5%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['CpG', 'CHG', 'CHH']
                },
                yAxis: {
                    name: 'Number of DMCs',
                    nameTextStyle: {
                        fontSize: 18
                    },
                    nameLocation: 'middle',
                    nameGap: 50,
                    axisLine: {
                        show: true
                    },
                    splitLine: {
                        show: false
                    },
                    axisTick: {
                        show: true
                    },
                    type: 'value'
                },

            };
            // 使用配置项和数据渲染图表
            myChart.setOption(option);

            $.ajax({
                url: `/analysis/wgbs/${taskId}/7`,
                success: function (result) {
                    // console.log(result)
                    if (result.error) {
                        $("#chart-07").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                        $("#chart-07").hide();
                        return;
                    }
                    var data = result.data;
                    // console.log(data)
                    let group = Object.keys(data);
                    let seriesData = []
                    group.forEach(key => {
                        let dataArr = data[key];
                        seriesData.push({
                            barMaxWidth: 75,
                            name: key,
                            type: 'bar',
                            data: dataArr.map(it => it.value),
                            label: {
                                show: true,
                                position: 'top'
                            }
                        })
                    })

                    myChart.setOption({
                        series: seriesData
                    });
                }
            })
        }

        function initChart08() {
            var myChart = echarts.init(document.getElementById('chart-08'));
            window.onresize = () => {
                myChart.resize()
            }

            // 配置图表的选项和数据
            var option = {
                color: ['#F8766D', '#00BFC4'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'vertical',
                    right: 10,
                    top: 120,
                    bottom: 20
                },
                grid: {
                    left: '5%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['CpG', 'CHG', 'CHH']
                },
                yAxis: {
                    name: 'Number of DMRs',
                    nameTextStyle: {
                        fontSize: 18
                    },
                    nameLocation: 'middle',
                    nameGap: 45,
                    axisLine: {
                        show: true
                    },
                    splitLine: {
                        show: false
                    },
                    axisTick: {
                        show: true
                    },
                    type: 'value'
                },
            };
            // 使用配置项和数据渲染图表
            myChart.setOption(option);


            $.ajax({
                url: `/analysis/wgbs/${taskId}/8`,
                success: function (result) {
                    // console.log(result)
                    if (result.error) {
                        $("#chart-08").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                        $("#chart-08").hide();
                        return;
                    }
                    var data = result.data;
                    // console.log(data)
                    let group = Object.keys(data);
                    let seriesData = []
                    group.forEach(key => {
                        let dataArr = data[key];
                        seriesData.push({
                            barMaxWidth: 75,
                            name: key,
                            type: 'bar',
                            data: dataArr.map(it => it.value),
                            label: {
                                show: true,
                                position: 'top'
                            }
                        })
                    })

                    myChart.setOption({
                        series: seriesData
                    });
                }
            })
        }

        function initChart09() {
            var myChart = echarts.init(document.getElementById('chart-09'));
            window.onresize = () => {
                myChart.resize()
            }

            $("#chart-09-dmrs, #chart-09-context").on('change', function () {
                let dmrs = $('#chart-09-dmrs').find('option:selected')[0].text;
                let context = $('#chart-09-context').find('option:selected')[0].text;
                $('#chart-09-dmrs-span').text(dmrs)
                $('#chart-09-context-span').text(context)
                refreshUrl('chart-09')
                drawChart(myChart);
            });
            drawChart(myChart)

            function drawChart(myChart) {
                myChart.clear()

                var dmrs = $("#chart-09-dmrs").val();
                var context = $("#chart-09-context").val();

                $.ajax({
                    url: `/analysis/wgbs/${taskId}/9`,
                    data: {
                        dmrs: dmrs,
                        context: context
                    },
                    success: function (result) {
                        // console.log(result)
                        if (result.error) {
                            $("#chart-09").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-09").hide();
                            return;
                        }
                        var data = result.data;
                        // console.log(data)
                        let seriesData = []
                        Object.keys(data).forEach(key => {
                            seriesData.push({
                                name: key,
                                value: data[key]
                            })
                        })
                        myChart.setOption({
                            tooltip: {
                                trigger: 'item',
                                formatter: '{b}: {d}%'
                            },
                            legend: {
                                type: 'scroll',
                                orient: 'vertical',
                                right: 10,
                                top: 20,
                            },
                            series: [
                                {
                                    label: {
                                        show: false // 不显示文字标签
                                    },
                                    labelLine: {
                                        show: false // 不显示指导线条
                                    },
                                    name: 'Access From',
                                    type: 'pie',
                                    radius: '90%',
                                    data: seriesData
                                }
                            ]
                        });
                    }
                })
            }
        }

        function subExp(value) {
            let exponent = Number.parseFloat(value.toExponential().split('e')[0]).toPrecision(3);
            let newExponent = parseFloat(exponent) - 0.01;
            let newNumber = newExponent.toPrecision(3) + 'e' + value.toExponential().split('e')[1];
            return parseFloat(newNumber)
        }

        function truncateString(str, maxLength = 30) {
            if (str.length > maxLength) {
                return str.substring(0, maxLength) + '...';
            }
            return str;
        }

        function initChart10() {
            var myChart = echarts.init(document.getElementById('chart-10'));
            window.onresize = () => {
                myChart.resize()
            }

            $("#chart-10-dmrs, #chart-10-context").on('change', function () {
                let dmrs = $('#chart-10-dmrs').find('option:selected')[0].text;
                let context = $('#chart-10-context').find('option:selected')[0].text;
                $('#chart-10-dmrs-span').text(dmrs)
                $('#chart-10-context-span').text(context)
                refreshUrl('chart-10')
                drawChart(myChart);
            });
            drawChart(myChart)

            function drawChart(myChart) {
                myChart.clear()


                var dmrs = $("#chart-10-dmrs").val();
                var context = $("#chart-10-context").val();

                $.ajax({
                    url: `/analysis/wgbs/${taskId}/10`,
                    data: {
                        dmrs: dmrs,
                        context: context
                    },
                    success: function (result) {
                        // console.log(result)
                        if (result.error) {
                            $("#chart-10").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-10").hide();
                            return;
                        }
                        var data = result.data;
                        // console.log(data)
                        let bp = data["BP"]
                        let cc = data["CC"]
                        let mf = data["MF"]
                        let bpYAxis = bp.map(it => {
                            return it[2]
                        })
                        let ccYAxis = cc.map(it => {
                            return it[2]
                        })
                        let mfYAxis = mf.map(it => {
                            return it[2]
                        })
                        let min = Number.MAX_VALUE
                        let max = Number.MIN_VALUE
                        Object.keys(data).forEach(key => {
                            for (let item of data[key]) {
                                let num = Number.parseFloat(item[3]);
                                if (num > max) {
                                    max = num
                                }
                                if (num < min) {
                                    min = num
                                }
                            }
                        })
                        // console.log(min.toPrecision(3), max.toPrecision(3))

                        let list = []
                        Object.keys(data).forEach(key => {
                            let name = key
                            let val = data[key].map(x => {
                                return [x[4], x[2], x[1], x[3], x[0]]
                            })

                            list.push(val)

                        })
                        // console.log('list', list)

                        // 配置图表的选项和数据
                        var option = {
                            visualMap: {
                                min: subExp(min),
                                max: subExp(max),
                                inverse: true,
                                dimension: 3,
                                align: 'left',
                                inRange: {
                                    color: ["#a50026", "#d73027", "#f46d43",
                                        "#fdae61", "#fee090", "#ffffbf",
                                        "#e0f3f8", "#abd9e9", "#74add1",
                                        "#4575b4", "#313695"]
                                },
                                formatter: function (value) {
                                    return value; // 保留两位小数
                                },
                                calculable: true,
                                orient: 'vertical',
                                right: '0',
                                top: 'center'
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                },
                                formatter: function (params) {
                                    let node = params[0]
                                    var value = Number.parseFloat(node.value[3]).toPrecision(3)
                                    var p = Math.floor(Math.log(value) / Math.LN10)
                                    var n = value * Math.pow(10, -p)
                                    //分类名称<br />左边列字符名称<br />p.Adjust: 原始的p.Adjust值
                                    return `${node.marker}${node.value[4]}<br/>
                            <b>ID: </b>${node.value[2]}<br/>
                            <b>Description: </b>${node.value[1]}<br/>
                            <b>p.adjust: </b>${Number.parseFloat(n).toPrecision(3) + 'e' + p}`
                                }
                            },
                            grid: [
                                {
                                    top: '1%', height: '230px', right: '15%', left: '25%',
                                },
                                {
                                    top: '255', height: '230px', right: '15%', left: '25%',
                                },
                                {
                                    top: '495',
                                    right: '15%',
                                    left: '25%',
                                    height: '230px'
                                },
                            ],
                            xAxis: [
                                {
                                    gridIndex: 0,
                                    type: 'value',
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            type: 'dashed'
                                        }
                                    },
                                    axisLabel: {
                                        show: false
                                    },
                                    axisLine: {
                                        show: true
                                    },
                                    axisTick: {
                                        show: false
                                    },
                                },
                                {
                                    gridIndex: 1,
                                    type: 'value',
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            type: 'dashed'
                                        }
                                    },
                                    axisLabel: {
                                        show: false
                                    },
                                    axisLine: {
                                        show: true
                                    },
                                    axisTick: {
                                        show: false
                                    }
                                },
                                {
                                    gridIndex: 2,
                                    type: 'value',
                                    name: 'Count',
                                    nameLocation: 'middle',
                                    nameGap: 25,
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            type: 'dashed'
                                        }
                                    },
                                    axisLine: {
                                        show: true
                                    }
                                }
                            ],
                            yAxis: [
                                {
                                    gridIndex: 0,
                                    type: 'category',
                                    data: bpYAxis,
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            type: 'dashed'
                                        }
                                    },
                                    axisLine: {
                                        show: true
                                    },
                                    axisLabel: {
                                        formatter: function (value) {
                                            return truncateString(value)
                                        }
                                    }
                                },
                                {
                                    gridIndex: 1,
                                    type: 'category',
                                    data: ccYAxis,
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            type: 'dashed'
                                        }
                                    },
                                    axisLine: {
                                        show: true
                                    },
                                    axisLabel: {
                                        formatter: function (value) {
                                            return truncateString(value)
                                        }
                                    }
                                },
                                {
                                    gridIndex: 2,
                                    type: 'category',
                                    data: mfYAxis,
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            type: 'dashed'
                                        }
                                    },
                                    axisLine: {
                                        show: true
                                    },
                                    axisLabel: {
                                        formatter: function (value) {
                                            return truncateString(value)
                                        }
                                    }
                                },
                                {
                                    gridIndex: 0,
                                    name: 'BP',
                                    nameGap: '0',
                                    nameTextStyle: {
                                        // width:1000,
                                        // height:300,
                                        backgroundColor: '#D9D9D9',
                                        padding: [2, 108, 0, 108],
                                        borderType: 'solid',
                                        borderWidth: '1',
                                        borderColor: 'black'
                                    },
                                    nameLocation: 'middle',
                                    type: 'category',
                                    nameRotate: '-90',
                                    position: 'right',
                                },
                                {
                                    gridIndex: 1,
                                    position: 'right',
                                    name: 'CC',
                                    nameGap: '0',
                                    nameLocation: 'middle',
                                    nameTextStyle: {
                                        // width:1000,
                                        // height:300,
                                        backgroundColor: '#D9D9D9',
                                        padding: [2, 108, 0, 108],
                                        borderType: 'solid',
                                        borderWidth: '1',
                                        borderColor: 'black'
                                    },
                                    type: 'category',
                                    nameRotate: '-90',
                                },
                                {
                                    gridIndex: 2,
                                    nameGap: '0',
                                    position: 'right',
                                    nameTextStyle: {
                                        // width:1000,
                                        // height:300,
                                        backgroundColor: '#D9D9D9',
                                        padding: [2, 108, 0, 108],

                                        borderType: 'solid',
                                        borderWidth: '1',
                                        borderColor: 'black'
                                    },
                                    nameRotate: '-90',
                                    name: 'MF',
                                    nameLocation: 'middle',
                                    type: 'category',
                                },
                            ],
                            series: [
                                {
                                    xAxisIndex: 0,
                                    yAxisIndex: 0,
                                    data: list[0],
                                    type: 'bar'
                                },
                                {
                                    xAxisIndex: 1,
                                    yAxisIndex: 1,
                                    data: list[1],
                                    type: 'bar'
                                },
                                {
                                    xAxisIndex: 2,
                                    yAxisIndex: 2,
                                    data: list[2],
                                    type: 'bar'
                                }
                            ]
                        };
                        // 使用配置项和数据渲染图表
                        myChart.setOption(option);
                    }
                })
            }
        }

        function formatNum(num) {
            var result = parseFloat(num);
            if (isNaN(result)) {
                return null;
            }
            result = Math.round(num * 1000) / 1000;
            return result;
        }

        initChart01()
        initChart02()
        initChart03()
        initChart05()
        initChart06()
        initChart07()
        initChart08()
        initChart09()
        initChart10()

        setTimeout(() => {
            initChart04()
        }, 3000)


    </script>
</th:block>
</html>
