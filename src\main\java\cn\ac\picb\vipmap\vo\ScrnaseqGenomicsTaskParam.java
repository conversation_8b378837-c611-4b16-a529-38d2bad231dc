package cn.ac.picb.vipmap.vo;

import cn.ac.picb.scrnaseq.vo.GenomicsMatrixRowVO;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ScrnaseqGenomicsTaskParam {

    @NotBlank
    private String taskName;

    @NotBlank
    private String dataMode;

    private List<GenomicsMatrixRowVO> files;

    private String species;

    private String version;

    private String mapMethod;

    private String countMethod;

    private String rangerMode;

    @Max(2500)
    @Min(200)
    private Integer nGeneStart;

    @Max(2500)
    @Min(200)
    private Integer nGeneEnd;

    @Max(1)
    @Min(0)
    private Double toPerStart;

    @Max(1)
    @Min(0)
    private Double toPerEnd;

    private Double resolution;

    private List<String> tGenes;
}
