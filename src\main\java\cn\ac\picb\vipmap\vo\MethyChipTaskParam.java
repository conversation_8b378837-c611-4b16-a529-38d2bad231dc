package cn.ac.picb.vipmap.vo;

import cn.ac.picb.methychip.vo.MethyChipTaskInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:04
 */
@Data
public class MethyChipTaskParam {
    @NotBlank
    private String taskName;

    @NotNull
    private List<MethyChipTaskInput> inputs;

    @NotBlank
    private String arrayType;

    @NotBlank
    private String normMethod;

    @NotBlank
    private String adjPVal;


}
