<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('methylation-wgbs-introduction')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Introduction</h4>
                <div class="tool-box">
                    <div class="tool-title">Summary</div>
                    <div class="tool-content">
                        <p class="text-muted">
                            WGBS, also known as whole genome methylation sequencing, uses bisulfite to treat genomic DNA
                            to convert unmethylated cytosine C to uracil U. By resequencing the treated DNA and
                            comparing it with the reference genome, it can achie'e single-base
                            resolution and high precision methylation level analysis at the genomic level.

                        </p>
                        <p class="text-muted">
                            The pipeline can automatically analyze WGBS sequencing data and generate a series of visual
                            graphs and result files. In this pipeline, both human and mouse species are supported. The
                            pipeline consists of seven analysis steps. Quality analysis of raw sequencing data was first
                            performed using FastQC software. The raw sequencing data were then quality controlled using
                            Trimmomatic software. The quality of the data after quality control was analyzed again using
                            FastQC. Bismark software was used for sequence alignment, deduplication and methylation
                            information extraction after quality control. There are three contexts in the WGBS analysis,
                            CpG, CHG and CHH. Batmeth2 software is used to partition the sequencing data into windows
                            and obtain the average methylation levels of different contexts in different windows.
                        </p>
                        <p class="text-muted">We decompressed the Bismark processed files and then converted them into
                            the input file
                            format of the R package DSS. After obtaining differentially methylated locis (DMLs) and
                            differentially methylated regions (DMRs) using the DSS package, we annotated the DMRs based
                            on the ChIPseeker package and provided visual analysis results.</p>
                        <p class="text-muted">The schematic diagram of WGBS pipline is as follows :
                        </p>
                        <p>
                            <img th:src="@{/images/pic-wgbs.png}" alt="">
                        </p>
                        <h6>Software and references:</h6>
                        <ul>
                            <li><b>FastQC: </b>Used for quality inspection of WGBS data</li>
                            <li><b>Trimmomatic: </b>Flexible trimmer for WGBS data.</li>
                            <li><b>Bismark: </b>Used for alignment, deduplication and extraction of methylation
                                information from WGBS data.
                            </li>
                            <li><b>Batmeth2: </b>Used for calculated the methylation levels of the three contexts in
                                different regions of the WGBS data.
                            </li>
                            <li><b>DSS: </b>An R package for extracting differentially methylated sites and
                                differentially methylated regions between the two groups.&nbsp;
                            </li>
                            <li><b>ChIPseeker: </b>An R package for annotation and visualization of differentially
                                methylated regions.
                            </li>
                        </ul>
                    </div>

                </div>
                <div class="text-center">
                    <a th:href="@{/analysis/wgbs/form}" class="btn btn-outline-primary btn-custom"><span>Next</span><i
                            class="fa fa-long-arrow-right"></i></a>
                </div>

            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
</th:block>
</html>
