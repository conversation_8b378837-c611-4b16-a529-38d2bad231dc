package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnaseq.dto.PagaTaskDTO;
import cn.ac.picb.scrnaseq.po.PagaTaskPO;
import cn.ac.picb.scrnaseq.vo.PagaTaskParamVO;
import cn.ac.picb.scrnaseq.vo.TaskQueryVO;
import cn.ac.picb.vipmap.client.ScrnaseqPagaServiceClient;
import org.springframework.stereotype.Component;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaseqPagaServiceClientFallback implements ScrnaseqPagaServiceClient {

    private final static String SERVER_NAME = "scrnaseq-service";

    @Override
    public CommonResult<PageResult<PagaTaskDTO>> findPagaTaskPage(TaskQueryVO taskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PagaTaskPO> savePagaTask(PagaTaskParamVO pagaTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PagaTaskPO> findPagaById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PagaTaskDTO> findPagaDtoById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PagaTaskPO> deletePagaById(String s) {
        return serverError(SERVER_NAME);
    }
}
