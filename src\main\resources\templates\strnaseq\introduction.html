<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
  <div class="container-fulid">
    <div class="d-flex">
      <div th:replace="~{base/menu :: sideBar('strnaseq-introduction')}"></div>
      <main>
        <h4 class="border-bottom pb-2 mb-3">Introduction</h4>
        <div class="tool-box">
          <div class="tool-title">Summary</div>
          <div class="tool-content">
            <p class="text-muted">For this module, we will provide a pipeline to analyse spatial transcriptome RNA sequencing dataset from 10X Genomics Visium.</p>
            <p class="text-center"><img height="400px" th:src="@{/images/pic-stseq-service-10X-1.png}" alt=""></p>
            <p class="text-muted">This pipeline starts by reading in the raw fastq data through spaceranger pipeline and return a unique molecular identified count matrix(UMI).</p>
            <p class="text-muted">Then we use the count matrix for post preprocessing by Seurat. During post preprocessing, the input count matrix will be filtered, normalized, reduced dimension and clustered by Seurat first.</p>
          </div>
        </div>
      </main>
    </div>
  </div>
</div>
<th:block layout:fragment="custom-script">

</th:block>
</html>
