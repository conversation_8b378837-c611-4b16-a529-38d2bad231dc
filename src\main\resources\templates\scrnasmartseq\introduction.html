<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-scrnasmartseq-introduction')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Introduction</h4>

                <div class="tool-box">
                    <div class="tool-title">Summary</div>
                    <div class="tool-content">
                        <p class="text-muted">Smart-Seq was developed as a single-cell sequencing protocol with improved
                            read coverage across transcripts.</p>
                        <p class="text-muted">The pipeline can automatically analyze Smart-Seq sequencing data and
                            produces a series of visual figures and tables of results. In this pipeline, both human and
                            mouse species are supported. The pipeline consists of five analysis steps. Firstly,
                            trimmomatic software was used for quality control. For reads after quality control, STAR
                            software is used for mapping. Then, a gene expression matrix can be obtained by using
                            featureCounts software.</p>
                        <p class="text-muted">The schematic diagram of Smart-Seq pipeline is as follows:</p>
                        <p class="text-center">
                            <img th:src="@{/images/smartseq.png}" alt="">
                        </p>
                        <p>Software and references:</p>
                        <div><b>Trimmomatic:</b>Trimmomatic: a flexible trimmer for Illumina sequence data.</div>
                        <div><b>STAR:</b>Mapping Smart-Seq Reads with STAR.</div>
                        <div><b>featureCounts:</b>featureCounts: an efficient general purpose program for assigning sequence reads to
                            genomic features.
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <a th:href="@{/analysis/scrnaSmartseq/form}" class="btn btn-outline-primary btn-custom"><span>Next</span><i
                            class="fa fa-long-arrow-right"></i></a>
                </div>

            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
</th:block>
</html>
