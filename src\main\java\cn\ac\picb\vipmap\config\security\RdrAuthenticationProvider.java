package cn.ac.picb.vipmap.config.security;

import cn.hutool.core.util.StrUtil;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * VipMap自定义认证提供者
 * 参考RdrAuthenticationProvider实现，负责验证用户凭据
 *
 * <AUTHOR>
 */
public class RdrAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService userDetailsService;

    public RdrAuthenticationProvider(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String name = authentication.getName();
        if (StrUtil.isBlank(name)) {
            throw new UsernameNotFoundException("用户名为空");
        }

        UserDetails userDetails = this.userDetailsService.loadUserByUsername(name);

        return new UsernamePasswordAuthenticationToken(userDetails, userDetails.getPassword(), userDetails.getAuthorities());
    }

    @Override
    public boolean supports(Class<?> aClass) {
        return aClass.equals(UsernamePasswordAuthenticationToken.class);
    }
}
