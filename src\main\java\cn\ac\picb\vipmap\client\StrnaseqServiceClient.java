package cn.ac.picb.vipmap.client;

import cn.ac.picb.strnaseq.client.StrnaseqServiceApi;
import cn.ac.picb.vipmap.client.fallback.StrnaseqServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/3/2 11:29
 */
@FeignClient(value = "strnaseq-service", fallback = StrnaseqServiceClientFallback.class)
public interface StrnaseqServiceClient extends StrnaseqServiceApi {
}
