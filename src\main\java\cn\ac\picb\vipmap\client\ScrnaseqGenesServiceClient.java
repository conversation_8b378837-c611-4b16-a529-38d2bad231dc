package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnaseq.client.ScrnaseqGenesServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqGenesServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "scrnaseq-service", contextId = "genes", fallback = ScrnaseqGenesServiceClientFallback.class)
public interface ScrnaseqGenesServiceClient extends ScrnaseqGenesServiceApi {
}
