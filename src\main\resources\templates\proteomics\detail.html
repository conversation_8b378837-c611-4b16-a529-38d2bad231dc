<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('proteomics-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Proteomics</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                </div>
                <th:block th:switch="${task.status}">
                    <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis Error</p>
                    </div>

                    <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Prepared</p>
                    </div>
                    <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="7">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis done</p>
                    </div>

                    <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Data prepared</p>
                    </div>
                </th:block>

                <ul class="detail-info row mb-2">
                    <li class="col-xl-4 col-lg-6"><span class="text-muted sm">TaskNo：</span>
                        <div th:text="${task.taskId}">AA20200722001</div>
                    </li>
                    <li class="col-xl-4 col-lg-6"><span class="text-muted sm">StartTime：</span>
                        <div th:text="${#dates.format(task.createTime,'yyyy-MM-dd HH:mm:ss')}">2020-07-22</div>
                    </li>
                    <th:block th:if="${task.status== 4}">
                        <li class="col-xl-4 col-lg-6"><span class="text-muted sm">Consuming：</span>
                            <div th:text="${task.useTime}">42分</div>
                        </li>
                    </th:block>
                </ul>

                <div class="form-group-box">
                    <a href="#coll-0" data-toggle="collapse" class="h6 text-primary font-weight-bold">Parameters</a>
                    <div class="collapse show" id="coll-0">
                        <div class="pl-4 pt-2">
                            <div class="form-group-box">
                                <a href="#coll-1" data-toggle="collapse" class="h6 text-primary font-weight-bold">Load File</a>
                                <div class="collapse show" id="coll-1">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-sm table-center table-middle">
                                                    <thead>
                                                    <tr class="thead-light">
                                                        <th>File</th>
                                                        <th width="180">Parameter group</th>
                                                        <th width="80">Experiment</th>
                                                        <th width="80">Fraction</th>
                                                        <th width="80">PTM</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr th:each="input:${task.inputs}">
                                                        <td th:text="${input.file == null ? '' : input.file.path}"></td>
                                                        <td th:text="${input.group}">0</td>
                                                        <td th:text="${input.experiment}">0</td>
                                                        <td th:text="${input.fraction}">1</td>
                                                        <td th:text="${input.ptm}">True</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="form-group-box">
                                <a href="#coll-2" data-toggle="collapse" class="h6 text-primary font-weight-bold">Global parameters</a>
                                <div class="collapse show" id="coll-2">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Database</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <span class="text-primary" th:text="${task.refDb}">D01</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Include contaminants</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <span class="text-primary" th:text="${task.includeCont}">D01</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">iBAQ</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <span class="text-primary" th:text="${task.ibaq}">D01</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center m-0" th:if="${task.ibaq}">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Log fit</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <span class="text-primary" th:text="${task.logFit}">D01</span>
                                                </div>
                                            </div>
                                            <h6 class="border-bottom pb-2 m-0">MS/MS analyzer</h6>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">FTMS MS/MS match tolerance / Unit</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <span class="text-primary" th:text="${task.ftms + ' ' + (task.ftmsUnit ? 'ppm':'Da')}">20 ppm</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">ITMS MS/MS match tolerance / Unit</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <span class="text-primary" th:text="${task.itms + ' ' + (task.itmsUnit ? 'ppm':'Da')}">20 ppm</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">TOF MS/MS match tolerance / Unit</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <span class="text-primary" th:text="${task.tof + ' ' + (task.tofUnit ? 'ppm':'Da')}">20 ppm</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Unknown MS/MS match tolerance / Unit</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <span class="text-primary" th:text="${task.unknown + ' ' + (task.unknownUnit ? 'ppm':'Da')}">20 ppm</span>
                                                </div>
                                            </div>
                                            <h6 class="border-bottom pb-2 m-0">Identification</h6>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">PSM FDR</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.psfFdr}">0.01</span></div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Protein FDR</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.proteinFdr}">0.01</span></div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Min. peptides</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.minPeptides}">1</span></div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Min. raror + unique peptides</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.minRaror}">1</span></div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-4 col-lg-4 col-md-4 col-form-label pr-0">Min. unique peptides</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.minUnique}">0</span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group-box">
                                <a href="#coll-3" data-toggle="collapse" class="h6 text-primary font-weight-bold">Group parameter</a>
                                <div class="collapse show" id="coll-3">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <th:block th:if="${task.groupType == 'Standard'}">
                                                <div class="form-group row align-items-center m-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Type</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.refDb}">Standard</span></div>
                                                </div>
                                                <div class="form-group row align-items-center m-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Multiplicity</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.multiplicity}">1</span></div>
                                                </div>
                                                <div class="form-group row align-items-center m-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Label</label>
                                                    <div class="col-xl-8 col-lg-7 col-md-7" th:if="${task.multiplicity == 1}">
                                                        <th:block th:each="label : ${task.lightLabels}" >
                                                            <span class="badge badge-primary" th:text="${label}">180</span>
                                                        </th:block>
                                                    </div>
                                                    <div class="col-xl-8 col-lg-7 col-md-7" th:if="${task.multiplicity == 2}">
                                                        <div class="label-groups-box">
                                                            <h5 class="font-13 font-weight-bold mb-2 border-bottom-0">Light labels</h5>
                                                            <th:block th:each="label : ${task.lightLabels}" >
                                                                <span class="badge badge-primary" th:text="${label}">180</span>
                                                            </th:block>
                                                        </div>
                                                        <div class="label-groups-box">
                                                            <h5 class="font-13 font-weight-bold mb-2 border-bottom-0">Heavy labels</h5>
                                                            <th:block th:each="label : ${task.heavyLabels}" >
                                                                <span class="badge badge-primary" th:text="${label}">180</span>
                                                            </th:block>
                                                        </div>
                                                    </div>
                                                    <div class="col-xl-8 col-lg-7 col-md-7" th:if="${task.multiplicity == 3}">
                                                        <div class="label-groups-box">
                                                            <h5 class="font-13 font-weight-bold mb-2 border-bottom-0">Light labels</h5>
                                                            <th:block th:each="label : ${task.lightLabels}" >
                                                                <span class="badge badge-primary" th:text="${label}">180</span>
                                                            </th:block>
                                                        </div>
                                                        <div class="label-groups-box">
                                                            <h5 class="font-13 font-weight-bold mb-2 border-bottom-0">Medium labels</h5>
                                                            <th:block th:each="label : ${task.mediumLabels}" >
                                                                <span class="badge badge-primary" th:text="${label}">180</span>
                                                            </th:block>
                                                        </div>
                                                        <div class="label-groups-box">
                                                            <h5 class="font-13 font-weight-bold mb-2 border-bottom-0">Heavy labels</h5>
                                                            <th:block th:each="label : ${task.heavyLabels}" >
                                                                <span class="badge badge-primary" th:text="${label}">180</span>
                                                            </th:block>
                                                        </div>
                                                    </div>
                                                </div>

                                            </th:block>
                                            <th:block th:if="${task.groupType == 'Report ion MS2'}">
                                                <div class="form-group row align-items-center m-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Type</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6">
                                                        <span class="text-primary" th:text="${task.refDb}">Standard</span>
                                                    </div>
                                                </div>

                                            </th:block>
                                            <th:block th:if="${task.groupType == 'TIMS-DDA'}">
                                                <div class="form-group row align-items-center m-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Type</label>
                                                    <div class="col-xl-6 col-lg-6 col-md-6">
                                                        <span class="text-primary" th:text="${task.refDb}">Standard</span>
                                                    </div>
                                                </div>
                                            </th:block>

                                            <div class="form-group row align-items-baseline m-0">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Variable modifications</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <th:block th:each="variableMod : ${task.variableMods}">
                                                        <span class="text-primary" th:text="${variableMod}">Acetyl (Protein N-term)</span><br>
                                                    </th:block>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-baseline m-0">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Fixed modifications</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <th:block th:each="fixMod : ${task.fixMods}">
                                                        <span class="text-primary" th:text="${fixMod}">Acetyl (Protein N-term)</span><br>
                                                    </th:block>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group-box">
                                <a href="#coll-4" data-toggle="collapse" class="h6 text-primary font-weight-bold">Instrument</a>
                                <div class="collapse show" id="coll-4">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Instrument type</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.instrumentType}">Orbtrap</span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group-box">
                                <a href="#coll-5" data-toggle="collapse" class="h6 text-primary font-weight-bold">Digestion</a>
                                <div class="collapse show" id="coll-5">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Digestion mode</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.digestionMode}">Specific</span></div>
                                            </div>

                                            <div class="form-group row align-items-baseline m-0" th:unless="${task.digestionMode == 'Unspecific' or task.digestionMode == 'No digestion'}">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Enzyme</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6">
                                                    <th:block th:each="enzyme : ${task.enzymes}">
                                                        <span class="text-primary" th:text="${enzyme}">Trypsin/P</span><br>
                                                    </th:block>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center m-0" th:if="${task.digestionMode == 'Specific'}">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Max missed</label>
                                                <div class="col-xl-6 col-lg-6 col-md-6"><span class="text-primary" th:text="${task.maxMissed}">2</span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group-box" th:if="${task.status == 4}">
                    <a href="#coll-01" data-toggle="collapse" class="h6 text-primary font-weight-bold">Results</a>
                    <div class="collapse show" id="coll-01">
                        <div class="tool-content pl-4 pt-2">
                            <a th:href="@{/analysis/proteomics/result/{id}(id=${task.taskId})}" class="d-flex align-items-center"><span class="d-inline-block rounded-circle py-1 px-2 bg-primary text-light mb-0 mr-2"><i class="fa fa-download"></i></span><span class="h5 m-0 d-inline-block rounded py-2">Download result</span></a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<th:block layout:fragment="custom-script">
    <script th:if="${task.status == 7}">
        $(document).ready(function () {
            var name = $("#name").val();
            $("#gfc-img").html('<img height="300" src="[[@{/analysis/somatic/gfcImg}]]?taskId=[[${task.taskId}]]&name=' + name + '">')

            var filename = $("#file-name").val();
            $("#bq-img").html('<img height="300" src="[[@{/analysis/somatic/baseQualityImg}]]?taskId=[[${task.taskId}]]&name=' + filename + '">')
            $("#gc-img").html('<img height="300" src="[[@{/analysis/somatic/gcContentImg}]]?taskId=[[${task.taskId}]]&name=' + filename + '">')
        })

        function changeFileName(_this) {
            var filename = $("#file-name").val();
            $("#bq-img").html('<img height="300" src="[[@{/analysis/somatic/baseQualityImg}]]?taskId=[[${task.taskId}]]&name=' + filename + '">');
            $("#gc-img").html('<img height="300" src="[[@{/analysis/somatic/gcContentImg}]]?taskId=[[${task.taskId}]]&name=' + filename + '">');
        }

        function changeName(_this) {
            var name = $(_this).val();
            $("#gfc-img").html('<img height="300" src="[[@{/analysis/somatic/gfcImg}]]?taskId=[[${task.taskId}]]&name=' + name + '">')
        }
    </script>
</th:block>
</html>
