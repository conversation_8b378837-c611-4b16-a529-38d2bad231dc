package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.proteomics.vo.ProteomicsTaskParamVO;
import cn.ac.picb.proteomics.vo.ProteomicsTaskQueryVO;
import cn.ac.picb.vipmap.vo.ProteomicsTaskParam;
import cn.ac.picb.vipmap.vo.ProteomicsTaskSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ProteomicsMapper {

    ProteomicsMapper INSTANCE = Mappers.getMapper(ProteomicsMapper.class);

    ProteomicsTaskQueryVO convertToQueryVO(ProteomicsTaskSearchVO queryVO);

    ProteomicsTaskParamVO convertToVO(ProteomicsTaskParam param);
}
