package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.somatic.dto.SomaticCnvsTaskDTO;
import cn.ac.picb.somatic.enums.SomaticTaskStatus;
import cn.ac.picb.somatic.vo.SomaticBamValidateResultVO;
import cn.ac.picb.somatic.vo.SomaticTaskIdVO;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.SomaticCnvsService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.SomaticCnvsTaskParam;
import cn.ac.picb.vipmap.vo.SomaticCnvsTaskVO;
import cn.ac.picb.vipmap.vo.SomaticTaskSearchVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/analysis/somatic-cnvs")
@RequiredArgsConstructor
public class SomaticCnvsController {
    private static final boolean USE_CNVS_DESC = true;

    private final SomaticCnvsService somaticCnvsService;
    private final AppProperties appProperties;

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") SomaticTaskSearchVO queryVO, @ModelAttribute PageParam pageParam, Model model) {
        PageResult<SomaticCnvsTaskDTO> pageResult = somaticCnvsService.findPage(user, queryVO, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = SomaticTaskStatus.statusMap(appProperties.getLocale(), USE_CNVS_DESC);
        model.addAttribute("codeDescMap", codeDescMap);

        SomaticTaskIdVO somaticTaskIdVO = somaticCnvsService.findSomaticIdInfo(user.getId());
        model.addAttribute("somaticTaskIdVO", somaticTaskIdVO);
        return "somatic-cnvs/list";
    }

    @RequestMapping("/form")
    public String form() {
        return "somatic-cnvs/form";
    }

    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated SomaticCnvsTaskParam param) {
        String taskId = somaticCnvsService.createTask(user, param);
        return success(taskId);
    }


    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        somaticCnvsService.deleteTask(id);
        return success(true);
    }


    @RequestMapping("/downloadBamTemplate")
    @ResponseBody
    public void downloadBamTemplate() {
        Response response = somaticCnvsService.downloadBamTemplate();
        ResponseUtil.download(response);
    }

    @RequestMapping("/uploadBamTemplate")
    @ResponseBody
    public CommonResult<List<SomaticBamValidateResultVO>> uploadBamTemplate(CurrentUser user, MultipartFile file) {
        List<SomaticBamValidateResultVO> vos = somaticCnvsService.uploadBamTemplate(file, user);
        return success(vos);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        SomaticCnvsTaskVO vo = somaticCnvsService.findTaskVO(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = SomaticTaskStatus.statusMap(appProperties.getLocale(), USE_CNVS_DESC);
        model.addAttribute("codeDescMap", codeDescMap);
        return "somatic-cnvs/detail";
    }

    @RequestMapping("/downloadGsf")
    @ResponseBody
    @SneakyThrows
    public void downloadGsf(String taskId, String runName) {
        Response response = somaticCnvsService.downloadGsf(taskId, runName);
        ResponseUtil.download(response);
    }

    @RequestMapping("/downloadSsf")
    @ResponseBody
    @SneakyThrows
    public void downloadSsf(String taskId, String runName) {
        Response response = somaticCnvsService.downloadSsf(taskId, runName);
        ResponseUtil.download(response);
    }

}
