package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.somatic.dto.SomaticCnvsTaskDTO;
import cn.ac.picb.somatic.dto.SomaticTaskDTO;
import cn.ac.picb.somatic.enums.SomaticTaskStatus;
import cn.ac.picb.somatic.po.SomaticCnvsTaskPO;
import cn.ac.picb.somatic.po.SomaticTaskPO;
import cn.ac.picb.somatic.vo.*;
import cn.ac.picb.vipmap.client.SomaticServiceClient;
import cn.ac.picb.vipmap.mapper.SomaticMapper;
import cn.ac.picb.vipmap.vo.*;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SomaticCnvsService {

    private final SomaticServiceClient somaticServiceClient;

    public PageResult<SomaticCnvsTaskDTO> findPage(CurrentUser user, SomaticTaskSearchVO queryVO, PageParam pageParam) {
        SomaticTaskQueryVO vo = initFindPageParam(user, queryVO, pageParam);

        CommonResult<PageResult<SomaticCnvsTaskDTO>> result = somaticServiceClient.findCnvsTaskPage(vo);
        result.checkError();
        return result.getData();
    }

    public SomaticTaskIdVO findSomaticIdInfo(String userId) {
        return somaticServiceClient.findSomaticCnvsIdInfo(userId).getData();
    }

    public static final SomaticTaskQueryVO initFindPageParam(CurrentUser user, SomaticTaskSearchVO queryVO, PageParam pageParam) {
        SomaticTaskQueryVO vo = SomaticMapper.INSTANCE.convertToQueryVO(queryVO);
        vo.setUserId(user.getId());
        vo.setPage(pageParam.getPage());
        vo.setSize(pageParam.getSize());
        return vo;
    }

    public Response downloadBamTemplate() {
        return somaticServiceClient.downloadBamTemplateExcel();
    }

    public List<SomaticBamValidateResultVO> uploadBamTemplate(MultipartFile file, CurrentUser user) {
        final CommonResult<List<SomaticBamValidateResultVO>> result = somaticServiceClient.uploadBamTemplateExcel(file, user.getUsername());
        result.checkError();
        return result.getData();
    }


    public String createTask(CurrentUser user, SomaticCnvsTaskParam param) {
        SomaticCnvsTaskParamVO vo = SomaticMapper.INSTANCE.convertToCnvsVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<String> result = somaticServiceClient.saveCnvsTask(vo);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<SomaticCnvsTaskPO> result = somaticServiceClient.deleteCnvsById(id);
        result.checkError();
    }

    public SomaticCnvsTaskVO findTaskVO(String id) {
        SomaticCnvsTaskVO vo = new SomaticCnvsTaskVO();
        CommonResult<SomaticCnvsTaskDTO> result = somaticServiceClient.findCnvsDetailById(id);
        result.checkError();
        SomaticCnvsTaskDTO dto = result.getData();
        vo.setTask(dto);

        SomaticCnvsTaskPO task = dto.getTask();
        if (task.getStatus().equals(SomaticTaskStatus.complete.getCode())) {
            CommonResult<SomaticCnvsTaskDetailVO> taskDetailVO = somaticServiceClient.findCnvsTaskDetailVO(task.getTaskId());
            taskDetailVO.checkError();
            SomaticCnvsTaskDetailVO data = taskDetailVO.getData();
            vo.setDetailVO(data);
        }
        return vo;
    }

    public Response downloadGsf(String taskId, String runName) {
        return somaticServiceClient.downloadGsf(taskId, runName, "");
    }

    public Response downloadSsf(String taskId, String runName) {
        return somaticServiceClient.downloadSsf(taskId, runName, "");
    }
}
