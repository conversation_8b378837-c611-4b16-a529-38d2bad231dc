package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.germline.vo.GermlineTaskParamVO;
import cn.ac.picb.germline.vo.GermlineTaskQueryVO;
import cn.ac.picb.vipmap.vo.GermlineTaskParam;
import cn.ac.picb.vipmap.vo.GermlineTaskSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface GermlineMapper {

    GermlineMapper INSTANCE = Mappers.getMapper(GermlineMapper.class);

    GermlineTaskQueryVO convertToQueryVO(GermlineTaskSearchVO search);

    GermlineTaskParamVO convertToVO(GermlineTaskParam param);
}
