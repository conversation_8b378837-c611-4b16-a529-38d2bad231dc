package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.proteomics.po.ProteomicsTaskPO;
import cn.ac.picb.proteomics.vo.ProteomicsTaskParamVO;
import cn.ac.picb.proteomics.vo.ProteomicsTaskQueryVO;
import cn.ac.picb.proteomics.vo.ProteomicsValidateResultVO;
import cn.ac.picb.vipmap.client.ProteomicsServiceClient;
import cn.ac.picb.vipmap.mapper.ProteomicsMapper;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.ProteomicsTaskParam;
import cn.ac.picb.vipmap.vo.ProteomicsTaskSearchVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProteomicsService {

    private final ProteomicsServiceClient proteomicsServiceClient;

    public PageResult<ProteomicsTaskPO> findPage(CurrentUser user, ProteomicsTaskSearchVO queryVO, PageParam pageParam) {
        ProteomicsTaskQueryVO vo = ProteomicsMapper.INSTANCE.convertToQueryVO(queryVO);
        vo.setUserId(user.getId());
        vo.setPage(pageParam.getPage());
        vo.setSize(pageParam.getSize());

        CommonResult<PageResult<ProteomicsTaskPO>> result = proteomicsServiceClient.findTaskPage(vo);
        result.checkError();
        return result.getData();
    }

    public String createTask(CurrentUser user, ProteomicsTaskParam param) {
        ProteomicsTaskParamVO vo = ProteomicsMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());

        CommonResult<String> result = proteomicsServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public ProteomicsTaskPO findById(String id) {
        final CommonResult<ProteomicsTaskPO> result = proteomicsServiceClient.findById(id);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<ProteomicsTaskPO> result = proteomicsServiceClient.deleteById(id);
        result.checkError();
    }

    public Response downloadTemplate() {
        return proteomicsServiceClient.downloadTemplateExcel();
    }

    public List<ProteomicsValidateResultVO> uploadTemplate(MultipartFile file, CurrentUser user) {
        final CommonResult<List<ProteomicsValidateResultVO>> result = proteomicsServiceClient.uploadTemplateExcel(file, user.getUsername());
        result.checkError();
        return result.getData();
    }

    public Response downloadResult(String id) {
        return proteomicsServiceClient.downloadResult(id, id + ".zip");
    }
}
