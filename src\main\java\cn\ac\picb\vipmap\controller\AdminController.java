package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.vipmap.service.AdminService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.DataTableResult;
import cn.ac.picb.vipmap.vo.TaskSearchVO;
import cn.ac.picb.vipmap.vo.TaskVO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date 2023/7/17
 */
@Controller
@RequestMapping("/admin")
@RequiredArgsConstructor
public class AdminController {

    private final AdminService adminService;


    @RequestMapping("/list")
    @PreAuthorize("hasRole('ADMIN')")
    public String list2(CurrentUser user, Model model) {
        Long userNum = adminService.countUser();
        Integer all = adminService.countAllTask();
        Integer complete = adminService.countCompleteTask();
        String totalDisk = adminService.countDiskSize();
        model.addAttribute("userNum", userNum);
        model.addAttribute("all", all);
        model.addAttribute("complete", complete);
        model.addAttribute("totalDisk", totalDisk);
        return "admin/list";
    }

    @ResponseBody
    @PreAuthorize("hasRole('ADMIN')")
    @RequestMapping("/getTableData")
    public DataTableResult<TaskVO> getTableData(CurrentUser user, TaskSearchVO query) {
        Page<TaskVO> page = adminService.findData(query);
        return DataTableResult.success(query.getDraw(), page);
    }

    @ResponseBody
    @PreAuthorize("hasRole('ADMIN')")
    @RequestMapping("/addAdmin")
    public CommonResult<String> addAdmin(String username, String token) {
        String result = adminService.addAdmin(username, token);
        return CommonResult.success(result);
    }

    @ResponseBody
    @PreAuthorize("hasRole('ADMIN')")
    @RequestMapping("/chartData")
    public CommonResult<Object> chartData(@RequestParam Integer chartNo) {
        Object result = adminService.chartData(chartNo);
        return CommonResult.success(result);
    }

}
