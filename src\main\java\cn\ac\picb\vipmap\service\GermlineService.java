package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.germline.dto.GermlineTaskDTO;
import cn.ac.picb.germline.enums.GermlineTaskStatus;
import cn.ac.picb.germline.po.GermlineTaskPO;
import cn.ac.picb.germline.vo.*;
import cn.ac.picb.vipmap.client.GermlineServiceClient;
import cn.ac.picb.vipmap.mapper.GermlineMapper;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.GermlineTaskParam;
import cn.ac.picb.vipmap.vo.GermlineTaskSearchVO;
import cn.ac.picb.vipmap.vo.GermlineTaskVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class GermlineService {

    private final GermlineServiceClient germlineServiceClient;

    public PageResult<GermlineTaskDTO> findPage(CurrentUser user, GermlineTaskSearchVO queryVO, PageParam pageParam) {
        GermlineTaskQueryVO vo = GermlineMapper.INSTANCE.convertToQueryVO(queryVO);
        vo.setUserId(user.getId());
        vo.setPage(pageParam.getPage());
        vo.setSize(pageParam.getSize());

        CommonResult<PageResult<GermlineTaskDTO>> result = germlineServiceClient.findTaskPage(vo);
        result.checkError();
        return result.getData();
    }

    public GermlineTaskPO createTask(CurrentUser user, GermlineTaskParam param) {
        GermlineTaskParamVO vo = GermlineMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<GermlineTaskPO> result = germlineServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public GermlineTaskVO findTaskVO(String id) {
        GermlineTaskVO vo = new GermlineTaskVO();

        CommonResult<GermlineTaskDTO> result = germlineServiceClient.findDetailById(id);
        result.checkError();
        GermlineTaskDTO taskDTO = result.getData();
        vo.setTaskDTO(taskDTO);

        final List<String> runNames = taskDTO.getInputs().stream().map(GermlineTaskInput::getRunName).collect(Collectors.toList());
        vo.setRunNames(runNames);

        final GermlineTaskPO task = taskDTO.getTask();
        if (Objects.equals(task.getStatus(), GermlineTaskStatus.complete.getCode())) {
            final CommonResult<GermlineTaskMappingDataVO> mappingDataResult = germlineServiceClient.findMappingData(task.getTaskId());
            mappingDataResult.checkError();
            final GermlineTaskMappingDataVO mappingData = mappingDataResult.getData();
            vo.setMappingData(mappingData);
        }
        return vo;
    }

    public GermlineTaskQcDataVO findQcData(String taskId, String runName) {
        final CommonResult<GermlineTaskQcDataVO> result = germlineServiceClient.findQcData(taskId, runName);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<GermlineTaskPO> result = germlineServiceClient.deleteById(id);
        result.checkError();
    }

    public ResponseEntity<byte[]> getBaseQualityImg(String taskId, String runName) {
        return germlineServiceClient.getBaseQualityImg(taskId, runName);
    }

    public ResponseEntity<byte[]> getGcContentImg(String taskId, String runName) {
        return germlineServiceClient.getGcContentImg(taskId, runName);
    }

    public ResponseEntity<byte[]> getGenomeFractionCoverageImg(String taskId, String name) {
        return germlineServiceClient.getGenomeFractionCoverageImg(taskId, name);
    }

    public Response downloadVcf(String taskId, String type) {
        return germlineServiceClient.downloadVcf(taskId, type, "");
    }

    public Response downloadAnnotatedFile(String taskId, String type) {
        return germlineServiceClient.downloadAnnotatedFile(taskId, type, "");
    }

    public Response downloadTemplate() {
        return germlineServiceClient.downloadGermlineTemplate();
    }

    public List<GermlineValidateResultVO> uploadTemplate(MultipartFile file, CurrentUser user) {
        final CommonResult<List<GermlineValidateResultVO>> result = germlineServiceClient.uploadGermlineTemplate(file, user.getUsername());
        result.checkError();
        return result.getData();
    }
}
