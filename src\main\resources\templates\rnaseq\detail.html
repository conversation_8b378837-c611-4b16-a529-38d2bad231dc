<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <style>
        .modal-table thead td {
            background: rgb(242, 242, 242) !important;
            padding: 8px 5px;
            font-weight: bold;
            border-bottom-width: 0px !important;
        }

        .modal-table tbody td {
            border: 1px solid #dee2e6 !important;
            padding: 8px 5px;
        }
    </style>
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-deg-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">RNA-Seq-DEG</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                </div>
                <th:block th:switch="${vo.task.status}">
                    <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <span class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Analysis Error

                        </span><span th:if="${vo.task.errMsg!=null}">
                             : [[${vo.task.errMsg}]]
                            </span>

                    </div>

                    <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Prepared</p>
                    </div>
                    <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="7">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Analysis done</p>
                    </div>

                    <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Data prepared</p>
                    </div>
                </th:block>

                <ul class="detail-info row mb-2">
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Task ID：</span>
                        <div th:text="${vo.task.taskId}">AA20200722001</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Task Name：</span>
                        <div th:text="${vo.task.taskName}">AA20200722001</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Start Time：</span>
                        <div th:text="${#dates.format(vo.task.createTime,'yyyy-MM-dd HH:mm:ss')}">2020-07-22</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Consuming：</span>
                        <div th:text="${vo.task.useTime}">42分</div>
                    </li>
                    <!--通过是否存在taskName来区分新老版本-->
                    <th:block th:if="${vo.task.taskName!=null}">
                        <th:block th:if="${vo.maxStep >= 4}">
                            <li class="col-xl-12 col-lg-12"><p class="text-muted mb-0">Quality Control：</p>
                                <div>
                                    <a th:href="@{/analysis/rnaseq/download(taskId=${vo.task.taskId},step=1)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                        </th:block>
                        <th:block th:if="${vo.maxStep >= 4}">
                            <li class="col-xl-12 col-lg-12"><p class="text-muted mb-0">Counting(Expression Matrix)：</p>
                                <div>
                                    <a th:href="@{/analysis/rnaseq/download(taskId=${vo.task.taskId},step=2)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                        </th:block>
                        <th:block th:if="${vo.maxStep >= 5}">
                            <li class="col-xl-12 col-lg-12"><p class="text-muted mb-0">Differential Analysis：</p>
                                <div>
                                    <a th:href="@{/analysis/rnaseq/download(taskId=${vo.task.taskId},step=3)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                        </th:block>
                        <th:block th:if="${vo.maxStep >= 7}">
                            <li class="col-xl-12 col-lg-12"><p class="text-muted mb-0">GO, KEGG functional enrichment
                                analysis：</p>
                                <div>
                                    <a th:href="@{/analysis/rnaseq/download(taskId=${vo.task.taskId},step=4)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                        </th:block>
                        <th:block th:if="${vo.maxStep >= 7}">
                            <li class="col-xl-12 col-lg-12"><p class="text-muted mb-0">Bam files：</p>
                                <div class="d-flex">
                                    <a href="javascript:void(0);" onclick="openSyncToNodeModal()">
                                        <i class="fa fa-send mr-1"></i>Sync to NODE</a>
                                    <span id="syncTip"
                                          class="ml-1 text-danger"
                                          style="display: none">(all files synced)</span>

                                </div>
                            </li>
                        </th:block>
                    </th:block>
                    <th:block th:if="${vo.task.taskName==null}">
                        <th:block th:if="${vo.maxStep >= 7}">
                            <li class="col-xl-12 col-lg-12"><p class="text-muted mb-0">Result download：</p>
                                <div>
                                    <a th:href="@{/analysis/rnaseq/download(taskId=${vo.task.taskId},step=0)}"><i
                                            class="fa fa-download mr-1"></i>Download</a>
                                </div>
                            </li>
                        </th:block>
                    </th:block>
                </ul>
                <div class="form-group-box">
                    <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                    <div class="collapse show" id="coll-1">
                        <div class="pt-2 pl-2">
                            <div class="result-box">
                                <h6 class="border-bottom pb-2 m-0">Select Sample</h6>
                                <div class="table-responsive mb-3">
                                    <table class="table table-bordered table-sm table-middle mb-0">
                                        <thead class="thead-light">
                                        <tr>
                                            <td width="200" class="text-center text-nowrap">Sample name</td>
                                            <td class="text-center">Select file</td>
                                            <td width="200" class="text-center text-nowrap">Group</td>
                                            <td width="150" class="text-center text-nowrap">Batch</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="input : ${vo.inputs}">
                                            <td class="text-center" th:text="${input.sample}"></td>
                                            <td class="text-center">
                                                <div class="d-flex justify-content-center align-items-center"
                                                     th:each="file : ${input.files}">
                                                    [[${file.name}]]<span class="badge badge-secondary ml-1"><em
                                                        th:text="${file.size}">412.96K</em></span>
                                                </div>
                                            </td>
                                            <td class="text-center" th:text="${input.group}"></td>
                                            <td class="text-center" th:text="${input.batch}"></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Quality Control</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method: </label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary"
                                                                                  th:text="${vo.task.qcMethod}">Trimmomatic</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3" th:switch="${vo.task.species}">
                                        <span class="text-primary" th:case="'human'">Homo sapiens（human）</span>
                                        <span class="text-primary" th:case="'mouse'">Mus musculus（mouse）</span>
                                        <span class="text-primary" th:case="*">[[${vo.task.species}]]</span>
                                    </div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Version：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3" th:switch="${vo.task.specVersion}">
                                        <span class="text-primary"
                                              th:case="'hg38'">GRCh38</span>
                                        <span class="text-primary"
                                              th:case="'mm10'">GRCm38</span>
                                        <span class="text-primary" th:case="*">[[${vo.task.specVersion}]]</span>
                                    </div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3" th:switch="${vo.task.mappingMethod}">
                                        <span class="text-primary" th:case="'STAR'">STAR</span>
                                        <span class="text-primary"
                                              th:case="'hisat2'">HISAT2</span>
                                        <span class="text-primary"
                                              th:case="'rsem'">RSEM(STAR aligner)</span>
                                        <span class="text-primary" th:case="*">[[${vo.task.mappingMethod}]]</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Counting</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3" th:switch="${vo.task.countingMethod}">
                                        <span class="text-primary" th:case="'featurecounts'">featureCounts</span>
                                        <span class="text-primary"
                                              th:case="'htseq'">HTSeq</span>
                                        <span class="text-primary" th:case="'rsem'">RSEM</span>
                                        <span class="text-primary" th:case="*">[[${vo.task.countingMethod}]]</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Differential Analysis</h6>
                                <div class="form-group row m-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 pr-0">Comparison：</label>
                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm mb-2 w-auto text-center">
                                                <thead>
                                                <tr>
                                                    <th>GroupA</th>
                                                    <th>GroupB</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr th:each="g : ${vo.comparisons}">
                                                    <td th:text="${g.groupA}">patient</td>
                                                    <td th:text="${g.groupB}">control</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row m-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 pr-0">log2 fold change threshold：</label>
                                    <div class="col-xl-2 col-lg-3 col-md-4">
                                        <span class="text-primary" th:text="${vo.task.log2FoldChange}">STAR</span>
                                    </div>
                                    <label class="col-xl-2 col-lg-3 col-md-4 pr-0">p.adjust cutoff：</label>
                                    <div class="col-xl-2 col-lg-3 col-md-4">
                                        <span class="text-primary" th:text="${vo.task.PAdjust}">STAR</span>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">GO, KEGG functional enrichment analysis</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">p-value
                                        cutoff：</label>
                                    <div class="col-xl-2 col-lg-3 col-md-4">
                                        <span class="text-primary" th:text="${vo.task.PValueCutOff}">STAR</span>
                                    </div>
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">q-value
                                        cutoff：</label>
                                    <div class="col-xl-2 col-lg-3 col-md-4">
                                        <span class="text-primary" th:text="${vo.task.QValueCutOff}">STAR</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group-box" th:if="${vo.task.status == 7}">
                    <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                    <div class="collapse show" id="coll-2">
                        <div class="pt-3">
                            <!--通过是否存在taskName来区分新老版本-->
                            <th:block th:if="${vo.task.taskName!=null}">
                                <h5 class="font-weight-bold pb-2 m-0 text-center">Bastic Statistic</h5>
                                <div class="table-responsive">
                                    <table id="chart-0"
                                           class="table table-hover table-striped table-nowrap font-12 m-0">
                                        <thead>
                                        <tr>
                                            <th scope="col">Task ID</th>
                                            <th scope="col">Start time</th>
                                            <th scope="col">Status</th>
                                            <th scope="col">Status time</th>
                                            <th scope="col">Consuming</th>
                                            <th scope="col">Action</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td>200302151050411</td>
                                            <td>2020-03-02 15:10:50</td>
                                            <td><span class="text-info"><i class="fa fa-circle"></i> Ready</span></td>
                                            <td>2020-03-02 15:37:45</td>
                                            <td>26m55s</td>

                                            <td>
                                                <div class="btn-group">
                                                    <a href="pipeline-rnaseq-task-s3.html" class="text-primary"
                                                       data-toggle="tooltip" title="View Result"><i
                                                            class="fa fa-eye font-14"></i></a>
                                                    <a href="javascript:void(0);" class="text-danger"
                                                       data-toggle="tooltip"
                                                       title="Delete"><i class="fa fa-times font-14"></i></a>
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </th:block>
                            <h5 class="font-weight-bold pb-2 m-0 mt-3 text-center">PCA plot</h5>
                            <div class="row justify-content-center">
                                <div class="col-lg-10">
                                    <div id="chart-1" style="height: 300px"></div>
                                </div>
                            </div>
                        </div>
                        <!-- 新老版本不同 -->
                        <th:block th:if="${vo.task.taskName!=null}">
                            <div class="d-flex align-items-center mb-3 mt-3">
                                <h6 class="text-muted m-0">Transformation of Expression Matrix：</h6>
                                <select class="custom-select w-auto" id="p1-group">
                                    <option value="rlog">Regularized log(rlog)</option>
                                    <option value="normal">Normalized Count</option>
                                </select>
                            </div>
                            <div class="row justify-content-center">
                                <h5 class="font-weight-bold pb-2 m-0 text-center mt-3">Pearson Correlation Coefficient
                                    Heatmap</h5>
                                <div class="col-lg-10">
                                    <div class="d-flex justify-content-center">
                                        <div id="chart-p1" style="width: 100%;height: 600px"></div>
                                    </div>
                                </div>
                            </div>
                        </th:block>
                        <th:block th:unless="${vo.task.taskName!=null}">
                            <h5 class="font-weight-bold pb-2 m-0 text-center mt-3">rlog pearson correlation
                                coefficient
                                heatmap</h5>
                            <div class="row justify-content-center">
                                <div class="col-lg-10">
                                    <div class="d-flex justify-content-center">
                                        <div id="chart-2" style="width: 100%;"></div>
                                    </div>
                                </div>
                            </div>
                            <h5 class="font-weight-bold pb-2 m-0 text-center mt-3">normalized count pearson
                                correlation
                                coefficient heatmap</h5>
                            <div class="row justify-content-center">
                                <div class="col-lg-10">
                                    <div class="d-flex justify-content-center">
                                        <div id="chart-3" style="width: 100%;"></div>
                                    </div>
                                </div>
                            </div>
                        </th:block>
                        <hr>
                        <div class="d-flex align-items-center mb-3 mt-3">
                            <h6 class="text-muted m-0">Group：</h6>
                            <select class="custom-select w-auto" id="group">
                                <option th:each="item, sta :${vo.comparisons}"
                                        th:text="${item.groupA + '_vs_' + item.groupB}"
                                        th:selected="${sta.first}"
                                        th:attr="data-group-a=${item.groupA},data-group-b=${item.groupB}"></option>
                            </select>
                        </div>
                        <h5 class="font-weight-bold text-center pb-2 m-0 mt-3" id="cv-title">volcanoPlot</h5>
                        <div class="row justify-content-center">
                            <div class="col-lg-10">
                                <div class="d-flex justify-content-center">
                                    <div id="chart-4" style="width: 100%;height: 600px"></div>
                                </div>
                            </div>
                        </div>
                        <h5 class="font-weight-bold text-center pb-2 m-0 mt-3" id="ma-title">MAplot</h5>
                        <div class="row justify-content-center">
                            <div class="col-lg-10">
                                <div class="d-flex justify-content-center">
                                    <div id="chart-5" style="width: 100%;height: 400px"></div>
                                </div>
                            </div>
                        </div>
                        <h5 class="font-weight-bold text-center pb-2 m-0 mt-3" id="de-title">differentially expressed
                            genes heatmap</h5>
                        <div class="row justify-content-center">
                            <div class="col-lg-10">
                                <div class="d-flex justify-content-center">
                                    <div id="chart-6" style="width: 100%; height: 600px"></div>
                                </div>
                            </div>
                        </div>
                        <!--通过是否存在taskName来区分新老版本-->
                        <th:block th:if="${vo.task.taskName!=null}">
                            <div class="d-flex align-items-center mb-3 mt-3">
                                <h6 class="text-muted m-0">Group：</h6>
                                <select class="custom-select w-auto group_select">
                                    <option th:each="item, sta :${vo.comparisons}"
                                            th:text="${item.groupA + '_vs_' + item.groupB}"
                                            th:selected="${sta.first}"
                                            th:attr="data-group-a=${item.groupA},data-group-b=${item.groupB}"></option>
                                </select>

                                <h6 class="text-muted ml-4">Regulation：</h6>
                                <select class="custom-select w-auto " id="regulation">
                                    <option>all_regulated_genes</option>
                                    <option>down_regulated_genes</option>
                                    <option>up_regulated_genes</option>
                                </select>

                            </div>
                            <div class="row justify-content-center">
                                <h5 class="font-weight-bold pb-2 m-0 text-center mt-3">Functional Enrichment
                                    Analysis(GO)</h5>
                                <div class="col-lg-10">
                                    <div class="d-flex justify-content-center">
                                        <div id="chart-7" style="width: 100%;height: 600px"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-center">
                                <h5 class="font-weight-bold pb-2 m-0 text-center mt-3">Functional Enrichment
                                    Analysis(KEGG)</h5>
                                <div class="col-lg-10">
                                    <div class="d-flex justify-content-center">
                                        <div id="chart-8-box"></div>
                                        <div id="chart-8" style="width: 100%;height: 600px"></div>
                                    </div>
                                </div>
                            </div>
                        </th:block>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <div th:replace="~{fragment/sync-to-node-modal :: syncFileModal(${vo.task.taskId},'RNA-Seq-DEG','rnaseq')}"></div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>
    <script th:src="@{/js/pheatmap4js.js}"></script>
    <script th:inline="javascript">
        let comparisons = [[${vo.comparisons}]]
        let maxStep = [[${vo.maxStep}]]
        let status = [[${vo.task.status }]]
    </script>
    <script>
        $(document).ready(function () {
            $('.group_select').on('change', function () {
                let map = new Map()
                comparisons.forEach(x => {
                    map.set(x['groupA'] + '_vs_' + x['groupB'], x.regulation)
                })
                var groupA = $('.group_select option:selected').data('group-a')
                var groupB = $('.group_select option:selected').data('group-b')
                let regulationList = map.get(groupA + '_vs_' + groupB)
                let options = regulationList.map(x => {
                    return `<option>${x}</option>`
                })
                $('#regulation').html(options.join(''))
            })
            $('.group_select').trigger('change')

            if (status === 7) {
                initChart00()
                initChart01()
                initChart02()
                initChart03()
                initChart04()
                initChart05()
                initChart06()
                initChart07()
                initChart08()
                initChartP1()
                getSyncResult()
            }

            $('#group').on('change', function () {
                initChart04()
                initChart05()
                initChart06()
            })
            $('#regulation').on('change', function () {
                initChart07()
                initChart08()
            })
            $('#p1-group').on('change', function () {
                initChartP1()
            })
        })

        function getSyncResult() {
            $.ajax({
                url: `/analysis/rnaseq/getSyncToNodeFileList?taskId=${taskId}`,
                type: "get",
                dataType: "json",
                success: function (result) {
                    if (result.success) {
                        let flag = true
                        result.data.forEach(it => {
                            if (it.status === "" || it.status === null || it.status === undefined) {
                                flag = false
                            }
                        })
                        if (flag) {
                            $('#syncTip').show()
                        }
                    }
                }
            })

        }

        function initChartP1() {
            if (!document.getElementById('chart-p1')) {
                return
            }

            let group = $('#p1-group').val()
            drawChart('chart-p1', group)

            function drawChart(chartId, group) {
                $.ajax({
                    url: '/analysis/rnaseq/chartData',
                    data: {
                        'taskId': '[[${vo.task.taskId}]]',
                        'chartNo': 9,
                        'regulation': group
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message)
                            return
                        }
                        let data = result.data
                        let mat = [data.data['header'], ...data.data['rows']];

                        let annotationRows = [], annotationCols = [];
                        data.data['rows'].map(it => {
                            annotationRows.push(data.group[it[0]])
                        })
                        data.data['header'].slice(1).map(it => {
                            annotationCols.push(data.group[it])
                        })
                        let colors = [
                            "#7fc97f",
                            "#beaed4",
                            "#fdc086",
                            "#ffff99",
                            "#386cb0",
                            "#f0027f",
                            "#bf5b17",
                            "#666666"
                        ];
                        let groups = Array.from(new Set(Object.values(data.group)))
                        groups.sort();
                        let samples = {};
                        groups.forEach((it, i) => {
                            samples[it] = colors[i % colors.length]
                        })

                        let option = {
                            server_host: pheatmap_renderer_server,
                            mat,
                            legend: true,
                            show_rownames: true,
                            show_colnames: true,
                            fontsize: 13,
                            angle_col: -90,
                            display_numbers: false,
                            fontsize_number: 8,
                            cluster_rows: true,
                            cluster_cols: true,

                            annotation_row: {
                                samples: annotationRows
                            },
                            annotation_col: {
                                samples: annotationCols
                            },
                            annotation_colors: {
                                samples: samples
                            },

                            annotation_legend: true,
                            annotation_names_row: true,
                            annotation_names_col: true,
                        }
                        const chart = echarts.init(document.getElementById(chartId));
                        pheatmap4js.genOption(option, chart).then(chartOption => {
                            chartOption.toolbox = {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            }
                            chart.setOption(chartOption);
                            chart.on('datarangeselected', e => {
                                let b = true;
                                for (let key of Object.keys(e.selected)) {
                                    if (!e.selected[key]) {
                                        e.selected[key] = true;
                                        b = false;
                                    }
                                }
                                if (!b) {
                                    chart.dispatchAction({
                                        type: 'selectDataRange',
                                        selected: e.selected
                                    })
                                }
                            })
                        })
                    }
                })
            }
        }

        function initChart00() {
            $.ajax({
                url: '/analysis/rnaseq/chartData',
                data: {
                    'taskId': '[[${vo.task.taskId}]]',
                    'chartNo': 0
                },
                success: function (result) {
                    if (result.error) {
                        layer.msg(result.message)
                        return
                    }
                    $('#chart-0')
                    let data = result.data
                    let htmlArr = []
                    for (let i = 0; i < data.length; i++) {
                        let line = data[i]
                        if (i === 0) {
                            htmlArr.push('<thead>')
                            htmlArr.push('<tr>')
                            let split = line.split('\t')
                            split.forEach(item => {
                                htmlArr.push(`<th>${item}</th>`)
                            })
                            htmlArr.push('</tr>')
                            htmlArr.push('</thead>')
                            htmlArr.push('<tbody>')
                        } else {
                            htmlArr.push('<tr>')
                            let split = line.split('\t')
                            split.forEach(item => {
                                htmlArr.push(`<td>${item}</td>`)
                            })
                            htmlArr.push('</tr>')
                        }
                        htmlArr.push('</tbody>')

                    }
                    $('#chart-0').html(htmlArr.join(''))
                }
            })
        }

        function initChart01() {
            if (!document.getElementById('chart-1')) {
                return
            }
            var myChart = echarts.init(document.getElementById('chart-1'), 'roma')
            drawChart(myChart)

            function drawChart(chart) {
                chart.clear()
                $.ajax({
                    url: '/analysis/rnaseq/chartData',
                    data: {
                        'taskId': '[[${vo.task.taskId}]]',
                        'chartNo': 1
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message)
                            return
                        }
                        var data = result.data
                        var series = [], legendData = []

                        for (var item in data.points) {
                            legendData.push(item)
                            data.points[item] = data.points[item].map(function (it) {
                                it.value = [it.x, it.y]
                                return it
                            })
                            series.push({
                                name: item,
                                type: 'scatter',
                                symbolSize: 10,
                                data: data.points[item]
                            })
                        }
                        chart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                right: 20,
                                top: 30,
                                bottom: 20,
                                data: legendData,
                                orient: 'vertical',
                                type: 'scroll',
                                width: 120
                            },
                            grid: {
                                top: 30,
                                right: 140,
                                bottom: 40,
                                left: 30,
                                containLabel: true
                            },
                            tooltip: {
                                formatter: function (params) {
                                    return 'group: ' + params.data.group
                                        + '<br> sample: ' + params.data.name + '<br>'
                                        + 'PC1: ' + params.data.x.toFixed(3) + '<br>'
                                        + 'PC2: ' + params.data.y.toFixed(3)
                                }
                            },
                            xAxis: {
                                type: 'value',
                                name: data.x,
                                nameLocation: 'center',
                                nameGap: 35,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                }
                            },
                            yAxis: {
                                type: 'value',
                                name: data.y,
                                nameLocation: 'center',
                                nameRotate: 90,
                                nameGap: 35,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                }
                            },
                            series: series
                        })
                    }
                })
            }
        }

        function initChart02() {
            if (!document.getElementById('chart-2')) {
                return
            }
            $('#chart-2').css('height', $('#chart-2').width())
            var myChart = echarts.init(document.getElementById('chart-2'), 'macarons')
            drawChart(myChart)

            function drawChart(chart) {
                $.ajax({
                    url: '/analysis/rnaseq/chartData',
                    data: {
                        'taskId': '[[${vo.task.taskId}]]',
                        'chartNo': 2
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message)
                            return
                        }
                        var data = result.data
                        var min
                        var sd = data.data.map(function (item) {
                            if (!min) {
                                min = item[2]
                            }
                            min = min > item[2] ? item[2] : min
                            return [item[1], item[0], item[2] || '-']
                        })
                        chart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            tooltip: {
                                formatter: function (params) {
                                    return data.x[params.data[0]] + '_' + data.y[params.data[1]] + ':' + params.data[2].toFixed(3)
                                }
                            },
                            grid: {
                                top: 50,
                                left: 0,
                                bottom: 0,
                                right: 50,
                                containLabel: true
                            },
                            xAxis: {
                                name: 'Samples',
                                type: 'category',
                                data: data.x,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0,
                                    rotate: 90,
                                    fontSize: 13
                                },
                                // nameLocation: 'center',
                                // nameGap: 60,
                                nameRotate: 90,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                }
                            },
                            yAxis: {
                                name: 'Samples',
                                type: 'category',
                                data: data.y,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0,
                                    fontSize: 13
                                },
                                inverse: true,
                                // nameRotate: 90,
                                nameLocation: 'start',
                                // nameGap: 60,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                }
                            },
                            visualMap: {
                                min: min,
                                max: 1,
                                calculable: true,
                                precision: 3,
                                orient: 'horizontal',
                                left: 'center',
                                top: 0,
                                inRange: {
                                    color: ['#5d99ff', '#fefab7', '#f8736a']
                                }
                            },
                            series: [{
                                type: 'heatmap',
                                data: sd
                            }]
                        })
                    }
                })
            }
        }

        function initChart03() {
            if (!document.getElementById('chart-3')) {
                return
            }
            $('#chart-3').css('height', $('#chart-3').width())
            var myChart = echarts.init(document.getElementById('chart-3'))
            drawChart(myChart)

            function drawChart(chart) {
                chart.clear()
                $.ajax({
                    url: '/analysis/rnaseq/chartData',
                    data: {
                        'taskId': '[[${vo.task.taskId}]]',
                        'chartNo': 3
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message)
                            return
                        }
                        var data = result.data
                        var min
                        var sd = data.data.map(function (item) {
                            if (!min) {
                                min = item[2]
                            }
                            min = min > item[2] ? item[2] : min
                            return [item[1], item[0], item[2] || '-']
                        })
                        chart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            grid: {
                                top: 50,
                                left: 0,
                                bottom: 0,
                                right: 50,
                                containLabel: true
                            },
                            xAxis: {
                                name: 'Samples',
                                type: 'category',
                                data: data.x,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0,
                                    rotate: 90,
                                    fontSize: 13
                                },
                                // nameLocation: 'center',
                                // nameGap: 60,
                                nameRotate: 90,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                }
                            },
                            yAxis: {
                                name: 'Samples',
                                type: 'category',
                                data: data.y,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0,
                                    fontSize: 13
                                },
                                inverse: true,
                                // nameRotate: 90,
                                nameLocation: 'start',
                                // nameGap: 60,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                formatter: function (params) {
                                    return data.x[params.data[0]] + '_' + data.y[params.data[1]] + ':' + params.data[2].toFixed(3)
                                }
                            },
                            visualMap: {
                                min: min,
                                max: 1,
                                calculable: true,
                                precision: 3,
                                orient: 'horizontal',
                                left: 'center',
                                top: 0,
                                inRange: {
                                    color: ['#5d99ff', '#fefab7', '#f8736a']
                                }
                            },
                            series: [{
                                type: 'heatmap',
                                data: sd
                            }]
                        })
                    }
                })
            }
        }

        function initChart04() {
            if (!document.getElementById('chart-4')) {
                return
            }
            var myChart = echarts.init(document.getElementById('chart-4'))
            drawChart(myChart)

            function drawChart(chart) {
                chart.clear()

                var groupA = $('#group option:selected').data('group-a')
                var groupB = $('#group option:selected').data('group-b')
                var pAdjust = Number.parseFloat('[[${vo.task.PAdjust}]]')
                var log2FoldChange = Number.parseFloat('[[${vo.task.log2FoldChange}]]')

                var v1 = -1 * Math.log10(pAdjust)
                var v2 = log2FoldChange
                var v3 = -1 * log2FoldChange

                $('#cv-title').html(groupA + '_vs_' + groupB + ' Volcano plot')

                $.ajax({
                    url: '/analysis/rnaseq/chartData',
                    data: {
                        'taskId': '[[${vo.task.taskId}]]',
                        'chartNo': 4,
                        'groupA': groupA,
                        'groupB': groupB
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message)
                            return
                        }
                        var data = result.data
                        var series = [], legendData = []

                        var colors = {
                            'down': '#5d99ff',
                            'no': '#bcbbbb',
                            'up': '#f8736a'
                        }
                        for (var item in data.data) {
                            legendData.push(item)
                            var tempData = data.data[item].map(function (it) {
                                it.value = [it.x, it.y]
                                return it
                            })
                            series.push({
                                name: item,
                                type: 'scatter',
                                symbolSize: 3,
                                itemStyle: {
                                    color: colors[item]
                                },
                                data: tempData
                            })
                        }
                        series.push({
                            type: 'scatter',
                            markLine: {
                                lineStyle: {
                                    type: 'dashed',
                                    color: '#666',
                                },
                                symbol: 'none',
                                data: [
                                    {xAxis: v2},
                                    {xAxis: v3},
                                    {
                                        name: 'aaa',
                                        yAxis: v1,
                                        label: {
                                            formatter: '-log10([[${vo.task.PAdjust}]])',
                                            position: 'insideEndTop'
                                        }
                                    }
                                ]
                            }
                        })
                        chart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                right: 5,
                                top: 'middle',
                                data: legendData,
                                orient: 'vertical',
                                type: 'scroll',
                                width: 110
                            },
                            tooltip: {
                                formatter: function (params) {
                                    if (params.componentType === 'markLine') {
                                        return ''
                                    }
                                    return 'gene: ' + params.data.name + '<br>log2(FC): ' + params.data.value[0].toFixed(3) + '<br>-log10(p_adjust): ' + params.data.value[1].toFixed(3)
                                }
                            },
                            grid: {
                                top: 10,
                                right: 120,
                                bottom: 40,
                                left: 30,
                                containLabel: true
                            },
                            xAxis: {
                                name: 'log2(FC)',
                                type: 'value',
                                nameLocation: 'center',
                                nameGap: 35,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                axisLabel: {
                                    fontSize: 13
                                },
                                splitLine: {show: false}
                            },
                            yAxis: {
                                type: 'value',
                                name: '-log10(p_adjust)',
                                nameLocation: 'center',
                                nameRotate: 90,
                                nameGap: 35,
                                axisLabel: {
                                    fontSize: 13
                                },
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                splitLine: {show: false}
                            },
                            series: series
                        })
                    }
                })
            }
        }

        function initChart05() {
            if (!document.getElementById('chart-5')) {
                return
            }
            var myChart = echarts.init(document.getElementById('chart-5'))
            drawChart(myChart)

            function drawChart(chart) {
                chart.clear()
                var groupA = $('#group option:selected').data('group-a')
                var groupB = $('#group option:selected').data('group-b')

                $('#ma-title').html(groupA + '_vs_' + groupB + ' MAplot')

                $.ajax({
                    url: '/analysis/rnaseq/chartData',
                    data: {
                        'taskId': '[[${vo.task.taskId}]]',
                        'chartNo': 5,
                        'groupA': groupA,
                        'groupB': groupB
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message)
                            return
                        }
                        var data = result.data
                        var series = []

                        var colors = {
                            'red': '#f8736a',
                            'grey': '#bcbbbb',
                        }
                        for (var item in data) {
                            var tempData = data[item].map(function (it) {
                                it.value = [it.x, it.y]
                                return it
                            })
                            series.push({
                                name: item,
                                type: 'scatter',
                                symbolSize: 3,
                                itemStyle: {
                                    color: colors[item]
                                },
                                data: tempData
                            })
                        }
                        series.push({
                            type: 'scatter',
                            markLine: {
                                lineStyle: {
                                    type: 'solid',
                                    color: '#f00',
                                },
                                symbol: 'none',
                                data: [
                                    {yAxis: 0},
                                ]
                            }
                        })
                        chart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            grid: {
                                top: 10,
                                right: 120,
                                bottom: 40,
                                left: 30,
                                containLabel: true
                            },
                            tooltip: {
                                show: true,
                                formatter: function (params) {
                                    if (params.componentType === 'markLine') {
                                        return ''
                                    }
                                    return 'gene: ' + params.data.name + '<br>baseMean: ' + params.data.value[0].toFixed(3) + '<br>log2(FC): ' + params.data.value[1].toFixed(3)
                                }
                            },
                            xAxis: {
                                name: 'mean of normalized counts',
                                type: 'log',
                                nameLocation: 'center',
                                nameGap: 35,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                splitLine: {show: false}
                            },
                            yAxis: {
                                type: 'value',
                                name: 'log2(FC)',
                                nameLocation: 'center',
                                nameRotate: 90,
                                nameGap: 35,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                splitLine: {show: false}
                            },
                            series: series
                        })
                    }
                })
            }
        }

        function initChart06() {
            if (!document.getElementById('chart-6')) {
                return
            }
            drawChart('chart-6')

            function drawChart(chartId) {
                let groupA = $('#group option:selected').data('group-a')
                let groupB = $('#group option:selected').data('group-b')

                $('#de-title').html(groupA + '_vs_' + groupB + ' Differentially Expressed Genes Heatmap')
                $.ajax({
                    url: '/analysis/rnaseq/chartData',
                    data: {
                        'taskId': '[[${vo.task.taskId}]]',
                        'chartNo': 6,
                        'groupA': groupA,
                        'groupB': groupB
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message)
                            return
                        }

                        const chart = echarts.init(document.getElementById(chartId));
                        chart.clear();
                        let data = result.data

                        if (data.group != null) {     // 兼容旧版本
                            let mat = [data.data['header'], ...data.data['rows']];

                            let annotationCols = [];

                            data.data['header'].slice(1).map(it => {
                                annotationCols.push(data.group[it])
                            })
                            let colors = [
                                "#7fc97f",
                                "#beaed4",
                                "#fdc086",
                                "#ffff99",
                                "#386cb0",
                                "#f0027f",
                                "#bf5b17",
                                "#666666"
                            ];
                            let groups = Array.from(new Set(Object.values(data.group)))
                            groups.sort();
                            let samples = {};
                            groups.forEach((it, i) => {
                                samples[it] = colors[i % colors.length]
                            })

                            let option = {
                                server_host: pheatmap_renderer_server,
                                mat,
                                legend: true,
                                show_rownames: mat.length < 15,
                                show_colnames: true,
                                fontsize: 13,
                                angle_col: -90,
                                display_numbers: false,
                                cluster_rows: true,
                                cluster_cols: true,

                                annotation_col: {
                                    samples: annotationCols
                                },
                                annotation_colors: {
                                    samples: samples
                                },

                                annotation_legend: true,
                                annotation_names_row: false,
                                annotation_names_col: true,
                            }


                            pheatmap4js.genOption(option, chart).then(opt => {
                                opt.toolbox = {
                                    show: true,
                                    feature: {
                                        saveAsImage: {}
                                    }
                                }
                                let mainXaxis = opt.xAxis[0], mainYaxis = opt.yAxis[0];
                                opt.tooltip = {
                                    position: "top",
                                    formatter: function (param) {
                                        return `Sample: ${mainXaxis.data[param.data[0]]}<br>
                                              Gene: ${mainYaxis.data[param.data[1]]}<br>
                                              Value: ${param.data[2]}`;
                                    },
                                };

                                chart.setOption(opt);
                                chart.on('datarangeselected', e => {
                                    let b = true;
                                    for (let key of Object.keys(e.selected)) {
                                        if (!e.selected[key]) {
                                            e.selected[key] = true;
                                            b = false;
                                        }
                                    }
                                    if (!b) {
                                        chart.dispatchAction({
                                            type: 'selectDataRange',
                                            selected: e.selected
                                        })
                                    }
                                })
                            })
                        } else {
                            data = data.data;
                            let min, max = 0
                            let sd = data.data.map(function (item) {
                                if (!min) {
                                    min = item[2]
                                }
                                min = min > item[2] ? item[2] : min
                                max = max < item[2] ? item[2] : max
                                return [item[1], item[0], item[2] || '-']
                            })
                            chart.setOption({
                                toolbox: {
                                    show: true,
                                    feature: {
                                        saveAsImage: {}
                                    }
                                },
                                tooltip: {
                                    formatter: function (params) {
                                        var x = params.data[0]
                                        var y = params.data[1]
                                        return 'sample: ' + data.x[x] + '<br>gene: ' + data.y[y] + '<br> value:' + params.data[2].toFixed(3)
                                    }
                                },
                                grid: {
                                    top: 50,
                                    left: 10,
                                    bottom: 0,
                                    containLabel: true,
                                    right: 10
                                },
                                xAxis: {
                                    // name: 'Samples',
                                    type: 'category',
                                    data: data.x,
                                    splitArea: {
                                        show: true
                                    },
                                    axisLabel: {
                                        interval: 0,
                                        rotate: 90
                                    },
                                    nameLocation: 'center',
                                    nameGap: 60,
                                    nameTextStyle: {
                                        fontWeight: 'bold'
                                    }
                                },
                                yAxis: {
                                    // name: 'Samples',
                                    show: false,
                                    type: 'category',
                                    data: data.y,
                                    splitArea: {
                                        show: true
                                    },
                                    axisLabel: {
                                        fontSize: 13
                                    },
                                    inverse: true,
                                    nameRotate: 90,
                                    nameLocation: 'center',
                                    nameGap: 60,
                                    nameTextStyle: {
                                        fontWeight: 'bold'
                                    }
                                },
                                visualMap: {
                                    min: min,
                                    max: max,
                                    calculable: true,
                                    precision: 3,
                                    orient: 'horizontal',
                                    left: 'center',
                                    top: 0,
                                    inRange: {
                                        color: ['#5d99ff', '#fefab7', '#f8736a']
                                    }
                                },
                                series: [{
                                    type: 'heatmap',
                                    data: sd
                                }]
                            })
                        }
                    }
                })
            }
        }

        function initChart07() {
            if (!document.getElementById('chart-7')) {
                return
            }
            var myChart = echarts.init(document.getElementById('chart-7'))
            drawChart(myChart)

            function drawChart(chart) {
                chart.clear()
                chart.clear()
                var groupA = $('.group_select option:selected').data('group-a')
                var groupB = $('.group_select option:selected').data('group-b')

                $.ajax({
                    url: '/analysis/rnaseq/chartData',
                    data: {
                        'taskId': '[[${vo.task.taskId}]]',
                        'chartNo': 7,
                        'groupA': groupA,
                        'groupB': groupB,
                        'regulation': $('#regulation').val()
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message)
                            return
                        }
                        if (result.code === 1002000000) {
                            $("#chart-7").hide()
                            $("#chart-7").after('<div class="bg-light text-muted p-3">No Results</div>')
                            return
                        }
                        $("#chart-7").show()
                        $("#chart-7").next().remove()

                        let data = result.data
                        let legendData = Object.keys(data).map(x => {
                            return x
                        })
                        let option = {
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                right: -5,
                                top: 'middle',
                                data: legendData,
                                orient: 'vertical',
                                type: 'scroll',
                                width: 110
                            },
                            // title: {
                            //   text: 'Functional Enrichment Analysis(GO)',
                            //   x: 'center',
                            //   y: 'top',
                            //   textAlign: 'center'
                            // },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                },
                                formatter: function (params) {
                                    let node = params[0]
                                    var value = Number.parseFloat(node.value[2]).toPrecision(3)
                                    var p = Math.floor(Math.log(value) / Math.LN10)
                                    var n = value * Math.pow(10, -p)
                                    //分类名称<br />左边列字符名称<br />p.Adjust: 原始的p.Adjust值
                                    return `${node.marker}${node.seriesName}<br/>
                            <b>ID: </b>${node.value[3]}<br/>
                            <b>Description: </b>${node.value[1]}<br/>
                            <b>p.adjust: </b>${Number.parseFloat(n).toPrecision(3) + 'e' + p}`
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '8%',
                                bottom: '5%',
                                containLabel: true
                            },
                            xAxis: {
                                name: '-log10(p.Adjust)',
                                type: 'value',
                                nameLocation: 'center',
                                nameGap: 35,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                axisLabel: {
                                    fontSize: 13
                                },
                            },
                            yAxis: {
                                type: 'category',
                                data: (function () {
                                    let list = []
                                    Object.keys(data).forEach(key => {
                                        let value = data[key]
                                        value.forEach(x => {
                                            list.push(x[1])
                                        })
                                    })
                                    return list
                                })()
                            },
                            series: (function () {
                                let list = []
                                Object.keys(data).forEach(key => {
                                    let name = key
                                    let val = data[key].map(x => {
                                        return [-Math.log10(x[2]), x[1], x[2], x[3]]
                                    })
                                    let obj = {
                                        name: name,
                                        type: 'bar',
                                        stack: 'Total',
                                        // label: {
                                        //   show: false,
                                        //   position: 'right'
                                        // },
                                        data: val
                                    }
                                    list.push(obj)

                                })
                                return list
                            })()
                        }
                        myChart.setOption(option)
                    }
                })
            }
        }

        function initChart08() {
            if (!document.getElementById('chart-8')) {
                return
            }
            drawChart()

            function drawChart() {

                var groupA = $('.group_select option:selected').data('group-a')
                var groupB = $('.group_select option:selected').data('group-b')

                $.ajax({
                    url: '/analysis/rnaseq/chartData',
                    data: {
                        'taskId': '[[${vo.task.taskId}]]',
                        'chartNo': 8,
                        'groupA': groupA,
                        'groupB': groupB,
                        'regulation': $('#regulation').val()
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message)
                            return
                        }
                        if (result.code === 1002000000) {
                            $("#chart-8").hide()
                            $("#chart-8").next().remove()
                            $('#chart-8-box').html('')
                            $("#chart-8").after('<div class="bg-light text-muted p-3">No Results</div>')
                            return
                        }
                        $("#chart-8").next().remove()
                        $('#chart-8-box').html('')
                        $("#chart-8").show()

                        let data = result.data
                        let visMin = Number.MAX_VALUE
                        let visMax = Number.MIN_VALUE
                        let countSet = new Set()
                        data.forEach(item => {
                            countSet.add(item[3])
                            let val = item[2]
                            if (val > visMax) {
                                visMax = val
                            }
                            if (val < visMin) {
                                visMin = val
                            }
                        })
                        // 根据count大小对右边图例大小进行分组
                        // 对count进行去重，排序
                        let countArr = []
                        countSet.forEach(item => {
                            countArr.push(Number.parseInt(item))
                        })
                        countArr = countArr.sort((a, b) => {
                            return a - b
                        })
                        // count根据数列的个数求公差 d = (最大值 - 最小值) / (数列长度 - 1)
                        let countLength = countArr.length
                        let number
                        let dArr = []
                        if (countLength >= 5) {
                            number = 5
                        } else if (countLength < 5 && countLength > 2) {
                            number = countLength - 1
                        } else {
                            number = countLength
                        }
                        if (countLength > 2) {
                            // 需要向上取整
                            let d = Math.ceil((countArr[countLength - 1] - countArr[0]) / (number - 1))
                            for (let i = 0; i < number; i++) {
                                dArr.push(countArr[0] + d * i)
                            }
                        } else {
                            dArr = countArr
                        }

                        document.getElementById("chart-8").style.height = (data.length * 1 + 50 * number + 200) + 'px'
                        data = data.reverse()
                        data.unshift(['Description', 'GeneRatio', 'p.adjust', 'Count'])

                        function intoScatter() {

                            let circleArr = []
                            let textArr = []
                            for (let i = 0; i < dArr.length; i++) {
                                circleArr.push(`<div style="height: 20px;text-align: center;">
                                       <div class="circle" style="width: ${10 + 3 * i}px; height: ${10 + 3 * i}px;margin-top: 25%"></div>
                                  </div>`)
                                textArr.push(`<div style="height: 20px;text-align: center;">
                                       <div style="width: ${10 + 3 * i}px; height: ${10 + 3 * i}px;margin-top: 25%">${dArr[i]}</div>
                                  </div>`)

                            }
                            let html = `<div class="d-flex justify-content-between" style="height: 10px;position: absolute;left: 755px; top: 60px;"><p style="color: grey;font-size: smaller">Count</p></div>
                            <div class="d-flex justify-content-between" style="height: 100px;position: absolute;left: 760px; top: 80px;">
                                  <div>
                                      ${circleArr.join('')}
                                  </div>
                                  <div class="ml-2">
                                      ${textArr.join('')}
                                  </div>
                            </div>`
                            var box = document.getElementById("chart-8-box")
                            box.innerHTML = html
                        }

                        var chart = echarts.init(document.getElementById('chart-8'))
                        chart.clear()
                        intoScatter()
                        chart.setOption({
                            title: {},
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            dataset: {
                                source: data
                            },
                            grid: {
                                left: '3%',
                                right: '8%',
                                bottom: '12%',
                                containLabel: true
                            },
                            xAxis: {
                                name: 'GeneRatio',
                                scale: true,
                                type: 'value',
                                nameLocation: 'center',
                                nameGap: 30,
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                axisLabel: {
                                    fontSize: 13
                                },
                            },
                            tooltip: {
                                formatter: function (params) {
                                    let data = params.data
                                    var value = Number.parseFloat(data[2]).toPrecision(3)
                                    var p = Math.floor(Math.log(value) / Math.LN10)
                                    var n = value * Math.pow(10, -p)
                                    return `<b>ID: </b>${data[4]}<br/>
                            <b>Description: </b>${data[0]}<br/>
                            <b>p.adjust: </b>${Number.parseFloat(n).toPrecision(3) + 'e' + p}<br/>
                            <b>GeneRatio: </b>${data[5]}<br/>
                            <b>Count: </b>${data[3]}`
                                }
                            },
                            yAxis: {
                                type: 'category',
                            },
                            visualMap: {
                                orient: 'vertical',
                                right: 10,
                                top: '48%',
                                itemHeight: 25 * number + 10,
                                inverse: true,
                                max: Number.parseFloat(visMax) + 1 / (Math.pow(10, (visMax.length > visMin.length ? visMax.length : visMin.length) - 2)),
                                min: Number.parseFloat(visMin) - 1 / (Math.pow(10, (visMax.length > visMin.length ? visMax.length : visMin.length) - 2)),
                                text: ['', 'p.adjust'],
                                dimension: 2,
                                precision: 10,
                                inRange: {
                                    color: ['#0000cc', '#fd2700']
                                }
                            },
                            series: [
                                {
                                    type: 'scatter',
                                    symbolSize: function (seriesdata) {
                                        let count = seriesdata[3]
                                        if (count > 0 && count <= dArr[0]) {
                                            return 10
                                        } else if (count > dArr[0] && count <= dArr[1]) {
                                            return 13
                                        } else if (count > dArr[1] && count <= dArr[2]) {
                                            return 16
                                        } else if (count > dArr[2] && count <= dArr[3]) {
                                            return 19
                                        } else if (count > dArr[3] && count <= dArr[4]) {
                                            return 22
                                        }
                                    },
                                    encode: {
                                        x: 'GeneRatio',
                                        y: 'Description'
                                    }
                                }
                            ]
                        })
                    }
                })
            }
        }
    </script>
</th:block>
</html>
