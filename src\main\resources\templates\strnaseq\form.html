<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/bootstrap-tagsinput.css}">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('strnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">10X genomics visium</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/strnaseq/form}" class="active">Add Task</a>
                            <a th:href="@{/analysis/strnaseq/list}">Task List</a>
                        </div>
                    </div>
                    <div id="form-content">
                        <form action="" class="form-custom" id="strnaseq_form" style="padding: 0 15px;">
                            <div class="form-group-box">
                                <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Expression matrix</a>
                                <div class="collapse show" id="coll-1">
                                    <div class="pl-4 pt-2">
                                        <div class="form-group align-items-center row pl-3">
                                            <label>Current Task Name</label>
                                            <div class="col-xl-4 col-lg-3 col-md-8">
                                                <input class="form-control input-name"
                                                       name="taskName"
                                                       type="text"
                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-sm table-middle table-center">
                                                    <thead class="thead-light">
                                                    <tr>
                                                        <td style="min-width: 50px"></td>
                                                        <td style="min-width: 150px">RunName</td>
                                                        <td style="min-width: 150px">I1</td>
                                                        <td style="min-width: 150px">R1<b>*</b></td>
                                                        <td style="min-width: 150px">R2<b>*</b></td>
                                                        <td style="min-width: 150px">Image File<b>*</b>
                                                            <a class="text-danger ml-1" data-toggle="tooltip"
                                                               data-placement="top"
                                                               title="Single H&E brightfield image in either TIFF or JPG format">
                                                                <i class="fa fa-question-circle"></i>
                                                            </a>
                                                        </td>
                                                        <td style="min-width: 150px">Slide ID<b>*</b>
                                                            <a class="text-danger ml-1" data-toggle="tooltip"
                                                               data-placement="top"
                                                               title="Visium slide serial number, for example 'V10J25-015'">
                                                                <i class="fa fa-question-circle"></i>
                                                            </a>
                                                        </td>
                                                        <td style="min-width: 150px">Area ID<b>*</b>
                                                            <a class="text-danger ml-1" data-toggle="tooltip"
                                                               data-placement="top"
                                                               title="Visium area identifier, for example 'A1'">
                                                                <i class="fa fa-question-circle"></i>
                                                            </a>
                                                        </td>
                                                        <td style="min-width: 200px">Loupe Alignment File
                                                            <a class="text-danger ml-1" data-toggle="tooltip"
                                                               data-placement="top"
                                                               title="Alignment file produced by the manual Loupe alignment step by LoupeBrowser (optional)">
                                                                <i class="fa fa-question-circle"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    </thead>
                                                    <tbody id="fastq_tbody">
                                                    <tr>
                                                        <td style="padding: 0;">
                                                            <i class="fa fa-plus-square text-primary"
                                                               onclick="addRow(this)"></i>
                                                            <i class="fa fa-minus-square text-muted ml-1"
                                                               onclick="removeRow(this)"></i>
                                                        </td>
                                                        <td>
                                                            <input class="form-control RunName validate[required]"
                                                                name="runName" type="text" onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                        </td>
                                                        <td class="td-input">
                                                            <div class="input-group input-group-sm">
                                                                <div class="input-group-prepend">
                                                                    <button class="btn btn-primary btn-sm" type="button"
                                                                            onclick="showFastqFileFileModal(this)">Select
                                                                    </button>
                                                                </div>
                                                                <div class="input-group-prepend ">
                                                                  <span class="input-group-text">
                                                                    <em class="seled" data-errormessage-value-missing="* Please select a file"></em>
                                                                  </span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="td-input">
                                                            <div class="input-group input-group-sm">
                                                                <div class="input-group-prepend">
                                                                    <button class="btn btn-primary btn-sm" type="button"
                                                                            onclick="showFastqFileFileModal(this)">Select
                                                                    </button>
                                                                </div>
                                                                <div class="input-group-prepend ">
                                                                  <span class="input-group-text">
                                                                    <em class="seled validate-required" name="r1" title="R1"
                                                                        data-errormessage-value-missing="* Please select a file"></em>
                                                                  </span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="td-input">
                                                            <div class="input-group input-group-sm">
                                                                <div class="input-group-prepend">
                                                                    <button class="btn btn-primary btn-sm" type="button"
                                                                            onclick="showFastqFileFileModal(this)">Select
                                                                    </button>
                                                                </div>
                                                                <div class="input-group-prepend ">
                                                                  <span class="input-group-text">
                                                                    <em class="seled validate-required" name="r2" title="R2"
                                                                        data-errormessage-value-missing="* Please select a file"></em>
                                                                  </span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="td-input">
                                                            <div class="input-group input-group-sm">
                                                                <div class="input-group-prepend">
                                                                    <button class="btn btn-primary btn-sm" type="button"
                                                                            onclick="showImageFileFileModal(this)">Select
                                                                    </button>
                                                                </div>
                                                                <div class="input-group-prepend ">
                                                                  <span class="input-group-text">
                                                                    <em class="seled validate-required" name="imageFile" title="Image File"
                                                                        data-errormessage-value-missing="* Please select a file"></em>
                                                                  </span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="td-input">
                                                            <input type="text" class="form-control" name="slideId"
                                                                   onkeyup="value=value.replace(/[^\w-\/]/ig,'')"/>
                                                        </td>
                                                        <td class="td-input">
                                                            <input type="text" class="form-control" name="areaId"
                                                                   onkeyup="value=value.replace(/[^\w-\/]/ig,'')"/>
                                                        </td>
                                                        <td class="td-input">
                                                            <div class="input-group input-group-sm">
                                                                <div class="input-group-prepend">
                                                                    <button class="btn btn-primary btn-sm"
                                                                            type="button"
                                                                            onclick="showLoupeFileFileModal(this)">
                                                                        Select
                                                                    </button>
                                                                </div>
                                                                <div class="input-group-prepend ">
                                                                  <span class="input-group-text">
                                                                    <em class="seled" name="loupeFile"
                                                                        data-errormessage-value-missing="* Please select a file"></em>
                                                                  </span>
                                                                </div>
                                                            </div>
                                                        </td>

                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <h6 class="text-primary border-bottom pb-2">Mapping</h6>
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center select_species">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Species</label>
                                                    <div class="col-xl-4 col-lg-3 col-md-8 ml--30">
                                                        <select class="form-control"
                                                                name="species">
                                                            <option value="human">Homo sapiens（human）</option>
                                                            <option value="mouse">Mus musculus (mouse)</option>
                                                        </select>
                                                    </div>
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label">Version</label>
                                                    <div class="col-xl-4 col-lg-3 col-md-4">
                                                        <select class="form-control"
                                                                name="version">
                                                            <option value="hg38">hg38 (GRCh38)</option>
                                                            <option value="hg19">hg19 (GRCh37)</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-xl-10 offset-xl-2">
                                                        <span class="d-block pt-2">Or if you want to use your own genome please contact us</span>
                                                    </div>
                                                </div>

                                            </div>
                                            <h6 class="text-primary border-bottom pb-2">Counting</h6>
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center mb-0">
                                                    <label
                                                            class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8 ml--50">
                                                        <div
                                                                class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input"
                                                                   id="md2" name="md2" checked>
                                                            <label for="md2"
                                                                   class="custom-control-label">spaceranger</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="text-center">
                            <button onclick="uploadMetadata()" class="btn btn-outline-primary btn-custom">
                                <span>Submit</span><i class="fa fa-long-arrow-right"></i></button>
                        </div>
                        <div id="file-modal"></div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-zh_CN.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>
    <script>
        $(document).ready(function () {
            $('#file-modal').fileModal('/analysis/fileTree')

            $('select[name=\'species\']').on('change', function () {
                var versionSelect = $(this).parents('div.select_species').find('select[name="version"]')
                if (!versionSelect) {
                    return
                }
                if ($(this).val() === 'human') {
                    versionSelect.html('<option value="hg38">hg38 (GRCh38)</option><option value="hg19">hg19 (GRCh37)</option>')
                } else if ($(this).val() === 'mouse') {
                    versionSelect.html('<option value="mm10">mm10</option>')
                }
            })
        })

        var _selectBtn

        $('#file-modal').on('__SELECT_FILES__', function (e, data) {
            var nodes = data.nodes || []
            if (nodes.length === 0) {
                $(_selectBtn).parent().next().find('em.seled:first').addClass('validate[required]')
                return
            }

            var html = []
            $.each(nodes, function (i, node) {
                var filePath = node.path
                var fileName = node.name
                html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '')
                html.push('<input type="hidden" value="' + filePath + '">')
                html.push('<input type="hidden" value="' + fileName + '">')
                html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
                html.push('</b>')
            })

            $(_selectBtn).parent().next().find('em.seled:first').html(html.join(''))
            $(_selectBtn).parent().next().find('em.seled:first').removeClass('validate[required]')
            $('[data-toggle="tooltip"]').tooltip()
        })

        function showFileModal(_this) {
            _selectBtn = _this

            var selectIds = []
            $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
                selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
            })
            $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: selectIds
            })
        }

        function showFastqFileFileModal(_this) {
            _selectBtn = _this

            // .fastq.gz
            var selectIds = []
            $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
                selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
            })
            $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: selectIds,
                func: function (data) {
                    return /.+\.fastq\.gz/i.test(data.name)
                }
            })
        }

        function showImageFileFileModal(_this) {
            _selectBtn = _this

            var selectIds = []
            $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
                selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
            })
            $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectMode: 1,
                selectIds: selectIds,
                func: function (data) {
                    return /.+(\.jpg)|(\.tif)/i.test(data.name)
                }
            })
        }

        function showLoupeFileFileModal(_this) {
            _selectBtn = _this

            var selectIds = []
            $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
                selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
            })
            $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectMode: 1,
                selectIds: selectIds,
                func: function (data) {
                    return /.+\.json/i.test(data.name)
                }
            })
        }

        function removeFile(_this) {
            $(_this).parent().parent().remove()
        }

        function addRow(_this) {
            var length = $('#fastq_tbody').find('tr').length
            if (length >= 5) {
                return
            }
            var trClone = $(_this).parents('tbody:first').find('tr').eq(0).clone(true)
            trClone.find('input').val('')
            trClone.find('em.seled').html('')
            $(_this).parents('tbody:first').append(trClone)
        }

        function removeRow(_this) {
            if ($(_this).parents('tbody:first').find('tr').length < 2) {
                return
            }
            $(_this).parents('tr:first').remove()
        }

        function validateRequired() {

            let taskName = $('input[name="taskName"]').val()

            if (!taskName || taskName == '') {
                layer.msg('Task Name can not be empty');
                return  false;
            }

            let runNames = [];

            let input = $('input[name=runName]');
            for (let i = 0; i < input.length; i++) {
                let value = $(input[i]).val();
                if (!value || value == '') {
                    layer.msg('RunName can not be empty');
                    return false;
                } else {
                    if (runNames.indexOf(value.trim()) !== -1) {
                        layer.msg('RunName can not be repeated');
                        return false;
                    } else {
                        runNames.push(value.trim())
                    }
                }
            }

            input = $('input[name=slideId]')
            for (let i = 0; i < input.length; i++) {
                let value = $(input[i]).val();
                if (!value || value == '') {
                    layer.msg('Slide ID can not be empty')
                    return false;
                }
            }

            input = $('input[name=areaId]')
            for (let i = 0; i < input.length; i++) {
                let value = $(input[i]).val();
                if (!value || value == '') {
                    layer.msg('Area ID can not be empty')
                    return false;
                }
            }

            input = $('td em.seled.validate-required')
            for (let i = 0; i < input.length; i++) {
                let _this = $(input[i]);
                if (_this.find('b').length === 0) {
                    layer.msg(`please must select ${_this.attr('title')} file`)
                    return false;
                }
            }
            return true;
        }

        function uploadMetadata() {
            if (!validateRequired()) {
                return;
            }

            // 禁用按钮防止重复提交
            $('#submitBtn').removeAttr('onclick')

            let taskName = $('input[name="taskName"]').val()
            let species = $('select[name="species"]').val()
            let version = $('select[name="version"]').val()

            let formData = new FormData()
            formData.append('taskName', taskName)
            formData.append('species', species)
            formData.append('version', version)


            $('#fastq_tbody').find('tr').each(function (i) {
                let tds = $(this).find('td');
                let runName  = tds.eq(1).find('input:eq(0)').val();
                let slideId = tds.eq(6).find('input:eq(0)').val();
                let areaId = tds.eq(7).find('input:eq(0)').val();

                tds.eq(2).find('em.seled:eq(0)').find('b').each(function (i2) {
                    let key = 'i1';
                    let path = $(this).find('input:eq(0)').val()
                    let name = $(this).find('input:eq(1)').val()
                    formData.append(`fileParam[${i}].${key}[${i2}].path`, path)
                    formData.append(`fileParam[${i}].${key}[${i2}].name`, name)
                })
                tds.eq(3).find('em.seled:eq(0)').find('b').each(function (i2) {
                    let key = 'r1';
                    let path = $(this).find('input:eq(0)').val()
                    let name = $(this).find('input:eq(1)').val()
                    formData.append(`fileParam[${i}].${key}[${i2}].path`, path)
                    formData.append(`fileParam[${i}].${key}[${i2}].name`, name)
                })
                tds.eq(4).find('em.seled:eq(0)').find('b').each(function (i2) {
                    let key = 'r2';
                    let path = $(this).find('input:eq(0)').val()
                    let name = $(this).find('input:eq(1)').val()
                    formData.append(`fileParam[${i}].${key}[${i2}].path`, path)
                    formData.append(`fileParam[${i}].${key}[${i2}].name`, name)
                })

                let _imageFile =  tds.eq(5).find('em.seled:eq(0)').find('b').eq(0);
                if(_imageFile.length > 0) {
                    let key = 'imageFile';
                    let path = _imageFile.find('input:eq(0)').val()
                    let name = _imageFile.find('input:eq(1)').val()
                    formData.append(`fileParam[${i}].${key}.path`, path)
                    formData.append(`fileParam[${i}].${key}.name`, name)
                }
                let _loupeFile =  tds.eq(8).find('em.seled:eq(0)').find('b').eq(0);
                if(_loupeFile.length > 0) {
                    let key = 'loupeFile';
                    let path = _loupeFile.find('input:eq(0)').val()
                    let name = _loupeFile.find('input:eq(1)').val()
                    formData.append(`fileParam[${i}].${key}.path`, path)
                    formData.append(`fileParam[${i}].${key}.name`, name)
                }

                formData.append(`fileParam[${i}].runName`, runName)
                formData.append(`fileParam[${i}].slideId`, slideId)
                formData.append(`fileParam[${i}].areaId`, areaId)
            })

            $.ajax({
                url: '/analysis/strnaseq/save',
                method: 'post',
                dataType: 'json',
                contentType: false,
                processData: false,
                data: formData,
                success: function (result) {
                    if (result.success) {
                        window.location.href = '[[@{/analysis/strnaseq/list}]]'
                    } else {
                        layer.msg(result.message)
                        $('#submitBtn').attr('onclick', 'uploadMetadata()')
                    }
                }
            })
        }
    </script>
</th:block>
</html>
