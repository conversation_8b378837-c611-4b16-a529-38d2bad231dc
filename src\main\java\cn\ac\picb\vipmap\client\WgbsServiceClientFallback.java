package cn.ac.picb.vipmap.client;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.wgbs.dto.WgbsTaskDTO;
import cn.ac.picb.wgbs.po.WgbsTaskPO;
import cn.ac.picb.wgbs.vo.WgbsChartFilterVO;
import cn.ac.picb.wgbs.vo.WgbsTaskInput;
import cn.ac.picb.wgbs.vo.WgbsTaskParamVO;
import cn.ac.picb.wgbs.vo.WgbsTaskQueryVO;
import feign.Response;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class WgbsServiceClientFallback implements WgbsServiceClient {

    private final static String SERVER_NAME = "wgbs-service";


    @Override
    public CommonResult<PageResult<WgbsTaskPO>> findTaskPage(WgbsTaskQueryVO wgbsTaskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<WgbsTaskPO> saveTask(WgbsTaskParamVO wgbsTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<WgbsTaskPO> findById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<WgbsTaskDTO> findDetailById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<WgbsTaskPO> deleteById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadResult(String taskId, Integer step, String displayName) {
        return null;
    }


    @Override
    public Response downloadTemplateExcel() {
        return null;
    }

    @Override
    public CommonResult<Object> getChartData(WgbsChartFilterVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<String> diskCount() {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadChart(WgbsChartFilterVO vo) {
        return null;
    }

    @Override
    public CommonResult<List<WgbsTaskInput>> uploadDataExcel(String s, MultipartFile multipartFile) {
        return serverError(SERVER_NAME);
    }


}
