package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnaseq.client.ScrnaseqFileServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqFileServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "scrnaseq-service", contextId = "file", fallback = ScrnaseqFileServiceClientFallback.class)
public interface ScrnaseqFileServiceClient extends ScrnaseqFileServiceApi {
}
