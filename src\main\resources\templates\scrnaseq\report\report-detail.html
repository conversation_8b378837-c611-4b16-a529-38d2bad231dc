<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}"
      th:with="reportTask=${vo.reportTask},
      genomicsTaskDTO=${vo.genomicsTaskDTO},
      genomicsTask=${genomicsTaskDTO.genomicsTask},
      baselineTask=${vo.baselineTask},
      pagaTask=${vo.pagaTask},
      degTask=${vo.degTask},
      genesTask=${vo.genesTask}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Report</div>
                    <ul class="detail-info row mb-0">
                        <li class="col-xl-4 col-lg-6"><span class="text-muted auto pr-2">Report ID:</span>
                            <div th:text="${reportTask.taskId}"></div>
                        </li>
                        <li class="col-xl-4 col-lg-6"><span class="text-muted auto pr-2">Filter matrix:</span>
                            <div th:text="${genomicsTask.taskId}">1120040300001</div>
                        </li>
                        <li class="col-xl-4 col-lg-6" th:if="${baselineTask != null}"><span class="text-muted auto pr-2">Clustering:</span>
                            <div th:text="${baselineTask.taskId}">1120040300001</div>
                        </li>
                        <li class="col-xl-4 col-lg-6" th:if="${pagaTask != null}"><span class="text-muted auto pr-2">Graph and trajectory:</span>
                            <div th:text="${pagaTask.taskId}">1120040300001</div>
                        </li>
                        <li class="col-xl-4 col-lg-6" th:if="${degTask != null}"><span class="text-muted auto pr-2">DEG and enrichment:</span>
                            <div th:text="${degTask.taskId}">1120040300001</div>
                        </li>
                        <li class="col-xl-4 col-lg-6" th:if="${genesTask != null}"><span class="text-muted auto pr-2">Expression and correlation:</span>
                            <div th:text="${genesTask.taskId}">1120040300001</div>
                        </li>
                        <li class="col-xl-4 col-lg-6"><span class="text-muted auto pr-2">Status:</span>
                            <div th:if="${reportTask.status == 1}"><span class="text-info"><i class="fa fa-circle"></i> Running</span></div>
                            <div th:if="${reportTask.status == 2}"><span class="text-info"><i class="fa fa-circle"></i> Complete</span></div>
                        </li>
                        <li class="col-xl-4 col-lg-6" th:if="${reportTask.status == 2}"><span class="text-muted auto pr-2">Download:</span>
                            <div><a th:href="@{/analysis/scrnaseq/report/download(taskId=${reportTask.taskId})}" class="text-primary" title="Download"><i class="fa fa-download font-14"></i> </a></div>
                        </li>
                    </ul>

                    <div class="form-group-box">
                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary mb-2">Parameters</a>
                        <div class="collapse show" id="coll-1">
                            <hr>
                            <th:block th:if="${genomicsTaskDTO != null}">
                                <a href="#coll-1-01" data-toggle="collapse" class="h6 text-primary ml-2 d-block">10X genomics</a>
                                <div class="collapse show" id="coll-1-01">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <h5 class="mb-3 font-16"><span>Expression matrix</span></h5>
                                            <th:block th:if="${genomicsTask.dataMode == 'fastq'}">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-center table-middle">
                                                        <thead>
                                                        <tr class="thead-light">
                                                            <th>Sample ID</th>
                                                            <th>I1</th>
                                                            <th>R1</th>
                                                            <th>R2</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr th:each="svo : ${vo.genomicsTaskDTO.matrix}">
                                                            <td th:text="${svo.sampleId}">HG00114</td>
                                                            <td>
                                                                <th:block th:each="file, sta : ${svo.row.i1}">
                                                                    <span class="font-12" th:text="${file.path}">Sample1_S1_L001_R1_001.fastq.gz</span><br th:unless="${sta.last}">
                                                                </th:block>
                                                            </td>
                                                            <td>
                                                                <th:block th:each="file, sta : ${svo.row.r1}">
                                                                    <span class="font-12" th:text="${file.path}">Sample1_S1_L001_R1_001.fastq.gz</span><br th:unless="${sta.last}">
                                                                </th:block>
                                                            </td>
                                                            <td>
                                                                <th:block th:each="file, sta : ${svo.row.r2}">
                                                                    <span class="font-12" th:text="${file.path}">Sample1_S1_L001_R1_001.fastq.gz</span><br th:unless="${sta.last}">
                                                                </th:block>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                                <div class="form-group row align-items-center m-0">
                                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                                        <span class="text-primary" th:text="${genomicsTask.species}">Homo sapiens（human）</span>
                                                    </div>
                                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Version</label>
                                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                                        <span class="text-primary" th:text="${genomicsTask.version}">GRCH38（hg38）</span>
                                                    </div>
                                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                                        <span class="text-primary" th:text="${genomicsTask.mapMethod}">STAR</span>
                                                    </div>
                                                </div>
                                                <h6 class="border-bottom pb-2 m-0">Counting</h6>
                                                <div class="form-group row align-items-center m-0">
                                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                                        <span class="text-primary" th:text="${genomicsTask.countMethod}">cellranger</span>
                                                    </div>
                                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Option</label>
                                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                                        <span class="text-primary" th:text="${genomicsTask.rangerMode}">count</span>
                                                    </div>
                                                </div>
                                            </th:block>

                                            <th:block th:if="${genomicsTask.dataMode == 'matrix'}">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-center table-middle">
                                                        <thead>
                                                        <tr class="thead-light">
                                                            <th>Sample ID</th>
                                                            <td>barcodes.tsv.gz</td>
                                                            <td>features.tsv.gz</td>
                                                            <td>matrix.mtx.gz</td>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr th:each="svo : ${vo.genomicsTaskDTO.matrix}">
                                                            <td th:text="${svo.sampleId}">HG00114</td>
                                                            <td>
                                                                <span class="font-12" th:text="${svo.row.barcode.path}">Sample1_S1_L001_R1_001.fastq.gz</span>
                                                            </td>
                                                            <td>
                                                                <span class="font-12" th:text="${svo.row.barcode.path}">Sample1_S1_L001_R1_001.fastq.gz</span>
                                                            </td>
                                                            <td>
                                                                <span class="font-12" th:text="${svo.row.barcode.path}">Sample1_S1_L001_R1_001.fastq.gz</span>

                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                                <div class="form-group row align-items-center m-0">
                                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                                        <span class="text-primary" th:text="${genomicsTask.species}">Homo sapiens（human）</span>
                                                    </div>
                                                </div>
                                            </th:block>

                                            <th:block th:if="${genomicsTask.dataMode == 'csv'}">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-center table-middle">
                                                        <thead>
                                                        <tr class="thead-light">
                                                            <th>Sample ID</th>
                                                            <th>csv file</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr th:each="svo : ${vo.genomicsTaskDTO.matrix}">
                                                            <td th:text="${svo.sampleId}">HG00114</td>
                                                            <td>
                                                                <span class="font-12" th:text="${svo.row.csv.path}">Sample1_S1_L001_R1_001.fastq.gz</span>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                                <div class="form-group row align-items-center m-0">
                                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                                        <span class="text-primary" th:text="${genomicsTask.species}">Homo sapiens（human）</span>
                                                    </div>
                                                </div>
                                            </th:block>

                                            <h5 class="mb-1 font-16"><span>Post processing</span></h5>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                                <div class="col-xl-3 col-lg-3 col-md-3">
                                                    <span class="text-primary">Seurat</span>
                                                </div>
                                            </div>
                                            <h6 class="border-bottom pb-2 m-0">Cell filter</h6>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">number of nGene</label>
                                                <div class="col-xl-2 col-lg-2col-md-4">
                                                    <span class="text-primary">[[${genomicsTask.nGeneStart}]] - [[${genomicsTask.nGeneEnd}]]</span>
                                                </div>
                                                <label class="col-xl-3 col-lg-2 col-md-3 col-form-label pr-0">percentage of mitochondrial genes</label>
                                                <div class="col-xl-2 col-lg-2col-md-4">
                                                    <span class="text-primary">[[${genomicsTask.toPerStart}]] - [[${genomicsTask.toPerEnd}]]</span>
                                                </div>
                                            </div>
                                            <h6 class="border-bottom pb-2 m-0">Clustering</h6>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">resolution parameter</label>
                                                <div class="col-xl-2 col-lg-2col-md-4">
                                                    <span class="text-primary" th:text="${genomicsTask.resolution}">0.8</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-baseline m-0" th:unless="${#strings.isEmpty(genomicsTask.TGene)}">
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">t-SNE genes</label>
                                                <div class="col-xl-2 col-lg-2col-md-4">
                                                    <th:block th:each="gene, sta : ${#strings.listSplit(genomicsTask.TGene, ';')}">
                                                        <span class="text-primary" th:text="${gene}">MS4A1</span><br th:unless="${sta.last}">
                                                    </th:block>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </th:block>
                            <th:block th:if="${baselineTask != null}">
                                <a href="#coll-1-02" data-toggle="collapse" class="h6 text-primary ml-2 d-block">Baseline</a>
                                <div class="collapse show" id="coll-1-02">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <div class="table-responsive mt-2">
                                                <table class="table table-bordered table-sm table-center table-middle mb-1">
                                                    <thead>
                                                    <tr class="thead-light">
                                                        <th>Cluster Genomics ID</th>
                                                        <th>Start time</th>
                                                        <th>Status time</th>
                                                        <th>Consuming</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td th:text="${genomicsTask.taskId}"></td>
                                                        <td th:text="${#dates.format(genomicsTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                        <td th:text="${#dates.format(genomicsTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                        <td th:text="${genomicsTask.useTime}">26m55s</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-3 col-lg-2 col-md-3 col-form-label pr-0">Number of principle components</label>
                                                <div class="col-xl-2 col-lg-2 col-md-4">
                                                    <span class="text-primary" th:text="${baselineTask.selectPca}">10</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Annotation Method</label>
                                                <div class="col-xl-2 col-lg-2 col-md-4">
                                                    <span class="text-primary" th:text="${baselineTask.annMethod}">Elham A, et al</span>
                                                </div>
                                            </div>
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-sm table-center table-middle">
                                                    <thead>
                                                    <tr class="thead-light">
                                                        <th>Cell type</th>
                                                        <th>Gene marker</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody th:with="types=${#strings.listSplit(baselineTask.cellType,';')}, markers=${#strings.listSplit(baselineTask.geneMarker,';')}">
                                                    <tr th:each="idx: ${#numbers.sequence(0, types.size()-1)}">
                                                        <td th:text="${types.get(idx)}">B Cells</td>
                                                        <td>
                                                            <span class="font-12" th:text="${markers.get(idx)}">CD19</span>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </th:block>
                            <th:block th:if="${pagaTask !=null}">
                                <a href="#coll-1-03" data-toggle="collapse" class="h6 text-primary ml-2 d-block">PAGA & Pseudotime trajectory </a>
                                <div class="collapse show" id="coll-1-03">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <div class="table-responsive mt-2">
                                                <table class="table table-bordered table-sm table-center table-middle mb-1">
                                                    <thead>
                                                    <tr class="thead-light">
                                                        <th>Cluster Baseline ID</th>
                                                        <th>Start time</th>
                                                        <th>Status time</th>
                                                        <th>Consuming</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td th:text="${baselineTask.taskId}"></td>
                                                        <td th:text="${#dates.format(baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                        <td th:text="${#dates.format(baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                        <td th:text="${baselineTask.useTime}">26m55s</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-1 col-lg-1 col-md-3 col-form-label pr-0">neighbor</label>
                                                <div class="col-xl-3 col-lg-3 col-md-4">
                                                    <span class="text-primary" th:text="${pagaTask.neighbors}">value</span>
                                                </div>
                                                <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">threshold</label>
                                                <div class="col-xl-3 col-lg-3 col-md-4">
                                                    <span class="text-primary" th:text="${pagaTask.threshold}">value</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-baseline m-0">
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Compare cluster</label>
                                                <div class="col-xl-4 col-lg-4 col-md-4" th:with="clus=${#strings.listSplit(pagaTask.MCluster, ';')}">
                                                    <th:block th:each="c, sta : ${clus}">
                                                        <span class="text-primary" th:text="${c}">Cluster_1</span><br th:unless="${sta.last}">
                                                    </th:block>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </th:block>
                            <th:block th:if="${degTask !=null}">
                                <a href="#coll-1-04" data-toggle="collapse" class="h6 text-primary ml-2 d-block">DEG & GSVA</a>
                                <div class="collapse show" id="coll-1-04">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <div class="table-responsive mt-2">
                                                <table class="table table-bordered table-sm table-center table-middle mb-1">
                                                    <thead>
                                                    <tr class="thead-light">
                                                        <th>Cluster Baseline ID</th>
                                                        <th>Start time</th>
                                                        <th>Status time</th>
                                                        <th>Consuming</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td th:text="${baselineTask.taskId}"></td>
                                                        <td th:text="${#dates.format(baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                        <td th:text="${#dates.format(baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                        <td th:text="${baselineTask.useTime}">26m55s</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <div class="form-group row align-items-baseline m-0" th:if="${degTask.degMode == 'single'}">
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Compare cluster</label>
                                                <div class="col-xl-2 col-lg-2 col-md-4">
                                                    <span class="text-primary" th:text="${degTask.cluster1}">Cluster_1</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-baseline m-0" th:if="${degTask.degMode == 'multi'}">
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Compare cluster</label>
                                                <div class="col-xl-2 col-lg-2 col-md-4" th:with="cluster1s=${#strings.listSplit(degTask.cluster1, ';')}">
                                                    <th:block th:each="cluster1, sta : ${cluster1s}">
                                                        <span class="text-primary" th:text="${cluster1}">Cluster_1</span><br th:unless="${sta.last}">
                                                    </th:block>
                                                </div>
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">with cluster</label>
                                                <div class="col-xl-2 col-lg-2 col-md-4" th:with="cluster2s=${#strings.listSplit(degTask.cluster2, ';')}">
                                                    <th:block th:each="cluster2, sta : ${cluster2s}">
                                                        <span class="text-primary" th:text="${cluster2}">Cluster_1</span><br th:unless="${sta.last}">
                                                    </th:block>
                                                </div>
                                            </div>

                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Fold change(log2)</label>
                                                <div class="col-xl-2 col-lg-2 col-md-4">
                                                    <span class="text-primary" th:text="${degTask.cutfc}">0.05</span>
                                                </div>
                                                <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">FDR</label>
                                                <div class="col-xl-2 col-lg-2 col-md-4">
                                                    <span class="text-primary" th:text="${degTask.cutp}">0.05</span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Database</label>
                                                <div class="col-xl-2 col-lg-2 col-md-4">
                                                    <span class="text-primary" th:text="${degTask.annoDb}">REACTOME</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </th:block>
                            <th:block th:if="${genesTask !=null}">
                                <a href="#coll-1-05" data-toggle="collapse" class="h6 text-primary ml-2 d-block">Genes</a>
                                <div class="collapse show" id="coll-1-05">
                                    <div class="pl-4 pt-2">
                                        <div class="result-box">
                                            <div class="table-responsive mt-2">
                                                <table class="table table-bordered table-sm table-center table-middle mb-1">
                                                    <thead>
                                                    <tr class="thead-light">
                                                        <th>Cluster Baseline ID</th>
                                                        <th>Start time</th>
                                                        <th>Status time</th>
                                                        <th>Consuming</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td th:text="${baselineTask.taskId}"></td>
                                                        <td th:text="${#dates.format(baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                        <td th:text="${#dates.format(baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                        <td th:text="${baselineTask.useTime}">26m55s</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Input your genes</label>
                                                <div class="col-xl-10 col-lg-10 col-md-10">
                                                    <span class="badge badge-primary" th:each="gene:${#strings.listSplit(genesTask.genes, ';')}" th:text="${gene}">CCL17</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </th:block>
                        </div>
                    </div>
                    <div class="form-group-box">
                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                        <div class="collapse show tabs-ana" id="coll-2">
                            <ul class="nav nav-pills" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" data-toggle="tab" href="#tabs-a01" role="tab" aria-selected="true">
                                        Cluster
                                    </a>
                                </li>
                                <li class="nav-item" th:if="${#strings.listSplit(genomicsTask.sampleIds,',').size() > 1 }">
                                    <a class="nav-link" data-toggle="tab" href="#tabs-a02" role="tab" aria-selected="false">
                                        Sample
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-toggle="tab" href="#tabs-a03" role="tab" aria-selected="false">
                                        Gene
                                    </a>
                                </li>
                                <li class="nav-item" th:if="${baselineTask != null}">
                                    <a class="nav-link" data-toggle="tab" href="#tabs-a04" role="tab" aria-selected="false">
                                        Cell type
                                    </a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="tabs-a01" role="tabpanel">
                                    <div class="p-2 text-center">
                                        <div id="chart-01" style="width: 800px;height: 800px" th:if="${baselineTask == null}"></div>
                                        <div id="chart-062" style="width: 800px;height: 800px" th:if="${baselineTask != null}"></div>
                                    </div>
                                </div>
                                <div class="tab-pane" id="tabs-a02" role="tabpanel" th:if="${#strings.listSplit(genomicsTask.sampleIds,',').size() > 1 }">
                                    <div class="p-2 text-center">
                                        <div id="chart-02" style="width: 800px;height: 800px" th:if="${baselineTask == null}"></div>
                                        <div id="chart-05" style="width: 800px;height: 800px" th:if="${baselineTask != null}"></div>
                                    </div>
                                </div>
                                <div class="tab-pane" id="tabs-a03" role="tabpanel">
                                    <form action="" class="form-inline">
                                        <div class="form-group">
                                            <select id="gene-select" class="form-control form-control-sm"></select>
                                        </div>
                                    </form>
                                    <div class="p-2 text-center">
                                        <div id="chart-04" style="width: 800px;height: 800px"></div>
                                    </div>
                                </div>
                                <div class="tab-pane" id="tabs-a04" role="tabpanel" th:if="${baselineTask != null}">
                                    <div class="p-2 text-center">
                                        <div id="chart-061" style="width: 800px;height: 800px"></div>
                                    </div>
                                </div>
                            </div>
                            <th:block th:if="${pagaTask !=null}">
                                <hr>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="p-2 text-center">
                                            <h6 class="font-weight-bold text-center mb-2">PAGA</h6>
                                            <div id="chart-08" style="width: 100%;height: 500px"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="p-2 text-center">
                                            <h6 class="font-weight-bold text-center mb-2">Pseudotime trajectory</h6>
                                            <div class="row" id="chart-09" style="width: 100%;height: 500px"></div>
                                        </div>
                                    </div>
                                </div>
                            </th:block>
                            <th:block th:if="${degTask !=null}">
                                <hr>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="p-2 text-center">
                                            <h6 class="font-weight-bold text-center mb-2">Volcano Plot</h6>
                                            <div id="chart-10" style="width: 100%;height: 500px"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="p-2 text-center">
                                            <h6 class="font-weight-bold text-center mb-2">Gene Set Enrichment</h6>
                                            <div id="chart-11" style="width: 100%;height: 500px"></div>
                                        </div>
                                    </div>
                                </div>
                            </th:block>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>

    <!--  cluster 标签  -->
    <script th:if="${baselineTask == null}">
        $(document).ready(function () {
            initChart01();
        })

        function initChart01() {
            if (!document.getElementById('chart-01')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-01'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/genomics/[[${genomicsTask.id}]]/1',
                    beforeSend: function () {
                        $("#chart-01").next().remove();
                        $("#chart-01").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-01").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-01").hide();
                            return;
                        }

                        var data = result.data;
                        var legendData = [];
                        var series = [];

                        for (var group in data) {
                            legendData.push(group);
                            series.push({
                                name: group,
                                data: data[group],
                                type: 'scatter',
                                symbolSize: 5,
                                cursor: 'default',
                                itemStyle: {
                                    color: getClusterColor(group)
                                }
                            });
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: legendData
                            },
                            xAxis: {
                                name: 'tSNE_1',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameLocation: 'center',
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            yAxis: {
                                name: 'tSNE_2',
                                nameLocation: 'center',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            series: series
                        })
                    }
                })
            }
        }
    </script>
    <script th:if="${baselineTask != null}">
        $(document).ready(function () {
            initChart062();
        })

        function initChart062() {
            if (!document.getElementById('chart-062')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-062'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/baseline/[[${baselineTask.id}]]/62',
                    beforeSend: function () {
                        $("#chart-062").next().remove();
                        $("#chart-062").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-062").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-062").hide();
                            return;
                        }

                        var data = result.data;
                        var legendData = [];
                        var series = [];

                        for (var group in data) {
                            legendData.push(group);
                            series.push({
                                name: group,
                                data: data[group],
                                type: 'scatter',
                                symbolSize: 5,
                                cursor: 'default',
                                itemStyle: {
                                    color: getClusterColor(group)
                                }
                            });
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: legendData
                            },
                            xAxis: {
                                name: 'tSNE_1',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameLocation: 'center',
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            yAxis: {
                                name: 'tSNE_2',
                                nameLocation: 'center',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            series: series
                        })
                    }
                })
            }
        }
    </script>
    <!--  sample 标签  -->
    <script th:if="${#strings.listSplit(genomicsTask.sampleIds,',').size() > 1 and baselineTask == null }">
        $(document).ready(function () {
            initChart02();
        })

        function initChart02() {
            if (!document.getElementById('chart-02')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-02'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/genomics/[[${genomicsTask.id}]]/2',
                    beforeSend: function () {
                        $("#chart-02").next().remove();
                        $("#chart-02").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-02").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-02").hide();
                            return;
                        }

                        var data = result.data;
                        var legendData = [];
                        var series = [];

                        for (var group in data) {
                            legendData.push(group);
                            series.push({
                                name: group,
                                data: data[group],
                                type: 'scatter',
                                symbolSize: 5,
                                cursor: 'default'
                            });
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: legendData
                            },
                            xAxis: {
                                name: 'tSNE_1',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameLocation: 'center',
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            yAxis: {
                                name: 'tSNE_2',
                                nameLocation: 'center',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            series: series
                        })

                        $(function () {
                            window.onresize = function () {
                                myChart.resize();
                            }
                        })
                    }
                })
            }
        }
    </script>
    <script th:if="${#strings.listSplit(vo.genomicsTaskDTO.genomicsTask.sampleIds,',').size() > 1 and baselineTask != null }">
        $(document).ready(function () {
            initChart05();
        })

        function initChart05() {
            if (!document.getElementById('chart-05')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-05'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/baseline/[[${baselineTask.id}]]/5',
                    beforeSend: function () {
                        $("#chart-05").next().remove();
                        $("#chart-05").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-05").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-05").hide();
                            return;
                        }

                        var data = result.data, sData = data.sData, xData = data.xData, sd = [];
                        for (var item in sData) {
                            sd.push({
                                name: item,
                                type: 'bar',
                                stack: 'batch_level',
                                data: sData[item],
                                itemStyle: {
                                    color: getClusterColor(item)
                                }
                            })
                        }
                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                                    type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: [
                                {
                                    name: 'sample',
                                    nameLocation: 'center',
                                    nameGap: 20,
                                    type: 'category',
                                    data: xData
                                }
                            ],
                            yAxis: [
                                {
                                    name: 'fraction of cell type',
                                    nameGap: 25,
                                    nameLocation: 'center',
                                    type: 'value'
                                }
                            ],
                            series: sd
                        })

                        $(function () {
                            window.onresize = function () {
                                myChart.resize();
                            }
                        })
                    }
                })
            }
        }
    </script>

    <!--  gene 标签  -->
    <script th:if="${baselineTask == null}">
        function initOption() {
            $.ajax({
                url: '/analysis/scrnaseq/report/getGeneNames',
                data: {
                    genomicsId: '[[${genomicsTask.taskId}]]'
                },
                beforeSend: function () {
                    $("#gene-select").html('');
                },
                success: function (result) {
                    var data = result.data;

                    var html = [];
                    var i = 0;

                    for (var group in data) {
                        html.push('<optgroup label="' + group + ' genes">')
                        data[group].forEach(function (name) {
                            if (i == 0) {
                                html.push('<option selected data-type="' + group + '" data-val="' + name + '">' + name + '</option>');
                            } else {
                                html.push('<option data-type="' + group + '" data-val="' + name + '">' + name + '</option>');
                            }
                            i++;
                        })
                        html.push('</optgroup>');
                    }
                    $("#gene-select").html(html.join(''));

                    initChart();
                }
            })
        }

        function initChart() {
            var type = $("#gene-select option:selected").data('type');
            var gene = $("#gene-select option:selected").data('val');

            $.ajax({
                url: '/analysis/scrnaseq/report/geneData',
                data: {
                    genomicsId: '[[${genomicsTask.taskId}]]',
                    type: type,
                    gene: gene
                },
                success: function (result) {
                    var data = result.data;
                    var d = data.map(function (p) {
                        if (p[2] == 0) {
                            return {
                                value: [p[0], p[1], p[2]],
                                itemStyle: {
                                    color: 'rgb(110,184,202)'
                                }
                            }
                        } else {
                            return {
                                value: [p[0], p[1], p[2]],
                                itemStyle: {
                                    color: 'rgba(42, 6, 141,' + (0.5 + p[2] / 10) + ')'
                                }
                            }
                        }
                    })
                    var option = {
                        title: {
                            text: gene
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                                type: 'cross'        // 默认为直线，可选为：'line' | 'shadow'
                            },
                            formatter: function (params) {
                                return params[0].data.value[2];
                            }
                        },
                        toolbox: {
                            show: true,
                            feature: {
                                saveAsImage: {}
                            }
                        },
                        xAxis: {
                            name: 'tSNE_1',
                            nameTextStyle: {
                                fontWeight: 'bold'
                            },
                            nameLocation: 'center',
                            nameGap: 35,
                            splitLine: {
                                show: false
                            }
                        },
                        yAxis: {
                            name: 'tSNE_2',
                            nameLocation: 'center',
                            nameTextStyle: {
                                fontWeight: 'bold'
                            },
                            nameGap: 35,
                            splitLine: {
                                show: false
                            }
                        },
                        series: [{
                            data: d,
                            type: 'scatter',
                            symbolSize: 5,
                            cursor: 'default'
                        }]
                    }
                    var myChart = echarts.init(document.getElementById('chart-04'));
                    myChart.clear();
                    myChart.setOption(option);

                    $(function () {
                        window.onresize = function () {
                            myChart.resize();
                        }
                    })
                }
            })
        }

        $(document).ready(function () {
            initOption();
            $("#gene-select").on('change', initChart)
        })
    </script>
    <script th:if="${baselineTask != null and genesTask == null}">
        function initOption() {
            $.ajax({
                url: '/analysis/scrnaseq/report/getGeneNames',
                data: {
                    genomicsId: '[[${genomicsTask.taskId}]]',
                    baselineId: '[[${baselineTask.taskId}]]'
                },
                beforeSend: function () {
                    $("#gene-select").html('');
                },
                success: function (result) {
                    var data = result.data;

                    var html = [];
                    var i = 0;
                    for (var group in data) {
                        html.push('<optgroup label="' + group + ' genes">')
                        data[group].forEach(function (name) {
                            if (i == 0) {
                                html.push('<option selected data-type="' + group + '" data-val="' + name + '">' + name + '</option>');
                            } else {
                                html.push('<option data-type="' + group + '" data-val="' + name + '">' + name + '</option>');
                            }
                            i++;
                        })
                        html.push('</optgroup>');
                    }
                    $("#gene-select").html(html.join(''));

                    initChart();
                }
            })
        }

        function initChart() {
            var type = $("#gene-select option:selected").data('type');
            var gene = $("#gene-select option:selected").data('val');

            $.ajax({
                url: '/analysis/scrnaseq/report/geneData',
                data: {
                    genomicsId: '[[${genomicsTask.taskId}]]',
                    baselineId: '[[${baselineTask.taskId}]]',
                    type: type,
                    gene: gene
                },
                success: function (result) {
                    var data = result.data;
                    var d = data.map(function (p) {
                        if (p[2] == 0) {
                            return {
                                value: [p[0], p[1], p[2]],
                                itemStyle: {
                                    color: 'rgb(110,184,202)'
                                }
                            }
                        } else {
                            return {
                                value: [p[0], p[1], p[2]],
                                itemStyle: {
                                    color: 'rgba(42, 6, 141,' + (0.5 + p[2] / 10) + ')'
                                }
                            }
                        }
                    })
                    var option = {
                        title: {
                            text: gene
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                                type: 'cross'        // 默认为直线，可选为：'line' | 'shadow'
                            },
                            formatter: function (params) {
                                return params[0].data.value[2];
                            }
                        },
                        toolbox: {
                            show: true,
                            feature: {
                                saveAsImage: {}
                            }
                        },
                        xAxis: {
                            name: 'tSNE_1',
                            nameTextStyle: {
                                fontWeight: 'bold'
                            },
                            nameLocation: 'center',
                            nameGap: 35,
                            splitLine: {
                                show: false
                            }
                        },
                        yAxis: {
                            name: 'tSNE_2',
                            nameLocation: 'center',
                            nameTextStyle: {
                                fontWeight: 'bold'
                            },
                            nameGap: 35,
                            splitLine: {
                                show: false
                            }
                        },
                        series: [{
                            data: d,
                            type: 'scatter',
                            symbolSize: 5,
                            cursor: 'default'
                        }]
                    }
                    var myChart = echarts.init(document.getElementById('chart-04'));
                    myChart.clear();
                    myChart.setOption(option);

                    $(function () {
                        window.onresize = function () {
                            myChart.resize();
                        }
                    })
                }
            })
        }

        $(document).ready(function () {
            initOption();
            $("#gene-select").on('change', initChart)
        })
    </script>
    <script th:if="${baselineTask != null and genesTask != null}">
        function initOption() {
            $.ajax({
                url: '/analysis/scrnaseq/report/getGeneNames',
                data: {
                    genomicsId: '[[${genomicsTask.taskId}]]',
                    baselineId: '[[${baselineTask.taskId}]]',
                    genesId: '[[${genesTask.taskId}]]'
                },
                beforeSend: function () {
                    $("#gene-select").html('');
                },
                success: function (result) {
                    var data = result.data;

                    var html = [];
                    var i = 0;
                    for (var group in data) {
                        html.push('<optgroup label="' + group + ' genes">')
                        data[group].forEach(function (name) {
                            if (i == 0) {
                                html.push('<option selected data-type="' + group + '" data-val="' + name + '">' + name + '</option>');
                            } else {
                                html.push('<option data-type="' + group + '" data-val="' + name + '">' + name + '</option>');
                            }
                            i++;
                        })
                        html.push('</optgroup>');
                    }
                    $("#gene-select").html(html.join(''));

                    initChart();
                }
            })
        }

        function initChart() {
            var type = $("#gene-select option:selected").data('type');
            var gene = $("#gene-select option:selected").data('val');

            $.ajax({
                url: '/analysis/scrnaseq/report/geneData',
                data: {
                    genomicsId: '[[${genomicsTask.taskId}]]',
                    baselineId: '[[${baselineTask.taskId}]]',
                    genesId: '[[${genesTask.taskId}]]',
                    type: type,
                    gene: gene
                },
                success: function (result) {
                    var data = result.data;
                    var d = data.map(function (p) {
                        if (p[2] == 0) {
                            return {
                                value: [p[0], p[1], p[2]],
                                itemStyle: {
                                    color: 'rgb(110,184,202)'
                                }
                            }
                        } else {
                            return {
                                value: [p[0], p[1], p[2]],
                                itemStyle: {
                                    color: 'rgba(42, 6, 141,' + (0.5 + p[2] / 10) + ')'
                                }
                            }
                        }
                    })
                    var option = {
                        title: {
                            text: gene
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                                type: 'cross'        // 默认为直线，可选为：'line' | 'shadow'
                            },
                            formatter: function (params) {
                                return params[0].data.value[2];
                            }
                        },
                        toolbox: {
                            show: true,
                            feature: {
                                saveAsImage: {}
                            }
                        },
                        xAxis: {
                            name: 'tSNE_1',
                            nameTextStyle: {
                                fontWeight: 'bold'
                            },
                            nameLocation: 'center',
                            nameGap: 35,
                            splitLine: {
                                show: false
                            }
                        },
                        yAxis: {
                            name: 'tSNE_2',
                            nameLocation: 'center',
                            nameTextStyle: {
                                fontWeight: 'bold'
                            },
                            nameGap: 35,
                            splitLine: {
                                show: false
                            }
                        },
                        series: [{
                            data: d,
                            type: 'scatter',
                            symbolSize: 5,
                            cursor: 'default'
                        }]
                    }
                    var myChart = echarts.init(document.getElementById('chart-04'));
                    myChart.clear();
                    myChart.setOption(option);

                    $(function () {
                        window.onresize = function () {
                            myChart.resize();
                        }
                    })
                }
            })
        }

        $(document).ready(function () {
            initOption();
            $("#gene-select").on('change', initChart)
        })
    </script>

    <!--  celltype 标签  -->
    <script th:if="${baselineTask != null}">
        $(document).ready(function () {
            initChart061();
        })

        function initChart061() {
            if (!document.getElementById('chart-061')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-061'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/baseline/[[${baselineTask.id}]]/61',
                    beforeSend: function () {
                        $("#chart-061").next().remove();
                        $("#chart-061").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-061").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-061").hide();
                            return;
                        }

                        var data = result.data;
                        var legendData = [];
                        var series = [];

                        for (var group in data) {
                            legendData.push(group);
                            series.push({
                                name: group,
                                data: data[group],
                                type: 'scatter',
                                symbolSize: 5,
                                cursor: 'default'
                            });
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: legendData
                            },
                            xAxis: {
                                name: 'tSNE_1',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameLocation: 'center',
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            yAxis: {
                                name: 'tSNE_2',
                                nameLocation: 'center',
                                nameTextStyle: {
                                    fontWeight: 'bold'
                                },
                                nameGap: 35,
                                splitLine: {
                                    show: false
                                }
                            },
                            series: series
                        })

                        $(function () {
                            window.onresize = function () {
                                myChart.resize();
                            }
                        })
                    }
                })
            }
        }
    </script>

    <!--  第二大块  -->
    <script th:if="${pagaTask != null}">
        $(document).ready(function () {
            initChart08();
            initChart09();
        })

        function initChart09() {
            if (!document.getElementById('chart-09')) {
                return;
            }

            drawChart();

            function drawChart() {
                let myChart1, myChart2;
                $.ajax({
                    url: '/analysis/scrnaseq/paga/[[${pagaTask.id}]]/9',
                    beforeSend: function () {
                        $("#chart-09").empty();
                        $('#chart-09').append(`
                            <div id="chart-09-1" class="col-lg-6"
                                 style="width: 50%;height: 500px"></div>
                            <div id="chart-09-2" class="col-lg-6"
                                 style="width: 50%;height: 500px"></div>
                        `)
                        $("#chart-09").show();

                        myChart1 = echarts.init(document.getElementById('chart-09-1'));
                        myChart2 = echarts.init(document.getElementById('chart-09-2'));
                        myChart1.clear();
                        myChart2.clear();
                        myChart1.showLoading();
                        myChart2.showLoading();
                    },
                    complete: function () {
                        myChart1.hideLoading();
                        myChart2.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-09").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-09").hide();
                            return;
                        }
                        var data = result.data;

                        var lines = data.lines, nodes = data.nodes, points = data.points;

                        var series1 = [];
                        var lineSeries = lines.map(function (item) {
                            var data = []
                            for (var i = 0; i < 2; i++) {
                                data.push([item.lineX[i], item.lineY[i]])
                            }
                            return {
                                data: data,
                                type: 'line',
                                symbol: 'none',
                                lineStyle: {
                                    color: '#585656'
                                }
                            }
                        })

                        var pointData = points.map(function (item) {
                            return [item.x, item.y, item.name]
                        })
                        var pointSerie = {
                            name: 'point',
                            data: pointData,
                            type: 'scatter',
                            symbolSize: 20,
                            color: '#000',
                            label: {
                                show: true,
                                formatter: function (param) {
                                    return param.data[2];
                                },
                                position: 'top'
                            }
                        }

                        let nodeMap = {};
                        nodes.sort((a, b) => a.name.localeCompare(b.name))
                        nodes.forEach(it => {
                            nodeMap[it.name] = nodeMap[it.name] || [];
                            nodeMap[it.name].push([it.x, it.y, it.y])
                        })
                        var nodeSeries = [];
                        for (let k in nodeMap) {
                            nodeSeries.push({
                                name: k,
                                data: nodeMap[k],
                                type: 'scatter',
                                symbolSize: 6,
                                color: getClusterColor(k),
                                emphasis: {
                                    label: {
                                        show: true,
                                        formatter: function (param) {
                                            return param.data[2];
                                        },
                                        position: 'top'
                                    }
                                }
                            })
                        }

                        series1.push(...lineSeries, pointSerie, ...nodeSeries)
                        myChart1.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: Object.keys(nodeMap)
                            },
                            xAxis: {
                                name: 'Component 1',
                                nameGap: 25,
                                nameLocation: 'center',
                                type: 'value'
                            },
                            yAxis: {
                                name: 'Component 2',
                                nameGap: 25,
                                nameLocation: 'center',
                                type: 'value'
                            },
                            grid: {
                                left: '4%',
                                right: '4%',
                                containLabel: true
                            },
                            series: series1
                        });

                        var series2 = [];
                        series2.push(...lineSeries, pointSerie, {
                            type: 'scatter',
                            symbolSize: 6,
                            data: nodes.map(it => [it.x, it.y, it.pseudotime]),
                            emphasis: {
                                label: {
                                    show: true,
                                    formatter: function (param) {
                                        return param.data[2];
                                    },
                                    position: 'top'
                                }
                            }
                        })
                        myChart2.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            title: {
                                text: 'Pseudotime',
                                left: '8%',
                                top: '1%',
                                textStyle: {
                                    fontSize: 12
                                }
                            },
                            visualMap: {
                                min: 0,
                                max: Math.ceil(Math.max(...nodes.map(it => it.pseudotime))),
                                seriesIndex: series2.length - 1,
                                dimension: 2,
                                precision: 1,
                                calculable: true,
                                orient: 'horizontal',
                                left: 'center',
                                top: '0%',
                                inRange: {
                                    color: ['#053061', '#2fbce2']
                                }
                            },
                            xAxis: {
                                name: 'Component 1',
                                nameGap: 25,
                                nameLocation: 'center',
                                type: 'value'
                            },
                            yAxis: {
                                name: 'Component 2',
                                nameGap: 25,
                                nameLocation: 'center',
                                type: 'value'
                            },
                            grid: {
                                left: '4%',
                                right: '4%',
                                containLabel: true
                            },
                            series: series2
                        });
                    }
                })
            }
        }

        function initChart08() {
            if (!document.getElementById('chart-08')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-08'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/paga/[[${pagaTask.id}]]/8',
                    beforeSend: function () {
                        $("#chart-08").next().remove();
                        $("#chart-08").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-08").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-08").hide();
                            return;
                        }

                        var data = result.data, lines = data.lines, nodes = data.nodes;

                        var series = lines.map(function (item) {
                            var data = []
                            for (var i = 0; i < 2; i++) {
                                data.push([item.lineX[i], item.lineY[i]])
                            }
                            return {
                                data: data,
                                type: 'line',
                                color: 'rgba(0,0,0, 0.3)',
                                lineStyle: {
                                    width: item.width * 20
                                }
                            }
                        })

                        var colors = ['#711515', '#b2c416', '#68c616', '#1256b8',
                            '#c65e26', '#c22727', '#11cb22', '#23cae0']
                        var nodeData = nodes.map(function (item, idx) {
                            return {
                                value: [item.x, item.y, item.name, item.weight],
                                itemStyle: {
                                    color: getClusterColor(item.name)
                                }
                            }
                        })
                        var s = {
                            name: 'point',
                            data: nodeData,
                            type: 'scatter',
                            symbolSize: function (data) {
                                return data[3] * 200;
                            },
                            label: {
                                show: true,
                                formatter: function (param) {
                                    return param.data.value[2];
                                },
                                position: 'top'
                            }
                        }
                        series.push(s)

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            xAxis: {
                                type: 'value',
                                show: false
                            },
                            yAxis: {
                                type: 'value',
                                show: false
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            series: series
                        })

                    }
                })
            }
        }
    </script>
    <!--  第三大块  -->
    <script th:if="${degTask != null}">
        $(document).ready(function () {
            initChart10();
            initChart11();
        })

        function initChart10() {
            if (!document.getElementById('chart-10')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-10'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/deg/[[${degTask.id}]]/10',
                    beforeSend: function () {
                        $("#chart-10").next().remove();
                        $("#chart-10").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-10").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-10").hide();
                            return;
                        }
                        var data = result.data;
                        var nodes = data.data, cutp = data.cutp, cutfc = data.cutfc;
                        var nodeData = nodes.map(function (item) {
                            return {
                                value: [item.x, item.y, item.name],
                                itemStyle: {
                                    color: item.color
                                }
                            }
                        })
                        var points = {
                            type: 'scatter',
                            data: nodeData,
                            symbolSize: 6,
                        }
                        var line = {
                            type: 'line',
                            data: [],
                            markLine: {
                                data: [
                                    {
                                        name: 'x=Fold change(log2)',
                                        symbol: 'none',
                                        xAxis: cutfc,
                                        lineStyle: {
                                            color: '#ff0000'
                                        }
                                    },
                                    {
                                        name: 'x=-Fold change(log2)',
                                        symbol: 'none',
                                        xAxis: -cutfc,
                                        lineStyle: {
                                            color: '#ff0000'
                                        }
                                    },
                                    {
                                        name: 'y=-log10(FDR)',
                                        symbol: 'none',
                                        yAxis: -Math.log10(cutp),
                                        lineStyle: {
                                            color: '#ff0000'
                                        }
                                    }
                                ]
                            }
                        }

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            xAxis: {
                                name: 'log2(Fold change)',
                                nameGap: 25,
                                nameLocation: 'center',
                            },
                            yAxis: {
                                name: '-log10(FDR)',
                                nameGap: 35,
                                nameLocation: 'center',
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                containLabel: true
                            },
                            tooltip: {
                                formatter: function (params) {
                                    return params.data.value[2]
                                }
                            },
                            series: [
                                points,
                                line
                            ]
                        });
                    }
                })
            }
        }

        function initChart11() {
            if (!document.getElementById('chart-11')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-11'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/deg/[[${degTask.id}]]/11',
                    beforeSend: function () {
                        $("#chart-11").next().remove();
                        $("#chart-11").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-11").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-11").hide();
                            return;
                        }
                        var data = result.data;
                        var xData = data.xData, yData = data.yData, sData = data.data

                        var seriesData = sData.map(function (item) {
                            return [item[1], item[0], item[2] || '-'];
                        });

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            tooltip: {
                                position: 'top'
                            },
                            animation: false,
                            grid: {
                                height: '50%',
                                left: '30%'
                            },
                            xAxis: {
                                type: 'category',
                                data: xData,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0,
                                    rotate: 90
                                },
                            },
                            yAxis: {
                                type: 'category',
                                data: yData,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0,
                                    fontSize: 10,
                                    formatter: function (params) {
                                        if (params.length > 40) {
                                            return params.substring(0, 40) + '...';
                                        } else {
                                            return params;
                                        }
                                    }
                                },
                                triggerEvent: true
                            },
                            visualMap: {
                                min: -1,
                                max: 1,
                                dimension: 2,
                                precision: 3,
                                calculable: true,
                                orient: 'horizontal',
                                left: 'center',
                                bottom: '15%',
                                inRange: {
                                    color: ['#053061', '#2fbce2', '#f5f5f5', '#e7783c', '#69001f']
                                }
                            },
                            series: [{
                                type: 'heatmap',
                                data: seriesData,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }]
                        })

                        myChart.on('mouseover', function (params) {
                            if (params.componentType === 'yAxis') {
                                var tt = $('#tip');
                                tt.html(params.value);
                                tt.css('left', params.event.event.offsetX + 30);
                                tt.css('top', params.event.event.offsetY + 550);
                                tt.show();
                            }
                        })
                        myChart.on('mouseout', function () {
                            $('#tip').hide();
                        })
                    }
                })
            }
        }
    </script>

    <script>
        $("a[data-toggle='tab']").on("shown.bs.tab", function (e) {
            window.onresize();
        });
    </script>
</th:block>
</html>
