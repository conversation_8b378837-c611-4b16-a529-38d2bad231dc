<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('dnaseq-somatic-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">WES/WGS-somatic</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/somatic/form}">Add Task</a>
                            <a th:href="@{/analysis/somatic/list}" class="active">Task List</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade">
                            <form th:action="@{/analysis/somatic/list}" id="search-form"
                                  class="form-inline form-task mt-2">
                                <div class="d-flex">
                                    <label class="mx-2 font-12">Task ID</label>
                                    <select name="batch" th:attr="data-val=${query.batch}"
                                            class="form-control form-control-sm select2">
                                        <option value="">Choose</option>
                                        <option th:each="item:${somaticTaskIdVO?.batchList}" th:value="${item}"
                                                th:text="${item}" th:selected="${#strings.equals(item, query.batch)}">
                                        </option>
                                    </select>

                                    <label class="mx-2 font-12">Task Name</label>
                                    <input name="taskName" th:value="${query.taskName}" type="text"
                                           class="form-control form-control-sm width-150">

                                    <label class="mx-2 font-12">Run ID</label>
                                    <select name="taskId" th:attr="data-val=${query.taskId}"
                                            class="form-control form-control-sm select2">
                                        <option value="">Choose</option>
                                        <option th:each="item:${somaticTaskIdVO?.taskIdList}" th:value="${item}"
                                                th:text="${item}" th:selected="${#strings.equals(item, query.taskId)}">
                                        </option>
                                    </select>

                                    <label class="mx-2 font-12">Run Name</label>
                                    <input name="runName" th:value="${query.runName}" type="text"
                                           class="form-control form-control-sm width-150">

                                </div>

                                <div class="d-flex">
                                    <label class="mx-2 font-12">Time</label>
                                    <div class="input-daterange input-group">
                                        <input autocomplete="off" type="text" class="form-control form-control-sm"
                                               name="start"
                                               th:value="${query.start==null?'':#calendars.format(query.start,'yyyy-MM-dd')}"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                        </div>
                                        <input autocomplete="off" type="text" class="form-control form-control-sm"
                                               name="end"
                                               th:value="${query.end==null?'':#calendars.format(query.end,'yyyy-MM-dd')}"/>
                                        <div class="input-group-append">
                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex">
                                    <button type="submit" class="btn btn-primary btn-sm m-2 _submit_btn">Search</button>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table class="table table-hover table-striped table-nowrap font-12 m-0">
                                    <thead>
                                    <tr>
                                        <th scope="col">Task ID</th>
                                        <th scope="col">Task Name</th>
                                        <th scope="col">Run ID</th>
                                        <th scope="col">Run Name</th>
                                        <th scope="col">Start time</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Status time</th>
                                        <th scope="col">Consuming</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <th:block th:unless="${#lists.isEmpty(pageResult.content)}">
                                        <tr th:each="item : ${pageResult.content}">
                                            <td th:text="${item.task.batch}">200302151050411</td>
                                            <td th:text="${item.task.taskName}">200302151050411</td>
                                            <td th:text="${item.task.taskId}">200302151050411</td>
                                            <td th:text="${item.task.runName}">200302151050411</td>
                                            <td th:text="${#dates.format(item.task.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td>
                                                <th:block th:switch="${item.task.status}">
                                                    <th:block th:case="-1">
                                                        <span class="text-danger"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.task.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="1">
                                                        <span class="text-info"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.task.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="7">
                                                        <span class="text-dark"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.task.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="*">
                                                        <span class="text-success"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.task.status)}]]</span>
                                                    </th:block>
                                                </th:block>
                                            </td>
                                            <td th:text="${#dates.format(item.task.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:text="${item.task.useTime}">26m55s</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a th:href="@{/analysis/somatic/{id}(id=${item.task.id})}"
                                                       class="text-primary" data-toggle="tooltip" title="View">
                                                        <i class="fa fa-eye font-14"></i>
                                                    </a>
                                                    <a href="javascript:void(0);" class="text-danger"
                                                       data-toggle="tooltip"
                                                       th:onclick="deleteTask([[${item.task.id}]])" title="Delete">
                                                        <i class="fa fa-times font-14"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </th:block>
                                    <th:block th:if="${#lists.isEmpty(pageResult.content)}">
                                        <tr>
                                            <td colspan="10">no data</td>
                                        </tr>
                                    </th:block>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pt-1 mb-2">
                                <div th:replace="~{base/pageable}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script>
        $(document).ready(function () {
            $('.select2').select2();
            let batch = $('select[name="batch"]').attr('data-val');
            $('select[name="batch"]').val([batch]).trigger('change');

            let taskId = $('select[name="taskId"]').attr('data-val');
            $('select[name="taskId"]').val([taskId]).trigger('change');

            $('.input-daterange').datepicker({
                format: "yyyy-mm-dd",
                toggleActive: true,
                autoclose: true,
                todayHighlight: true
            });
        })

        function deleteTask(id) {
            layer.confirm('<p class="text-center">Are you sure to delete？</p>', {btn: ['Confirm', 'Cancel']}, function () {
                var loadLayerIndex;
                $.ajax({
                    url: "/analysis/somatic/deleteTask",
                    data: {"id": id},
                    dataType: 'json',
                    async: false,
                    method: 'post',
                    beforeSend: function () {
                        loadLayerIndex = layer.load(1, {
                            shade: [0.1, '#fff'] //0.1透明度的白色背景
                        });
                    },
                    success: function (result) {
                        if (result.code == 200) {
                            layer.msg("Successfully deleted", {time: 500}, function () {
                                location.reload();
                            });
                        } else {
                            layer.alert(result.message, {icon: 2});
                        }
                    },
                    complete: function () {
                        layer.close(loadLayerIndex);
                    }
                });
            });
        }
    </script>
</th:block>
</html>
