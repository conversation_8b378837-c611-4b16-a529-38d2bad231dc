package cn.ac.picb.vipmap.vo;

import cn.ac.picb.somatic.vo.SomaticFileVO;
import cn.ac.picb.somatic.vo.SomaticTaskInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SomaticTaskParam {

    @NotNull
    private List<SomaticTaskInput> input;

    @NotBlank
    private String qcMethod;

    private String refVersion;

    @NotBlank
    private String mappingMethod;

    private String varianceMethod;

    private String pon;

    private String exonBed;

    private List<String> ponNames;

    private SomaticFileVO exonFileVo;

    private String annoMethod;

    private String taskName;

}

