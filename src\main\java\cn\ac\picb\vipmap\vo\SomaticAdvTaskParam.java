package cn.ac.picb.vipmap.vo;

import cn.ac.picb.somatic.vo.SomaticFileVO;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SomaticAdvTaskParam {

    private String id;

    private List<String> runNameList;

    private String basicBatchId;

    private SomaticFileVO mafFileVo;

    private String taskName;
    private String refVersion;
    @Min(1)
    @Max(50)
    private Integer interestedGenesTop;

    private String interestedGenesCustom;

    private String captureSize;
    private String driverGeneMethod;
    private String dataBase;

}

