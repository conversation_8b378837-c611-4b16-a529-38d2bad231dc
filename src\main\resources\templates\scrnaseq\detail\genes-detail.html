<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">DEG & GSVA</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <th:block th:switch="${taskVo.genesTask.status}">
                        <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Prepared</p>
                        </div>
                        <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="2">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Data prepared</p>
                        </div>
                        <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="3">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis done</p>
                        </div>
                        <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis Error</p>
                        </div>
                    </th:block>
                    <div class="form-group-box">
                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                        <div class="collapse show" id="coll-1">
                            <div class="pl-4 pt-2">
                                <div class="result-box">
                                    <div class="table-responsive mt-2">
                                        <table class="table table-bordered table-sm table-center table-middle mb-1">
                                            <thead>
                                            <tr class="thead-light">
                                                <th>Cluster Baseline ID</th>
                                                <th>Start time</th>
                                                <th>Status time</th>
                                                <th>Consuming</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td th:text="${taskVo.baselineTask.taskId}"></td>
                                                <td th:text="${#dates.format(taskVo.baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${#dates.format(taskVo.baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${taskVo.baselineTask.useTime}">26m55s</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Input your genes</label>
                                        <div class="col-xl-10 col-lg-10 col-md-10">
                                            <span class="badge badge-primary" th:each="gene:${#strings.listSplit(taskVo.genesTask.genes, ';')}" th:text="${gene}">CCL17</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group-box" th:if="${taskVo.genesTask.status == 2}">
                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                        <div class="collapse show" id="coll-2">
                            <div class="tool-box">
                                <div class="tool-title">Result</div>
                                <div class="tool-content">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="d-flex align-items-center mb-2">
                                                <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">Expression level for chosen genes</h6>
                                                <!--                                                <a th:href="@{/analysis/scrnaseq/common/download(code='expression_level_for_chosen_genes',runName =${taskVo.genesTask.taskId})}">-->
                                                <!--                                                    <i class="fa fa-download text-primary"></i>-->
                                                <!--                                                </a>-->
                                            </div>
                                            <!--                                            <div id="pdf1" class="mb-3"></div>-->
                                            <div id="chart-13" style="width: 800px;height: 800px"></div>
                                        </div>
                                        <div class="col-lg-12">
                                            <div class="d-flex align-items-center mb-2">
                                                <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">correlation for chosen genes </h6>
                                                <!--                                                <a th:href="@{/analysis/scrnaseq/common/download(code='correlation_for_chosen_genes',runName =${taskVo.genesTask.taskId})}">-->
                                                <!--                                                    <i class="fa fa-download text-primary"></i>-->
                                                <!--                                                </a>-->
                                            </div>
                                            <div id="chart-12" style="width: 800px;height: 800px"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tool-box">
                    <div class="tool-title">Reference</div>
                    <div class="tool-content">
                        <ul class="r-list">
                            <li>1. Cell,2018, Chemoresistance Evolution in Triple-Negative Breast Cancer Delineated by Single Cell Sequencing</li>
                            <li>2. Cell,2017, Single-cell transcriptomic analysis of primary and metastatic tumor ecosystems in head and neck cancer</li>
                        </ul>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/pdfobject.min.js}"></script>

    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>

    <script>
        var options = {
            height: "800px",
            page: 2,
            pdfOpenParams: {
                navpanes: 1,
                view: "FitH",
                pagemode: "thumbs"
            }
        };

        PDFObject.embed("[[@{/analysis/scrnaseq/getGenesStackedViolinPdf(taskId =${taskVo.genesTask.taskId})}]]", "#pdf1", options);
    </script>

    <script>
        $(document).ready(function () {
            initChart12();
            initChart13();
        })

        function initChart13() {
            if (!document.getElementById('chart-13')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-13'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/genes/[[${taskVo.genesTask.id}]]/13',
                    beforeSend: function () {
                        $("#chart-13").next().remove();
                        $("#chart-13").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-13").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-13").hide();
                            return;
                        }

                        let data = result.data, header = data.header, nodes = data.data;

                        let series = [];
                        let xAxis = [], yAxis = [], grid = [];

                        for (let i = 0; i < header.length; i++) {
                            grid.push({
                                left: '10%',
                                right: '5%',
                                height: `${90/header.length - 2}%`,
                                bottom: `${10 + (90/header.length * i)}%`
                            });
                            xAxis.push({
                                type: 'category',
                                gridIndex: i,
                                boundaryGap: true,
                                nameGap: 30,
                                data: Object.keys(nodes),
                                axisLabel: {
                                    show: i === 0,
                                    rotate: 90,
                                },
                                splitArea: {
                                    show: false
                                },
                                axisTick: {
                                    show: i === 0
                                },
                                splitLine: {
                                    show: false
                                }
                            })
                            yAxis.push({
                                name: header[i],
                                nameLocation: 'center',
                                type: 'value',
                                gridIndex: i,
                                minInterval: 1,
                                nameGap: 20,
                                min: 0,
                                // max: 5,
                                splitArea: {
                                    show: false
                                },
                                splitLine: {
                                    show: false
                                }
                            })

                            let value =  Object.values(nodes).map(it => it.map(it2 => Number(it2[i])));
                            let boxplotData = echarts.dataTool.prepareBoxplotData(value)
                            series.push({
                                xAxisIndex: i,
                                yAxisIndex: i,
                                name: header[i],
                                type: 'boxplot',
                                boxWidth: ['30%', '50%'],
                                data: boxplotData.boxData.map((it, inx) => {
                                    let color = getClusterColor(Object.keys(nodes)[inx]);
                                    return {
                                        value: it,
                                        itemStyle: {
                                            color: color,
                                            borderColor: '#000'
                                        }
                                    }
                                }),
                            }, {
                                xAxisIndex: i,
                                yAxisIndex: i,
                                type: 'scatter',
                                symbolSize: 5,
                                data: boxplotData.outliers.map(it => {
                                    return {
                                        value: it,
                                        itemStyle: {
                                            color: getClusterColor(Object.keys(nodes)[it[0]])
                                        }
                                    }
                                })
                            })
                        }

                        myChart.setOption({
                            // legend: {
                            //     top: '10%'
                            // },
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            tooltip: {
                                trigger: 'item',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            grid,
                            xAxis,
                            yAxis,
                            series
                        })
                    }
                })
            }
        }

        function initChart12() {
            if (!document.getElementById('chart-12')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-12'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/genes/[[${taskVo.genesTask.id}]]/12',
                    beforeSend: function () {
                        $("#chart-12").next().remove();
                        $("#chart-12").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-12").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-12").hide();
                            return;
                        }

                        var data = result.data;


                        var sd = data.data.map(function (item) {
                            return [item[1], item[0], item[2] || '-'];
                        });

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            grid: {
                                height: '50%',
                                top: '10%'
                            },
                            xAxis: {
                                type: 'category',
                                data: data.x,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0,
                                    rotate: 90
                                }
                            },
                            yAxis: {
                                type: 'category',
                                data: data.y,
                                splitArea: {
                                    show: true
                                },
                                axisLabel: {
                                    interval: 0
                                },
                                inverse: true
                            },
                            visualMap: {
                                min: -1,
                                max: 1,
                                dimension: 2,
                                precision: 3,
                                calculable: true,
                                orient: 'horizontal',
                                left: 'center',
                                bottom: '15%',
                                inRange: {
                                    color: ['#053061', '#2fbce2', '#f5f5f5', '#e7783c', '#69001f']
                                }
                            },
                            series: [{
                                type: 'heatmap',
                                data: sd
                            }]
                        })
                    }
                })
            }
        }
    </script>
</th:block>
</html>
