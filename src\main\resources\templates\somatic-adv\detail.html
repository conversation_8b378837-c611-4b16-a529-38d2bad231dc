<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}" th:with="task=${vo?.task?.advTask}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('somatic-adv-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">WES/WGS-somatic</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <!--/*
                    <div style="display: none;" th:text="${task?.errMsg}"></div>
                    */-->
                </div>
                <th:block th:switch="${task.status}">
                    <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis Error</p>
                    </div>

                    <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Prepared</p>
                    </div>
                    <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="7">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis done</p>
                    </div>

                    <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Data prepared</p>
                    </div>
                </th:block>

                <ul class="detail-info row mb-0">
                    <li class="col-xl-3 col-lg-3">
                        <div class="text-muted">Basic Analysis</div>
                    </li>
                    <li class="col-xl-9 col-lg-9"><span class="text-muted sm">Task ID </span>
                        <div th:text="${task.basicBatchId}" class="text-primary">20200722001</div>
                    </li>
                    <li class="col-xl-3 col-lg-3">
                        <div class="text-muted">Advanced Analysis</div>
                    </li>
                    <li class="col-xl-9 col-lg-9"><span class="text-muted sm">Task ID </span>
                        <div th:text="${task.taskId}" class="text-primary">20200722001</div>
                    </li>
                    <li class="col-xl-3 col-lg-6"><span class="text-muted sm">Start Time：</span>
                        <div th:text="${#dates.format(task.createTime,'yyyy-MM-dd HH:mm:ss')}"
                             class="text-primary font-13">xxxxxxxxxx
                        </div>
                    </li>
                    <li class="col-xl-3 col-lg-6"><span class="text-muted sm">Consuming：</span>
                        <div th:text="${task.useTime}" class="text-primary font-13">xxxxxxxxxx</div>
                    </li>
                </ul>


                <div class="form-group-box">
                    <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                    <div class="collapse show" id="coll-1">
                        <div class="pl-4 pt-2">
                            <div class="result-box">
                                <h6 class="border-bottom pb-2 m-0">Input file</h6>
                                <div th:if="${#lists.isEmpty(vo?.task?.runNames)}"
                                     class="form-group row align-items-center mb-2">
                                    <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">File name</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span th:text="${vo?.task?.mafFileVo.path}"
                                              class="text-primary">/wes/xx/xx.maf</span>
                                    </div>
                                </div>

                                <th:block th:unless="${#lists.isEmpty(vo?.task?.runNames)}">
                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-3 col-lg-3 col-md-3 col-form-label pr-0">Basic analysis
                                            RunName </label>
                                    </div>
                                    <div class="result-box"
                                         th:with="runNames=${vo?.task?.runNames}, basicAnalysisIds=${vo?.task?.basicAnalysisIds}">
                                        <div class="table-responsive my-2"
                                             th:with="colSize=5, dataSize=${#lists.size(runNames)}">
                                            <table class="table table-bordered table-sm table-center table-middle mb-1">
                                                <tbody>
                                                <th:block th:if="${dataSize gt colSize}"
                                                          th:each="item,status:${runNames}">
                                                    <p th:remove="tag"
                                                       th:utext="${(status.index+1)%colSize==1 ? '&lt;tr&gt;':''}"/>
                                                    <td>
                                                        <a th:href="@{/analysis/somatic/{id}(id=${basicAnalysisIds.get(status.index)})}"
                                                           th:text="${item}">RunName 1</a>
                                                    </td>
                                                    <p th:remove="tag"
                                                       th:utext="${(status.index+1)%colSize==0 ? '&lt;/tr&gt;':''}"/>
                                                </th:block>

                                                <th:block th:unless="${dataSize gt colSize}">
                                                    <tr>
                                                        <td th:each="colIdx,status:${#numbers.sequence(1,colSize)}">
                                                            <a th:if="${colIdx le dataSize}"
                                                               th:text="${runNames.get(status.index)}"
                                                               th:href="@{/analysis/somatic/{id}(id=${basicAnalysisIds.get(status.index)})}"></a>

                                                            <span th:unless="${colIdx le dataSize}"> &nbsp;</span>
                                                        </td>
                                                    </tr>
                                                </th:block>

                                                </tbody>
                                            </table>
                                        </div>

                                    </div>
                                    <!--<div class="result-box">
                                        <div class="table-responsive my-2">
                                            <table class="table table-bordered table-sm table-center table-middle mb-1">
                                                <tbody>
                                                <tr>
                                                    <td>RunName 1</td>
                                                    <td>RunName 2</td>
                                                    <td>RunName 3</td>
                                                    <td>RunName 4</td>
                                                    <td>RunName 5</td>
                                                </tr>
                                                <tr>
                                                    <td>RunName 6</td>
                                                    <td>RunName 7</td>
                                                    <td>RunName 8</td>
                                                    <td>RunName 9</td>
                                                    <td>RunName 10</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                    </div>-->
                                </th:block>

                                <h6 class="border-bottom pb-2 m-0">Genomic Profiles & Statistically Significant Pairwise
                                    Relationships </h6>
                                <div class="form-group row align-items-center mb-2"
                                     th:with="hasInterestedGenesTop=${#strings.isEmpty(task?.interestedGenesCustom)}">
                                    <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Interested
                                        Genes</label>
                                    <div th:if="${hasInterestedGenesTop}" class="col-xl-10 col-lg-10 col-md-9">Top <span
                                            th:text="${task?.interestedGenesTop}" class="text-primary">20</span>
                                        genes
                                    </div>
                                    <div th:unless="${hasInterestedGenesTop}"
                                         th:with="geneItems=${#strings.listSplit(task?.interestedGenesCustom,',')}"
                                         class="col-xl-10 col-lg-10 col-md-9">
                                        <span th:each="geneItem:${geneItems}" th:text="${geneItem}"
                                              class="text-primary pr-1">CCL17</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Tumor Mutational Burden(TMB)</h6>

                                <div class="form-group row align-items-center mb-2">
                                    <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Capture Size</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3" th:if="${task?.captureSize != 'Null'}">
                                        <span class="text-primary" th:text="${task?.captureSize}">50</span> Mb
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-md-3" th:unless="${task?.captureSize != 'Null'}">
                                        <span class="text-primary" th:text="${task?.captureSize}"></span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Driver Gene</h6>
                                <div class="form-group row align-items-center mb-2">
                                    <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span th:text="${task?.driverGeneMethod}"
                                                                                  class="text-primary">oncodriveCLUST</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Signature Analysis</h6>
                                <div class="form-group row align-items-center mb-2">
                                    <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Database</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span th:text="${task?.dataBase}"
                                                                                  class="text-primary">legacy</span>
                                    </div>
                                </div>


                            </div>
                        </div>

                    </div>
                </div>
                <div class="form-group-box" th:if="${task.status == 7}">
                    <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                    <div class="collapse show" id="coll-2">
                        <div class="pl-4 pt-2">
                            <div class="result-box">
                                <div id="item-GenomicProfiles">
                                    <h6 class="border-bottom pb-2 m-0 mb-2">Genomic Profiles</h6>
                                    <div class="form-group row">
                                        <div class="col-md-12">
                                            <div class="d-flex justify-content-center">
                                                <div id="GenomicProfiles-chart" style="width: 100%;height: 600px"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row align-items-center">
                                        <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">Gene</label>
                                        <div class="col-xl-3 col-lg-3 col-md-8">
                                            <select id="gene-select"
                                                    class="form-control form-control-sm select2  width-150">
                                                <!--                                            <option value="">PTEN</option>-->
                                                <!--                                            <option value="" selected>PTPN11</option>-->
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-md-12">
                                            <div id="Gene-chart" style="width: 100%;height: 500px"></div>
                                        </div>
                                    </div>
                                </div>

                                <div id="item-Interactions">
                                    <h6 class="border-bottom pb-2 m-0 mb-2">Statistically Significant Pairwise
                                        Relationships </h6>
                                    <div class="form-group row">
                                        <div class="col-md-12">
                                            <div class="d-flex justify-content-center">
                                                <div id="Interactions-chart" style="width: 100%;height: 600px"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="item-TMBPlot">
                                    <h6 class="border-bottom pb-2 m-0 mb-2">Tumor Mutational Burden(TMB)</h6>
                                    <div class="form-group row">
                                        <div class="col-md-8">
                                            <div class="d-flex justify-content-center">
                                                <div id="TMBPlot-chart" style="width: 100%;height: 450px"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="table-responsive">
                                                <table id="TMB-table"
                                                       class="table table-bordered table-striped table-sm table-center table-middle mt-4 mb-0">
                                                    <thead>
                                                    <tr class="thead-light">
                                                        <th>Cohort</th>
                                                        <th>p-value</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="item-DriverGene">
                                    <h6 class="border-bottom pb-2 m-0 mb-2">Driver Gene</h6>
                                    <div class="form-group row">
                                        <div class="col-md-6" id="res-table-div">
                                            <div class="table-responsive">
                                                <table id="res-table"
                                                       class="table table-bordered table-striped table-sm table-center table-middle mb-0">
                                                    <thead>
                                                    <tr class="thead-light">
                                                        <th>Hugo_Symbol</th>
                                                        <th>pval</th>
                                                        <th>fdr</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody></tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="col-md-6" id="DriverGene-chart-div">
                                            <div class="d-flex justify-content-center">
                                                <div id="DriverGene-chart" style="width: 100%;height: 500px"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="item-Trinucleotide">
                                    <h6 class="border-bottom pb-2 m-0 mb-2">Transition and Transversions</h6>
                                    <div class="form-group row">
                                        <div class="col-md-8">
                                            <div class="d-flex justify-content-center">
                                                <div id="Trinucleotide-chart" style="width: 100%;height: 400px"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex justify-content-center">
                                                <div id="Titv-chart" style="width: 100%;height: 400px"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="item-Signatures">
                                    <h6 class="border-bottom pb-2 m-0 mb-2">Signature Analysis</h6>
                                    <div class="form-group row" style="padding-left: 10px">
                                        <div class="col-md-6" style="margin-top: 30px; margin-right: 0">
                                            <div class="col-md-12 row">
                                                <p>
                                                    Estimate number of signatures based on cophenetic correlation
                                                    metric. Best possible value is the one at which the correlation
                                                    value on the y-axis
                                                    drops significantly.
                                                </p>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-md-4 col-form-label pr-0">Number of signatures</label>
                                                <div class="col-md-5">
                                                    <select id="similarities-select"
                                                            class="form-control form-control-sm">
                                                        <option value="2" selected>2</option>
                                                        <option value="3">3</option>
                                                        <option value="4">4</option>
                                                        <option value="5">5</option>
                                                        <option value="6">6</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div id="Signatures-chart" style="width: 400px;height: 250px"></div>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <div class="col-md-12">
                                            <div class="d-flex justify-content-center">
                                                <div id="Similarities-chart" style="width: 100%;height: 300px"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-md-12">
                                            <div class="d-flex justify-content-center">
                                                <div id="CosmicSignature-chart" style="width: 100%;height: 600px"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="item-Pathway">
                                    <h6 class="border-bottom pb-2 m-0 mb-2">Oncogenic Signaling Pathways</h6>
                                    <div class="form-group row">
                                        <div class="col-md-6">
                                            <!--                                        <div class="text-center"><img th:src="@{/images/pic-somatic11.png}" height="470" alt=""></div>-->
                                            <div class="d-flex justify-content-center">
                                                <div id="Pathway-chart" style="width: 100%;height: 500px"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center mb-2">
                                                <h6 class="font-weight-bold text-center text-muted mb-0 pr-3">
                                                    Pathway</h6>
                                                <select id="pathway-select"
                                                        class="form-control form-control-sm select2  width-150">
                                                    <!--                                                <option value="">Cell Cycle</option>-->
                                                    <!--                                                <option value="" selected>Hippo</option>-->
                                                </select>
                                            </div>

                                            <p id="pathway-text" class="text-muted">Involved in the control of organ
                                                size. Central to this pathway is the regulation of the transcription
                                                co-activators
                                                YAP/TAZ that promote
                                                the transcription of genes involved in cell proliferation.
                                            </p>

                                            <!--                                            <img th:src="@{/images/pic-somatic13.png}" class="img-fluid" alt="">-->

                                            <div id="pathway-img" class="text-center"></div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-md-12" id="pathway-path-chart"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<th:block layout:fragment="custom-style">
    <link type="text/css" rel="stylesheet" th:href="@{/bootstrap-table-1.18.3/bootstrap-table.min.css}"/>
    <style>
        .hidden {
            display: none;
        }
    </style>
</th:block>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts5.min.js}"></script>
    <script th:src="@{/js/hclust.js}"></script>
    <script th:src="@{/bootstrap-table-1.18.3/bootstrap-table.min.js}"></script>


    <script th:if="${task.status == 7}">
        $(document).ready(function () {

            let taskId = '[[${task.taskId}]]';

            let ctx = $('[name=_context_path]').attr('content');

            $('#gene-select').on('change', function () {
                geneChart(taskId, $(this).val())
            })

            $('#similarities-select').on('change', function () {
                similaritiesChart(taskId, $(this).val())
                cosmicSignatureChart(taskId, $(this).val())
            })

            // pathway 图片
            $('#pathway-select').on('change', function () {
                pathwayInfo(taskId, $(this).val())
            })

            initChartGenomicProfiles(taskId);
            initChartInteractions(taskId);
            initChartTMBPlot(taskId);
            initChartDriverGene(taskId);
            initChartSignatures(taskId);
            initChartTrinucleotide(taskId);
            initChartTitv(taskId);
            initChartPathway(taskId);

            similaritiesChart(taskId, 2);
            cosmicSignatureChart(taskId, 2);

            let pathway = {
                'Cell_Cycle': 'Regulation of mitotic cell cycle progression involving a signaling cascade of cyclins and cyclin-dependent kinases as well as a number of regulatory checkpoints.',
                'Hippo': 'Involved in the control of organ size. Central to this pathway is the regulation of the transcription co-activators YAP/TAZ that promote the transcription of genes involved in cell proliferation.',
                'MYC': 'Involves a number of transcription regulation complexes: MYC-MAX, MAX-MXD, MAX-MGA, and the energy sensing, MondoA-Mlx complex in the regulation of apoptotic response and cell differentiation.',
                'NOTCH': 'Pathway involved in cell-cell communication, cell fate. Cleavage of Notch receptors leads to the displacement of a transcription repressor complex on RBPJ (a transcription factor also known as CSL) accompanied by recruitment of an activation complex (including MAMLs) leads to transcription of Notch target genes.',
                'NRF2': 'Involves the regulation of the transcription factor NFE2L2 by KEAP1. NFE2L2 regulates genes with the antioxidant response elements (ARE) that aid in cellular response against oxidative stress thought to aid in cancer chemoresistance.',
                "PI3K": "A signaling cascade involving PI3K phosphorylation of AKT leading to the activation of the mTORC1 complex. The mTORC1 functions as a metabolic sensor and controls protein abundance by affecting processes involved in protein production and RNA translation leading to changes in cell growth and survival.",
                "RTK-RAS": "A signaling cascade pathway initiated by activation of RTKs followed signal transduction through Ras then Raf and then MEK family members. This cascade leads to the activation of several transcription factors that regulate processes involving cell proliferation and survival.",
                "TGF-Beta": "A signaling network involved in growth, proliferation, apoptosis, and differentiation involving the activation of TGFβ receptors by the cytokine TGFβ that leads to the activation of gene transcription by SMADs.",
                "TP53": "Pathway centered around the regulation of the tumor suppressor TP53, a gene that regulates apoptosis, cell cycle arrest, senescence, and DNA repair.",
                "WNT": "Involved in both development and tissue homeostasis. The canonical Wnt pathway involves signal transduction initiated by Wnt ligand binding to Frizzled family receptors leading to the dysregulation of beta-catenin degradation and ultimately, the induction of transcription via TCF/LEF transcription factors by beta-catenin."
            }

            let resultCode = {
                FILE_NOT_EXISTS: 1002000006,
                FILE_PARSE_ERROR: 1003002005
            }

            // 处理响应的异常请求
            function processChartResultCode(result) {
                let {code, message} = result;
                if (code == 200) {
                    return true;
                }
                if (code == resultCode.FILE_PARSE_ERROR) {
                    layer.msg(message)
                }
                return false;
            }

            function initChartGenomicProfiles(taskId) {
                if (!document.getElementById('GenomicProfiles-chart')) {
                    return
                }
                let chart = echarts.init(document.getElementById('GenomicProfiles-chart'));
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'GenomicProfiles'
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#item-GenomicProfiles').hide();
                            return;
                        }
                        $('#item-GenomicProfiles').show();
                        drawChart(chart, result.data);
                    }
                })

                function drawChart(chart, data) {
                    let mainData = data.mainData;
                    let xlist = data.xlist;
                    let ylist = data.ylist;
                    let legendMap = data.legendMap;
                    let pctList = data.pctList;
                    let topData = data.topData;
                    let rightData = data.rightData;

                    ylist.forEach(it => {
                        $('#gene-select').append(`<option value="${it}">${it}</option>`)
                    })
                    // 请求渲染 gene 棒棒糖图
                    geneChart(taskId, ylist[0]);

                    let category = [];
                    let categoryColor = [{value: 0, color: '#D3D3D3'}];
                    Object.keys(legendMap).forEach((key, index) => {
                        category.push(key);
                        categoryColor.push({
                            value: index + 1,
                            color: legendMap[key]
                        });
                    })

                    let mainSeriesData = [];
                    mainData.forEach((v1, i1) => {
                        return v1.forEach((v2, i2) => {
                            mainSeriesData.push([i2, i1, category.indexOf(v2) + 1])
                        })
                    })

                    let topSeries = Object.keys(topData).map(key => {
                        return {
                            name: key,
                            type: 'bar',
                            stack: 'top-chart',
                            data: topData[key],
                            xAxisIndex: 1,
                            yAxisIndex: 1,
                            barCategoryGap: "2%",
                            itemStyle: {
                                normal: {
                                    color: categoryColor[category.indexOf(key) + 1].color
                                },
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: function (param) {
                                    return `${param.name}<br>${param.marker} ${param.seriesName}: ${param.value}`
                                }
                            }
                        }
                    })

                    let rightSeries = Object.keys(rightData).map(key => {
                        return {
                            name: key,
                            type: 'bar',
                            stack: 'right-chart',
                            data: rightData[key].reverse(),
                            xAxisIndex: 2,
                            yAxisIndex: 2,
                            barCategoryGap: "2%",
                            itemStyle: {
                                normal: {color: categoryColor[category.indexOf(key) + 1].color}
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: function (param) {
                                    return `${param.marker} ${param.seriesName}: ${param.value}`
                                }
                            },
                        }
                    })

                    let option = {
                        title: {
                            text: data.title,
                            x: 'center',
                            textStyle: {
                                fontSize: 13,
                            }
                        },
                        grid: [{
                            top: '30%',
                            right: '30%'
                        }, {
                            bottom: '73%',
                            right: '30%',
                        }, {
                            top: '30%',
                            left: '74%',
                        }],
                        tooltip: {
                            trigger: 'axis',
                            // trigger: 'item',
                            formatter: '{b0}',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        legend: {
                            data: category,
                            top: 'bottom'
                        },
                        visualMap: {
                            show: false,
                            type: 'piecewise',
                            seriesIndex: 0,
                            pieces: categoryColor
                        },
                        xAxis: [
                            {
                                type: 'category',
                                data: xlist,
                                gridIndex: 0,
                                axisTick: {show: false},
                                axisLabel: {show: false},
                            }, {
                                data: xlist,
                                type: 'category',
                                gridIndex: 1,
                                axisTick: {show: false},
                                axisLabel: {show: false},
                                axisLine: {show: false},
                            }, {
                                position: 'top',
                                type: 'value',
                                gridIndex: 2,
                                axisTick: {
                                    show: true,
                                }
                                // interval: Number.MAX_VALUE,
                            },
                        ],
                        yAxis: [
                            {
                                type: 'category',
                                data: ylist,
                                inverse: true,
                                gridIndex: 0,
                                axisTick: {show: false},
                                triggerEvent: true,
                            },
                            {
                                type: 'value',
                                gridIndex: 1,
                                // interval: Number.MAX_VALUE,
                            },
                            {
                                type: 'category',
                                gridIndex: 2,
                                axisTick: {show: false},
                                data: pctList.reverse(),
                                axisLabel: {
                                    margin: 35,
                                    textStyle: {
                                        align: 'left'
                                    }
                                },
                                axisLine: {show: false},
                            },
                        ],
                        series: [
                            {
                                type: 'heatmap',
                                data: mainSeriesData,
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                progressive: 300,
                                tooltip: {
                                    trigger: 'axis',
                                },
                                itemStyle: {
                                    emphasis: {
                                        shadowBlur: 10,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    },
                                    borderWidth: 1,
                                    borderColor: '#fff'
                                }
                            },
                            ...topSeries,
                            ...rightSeries
                        ]
                    }
                    chart.setOption(option);

                    chart.on('click', 'yAxis.category', function (param) {
                        $('#gene-select').val(param.value).trigger('change');
                    })
                }

            }

            function geneChart(taskId, gene) {
                if (!document.getElementById('Gene-chart')) {
                    return;
                }
                let chart = echarts.init(document.getElementById('Gene-chart'));
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'Gene',
                        'gene': gene
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#Gene-chart').hide();
                            return;
                        }
                        $('#Gene-chart').show();
                        drawChart(chart, result.data)
                    }
                });

                function drawChart(chart, data) {
                    let domainColorMap = data.domainColorMap || [];
                    let pointColorMap = data.pointColorMap;
                    let protData = data.protData || [];
                    let snpList = data.snpList || [];
                    let xpos = data.xpos || [];
                    let ypos = data.ypos || [];

                    let xMin = Math.min(...xpos);
                    let xMax = Math.max(...xpos);
                    let yMin = Math.min(...ypos);
                    let yMax = Math.max(...ypos);

                    let barSeries = [];
                    snpList.forEach(it => {
                        barSeries.push({
                            name: it[0],
                            type: 'line',
                            data: [
                                [it[1], 0],
                                [it[1], it[2]]
                            ],
                            symbolSize: 0.1,
                            itemStyle: {
                                color: '#a0a0a0'
                            },
                            barWidth: 1,
                            xAxisIndex: 0,
                            yAxisIndex: 0,
                        })
                    })

                    let scatterSeries = JSON.parse(JSON.stringify(snpList)).map(it => {
                        return {
                            name: it[0],
                            type: 'scatter',
                            z: 3,
                            symbolSize: 15,
                            xAxisIndex: 0,
                            yAxisIndex: 0,
                            data: [
                                [it[1], it[2], it[3]]
                            ],
                            itemStyle: {
                                color: pointColorMap[it[0]],
                                opacity: 1,
                            },
                        }
                    })

                    let bottomData = [
                        ['-', xMin, xMax, 20, 80],
                        ...protData.map(it => {
                            return [it[0], it[1], it[2], 10, 90]
                        })
                    ]

                    let bottomSeries = bottomData.map(it => {
                        return {
                            name: it[0],
                            type: 'custom',
                            xAxisIndex: 1,
                            yAxisIndex: 1,
                            renderItem: function (params, api) {
                                let start = api.coord([api.value(1), api.value(4)]);
                                let size = api.size([api.value(2) - api.value(1), api.value(4) - api.value(3)]);
                                let style = api.style();
                                style.text = ''

                                return {
                                    type: 'rect',
                                    shape: {
                                        x: start[0],
                                        y: start[1],
                                        width: size[0],
                                        height: size[1],
                                    },
                                    style: style
                                }
                            },
                            label: {
                                show: true,
                                position: 'top'
                            },
                            silent: true,
                            encode: {
                                x: [0, 1],
                                y: [2, 3],
                                itemName: 3
                            },
                            data: [it],
                            itemStyle: {
                                color: domainColorMap[it[0]] || '#A0A0A0',
                            }
                        }
                    })

                    let option = {
                        title: {
                            text: data.title,
                            x: 'center',
                            textStyle: {
                                fontSize: 13,
                            }
                        },
                        grid: [{
                            left: '5%',
                            width: '77%',
                            bottom: '25%',
                        }, {
                            top: '75%',
                            bottom: '18%',
                            left: '5%',
                            width: '77%',
                        }],
                        tooltip: {
                            formatter: function (params) {
                                // return `${params.marker} ${params.seriesName} : ${params.data[1]}`
                                return `${params.data[2] || ''}`
                            }
                        },
                        legend: [
                            {
                                top: '90%',
                                left: '10%',
                                width: '70%',
                                icon: 'rect',
                                data: Object.keys(domainColorMap),
                            }, {
                                top: '15%',
                                left: 'right',
                                width: '10%',
                                icon: 'circle',
                                // selectedMode: false,
                                data: Object.keys(pointColorMap),
                            }
                        ],
                        xAxis: [
                            {
                                type: 'value',
                                min: xMin,
                                max: xMax,
                                gridIndex: 0,
                                axisTick: {show: false},
                                axisLabel: {show: false},
                                axisLine: {show: false},
                                splitLine: {show: false},
                            },
                            {
                                type: 'value',
                                min: xMin,
                                max: xMax,
                                gridIndex: 1,
                                splitLine: {show: false},
                            },
                        ],
                        yAxis: [
                            {
                                type: 'value',
                                // min: yMin - 1,
                                max: yMax,
                                minInterval: 1,
                                nameGap: 100,
                                gridIndex: 0,
                                splitLine: {show: false},
                            },
                            {
                                type: 'value',
                                min: 0,
                                max: 100,
                                gridIndex: 1,
                                axisTick: {show: false},
                                axisLabel: {show: false},
                                axisLine: {show: false},
                                splitLine: {show: false},
                            }
                        ],
                        series: [
                            ...bottomSeries,
                            ...scatterSeries,
                            ...barSeries
                        ]
                    };

                    chart.setOption(option);
                }
            }

            function initChartInteractions(taskId) {
                if (!document.getElementById('Interactions-chart')) {
                    return
                }
                let chart = echarts.init(document.getElementById('Interactions-chart'));
                chart.clear();

                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'Interactions'
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#item-Interactions').hide();
                        } else {
                            $('#item-Interactions').show();
                            drawChart(chart, result.data);
                        }
                    }
                })

                function drawChart(chart, data) {
                    let xName = data.xname;
                    let yName = data.yname;
                    let resultData = data.data;

                    let seriesData = resultData.map(function (item) {
                        return [item[1], item[0], item[2]];
                    });

                    let option = {
                        title: {
                            text: '{dots|} {normal|< 0.05}\n{asterisk|} {normal|< 0.01}',
                            right: '20%',
                            top: '320',
                            textStyle: {
                                rich: {
                                    dots: {
                                        backgroundColor: {image: ctx + '/images/圆点.svg'},
                                        height: 10,
                                        width: 10,
                                    },
                                    asterisk: {
                                        backgroundColor: {image: ctx + '/images/星号.svg'},
                                        height: 12,
                                        width: 12
                                    },
                                    normal: {
                                        fontSize: 14
                                    }
                                },
                            }
                        },
                        tooltip: {
                            position: 'top',
                            formatter: function (params) {
                                let data = params.data;
                                return `${params.marker} ${yName[data[1]]}-${xName[data[0]]} : <b>${data[2]}</b>`
                            }
                        },
                        grid: [{
                            width: '500',
                            height: '500',
                            left: '25%',
                            bottom: '0'
                        }, {
                            width: '35px',
                            height: '145px',
                            right: '26%',
                            bottom: '0',
                        }],
                        xAxis: [{
                            gridIndex: 0,
                            position: 'top',
                            offset: 2,
                            type: 'category',
                            data: xName,
                            axisLabel: {
                                rotate: -90,
                                interval: 0
                            },
                            axisTick: {show: false},
                            axisLine: {show: false}
                        }, {
                            gridIndex: 1,
                        }],
                        yAxis: [{
                            gridIndex: 0,
                            type: 'category',
                            data: yName,
                            inverse: true,
                            axisLabel: {
                                interval: 0
                            },
                            axisTick: {show: false},
                            axisLine: {show: false},
                        }, {
                            name: '-log10(P-value)',
                            nameLocation: 'center',
                            nameRotate: '90',
                            type: 'value',
                            nameGap: -50,
                            gridIndex: 1,
                            nameTextStyle: {
                                fontSize: 13,
                            },
                            axisTick: {show: false},
                            axisLine: {show: false},
                        }],
                        visualMap: {
                            itemHeight: 130,
                            textStyle: {
                                fontSize: 13
                            },
                            min: -3,
                            max: 3,
                            type: 'continuous',
                            orient: 'vertical',
                            calculable: true,
                            right: '26%',
                            bottom: '0',
                            inRange: {
                                color: ['#8C510A', '#F5F5F5', 'rgb(84, 112, 189)'],
                                symbolSize: [90, 90]
                            },
                            formatter: function (value) {
                                if (value === -3)
                                    return '>3 (Mutually exclusive)';
                                else if (value === 3)
                                    return '>3 (Co-occurence)';
                                else
                                    return Math.abs(value.toFixed(2))
                            },
                            rich: {
                                dots: {
                                    backgroundColor: {image: ctx + '/images/圆点.svg'},
                                    height: 10
                                },
                                asterisk: {
                                    backgroundColor: {image: ctx + '/images/星号.svg'},
                                    height: 10
                                }
                            },
                        },
                        series: [{
                            type: 'heatmap',
                            data: seriesData,
                            itemStyle: {
                                normal: {
                                    borderColor: "#fff",
                                    borderWidth: 1,
                                    areaColor: '#272235',
                                }
                            },
                            label: {
                                show: true,
                                textStyle: {
                                    color: '#333333',
                                    fontWeight: 700,
                                    fontSize: '18'
                                },
                                formatter: function (value) {
                                    if (resultData[value.dataIndex][3] === 0) {
                                        return '{asterisk|}'
                                    } else if (resultData[value.dataIndex][3] === 1) {
                                        return '{dots|}'
                                    } else {
                                        return ''
                                    }
                                },
                                rich: {
                                    dots: {
                                        backgroundColor: {image: ctx + '/images/圆点.svg'},
                                        height: 8
                                    },
                                    asterisk: {
                                        backgroundColor: {image: ctx + '/images/星号.svg'},
                                        height: 12
                                    }
                                },
                            }
                        }]
                    };
                    chart.setOption(option)
                }
            }

            function initChartTMBPlot(taskId) {
                if (!document.getElementById('TMBPlot-chart')) {
                    return
                }
                let chart = echarts.init(document.getElementById('TMBPlot-chart'));
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'TMBPlot'
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#item-TMBPlot').hide();
                            return;
                        }
                        $('#item-TMBPlot').show();

                        let data = result.data;
                        drawChart(chart, data);

                        let pvalue = data.pvalue;
                        let tableData = pvalue.slice(1).map((it, index) => {
                            let item = {};
                            let val = Number(it[2]);
                            if (val < 0.01 && val > 0) {
                                val = val.toExponential(2);
                            } else {
                                val = val.toFixed(2);
                            }
                            item['Cohort2'] = it[1];
                            item['p-value'] = val;
                            return item;
                        })

                        // note: 表格初始化
                        $('#TMB-table').bootstrapTable({
                            classes: 'table-sm',
                            iconSize: 'sm',
                            pagination: true,
                            pageSize: 10,
                            pageList: [10],
                            paginationLoop: false,
                            paginationNextText: 'Next',
                            paginationPreText: 'Previous',
                            paginationDetailHAlign: ' hidden',  // 隐藏分页信息
                            columns: [{title: 'Cohort', field: 'Cohort2'}, {title: 'p-value', field: 'p-value'}],
                            data: tableData
                        });

                    }
                })

                function drawChart(chart, data) {
                    let tmpData = data.tmpData;
                    let yname = data.yname;

                    let xlist = [];
                    let xMax;
                    let scatterData = [];
                    let linesData = [];
                    let barData = [];
                    tmpData.forEach((it, i1) => {
                        it.xlogVals.forEach((x, i2) => {
                            scatterData.push([it.xlogVals[i2], it.ylogVals[i2], it.type]);

                            if (xMax === undefined || xMax < it.xlogVals[i2]) {
                                xMax = it.xlogVals[i2];
                            }
                        })


                        let medianMutations = it.medianMutationsLog10;
                        linesData.push({
                            coords: [
                                [it.minLogXval, medianMutations],
                                [it.maxLogXval, medianMutations]
                            ]
                        })

                        xlist.push([it.cancer, it.cohortSize]);

                        barData.push({
                            value: 1,
                            itemStyle: {
                                // color: i1 % 2 === 0 ? "rgba(245, 248, 233, 1)" : "rgba(207, 223, 235, 1)",
                                color: i1 % 2 === 0 ? "rgb(245, 248, 233)" : "rgb(207, 223, 235)",
                            },
                            emphasis: {
                                // label: `${it.cancer} (${it.cohortSize}) : ${toFixVal(it.medianMutationsLog10)}`,
                                itemStyle: {
                                    color: i1 % 2 === 0 ? "rgba(245, 248, 233, 0)" : "rgba(207, 223, 235, 0)",
                                    borderColor: "rgba(68, 208, 243, 1)",
                                    borderWidth: 1
                                }
                            },
                        })
                    })

                    let option = {
                        grid: {
                            right: '1%'
                        },
                        tooltip: {},
                        xAxis: [{
                            show: false,
                            type: 'value',
                            scale: true,
                            min: 0,
                            max: xMax,
                        }, {
                            type: 'category',
                            position: 'bottom',
                            axisLabel: {
                                rotate: 90,
                                fontSize: 10,
                            },
                            data: xlist.map(it => it[0]),
                            axisLine: {show: false},
                            axisTick: {show: false},
                        }, {
                            type: 'category',
                            position: 'top',
                            axisLabel: {
                                rotate: -90,
                                fontSize: 10,
                            },
                            data: xlist.map(it => it[1]),
                            axisLine: {show: false},
                            axisTick: {show: false},
                        }],
                        yAxis: [{
                            type: 'value',
                            name: yname,
                            nameRotate: 90,
                            nameGap: 25,
                            nameLocation: 'middle',
                            nameTextStyle: {fontSize: 14},
                            axisLabel: {
                                formatter: function (param) {
                                    return Math.pow(10, param)
                                }
                            },
                            axisLine: {show: false},
                            axisTick: {show: false},
                        }, {
                            show: false,
                            type: 'value',
                            min: 0,
                            max: 1,
                            axisLine: {show: false},
                            axisTick: {show: false},
                        }],
                        series: [
                            {
                                type: 'lines',
                                data: linesData,
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                coordinateSystem: 'cartesian2d',
                                lineStyle: {
                                    type: 'solid',
                                    color: 'red',
                                    width: 2,
                                },
                                tooltip: {
                                    formatter: function (param) {
                                        let info = tmpData[param.dataIndex]
                                        return `${info.cancer} (${info.cohortSize}) : ${toFixVal(info.medianMutationsLog10)}`
                                    }
                                },
                                z: 5
                            },
                            {
                                type: 'scatter',
                                data: scatterData,
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                symbolSize: 2,
                                // silent: true,
                                itemStyle: {
                                    color: function (param) {
                                        let type = param.data[2];
                                        return ("Input" == type) ? "rgba(0, 0, 0, 1)" : "rgba(179, 179, 179, 1)"
                                    }
                                },
                                z: 4
                            }, {
                                type: 'bar',
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                data: barData,
                                barWidth: '100%',
                                animation: false,
                                z: 1,
                                tooltip: {
                                    formatter: function (param) {
                                        let info = tmpData[param.dataIndex]
                                        return `${info.cancer} (${info.cohortSize})`
                                    }
                                }
                            }
                        ]
                    }

                    chart.setOption(option);
                }
            }

            function initChartDriverGene(taskId) {
                if (!document.getElementById('DriverGene-chart')) {
                    return
                }
                let chart = echarts.init(document.getElementById('DriverGene-chart'));
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'DriverGene'
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#item-DriverGene').hide();
                            return;
                        }
                        $('#item-DriverGene').show();

                        let {data, res} = result.data;
                        if (data) {
                            $('#DriverGene-chart-div').show();
                            drawChart(chart, data);
                            $('#res-table-div').attr('class', "col-md-6");
                        } else {
                            $('#DriverGene-chart-div').hide();
                            $('#res-table-div').attr('class', "col-md-11");
                        }

                        let tableColumns = res[0].map(it => {
                            return {
                                field: it,
                                title: it,
                            }
                        })
                        let tableData = res.slice(1).map((it, index) => {
                            let item = {};

                            item[res[0][0]] = it[0];
                            item[res[0][1]] = toFixVal(it[1]);
                            item[res[0][2]] = toFixVal(it[2]);
                            return item;
                        })

                        function toFixVal(val) {
                            let numb = Number(val);
                            if (numb < 0.01) {
                                numb = numb.toExponential(2);
                            } else {
                                numb = numb.toFixed(2);
                            }
                            return numb;
                        }

                        // 表格初始化
                        $('#res-table').bootstrapTable({
                            classes: 'table-sm',
                            iconSize: 'sm',
                            pagination: true,
                            pageSize: 15,
                            pageList: [15],
                            paginationLoop: false,
                            paginationNextText: 'Next',
                            paginationPreText: 'Previous',
                            paginationDetailHAlign: ' hidden',
                            columns: tableColumns,
                            data: tableData
                        });
                    }
                })

                function drawChart(chart, data) {
                    let option = {
                        title: {
                            text: 'Fraction of variants within clusters',
                            left: '30%',
                            bottom: '1%',
                            x: 'center',
                            textStyle: {
                                fontSize: 13,
                            }
                        },
                        grid: {
                            left: '15%',
                            top: '10%'
                        },
                        xAxis: {
                            splitLine: {
                                lineStyle: {
                                    type: 'dashed'
                                }
                            }
                        },
                        yAxis: {
                            splitLine: {
                                lineStyle: {
                                    type: 'dashed'
                                }
                            },
                            name: '-log10(FDR)',
                            nameRotate: 90,
                            nameGap: 25,
                            nameLocation: 'middle',
                            nameTextStyle: {fontSize: 14},
                            axisLine: {show: true},
                            scale: true
                        },
                        series: [{
                            name: 'red',
                            data: data[0],
                            type: 'scatter',
                            symbolSize: function (data) {
                                return data[2] * 6;
                            },
                            label: {
                                show: true,
                                color: 'red',
                                // 高亮时标签的文字。
                                formatter: '{@[3]}',
                                position: 'left'
                            },
                            itemStyle: {
                                color: 'red'
                            }
                        }, {
                            name: 'royalblue',
                            data: data[1],
                            type: 'scatter',
                            symbolSize: function (data) {
                                return data[2] * 6;
                            },
                            itemStyle: {
                                color: 'blue'
                            }
                        }]
                    };
                    chart.setOption(option)
                }
            }

            function initChartSignatures(taskId) {
                if (!document.getElementById('Signatures-chart')) {
                    return
                }
                let chart = echarts.init(document.getElementById('Signatures-chart'));
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'Signatures'
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#item-Signatures').hide();
                            return;
                        }
                        $('#item-Signatures').show();
                        drawChart(chart, result.data);
                    }
                })

                function drawChart(chart, data) {
                    let option = {
                        grid: {
                            // weight: '30%'
                            top: '5%',
                            bottom: '10%'
                        },
                        xAxis: {
                            min: 2,
                            splitLine: {show: false}
                        },
                        yAxis: {
                            scale: true,
                        },
                        series: [{
                            data: data.slice(1),
                            type: 'line',
                            symbol: 'none',
                            silent: true,
                            lineStyle: {
                                color: "#a0a0a0"
                            }
                        }, {
                            type: 'scatter',
                            data: data.slice(1),
                            itemStyle: {
                                color: 'rgb(255, 190, 0)',
                            },
                            z: 3
                        }]
                    }
                    chart.setOption(option)

                    chart.on('click', function (param) {
                        let value = param.data[0];
                        $('#similarities-select').val(value).trigger('change');
                    })
                }
            }

            function initChartTrinucleotide(taskId) {
                if (!document.getElementById('Trinucleotide-chart')) {
                    return;
                }
                let chart = echarts.init(document.getElementById('Trinucleotide-chart'));
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'Trinucleotide',
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#item-Trinucleotide').hide();
                            return;
                        }
                        $('#item-Trinucleotide').show();
                        drawChart(chart, result.data);
                    }
                });

                function drawChart(chart, rowData) {
                    let {data, cosmicColorMap} = rowData;

                    let mainData = [];
                    let bottomArr = [];
                    let xlist = []
                    Object.keys(data).forEach(key => {
                        bottomArr.push(key);
                        xlist.push(...data[key].map(it => it[0]))
                        mainData.push(...data[key]);
                    })

                    let option = {
                        title: {
                            text: 'Trinucleotide context',
                            x: 'center',
                            bottom: '10%',
                            textStyle: {
                                fontSize: 13,
                            }
                        },
                        grid: [{
                            bottom: '30%',
                            right: '1%'
                        }, {
                            top: '70%',
                            right: '1%',
                            height: 20,
                        }],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            formatter: function (param) {
                                let value = param[0].value;
                                if (cosmicColorMap[value[0]] != null) {
                                    return '';
                                }
                                return `${param[0].marker} ${value[0]} : ${Number(value[1]).toFixed(2)}`
                            }
                        },
                        xAxis: [{
                            type: 'category',
                            gridIndex: 0,
                            data: xlist,
                            axisLabel: {
                                show: true,
                                interval: 0,
                                rotate: 90,
                                margin: 25,
                                fontSize: 6,
                                color: '#000'
                            },
                            axisLine: {show: false},
                            splitLine: {show: false},
                            axisTick: {show: false}
                        }, {
                            type: 'category',
                            gridIndex: 1,
                            data: Array.from(bottomArr),
                            axisLabel: {
                                show: true,
                                margin: -12,
                                color: '#000'
                            },
                            zlevel: 1
                        }],
                        yAxis: [{
                            name: "Proportion of mutations(%)",
                            type: 'value',
                            gridIndex: 0,
                            position: 'left',
                            nameLocation: 'middle',
                            nameGap: 25,
                            nameRotate: 90,
                            nameTextStyle: {fontSize: 14},
                            axisLine: {show: true},
                        }, {
                            type: 'value',
                            gridIndex: 1,
                            min: 0,
                            max: 1,
                            axisLabel: {show: false},
                            axisLine: {show: false},
                            splitLine: {show: false},
                            axisTick: {show: false}
                        }],
                        series: [
                            {
                                type: "bar",
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                data: mainData,
                                itemStyle: {
                                    normal: {
                                        color: (params) => {
                                            return cosmicColorMap[params.data[2]]
                                        }
                                    }
                                }
                            }, {
                                type: "bar",
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                data: bottomArr.map(it => {
                                    return [it, 1, it]
                                }),
                                barWidth: '100%',
                                itemStyle: {
                                    normal: {
                                        color: (params) => {
                                            return cosmicColorMap[params.data[2]]
                                        }
                                    }
                                }
                            }
                        ]
                    }

                    chart.setOption(option);
                }
            }

            function initChartTitv(taskId) {
                if (!document.getElementById('Titv-chart')) {
                    return;
                }
                let chart = echarts.init(document.getElementById('Titv-chart'));
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'Titv',
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#Titv-chart').hide();
                            return;
                        }
                        $('#Titv-chart').show();
                        drawChart(chart, result.data);
                    }
                });

                function drawChart(chart, rowData) {
                    let {data, cosmicColorMap} = rowData;

                    let option = {
                        title: [],
                        xAxis: [],
                        grid: [],
                        dataset: [],
                        yAxis: [],
                        series: [],
                        tooltip: {
                            trigger: 'item',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                    };

                    Object.keys(data).forEach((key, index) => {
                        option.grid.push({
                            left: (index * (80 / Object.keys(data).length) + 10) + '%',
                            width: (80 / Object.keys(data).length) + '%'
                        })
                        option.xAxis.push({
                            gridIndex: index,
                            type: 'category',
                            boundaryGap: true,
                            nameGap: 30,
                            splitLine: {
                                show: true,
                            },
                        });
                        option.yAxis.push({
                            gridIndex: index,
                            type: 'value',
                            min: 0,
                            max: 100,
                            axisLine: {show: false},
                            axisLabel: {show: false},
                            axisTick: {show: false},
                            splitLine: {show: true}
                        });

                        option.dataset.push({
                            source: [data[key]]
                        })

                        option.dataset.push({
                            fromDatasetIndex: index * 3,
                            transform: {
                                type: 'boxplot',
                                config: {
                                    itemNameFormatter: key
                                },
                            }
                        })

                        option.dataset.push({
                            fromDatasetIndex: index * 3 + 1,
                            fromTransformResult: 1
                        })

                        option.series.push({
                            name: key,
                            type: 'scatter',
                            datasetIndex: index * 3 + 2,
                            xAxisIndex: index,
                            yAxisIndex: index,
                            tooltip: {
                                formatter: function (params) {
                                    return `${params.seriesName} : ${(params.data[1].toFixed(2))}`
                                }
                            },
                            color: 'black',
                            width: 0.5,
                            symbolSize: 4
                        });

                        option.series.push({
                            name: key,
                            type: 'boxplot',
                            datasetIndex: index * 3 + 1,
                            xAxisIndex: index,
                            yAxisIndex: index,
                            itemStyle: {
                                color: cosmicColorMap[key],
                                borderColor: '#000'
                            },
                            tooltip: {
                                formatter: function (param) {
                                    return [
                                        param.seriesName + ' :',
                                        'upper: ' + param.data[5].toFixed(2),
                                        'Q3: ' + param.data[4].toFixed(2),
                                        'median: ' + param.data[3].toFixed(2),
                                        'Q1: ' + param.data[2].toFixed(2),
                                        'lower: ' + param.data[1].toFixed(2)
                                    ].join('<br/>');
                                }
                            },
                        })
                    })

                    option.yAxis[0].axisLabel.show = true;
                    option.yAxis[0].axisLine.show = true;
                    option.yAxis[0].axisTick.show = true;

                    chart.setOption(option);
                }
            }

            function initChartPathway(taskId) {
                let element = document.getElementById('Pathway-chart');
                if (!element) {
                    return;
                }
                let chart = echarts.init(element);
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'Pathway'
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#item-Pathway').hide();
                            return;
                        }
                        $('#item-Pathway').show();
                        drawChart(chart, result.data);
                    }
                });

                function drawChart(chart, data) {
                    data.pathway.forEach((key, index) => {
                        if (index === 0) {
                            pathwayInfo(taskId, key);
                        }
                        $('#pathway-select').append(`<option value="${key}">${key}</option>`)
                    })

                    let pathWay = data.pathway || [];
                    let fractionAffected = data.fractionAffected || [];
                    let fractionMutatedSamples = data.fractionMutatedSamples || [];
                    let pathwayfLabel = data.pathwayfLabel || [];
                    let samplesfLabel = data.samplesfLabel || [];
                    // 获取x轴最大值
                    let maxXval = Math.max.apply(null, fractionAffected.concat(fractionMutatedSamples));

                    let option = {
                        title: [
                            {
                                text: 'Fraction of pathway affected',
                                // left: '10%',
                                top: '1%',
                                textStyle: {
                                    fontSize: 12,
                                }
                            }, {
                                text: 'Fraction of samples affected',
                                left: '55%',
                                top: '1%',
                                textStyle: {
                                    fontSize: 12,
                                }
                            }
                        ],
                        color: ['#c23531', '#c23531'],
                        tooltip: {
                            show: true,
                            trigger: "axis",
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
                            },
                            formatter: function (param) {
                                let currItem = param[0];
                                return currItem.axisValueLabel + '<br>' + getPoint(currItem.color) + $.trim(toFixVal(currItem.value * 100.0) + '%');
                            }
                        },
                        xAxis: [
                            {
                                type: "value",
                                max: maxXval,
                                inverse: false,
                                splitLine: {
                                    // 不显示分割线
                                    show: false
                                },
                                gridIndex: 0,
                                axisTick: {
                                    // 显示刻度
                                    show: true
                                },
                                axisLine: {
                                    // 显示坐标轴线
                                    show: true
                                }
                            },
                            {
                                type: "value",
                                max: maxXval,
                                splitLine: {
                                    show: false
                                },
                                gridIndex: 1,
                                axisTick: {
                                    show: true
                                },
                                axisLine: {
                                    show: true
                                }
                            }
                        ],
                        yAxis: [
                            {
                                triggerEvent: true,
                                type: 'category',
                                data: pathWay,
                                inverse: false,
                                gridIndex: 0,
                                axisTick: {
                                    show: false
                                },
                                axisLine: {
                                    show: false
                                },
                                position: "left"
                            },
                            {
                                type: 'category',
                                data: pathWay,
                                inverse: false,
                                gridIndex: 1,
                                axisTick: {
                                    show: false
                                },
                                axisLine: {
                                    show: false
                                },
                                show: false
                            }
                        ],
                        grid: [
                            {
                                left: '15%',
                                top: '6%',
                                width: '36%'
                            }, {
                                left: '58%',
                                top: '6%',
                                width: '36%'
                            }
                        ],
                        series: [
                            {
                                type: 'bar',
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                data: fractionAffected,
                                showBackground: true,
                                backgroundStyle: {
                                    color: 'rgba(180, 180, 180, 0.2)'
                                },
                                label: {
                                    show: true,
                                    // position: 'right',
                                    // position: ['350px', '30%'],
                                    formatter: function (value) {
                                        return pathwayfLabel[value.dataIndex]
                                    }
                                }
                            },
                            {
                                type: 'bar',
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                data: fractionMutatedSamples,
                                showBackground: true,
                                backgroundStyle: {
                                    color: 'rgba(180, 180, 180, 0.2)'
                                },
                                label: {
                                    show: true,
                                    // position: ['350px', '30%'],
                                    // position: 'right',
                                    formatter: function (value) {
                                        return samplesfLabel[value.dataIndex]
                                    }
                                }
                            }
                        ],
                        legend: {
                            show: true
                        },
                        animationDuration: 0
                    };

                    chart.setOption(option);

                    // 点击 y 轴 label 切换下方图例
                    chart.on('click', 'yAxis.category', function (params) {
                        $('#pathway-select').val(params.value).trigger('change');
                    });
                }
            }

            function similaritiesChart(taskId, number) {
                if (!document.getElementById('Similarities-chart')) {
                    return;
                }
                let chart = echarts.init(document.getElementById('Similarities-chart'));
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'Similarities',
                        'number': number
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#Similarities-chart').hide();
                            return;
                        }
                        $('#Similarities-chart').show();
                        drawChart(chart, result.data);
                    }
                });

                function drawChart(chart, data) {
                    let option = getHeatmapOption(data.data, data.xlist, data.ylist);
                    chart.setOption(option);

                    /**
                     *
                     * @param data 数据
                     * @param xlabels x 轴类目
                     * @param ylabels y 轴类目
                     * @param type 热力图类型
                     */
                    function getHeatmapOption(data, xlabels, ylabels, type) {

                        let treeData = genTreeData(data, xlabels, ylabels);


                        let lines = [];         // 聚类线
                        let nowX = [];          // 调整 x 轴顺序
                        let maxDepth = genDepth(treeData, 0, nowX);
                        genNodeX(treeData, nowX);
                        genTreeCoords(treeData.children, treeData.pX, treeData.depth, maxDepth, lines);

                        let nowXLabels = [];
                        nowX.forEach(it => {
                            nowXLabels.push(xlabels[it])
                        })

                        // 热力图数据
                        let mainSeriesData = [];
                        let maxValue = 0;
                        data.forEach((v1, i1) => {
                            v1.forEach((v2, i2) => {
                                let value = Number(v2)
                                mainSeriesData.push([nowX.indexOf(i2), i1, value]);
                                maxValue = maxValue > value ? maxValue : value;
                            })
                        })

                        return {
                            grid: [{
                                top: '15%',
                                bottom: '70%',
                            }, {
                                top: '30%',
                                bottom: '80px'
                            }],
                            title: {
                                text: 'cosine similarities against validated signatures',
                                left: '30%',
                                x: 'center',
                                textStyle: {
                                    fontSize: 13,
                                }
                            },
                            visualMap: {
                                min: 0,
                                max: maxValue,
                                seriesIndex: 1,
                                calculable: true,
                                precision: 2,
                                right: '1%',
                                bottom: '0',
                                inRange: {
                                    color: ['rgb(70, 120, 180)', 'rgb(250, 250, 200)', 'rgb(215, 50, 40)'],
                                },
                            },
                            xAxis: [{
                                gridIndex: 0,
                                type: 'value',
                                min: 0,
                                max: xlabels.length,
                                splitLine: {show: false,},
                                axisTick: {show: false},
                                axisLabel: {show: false},
                                axisLine: {show: false},
                            }, {
                                gridIndex: 1,
                                type: 'category',
                                data: nowXLabels,
                                axisTick: {show: false},
                                axisLabel: {
                                    rotate: 90,
                                }
                            }],
                            yAxis: [{
                                gridIndex: 0,
                                type: 'value',
                                min: 0,
                                max: maxDepth,
                                splitLine: {show: false,},
                                axisTick: {show: false},
                                axisLabel: {show: false},
                                axisLine: {show: false},
                            }, {
                                type: 'category',
                                gridIndex: 1,
                                inverse: true,
                                data: ylabels,
                                axisTick: {show: false}
                            }],
                            series: [{
                                type: 'lines',
                                xAxisIndex: 0,
                                yAxisIndex: 0,
                                coordinateSystem: 'cartesian2d',
                                polyline: true,
                                data: lines,
                                lineStyle: {
                                    type: 'solid',
                                    color: '#000'
                                }
                            }, {
                                type: 'heatmap',
                                xAxisIndex: 1,
                                yAxisIndex: 1,
                                data: mainSeriesData,
                            }]
                        };

                    }

                    /**
                     * 生成树状图数据
                     */
                    function genTreeData(data, xlabels, ylabels) {
                        let cols = [];
                        for (let i = 0; i < data[0].length; i++) {
                            let col = [];
                            for (let j = 0; j < data.length; j++) {
                                col.push(Number(data[j][i]))
                            }
                            cols.push(col)
                        }

                        let hclustData = hclust(cols, undefined, 'complete')[0].elements;


                        return {
                            children: genToTreeData(hclustData, xlabels, ylabels)
                        }

                    }

                    /**
                     * hclust 数据转换成 JSON 数据
                     */
                    function genToTreeData(data, xlabels, ylabels) {
                        let list = [];
                        data.forEach(item => {
                            if (Array.isArray(item)) {
                                if (item.length === 1) {
                                    list.push({index: item[0], name: xlabels[item[0]]})
                                } else {
                                    list.push({
                                        children: genToTreeData(item, xlabels, ylabels)
                                    })
                                }
                            } else {
                                list.push({index: item})
                            }
                        })
                        return list;
                    }

                    /**
                     * 设置节点深度，返回最大深度
                     * @param root 当前节点
                     * @param depth 当前节点深度
                     * @param nowX 调整 x 轴顺序，避免重回
                     * @returns {number} 最大深度
                     */
                    function genDepth(root, depth, nowX) {
                        if (root.children == null || root.children.length == 0) {
                            nowX.push(root.index)
                            return 0;
                        }
                        root.depth = depth;
                        let depths = [];
                        let children = root.children || [];
                        children.forEach(element => {
                            depths.push(genDepth(element, depth + 1, nowX));
                        });

                        return Math.max(...depths) + 1;
                    }

                    /**
                     * 设置节点 x 轴坐标
                     * @param root
                     * @param nowX 调整 x 轴顺序，避免重回
                     * @returns {number} 当前节点 x 轴坐标
                     */
                    function genNodeX(root, nowX) {
                        if (root.children == null && root.index != null) {
                            // let pX = root.index + 0.5;
                            let pX = nowX.indexOf(root.index) + 0.5;
                            root.pX = pX;
                            return pX;
                        }

                        let children = root.children || [];

                        let list = [];
                        children.forEach(item => {
                            list.push(genNodeX(item, nowX));
                        });

                        let sum = list.reduce(function (prev, curr) {
                            return prev + curr;
                        });

                        let pX = sum / list.length;
                        root.pX = pX;

                        return pX;
                    }

                    /**
                     * 根据坐标画折线
                     * @param childs 某节点下的子节点
                     * @param parentPx 子节点的父节点 x 轴坐标
                     * @param parentDepth 子节点的父节点深度
                     * @param maxDepth 最大深度
                     * @param result 结果数据
                     */
                    function genTreeCoords(childs, parentPx, parentDepth, maxDepth, result) {
                        childs.forEach(item => {
                            let coords = [
                                [parentPx, maxDepth - parentDepth],
                                [item.pX, maxDepth - parentDepth],
                                [item.pX, maxDepth - (item.depth || maxDepth)]
                            ]
                            result.push({
                                coords: coords
                            })

                            if (item.children != null && item.children.length > 0) {
                                genTreeCoords(item.children, item.pX, item.depth, maxDepth, result)
                            }
                        })
                    }
                }
            }

            function cosmicSignatureChart(taskId, number) {
                if (!document.getElementById('CosmicSignature-chart')) {
                    return;
                }
                let chart = echarts.init(document.getElementById('CosmicSignature-chart'));
                chart.clear();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'CosmicSignature',
                        'number': number
                    },
                    success: function (result) {
                        if (!processChartResultCode(result)) {
                            $('#CosmicSignature-chart').hide();
                            return;
                        }
                        $('#CosmicSignature-chart').show();
                        drawChart(chart, result.data);
                    }
                });

                function drawChart(chart, rowData) {
                    let {cosmicColorMap, title, rns, data} = rowData;

                    let option = {
                        grid: [],
                        title: [],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            formatter: function (param) {
                                let value = param[0].value;
                                if (cosmicColorMap[value[0]] != null) {
                                    return '';
                                }
                                return `${param[0].marker} ${value[0]} : ${value[1].toFixed(2)}`
                            }
                        },
                        xAxis: [],
                        yAxis: [],
                        series: []
                    };

                    let xlist = [];
                    Object.keys(cosmicColorMap).forEach((it, index) => {
                        data[it].forEach(it2 => xlist.push(it2[0]))
                    })

                    let mainSeries = [];
                    rns.forEach((rn, index) => {
                        let avgHeight = 90 / rns.length;
                        let titleHeight = avgHeight * 0.1;
                        let contentHeight = avgHeight * 0.7;
                        let bottomHeight = avgHeight * 0.1;
                        option.grid.push({
                            top: (index * avgHeight + titleHeight) + '%',
                            height: contentHeight + '%'
                        });
                        option.grid.push({
                            top: (index * avgHeight + titleHeight + contentHeight + 1) + '%',
                            height: bottomHeight + '%'
                        })
                        option.title.push({
                            text: title[index],
                            top: (index * avgHeight) + '%',
                            left: '50%',
                            textAlign: 'center',
                            textStyle: {
                                fontSize: 10,
                                fontWeight: 600,
                                fontStyle: 'italic'
                            }
                        })
                        option.xAxis.push({
                            type: 'category',
                            gridIndex: index * 2,
                            data: xlist,
                            axisLabel: {show: false},
                            axisLine: {show: false},
                            splitLine: {show: false},
                            axisTick: {show: false},
                        })
                        option.xAxis.push({
                            gridIndex: index * 2 + 1,
                            data: Object.keys(cosmicColorMap),
                            axisLabel: {show: false},
                            axisLine: {show: false},
                            splitLine: {show: false},
                            axisTick: {show: false},
                        })
                        option.yAxis.push({
                            type: 'value',
                            gridIndex: index * 2,
                            min: 0,
                            max: 0.3,
                            interval: 0.1,
                            axisLine: {show: true},
                            axisTick: {show: true}
                        })
                        option.yAxis.push({
                            gridIndex: index * 2 + 1,
                            min: 0,
                            max: 1,
                            interval: 1,
                            axisLabel: {show: false},
                            axisLine: {show: false},
                            splitLine: {show: false},
                            axisTick: {show: false}
                        })

                        let mainData = [];
                        Object.keys(data).forEach(key => {
                            data[key].forEach(it => {
                                mainData.push([it[0], it[index + 1], key])
                            })
                        })

                        mainSeries.push({
                            type: 'bar',
                            xAxisIndex: index * 2,
                            yAxisIndex: index * 2,
                            data: mainData,
                            itemStyle: {
                                normal: {
                                    color: (params) => {
                                        return cosmicColorMap[params.data[2]]
                                    }
                                }
                            }
                        });

                        let bottomData = Object.keys(cosmicColorMap).map(it => {
                            return [it, 1, it];
                        })

                        mainSeries.push({
                            type: 'bar',
                            xAxisIndex: index * 2 + 1,
                            yAxisIndex: index * 2 + 1,
                            data: bottomData,
                            barWidth: '100%',
                            itemStyle: {
                                normal: {
                                    color: (params) => {
                                        return cosmicColorMap[params.data[0]]
                                    }
                                }
                            },
                        })
                    })
                    option.xAxis[option.xAxis.length - 1].axisLabel.show = true;
                    option.series = mainSeries;

                    chart.setOption(option);
                }
            }

            function pathwayInfo(taskId, name) {
                $('#pathway-text').text(pathway[name])
                let url = `${ctx}/analysis/somatic-adv/getAdvImg?taskId=${taskId}&name=${name}`;
                $('#pathway-img').html(`<img style="max-width: 100%; max-height: 315px" alt="" src="${url}">`)

                $('#pathway-path-chart').empty();
                $.ajax({
                    url: '/analysis/somatic-adv/getChartData',
                    data: {
                        'taskId': taskId,
                        'chartFlag': 'PathwayPath',
                        'pathway': name
                    },
                    success: function (result) {
                        if (result.error) {
                            layer.msg(result.message);
                            return;
                        }
                        let data = result.data;
                        if (data.rn == null || data.rn.length === 0) {
                            return;
                        }

                        // 自适应高度
                        $('#pathway-path-chart').html(`<div class="d-flex justify-content-center">
                            <div id="PathwayPath-chart" style="width: 100%;height: ${data.rn.length * 25 + 100}px"></div>
                        </div>`);
                        let chart = echarts.init(document.getElementById('PathwayPath-chart'));
                        chart.clear();
                        drawChart(chart, data);
                    }
                });


                function drawChart(chart, data) {
                    chart.clear();

                    let rnData = data.rn;
                    let dataTitles = data.dataTitles;
                    let seriesData = data.seriesData;
                    if (!seriesData || seriesData.length == 0) {
                        return false;
                    }
                    let allSeriesData = [];
                    for (let i = 0; i < seriesData.length; i++) {
                        let eachSeriesItem = {
                            name: dataTitles[i],
                            type: 'bar',
                            // 柱状图间距
                            barCategoryGap: '1%',
                            stack: 'total',
                            label: {
                                show: true,
                                formatter: function (param) {
                                    let currType = param.data.type;
                                    if ('point' == currType) {
                                        return ' ▪ ';
                                    } else {
                                        return '';
                                    }
                                }
                            },
                            emphasis: {
                                focus: 'series'
                            },
                            data: seriesData[i]
                        };
                        allSeriesData.push(eachSeriesItem);
                    }

                    let option = {
                        title: {
                            text: "Hippo pathway",
                            left: '5%',
                            textStyle: {
                                fontSize: 13,
                            }
                        },
                        tooltip: {
                            show: true,
                            trigger: 'item',
                            axisPointer: {
                                type: 'shadow'
                            },
                            formatter: function (param) {
                                return param.name + ": " + param.seriesName;
                            }
                        },
                        legend: {
                            show: false
                        },
                        grid: {
                            left: '5%',
                            right: 0,
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'value',
                            show: false,
                            splitLine: {show: false},
                            axisTick: {show: false},
                            axisLine: {show: false},
                        },
                        yAxis: {
                            type: 'category',
                            inverse: true,
                            data: rnData,
                            splitLine: {show: false},
                            axisTick: {show: false},
                            axisLine: {show: false},
                        },
                        series: allSeriesData
                    };
                    chart.setOption(option);
                }

            }

            function getPoint(color) {
                return `<i style="width:10px;height:10px;border-radius:50%;background-color:${color};display: inline-block"></i> &nbsp;`;
            }

            function toFixVal(val) {
                return (val !== null) ? parseFloat(val.toFixed(2)) : null;
            }
        })


    </script>
</th:block>
</html>
