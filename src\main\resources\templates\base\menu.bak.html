<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<nav class="side-bar" th:fragment="sideBar (menu)">
    <h5><i class="fa fa-sitemap text-muted"></i> Analysis </h5>
    <ul class="level-1 mb-3">

        <li><a href="#nav-toggle-1"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'rnaseq') ? '' : 'collapsed'}">RNA-Seq-DEG</a>
        <li th:if="${@modules.contains('rnaseq')}"><a href="#nav-toggle-1"
                                                      data-toggle="collapse"
                                                      th:classappend="${#strings.startsWith(menu,'rnaseq') ? '' : 'collapsed'}">RNA-Seq-DEG</a>
            <div id="nav-toggle-1" th:classappend="${#strings.startsWith(menu,'rnaseq') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'rnaseq-introduction'}?'active'">
                        <a th:href="@{/rnaseq}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'rnaseq-analysis'}?'active'">
                        <a th:href="@{/analysis/rnaseq/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>


        <li><a href="#nav-toggle-8"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'ase') ? '' : 'collapsed'}">RNA-Seq-ASE-rMATS</a>
        <li th:if="${@modules.contains('ase')}"><a href="#nav-toggle-8"
                                                   data-toggle="collapse"
                                                   th:classappend="${#strings.startsWith(menu,'ase') ? '' : 'collapsed'}">RNA-Seq-ASE-rMATS</a>
            <div id="nav-toggle-8" th:classappend="${#strings.startsWith(menu,'ase') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'ase-introduction'}?'active'">
                        <a th:href="@{/ase}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'ase-analysis'}?'active'">
                        <a th:href="@{/analysis/ase/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>

        <li><a href="#nav-toggle-9"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'circrna') ? '' : 'collapsed'}">RNA-Seq-circRNA</a>
        <li th:if="${@modules.contains('circrna')}"><a href="#nav-toggle-9"
                                                       data-toggle="collapse"
                                                       th:classappend="${#strings.startsWith(menu,'circrna') ? '' : 'collapsed'}">RNA-Seq-circRNA</a>
            <div id="nav-toggle-9" th:classappend="${#strings.startsWith(menu,'circrna') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'circrna-introduction'}?'active'">
                        <a th:href="@{/circrna}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'circrna-analysis'}?'active'">
                        <a th:href="@{/analysis/circrna/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>

        <li><a href="#nav-toggle-2"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'paean') ? '' : 'collapsed'}">RNA-Seq-ASE-Paean</a>
        <li th:if="${@modules.contains('paean')}"><a href="#nav-toggle-2"
                                                     data-toggle="collapse"
                                                     th:classappend="${#strings.startsWith(menu,'paean') ? '' : 'collapsed'}">RNA-Seq-ASE-Paean</a>
            <div id="nav-toggle-2" th:classappend="${#strings.startsWith(menu,'paean') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'paean-introduction'}?'active'">
                        <a th:href="@{/paean}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'paean-demo'}?'active'">
                        <a th:href="@{/analysis/paean/demo}">Demo</a>
                    </li>
                    <li th:classappend="${menu == 'paean-analysis'}?'active'">
                        <a th:href="@{/analysis/paean/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>

        <li>
        <li th:if="${@modules.contains('scrnaseq')}">
            <a href="#nav-toggle-3"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'scrnaseq') ? '' : 'collapsed'}">scRNA-Seq_10X</a>
            <div id="nav-toggle-3" th:classappend="${#strings.startsWith(menu,'scrnaseq') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'scrnaseq-introduction'}?'active'">
                        <a th:href="@{/scrnaseq}">Introduction</a>
                    </li>
                    <!--<li th:classappend="${menu == 'scrnaseq-analysis'}?'active'">
                        <a th:href="@{/analysis/scrnaseq/all}">Start Analysis</a>
                    </li>-->
                    <li th:classappend="${menu == 'scrnaseq-analysis-genomics'}?'active'">
                        <a th:href="@{/analysis/scrnaseq/form/genomics}">Basic Analysis</a>
                    </li>
                    <li th:classappend="${menu == 'scrnaseq-advanced'}?'active'">
                        <a href="#nav-toggle-3-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'scrnaseq-advanced') ? '' : 'collapsed'}">
                            <a th:href="@{/analysis/scrnaseq/list(type='advanced')}">Advanced Analysis</a>
                        </a>
                        <div id="nav-toggle-3-1"
                             th:classappend="${#strings.startsWith(menu,'scrnaseq-advanced') ? '' : 'collapse'}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'scrnaseq-advanced-baseline'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/baseline}">Cluster Description</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-paga'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/paga}" style="font-size: 12px">Pseudotime
                                    Trajectory</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-deg'}?'active font-13'"
                                ><a
                                        th:href="@{/analysis/scrnaseq/form/deg}">DEG and Enrichment</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-genes'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/genes}">Gene Description</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-wgcna'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/wgcna}">WGCNA</a></li>
                            </ul>
                        </div>
                    </li>
                    <li th:classappend="${menu == 'scrnaseq-report-add'}?'active'">
                        <a th:href="@{/analysis/scrnaseq/report/form}">Report</a>
                    </li>
                </ul>
            </div>
        </li>

        <li><a href="#nav-toggle-7"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'scrnasmartseq') ? '' : 'collapsed'}">Smart-Seq</a>
        <li th:if="${@modules.contains('scrnasmartseq')}"><a href="#nav-toggle-7"
                                                             data-toggle="collapse"
                                                             th:classappend="${#strings.startsWith(menu,'scrnasmartseq') ? '' : 'collapsed'}">Smart-Seq</a>
            <div id="nav-toggle-7" th:classappend="${#strings.startsWith(menu,'scrnasmartseq') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'scrnasmartseq-introduction'}?'active'">
                        <a th:href="@{/scrnasmartseq}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'scrnasmartseq-analysis'}?'active'">
                        <a th:href="@{/analysis/scrnaSmartseq/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>

        <li><a href="#nav-toggle-10"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'strnaseq') ? '' : 'collapsed'}">stRNA-Seq</a>
        <li th:if="${@modules.contains('strnaseq')}"><a href="#nav-toggle-10"
                                                        data-toggle="collapse"
                                                        th:classappend="${#strings.startsWith(menu,'strnaseq') ? '' : 'collapsed'}">stRNA-Seq</a>
            <div id="nav-toggle-10" th:classappend="${#strings.startsWith(menu,'strnaseq') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li><a href="#nav-toggle-8-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'strnaseq-10x') ? 'collapsed' : ''}"> 10X Genomics
                        Visium</a>
                        <div id="nav-toggle-8-1"
                             th:classappend="${#strings.startsWith(menu,'strnaseq-10x') ? 'collapse' : ''}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'strnaseq-introduction'}?'active'"><a
                                        th:href="@{/strnaseq}">Introduction</a></li>
                                <li th:classappend="${menu == 'strnaseq-analysis'}?'active'"><a
                                        th:href="@{/analysis/strnaseq/form}">Basic Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </li>

        <li><a href="#nav-toggle-4"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'somatic') ? '' : 'collapsed'}">WES/WGS-somatic</a>
        <li th:if="${@modules.contains('somatic')}"><a href="#nav-toggle-4"
                                                       data-toggle="collapse"
                                                       th:classappend="${#strings.startsWith(menu,'somatic') ? '' : 'collapsed'}">WES/WGS-somatic</a>
            <div id="nav-toggle-4" th:classappend="${#strings.startsWith(menu,'somatic') ? '' : 'collapse'}">
                <ul class="level-2">
                    <!--/*
                    <li th:classappend="${menu == 'somatic-introduction'}?'active'">
                        <a th:href="@{/somatic}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'somatic-analysis'}?'active'">
                        <a th:href="@{/analysis/somatic/form}">Basic Analysis</a>
                    </li>
                    <li th:classappend="${menu == 'somatic-adv-analysis'}?'active'">
                        <a th:href="@{/analysis/somatic-adv/form}">Advanced Analysis</a>
                    </li>
                    */-->

                    <li><a href="#nav-toggle-4-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'somatic-cnvs') ? 'collapsed' : ''}">SNVs+indels</a>
                        <div id="nav-toggle-4-1"
                             th:classappend="${#strings.startsWith(menu,'somatic-cnvs') ? 'collapse' : ''}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'somatic-introduction'}?'active'"><a
                                        th:href="@{/somatic}">Introduction</a></li>
                                <li th:classappend="${menu == 'somatic-analysis'}?'active'"><a
                                        th:href="@{/analysis/somatic/form}">Basic Analysis</a></li>
                                <li th:classappend="${menu == 'somatic-adv-analysis'}?'active'"><a
                                        th:href="@{/analysis/somatic-adv/form}">Advanced Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                    <li>
                        <a href="#nav-toggle-4-2" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'somatic-cnvs') ? '' : 'collapsed'}">CNVs</a>
                        <div id="nav-toggle-4-2"
                             th:classappend="${#strings.startsWith(menu,'somatic-cnvs') ? '' : 'collapse'}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'somatic-cnvs-introduction'}?'active'"><a
                                        th:href="@{/somatic-cnvs}">Introduction</a></li>
                                <li th:classappend="${menu == 'somatic-cnvs-analysis'}?'active'"><a
                                        th:href="@{/analysis/somatic-cnvs/form}">Basic Analysis</a></li>
                            </ul>
                        </div>
                    </li>

                </ul>
            </div>
        </li>

        <li><a href="#nav-toggle-5"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'germline') ? '' : 'collapsed'}">WES/WGS-germline</a>
        <li th:if="${@modules.contains('germline')}"><a href="#nav-toggle-5"
                                                        data-toggle="collapse"
                                                        th:classappend="${#strings.startsWith(menu,'germline') ? '' : 'collapsed'}">WES/WGS-germline</a>
            <div id="nav-toggle-5" th:classappend="${#strings.startsWith(menu,'germline') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'germline-introduction'}?'active'">
                        <a th:href="@{/germline}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'germline-analysis'}?'active'">
                        <a th:href="@{/analysis/germline/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>

        <li><a href="#nav-toggle-11"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'methychip') ? '' : 'collapsed'}">Methylation BeadChip</a>
        <li th:if="${@modules.contains('methychip')}"><a href="#nav-toggle-11"
                                                         data-toggle="collapse"
                                                         th:classappend="${#strings.startsWith(menu,'methychip') ? '' : 'collapsed'}">Methylation
            BeadChip</a>
            <div id="nav-toggle-11" th:classappend="${#strings.startsWith(menu,'methychip') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'methychip-introduction'}?'active'">
                        <a th:href="@{/methychip}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'methychip-analysis'}?'active'">
                        <a th:href="@{/analysis/methychip/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>

        <li><a href="#nav-toggle-12"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'wgbs') ? '' : 'collapsed'}">WGBS</a>
        <li th:if="${@modules.contains('wgbs')}"><a href="#nav-toggle-12"
                                                    data-toggle="collapse"
                                                    th:classappend="${#strings.startsWith(menu,'wgbs') ? '' : 'collapsed'}">WGBS</a>
            <div id="nav-toggle-12" th:classappend="${#strings.startsWith(menu,'wgbs') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'wgbs-introduction'}?'active'">
                        <a th:href="@{/wgbs}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'wgbs-analysis'}?'active'">
                        <a th:href="@{/analysis/wgbs/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>

        <li><a href="#nav-toggle-6"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'proteomics') ? '' : 'collapsed'}">Proteomics</a>
        <li th:if="${@modules.contains('proteomics')}"><a href="#nav-toggle-6"
                                                          data-toggle="collapse"
                                                          th:classappend="${#strings.startsWith(menu,'proteomics') ? '' : 'collapsed'}">Proteomics</a>
            <div id="nav-toggle-6" th:classappend="${#strings.startsWith(menu,'proteomics') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'proteomics-introduction'}?'active'">
                        <a th:href="@{/proteomics}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'proteomics-analysis'}?'active'">
                        <a th:href="@{/analysis/proteomics/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>
    </ul>
</nav>
</html>

