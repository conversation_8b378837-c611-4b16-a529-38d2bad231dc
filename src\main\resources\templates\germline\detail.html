<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}" th:with="task=${vo.taskDTO.task}, inputs=${vo.taskDTO.inputs}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('dnaseq-germline-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">WES/WGS-germline</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                </div>
                <th:block th:switch="${task.status}">
                    <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis Error</p>
                    </div>

                    <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Prepared</p>
                    </div>
                    <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="5">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis done</p>
                    </div>

                    <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(task.status)}">Data prepared</p>
                    </div>
                </th:block>

                <ul class="detail-info row mb-2">
                    <li class="col-xl-4 col-lg-6"><span class="text-muted sm">TaskNo：</span>
                        <div th:text="${task.taskId}">AA20200722001</div>
                    </li>
                    <li class="col-xl-4 col-lg-6"><span class="text-muted sm">StartTime：</span>
                        <div th:text="${#dates.format(task.createTime,'yyyy-MM-dd HH:mm:ss')}">2020-07-22</div>
                    </li>
                    <th:block th:if="${task.status== 5}">
                        <li class="col-xl-4 col-lg-6"><span class="text-muted sm">Consuming：</span>
                            <div th:text="${task.useTime}">42分</div>
                        </li>
                    </th:block>
                </ul>

                <div class="form-group-box">
                    <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                    <div class="collapse show" id="coll-1">
                        <div class="pl-4 pt-2">
                            <div class="result-box">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th>RunName</th>
                                            <th>R1(Read1)</th>
                                            <th>R2(Read2)</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="input : ${inputs}">
                                            <td th:text="${input.runName}">2151050411</td>
                                            <td th:text="${input.FFastq == null ? '-' : input.FFastq.path}">-</td>
                                            <td th:text="${input.RFastq == null ? '-' : input.RFastq.path}">-</td>
                                        </tr>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Quality Control</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${task.qcMethod == 'trimmomatic'}">Trimmomatic</span>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary">Homo sapiens</span></div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Version</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${task.refVersion == 'hg38'}">hg38(GRCh38)</span>
                                        <span class="text-primary" th:if="${task.refVersion == 'hg38_alt'}">hg38+HLA</span>
                                    </div>
                                </div>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${task.mappingMethod == 'bwa'}">BWA</span>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Germline Variances Calling
                                </h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${task.varianceMethod == 'gatk4'}">GATK4</span>
                                    </div>
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Filtering Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${task.filteredMethod == 'Hardfilter'}">Hard-filtering</span>
                                        <span class="text-primary" th:if="${task.filteredMethod == 'VQSR'}">VQSR</span>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Statistics</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Regions file</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${task.exonBed == 'default'}">Protein Coding Regions(CCDS)</span>
                                        <span class="text-primary" th:if="${task.exonBed == 'NONE'}">WGS</span>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Annotation</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${task.annoMethod == 'ANNOVAR'}">ANNOVAR</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group-box" th:if="${task.status == 5}">
                    <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                    <div class="collapse show" id="coll-2">
                        <div class="pl-4 pt-2">
                            <div class="result-box">
                                <div class="form-group row align-items-center">
                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">RunName</label>
                                    <div class="col-xl-3 col-lg-3 col-md-8">
                                        <select class="form-control form-control-sm" id="run-name" onchange="changeRunName()">
                                            <option th:each="name,sta : ${vo.runNames}" th:value="${name}" th:text="${name}" th:selected="${sta.first}">Choose..</option>
                                        </select>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0 mb-2">Quality Control</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th width="300">FileName</th>
                                            <th>Total_reads</th>
                                            <th>Total_bases</th>
                                            <th>Sequence_length</th>
                                            <th>GC_pct</th>
                                            <th>Q20_pct</th>
                                            <th>Q30_pct</th>
                                        </tr>
                                        </thead>
                                        <tbody id="qcSummary">
                                        </tbody>
                                    </table>
                                </div>
                                <div class="form-group row align-items-center">
                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">FileName</label>
                                    <div class="col-xl-3 col-lg-3 col-md-8">
                                        <select class="form-control form-control-sm" id="file-name" onchange="changeFileName()"></select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-md-6">
                                        <h6 class="font-weight-bold text-center text-muted">Base Quality</h6>
                                        <div style="height:300px" id="bq-img"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="font-weight-bold text-center text-muted">GC Content</h6>
                                        <div style="height:300px" id="gc-img"></div>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0 mb-2">Mapping</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th>Name</th>
                                            <th>Mapped_Reads</th>
                                            <th>Properly_Mapped_Reads</th>
                                            <th>Average_Depth</th>
                                            <th>Coverage</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="row: ${vo.mappingData.mappingSummaryVos}">
                                            <td th:text="${row.name}">test_PE.normal</td>
                                            <td th:text="${row.mappedReads}">test_PE.normal</td>
                                            <td th:text="${row.properlyMappedReads}">test_PE.normal</td>
                                            <td th:text="${row.averageDepth}">test_PE.normal</td>
                                            <td th:text="${row.coverage}">test_PE.normal</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="form-group row align-items-center">
                                    <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">Name</label>
                                    <div class="col-xl-3 col-lg-3 col-md-8">
                                        <select class="form-control form-control-sm" id="name" onchange="changeName()">
                                            <option th:each="name,sta : ${vo.mappingData.names}" th:value="${name}" th:text="${name}" th:selected="${sta.first}">Choose..</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-md-12">
                                        <h6 class="font-weight-bold text-center text-muted">Genome Fraction Coverage</h6>
                                        <div style="height:300px" id="gfc-img"></div>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0 mb-2">Germline Mutations</h6>
                                <p>
                                    <a th:href="@{/analysis/germline/downloadVcf(taskId=${task.taskId},type='indels')}"><i class="fa fa-download"></i> vcf-format file of indels</a><br>
                                    <a th:href="@{/analysis/germline/downloadAnnotatedFile(taskId=${task.taskId},type='indels')}"><i class="fa fa-download"></i> Annotated file of indels</a><br>
                                    <a th:href="@{/analysis/germline/downloadVcf(taskId=${task.taskId},type='snps')}"><i class="fa fa-download"></i> vcf-format file of snps</a><br>
                                    <a th:href="@{/analysis/germline/downloadAnnotatedFile(taskId=${task.taskId},type='snps')}"><i class="fa fa-download"></i> Annotated file of snps</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<th:block layout:fragment="custom-script">
    <script th:if="${task.status == 5}">
        $(document).ready(function () {
            changeRunName();
            changeName();
        })

        function changeRunName() {
            var runName = $("#run-name").val();
            $.ajax({
                url: '/analysis/germline/qcData',
                data: {
                    taskId: '[[${task.taskId}]]',
                    runName: runName
                },
                success: function (result) {
                    if (result.error) {
                        layer.msg(result.message);
                        return;
                    }
                    var data = result.data;
                    var qcSummaryVos = data.qcSummaryVos || [];
                    var fileNames = data.fileNames || [];
                    var html = []
                    $.each(qcSummaryVos, function (idx, item) {
                        html.push('<tr>')
                        html.push('<td>' + item.fileName + '</td>');
                        html.push('<td>' + item.totalReads + '</td>');
                        html.push('<td>' + item.totalBases + '</td>');
                        html.push('<td>' + item.sequenceLength + '</td>');
                        html.push('<td>' + item.gcPct + '</td>');
                        html.push('<td>' + item.q20Pct + '</td>');
                        html.push('<td>' + item.q30Pct + '</td>');
                        html.push('</tr>')
                    });
                    $("#qcSummary").html(html.join(''));

                    var options = []
                    $.each(fileNames, function (idx, name) {
                        if (idx === 0) {
                            options.push('<option selected value="' + name + '">' + name + '</option>')
                        } else {
                            options.push('<option value="' + name + '">' + name + '</option>')
                        }
                    })
                    $("#file-name").html(options.join());

                    changeFileName();
                }
            })
        }

        function changeFileName() {
            var filename = $("#file-name").val();
            $("#bq-img").html('<img height="300" src="[[@{/analysis/germline/baseQualityImg}]]?taskId=[[${task.taskId}]]&runName=' + filename + '">')
            $("#gc-img").html('<img height="300" src="[[@{/analysis/germline/gcContentImg}]]?taskId=[[${task.taskId}]]&runName=' + filename + '">')
        }

        function changeName() {
            var name = $("#name").val();
            $("#gfc-img").html('<img height="300" src="[[@{/analysis/germline/gfcImg}]]?taskId=[[${task.taskId}]]&name=' + name + '">')
        }
    </script>
</th:block>
</html>
