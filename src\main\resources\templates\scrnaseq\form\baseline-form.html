<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link type="text/css" rel="stylesheet" th:href="@{/select2/select2.min.css}"/>
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-advanced-baseline')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/form/baseline}" class="active">Add Task</a>
                            <a th:href="@{/analysis/scrnaseq/list(type='advanced')}">Task list</a>
                        </div>
                    </div>
                    <div id="form-content">
                        <form id="form" class="form-custom form-inline form-task mt-2" style="padding: 0 15px;">

                            <div class="form-group-box" style="border-top: transparent;">
                                <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">
                                    Input</a>
                                <div class="collapse show" id="coll-1">
                                    <div class="pl-4 pt-2">
                                        <h6 class="text-primary border-bottom pb-2">Search</h6>
                                        <div class="d-flex flex-wrap pl-4 pt-2">
                                            <input type="hidden" name="type" value="genomics">
                                            <div class="form-group row align-items-center mr-1 "
                                                 style="margin-left: -30px">
                                                <label class="mx-2 font-12">Basic Analysis ID</label>
                                                <div class="col-xl-4 col-lg-4 col-md-4 search">
                                                    <input class="form-control form-control-sm width-100 ml--16"
                                                           name="searchTaskId"
                                                           type="text">
                                                </div>
                                            </div>
                                            <div class="basic-name form-group">
                                                <label class="mx-2 font-12">Task Name</label>
                                                <div class="col-xl-4 col-lg-4 col-md-4 search">
                                                    <input class="form-control form-control-sm width-100"
                                                           name="searchTaskName" type="text"
                                                           onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                </div>

                                            </div>
                                            <div class="form-group ml">
                                                <label class="mx-2 font-12">Time</label>
                                                <div class="input-daterange input-group">
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="start"/>
                                                    <div class="input-group-append">
                                                            <span class="input-group-text"><i
                                                                    class="fa fa-calendar"></i></span>
                                                    </div>
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="end"/>
                                                    <div class="input-group-append">
                                                            <span class="input-group-text"><i
                                                                    class="fa fa-calendar"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" id="search-btn" onclick="filterTask()"
                                                    class="btn btn-primary btn-sm h-31">Search
                                            </button>
                                        </div>
                                        <div class="table-result" id="table-result" style="min-width: 890px;">
                                            <table
                                                    class="table table-bordered table-sm table-center table-middle font-12 w-100">
                                                <thead>
                                                <tr class="thead-light">
                                                    <th></th>
                                                    <th>Task ID</th>
                                                    <th>Task Name</th>
                                                    <th>Task Type</th>
                                                    <th>Speices</th>
                                                    <th>Start time</th>
                                                    <th>Status time</th>
                                                    <th>Consuming</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr class="w-100">
                                                    <td colspan="8" class="text-center">
                                                        <div class="spinner-border text-muted"></div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>

                                        </div>
                                        <h6 class="text-primary border-bottom pb-2 mt-4">Select</h6>
                                        <div class="pl-4 pt-2">
                                            <div class="form-group row align-items-center mb-0">
                                                <span>Cluster Description will be done based on Basic Analysis:<span
                                                        class="text-primary" id="genomicsIdSpan"></span></span>
                                            </div>

                                            <div class="basic-name form-group row align-items-center mb-0"
                                                 style="margin-top: 8px;">
                                                <label>Current Task Name</label>
                                                <input class="form-control input-name validate[required]"
                                                       name="taskName" type="text"
                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group-box not-bb">
                                <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                                <div class="collapse show min-width" id="coll-2">
                                    <div class="pl-4 pt-2">
                                        <div class="form-group row align-items-center mb-0">
                                            <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0 text-left">Number
                                                of
                                                principle
                                                components</label>
                                            <div class="col-xl-3 col-lg-3 col-md-3">
                                                <select class="form-control form-control-sel w-100 validate[required]"
                                                        name="selectPca">
                                                    <option value="">Choose...</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                    <option value="7">7</option>
                                                    <option value="8">8</option>
                                                    <option value="9">9</option>
                                                    <option value="10">10</option>
                                                    <option value="11">11</option>
                                                    <option value="12">12</option>
                                                    <option value="13">13</option>
                                                    <option value="14">14</option>
                                                    <option value="15">15</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group row align-items-center mb-1">
                                            <label class="col-xl-3 col-lg-3 col-md-4 col-form-label pr-0 text-left">
                                                <span style="margin: 0 0 0 -81px;">Annotation Method</span></label>
                                            <div class="col-xl-9 col-lg-9 col-md-8">
                                                <div class="custom-control custom-radio custom-control-inline">
                                                    <input type="radio" class="custom-control-input validate[required]"
                                                           id="am1"
                                                           name="annMethod" th:value="cellassign">
                                                    <label for="am1" class="custom-control-label">Cellassign</label>
                                                </div>
                                                <div class="custom-control custom-radio custom-control-inline">
                                                    <input type="radio" class="custom-control-input validate[required]"
                                                           id="am2"
                                                           name="annMethod" th:value="Elham">
                                                    <label for="am2" class="custom-control-label">Elham A, et al</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm table-center table-middle">
                                                <thead>
                                                <tr>
                                                    <td width="200">Cell type</td>
                                                    <td>Gene marker</td>
                                                    <td width="50"></td>
                                                </tr>
                                                </thead>
                                                <tbody id="cellBody">
                                                <tr>
                                                    <td class="td-input">
                                                        <select class="cellselect2" style="width: 100%"
                                                                name="params.cellType">
                                                        </select>
                                                    </td>
                                                    <td class="td-input">
                                                        <input type="text"
                                                               class="form-control text-center geneMarker w-100">
                                                    </td>
                                                    <td>
                                                        <i class="fa fa-plus-square text-primary"
                                                           onclick="addLine(this)"></i>
                                                        <i class="fa fa-minus-square text-muted ml-1"
                                                           onclick="deleteLine(this)"></i>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="text-center w-100">
                                <a href="javascript:void(0)" id="submitBtn" onclick="save()"
                                   class="btn btn-outline-primary btn-custom">
                                    <span>Submit</span>
                                    <i class="fa fa-long-arrow-right"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script th:src="@{/js/cellType.js}"></script>
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-zh_CN.js}"></script>
    <script th:src="@{/select2/select2.min.js}"></script>
    <script>


      $.fn.dataTable.ext.search.push(
        function (settings, data, dataIndex) {
          var start = parseInt($('input[name="start"]').val().replace(/-/g, "")) //获取把格式改为跟你要筛选的数据的格式一样，并转换为int类型
          var end = parseInt($('input[name="end"]').val().replace(/-/g, ""))
          var search = parseInt(data[5].replace(/-/g, "").substr(0, 8))
          if ((isNaN(start) && isNaN(end)) ||
            (isNaN(start) && search <= end) ||
            (start <= search && isNaN(end)) ||
            (start <= search && search <= end)) {
            return true
          }
          return false
        }
        )

      let table
      $(document).ready(function () {
        $('.input-daterange').datepicker({
          format: 'yyyy-mm-dd',
          toggleActive: true,
          autoclose: true,
          todayHighlight: true
        })
        obtainToFilterTable()
      })

      function filterTask () {
        table.column(1).search($('input[name="searchTaskId"]').val()).column(2).search($('input[name="searchTaskName"]').val()).draw()
      }

      function obtainToFilterTable () {
        $.ajax({
          url: `/analysis/scrnaseq/findCompleteTask/${$('input[name="type"]').val()}`,
          type: 'post',
          dataType: 'json',
          contentType: false,
          processData: false,
          success: function (result) {
            if (result.data) {
              generateResultTable(result.data)
            } else {
              layer.msg(result.message)
            }
          }
        })
      }

      // 生成表格
      function generateResultTable (data) {
        if (data === null) {
          $('#table-result').find('table tbody').html('<tr><td colspan="8"><div class="text-center">No Data</div></td></tr>')
          return
        }
        let htmlArr = []
        for (let item of data) {
          let task = item.genomicsTask
          htmlArr.push(`<tr class="fw">
                                                    <td>
                                                        <div class="custom-control custom-radio mb-1 center">
                                                            <input type="radio" id="${task.taskId}"  name="genomicId"
                                                                   value="${task.id}" class="custom-control-input validate[required]" onclick="changeGenomicsSpan(this)">
                                                            <label class="custom-control-label"
                                                                   for="${task.taskId}"><span
                                                                    class="text-primary"></span></label>
                                                        </div>
                                                    </td>
                                                    <td class="text-primary">${task.taskId}</td>
                                                    <td>${task.taskName ? task.taskName : ''}</td>
                                                    <td>Basic Analysis</td>
                                                    <td>${task.species ? task.species : ''}</td>
                                                    <td>${task.createTime ? task.createTime : ''}</td>
                                                    <td>${task.updateTime ? task.updateTime : ''}</td>
                                                    <td>${task.useTime ? task.useTime : ''}</td>
                                                </tr>`)
        }
        if (table) {
          table.destroy()
        }
        $('#table-result').find('table tbody').html(htmlArr.join(''))
        table = $('#table-result').find('table').DataTable({
          searching: true,
          ordering: false,
          dom: 'trilp',
          retrieve: true,
          lengthChange: false,
          displayLength: 5,
        })
      }

      $('.cellselect2').select2()

      function changeGenomicsSpan (_this) {
        $('#genomicsIdSpan').text($(_this).attr("id"))
      }

      function save () {
        if (!$('#form').validationEngine('validate')) {
          return
        }
        var celltypes = []
        var flag = false
        $('.cellselect2').each(function () {
          var optionValue = $(this).find('option:selected').val()
          if (optionValue != '') {
            if (celltypes.indexOf(optionValue) >= 0) {
              flag = true
            } else {
              celltypes.push(optionValue)
            }
          }
        })
        if (flag) {
          layer.msg('cell type is duplicate')
          return
        }
        // 禁用按钮防止重复提交
        $('#submitBtn').removeAttr('onclick')

        $('.cellselect2').each(function (i) {
          $(this).attr('name', 'params[' + i + '].cellType')
        })
        $('.geneMarker').each(function (i) {
          $(this).attr('name', 'params[' + i + '].geneMarker')
        })
        $.ajax({
          url: '/analysis/scrnaseq/saveBaseline',
          data: $('#form').serialize(),
          type: 'post',
          dataType: 'json',
          success: function (result) {
            if (result.success) {
              window.location.href = '[[@{/analysis/scrnaseq/list}]]?type=advanced'
            } else {
              layer.msg(result.message)
              $('#submitBtn').attr('onclick', 'save()')
            }
          }
        })
      }

      function addLine (_this) {
        $('.cellselect2').select2('destroy')
        var clone = $(_this).parents('tr:first').clone(true)
        $(_this).parents('tr:first').after(clone)
        $('.cellselect2').select2()
      }

      function deleteLine (_this) {
        if ($('#cellBody').find('tr').length > 1) {
          $(_this).parents('tr:first').remove()
        }
      }

      $('#clusters-radio input[type=radio]').click(function () {
        var id = $(this).attr('id')
        if (id == 'clusters1') {
          $('.cc-control').removeClass('d-flex').addClass('d-none')
          $('.item-cluster:first-child').nextAll().remove()
          $('#clustersOther').addClass('d-none')
        } else {
          $('.cc-control').removeClass('d-none').addClass('d-flex')
          $('#clustersOther').removeClass('d-none')
        }
      })

      $(document).ready(function () {
        //初始化加载SPECIES and version
        cellTypeUtil.initCellType()
        //有parent_id的回显
        var length = $('#detailBody').find('tr').length
        if (length > 0) {
          var taskId = $('#detailBody').find('tr').eq(0).find('td').eq(0).text()
          $('.select2').val(taskId).trigger('change')
          searchBaseLineTask()
        }
      })

      var cellTypeUtil = {
        cellTypeMap: new Map(),
        initCellType: function () {
          var options1 = []
          var inputValues = []
          for (var i = 0; i < CELL_TYPES.length; i++) {
            var celltype = CELL_TYPES[i]
            var code = celltype['code']
            var value = celltype['value']
            cellTypeUtil.cellTypeMap.set(code, celltype)
            options1.push('<option value="' + code + '">' + code + '</option>')
            inputValues.push(value)
          }
          $('#cellBody').find('.cellselect2').append(options1.join(''))
          $('#cellBody').find('.geneMarker').val(CELL_TYPES[0]['value'])

          $('#cellBody').find('.cellselect2').on('change', function () {
            var code = $(this).val()
            var cellType = cellTypeUtil.cellTypeMap.get(code)
            $(this).parents('tr:first').find('.geneMarker').val(cellType.value)
          })
        },
      }
    </script>
</th:block>
</html>
