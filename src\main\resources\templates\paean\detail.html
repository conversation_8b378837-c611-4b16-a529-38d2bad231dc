<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}" th:with="task = ${vo.paeanTask}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-paean-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Paean</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <th:block th:switch="${task.status}">
                        <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis Error</p>
                        </div>

                        <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0" th:text="${codeDescMap.get(task.status)}">Prepared</p>
                        </div>
                        <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="4">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0" th:text="${codeDescMap.get(task.status)}">Analysis done</p>
                        </div>

                        <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0" th:text="${codeDescMap.get(task.status)}">Data prepared</p>
                        </div>
                    </th:block>
                    <div class="form-group-box" th:if="${task.status == 4}">
                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                        <div class="collapse show" id="coll-2">
                            <div class="tool-content pl-4 pt-2">
                                <a th:href="@{/analysis/paean/download(taskId=${task.taskId})}" class="d-flex align-items-center"><span class="d-inline-block rounded-circle py-1 px-2 bg-primary text-light mb-0 mr-2"><i class="fa fa-download"></i></span><span class="h5 m-0 d-inline-block rounded py-2">Download result</span></a>
                            </div>
                        </div>
                    </div>
                    <div class="form-group-box">
                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                        <div class="collapse show" id="coll-1">
                            <div class="pl-4 pt-2">
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-2 col-form-label pr-0">BAM File</label>
                                    <div class="col-xl-10 col-lg-10 col-md-9">
                                        <span class="text-primary" th:text="${vo.bam.path}">Filename.bam</span>
                                    </div>
                                </div>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-2 col-form-label pr-0">mode</label>
                                    <div class="col-xl-10 col-lg-10 col-md-9">
                                        <span class="text-primary" th:text="${task.paeanMode == '2' ? 'Pair-end' : 'single-end'}">Pair-end</span>
                                    </div>
                                </div>
                                <h5 class="font-weight-bold font-16 pb-2 mb-2 border-bottom text-muted">Annotation</h5>
                                <div class="form-group row align-items-baseline m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-2 col-form-label pr-0">GFF</label>
                                    <div class="col-xl-10 col-lg-10 col-md-9">
                                        <span th:if="${vo.gff != null}" class="text-primary" th:text="${vo.gff.name}">Filename.gff</span>
                                    </div>
                                </div>
                                <div class="form-group row align-items-baseline m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-2 col-form-label pr-0">SE</label>
                                    <div class="col-xl-10 col-lg-10 col-md-9">
                                        <span th:if="${vo.se != null}" class="text-primary" th:text="${vo.se.name}">Filename.gff</span>
                                    </div>
                                </div>
                                <div class="form-group row align-items-baseline m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-2 col-form-label pr-0">A3SS</label>
                                    <div class="col-xl-10 col-lg-10 col-md-9">
                                        <span th:if="${vo.a3ss != null}" class="text-primary" th:text="${vo.a3ss.name}">Filename.gff</span>
                                    </div>
                                </div>
                                <div class="form-group row align-items-baseline m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-2 col-form-label pr-0">A5SS</label>
                                    <div class="col-xl-10 col-lg-10 col-md-9">
                                        <span th:if="${vo.a5ss != null}" class="text-primary" th:text="${vo.a5ss.name}">Filename.gff</span>
                                    </div>
                                </div>
                                <div class="form-group row align-items-baseline m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-2 col-form-label pr-0">RI</label>
                                    <div class="col-xl-10 col-lg-10 col-md-9">
                                        <span th:if="${vo.ri != null}" class="text-primary" th:text="${vo.ri.name}">Filename.gff</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
</th:block>
</html>
