package cn.ac.picb.vipmap.vo;

import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ScrnaSmartseqTaskParam {

    @NotNull
    private List<ScrnaSmartseqTaskInput> inputs;

    // qc

    @NotBlank
    private String qcMethod;

    // mapping

    @NotBlank
    private String species;

    @NotBlank
    private String specVersion;

    @NotBlank
    private String mappingMethod;

    // counting

    @NotBlank
    private String countingMethod;


}
