package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.germline.dto.GermlineTaskDTO;
import cn.ac.picb.germline.enums.GermlineTaskStatus;
import cn.ac.picb.germline.po.GermlineTaskPO;
import cn.ac.picb.germline.vo.GermlineTaskQcDataVO;
import cn.ac.picb.germline.vo.GermlineValidateResultVO;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.GermlineService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.GermlineTaskParam;
import cn.ac.picb.vipmap.vo.GermlineTaskSearchVO;
import cn.ac.picb.vipmap.vo.GermlineTaskVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/analysis/germline")
@RequiredArgsConstructor
public class GermlineController {

    private final AppProperties appProperties;
    private final GermlineService germlineService;

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") GermlineTaskSearchVO queryVO, @ModelAttribute PageParam pageParam, Model model) {
        PageResult<GermlineTaskDTO> pageResult = germlineService.findPage(user, queryVO, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = GermlineTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "germline/list";
    }

    @RequestMapping("/form")
    public String form() {
        return "germline/form";
    }

    @RequestMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemplate() {
        Response response = germlineService.downloadTemplate();
        ResponseUtil.download(response);
    }

    @RequestMapping("/uploadTemplate")
    @ResponseBody
    public CommonResult<List<GermlineValidateResultVO>> uploadTemplate(CurrentUser user, MultipartFile file) {
        List<GermlineValidateResultVO> vos = germlineService.uploadTemplate(file, user);
        return success(vos);
    }

    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated GermlineTaskParam param) {
        GermlineTaskPO task = germlineService.createTask(user, param);
        return success(task.getTaskId());
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        germlineService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        GermlineTaskVO vo = germlineService.findTaskVO(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = GermlineTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "germline/detail";
    }

    @RequestMapping("qcData")
    @ResponseBody
    public CommonResult<GermlineTaskQcDataVO> findQcData(String taskId, String runName) {
        GermlineTaskQcDataVO vo = germlineService.findQcData(taskId, runName);
        return success(vo);
    }

    @RequestMapping("/baseQualityImg")
    public ResponseEntity<byte[]> getBaseQualityImg(String taskId, String runName) {
        return germlineService.getBaseQualityImg(taskId, runName);
    }

    @RequestMapping("/gcContentImg")
    @SneakyThrows
    public ResponseEntity<byte[]> getGcContentImg(String taskId, String runName) {
        return germlineService.getGcContentImg(taskId, runName);
    }

    @RequestMapping("/gfcImg")
    @SneakyThrows
    public ResponseEntity<byte[]> getGfcImg(String taskId, String name) {
        return germlineService.getGenomeFractionCoverageImg(taskId, name);
    }

    @RequestMapping("/downloadVcf")
    @ResponseBody
    @SneakyThrows
    public void downloadVcf(String taskId, String type) {
        Response response = germlineService.downloadVcf(taskId, type);
        ResponseUtil.download(response);
    }

    @RequestMapping("/downloadAnnotatedFile")
    @ResponseBody
    @SneakyThrows
    public void downloadAnnotatedFile(String taskId, String type) {
        Response response = germlineService.downloadAnnotatedFile(taskId, type);
        ResponseUtil.download(response);
    }
}
