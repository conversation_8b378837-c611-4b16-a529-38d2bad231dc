// <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
// <script th:src="@{/js/jquery-ui.min.js}"></script>
// <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
// <script th:src="@{/js/jquery.fancytree.table.js}"></script>
// <script th:src="@{/js/file-modal.js}"></script>
//
//  div
//  <div id="tree"></div>
//  <button onclick="showModal()">文件选择框</button>
//
// js
// $("#tree").fileModal('/analysis/scrnaseq/fileData');
// function showModal() {
//     $("#tree").trigger('__SHOW_SELECT_FILES_MODAL__', []);
// }
//
// $("#tree").on('__SELECT_FILES__', function (e, ...nodes) {
//     console.info(nodes)
// })
(function ($) {
  'use strict'

  function FileModal (element, url) {
    this.$element = $(element)
    this.$container = this.initModal()
    this.build(url)
    this.$element.html(this.$container)
  }

  FileModal.prototype = {
    constructor: FileModal,
    initModal: function () {
      var html = []
      html.push('<div class="modal fade sel-modal" id="__file_select_modal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">')
      html.push('<div class="modal-dialog modal-lg modal-dialog-centered">')
      html.push('<div class="modal-content">')
      html.push('<div class="modal-header"><h5 class="modal-title font-14 mt-0">Select file</h5><button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button></div>')
      html.push('<div class="modal-body" style="max-height:450px;overflow:auto;">')
      html.push('<div class="table-responsive">')
      html.push('<table id="__tree_table" class="table table-striped mb-0 treetable font-12">')
      html.push('<colgroup><col width="*"><col width="70px"><col width="150px"></colgroup>')
      html.push('<thead class="thead-light"><tr><th>File Name</th><th>Size</th><th>Upload Time</th></tr></thead>')
      html.push('<tbody><tr><td></td><td></td><td class="alignRight"></td></tr></tbody>')
      html.push('</table>')
      html.push('</div>')
      html.push('</div>')
      html.push('<div class="modal-footer">')
      html.push('<button type="button" class="btn btn-secondary btn-sm font-12" data-dismiss="modal">Close</button>')
      html.push('<button type="button" class="btn btn-primary btn-sm font-12" id="__chose_btn">Accept</button>')
      html.push('</div></div></div></div>')
      return html.join('')
    },
    build: function (url) {
      var self = this
      self.$element.on('__SHOW_SELECT_FILES_MODAL__', function (e, data) {
        var selectedIds = data.selectIds || []
        var func = data.func || function () {
          return true
        }
        var allowFolder = data.allowFolder || function (data) {
          return !data.node.isFolder()
        }
        $('#__tree_table').fancytree({
          extensions: ['table'],
          selectMode: 3,
          table: {
            indentation: 20,
          },
          checkbox: function (event, data) {
            return allowFolder(data)
          },
          unselectable: function (event, data) {
            if (func) {
              return !func(data.node.data)
            }
            return false
          },
          source: {
            url: url,
            xhrFields: { withCredentials: true },
            data: { parentPath: '' }
          },
          lazyLoad: function (event, data) {
            var node = data.node
            data.result = {
              url: url,
              xhrFields: { withCredentials: true },
              data: { parentPath: node.data.path }
            }
          },
          renderColumns: function (event, data) {
            var node = data.node,
                $tdList = $(node.tr).find('>td')
            $tdList.eq(1).text(node.data.size)
            $tdList.eq(2).text(node.data.time)
          },
          postProcess: function (event, data) {
            if (selectedIds.length > 0) {
              $.each(data.response, function (idx, node) {
                node.selected = selectedIds.indexOf(node.data.path) !== -1
              })
            }
          }
        })
        $('#__file_select_modal').modal('show')
      })

      self.$element.on('click', '#__chose_btn', function () {
        var tree = $.ui.fancytree.getTree('#__tree_table')
        var selectedNodes = tree.getSelectedNodes(true)
        var nodes = $.map(selectedNodes, function (node) {
          return node.data
        })
        self.$element.trigger('__SELECT_FILES__', {
          nodes: nodes
        })
        $('#__file_select_modal').modal('hide')
        tree.destroy()
      })
    }
  }

  // 创建实例对象
  $.fn.fileModal = function (url) {
    var fileModal = $(this).data('fileModal')
    // Initialize a new modal
    if (!fileModal) {
      fileModal = new FileModal(this, url)
      $(this).data('fileModal', fileModal)
    }
  }
})(window.jQuery)

