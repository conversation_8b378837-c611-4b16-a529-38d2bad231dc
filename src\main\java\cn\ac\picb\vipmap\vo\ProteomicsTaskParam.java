package cn.ac.picb.vipmap.vo;

import cn.ac.picb.proteomics.vo.ProteomicsTaskInput;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProteomicsTaskParam {

    @NotNull
    private List<ProteomicsTaskInput> inputs;

    private String refDb;

    private Boolean includeCont;

    private Boolean ibaq;

    private Boolean logFit;

    private Integer ftms;

    private Boolean ftmsUnit;

    private Double itms;

    private Boolean itmsUnit;

    private Integer tof;

    private Boolean tofUnit;

    /**
     * Unknown MS/MS match tolerance
     */
    private Integer unknown;

    /**
     * Unknown MS/MS match tolerance / Unit
     */
    private Boolean unknownUnit;

    /**
     * PSM FDR
     */
    private Double psfFdr;

    /**
     * Protein FDR
     */
    private Double proteinFdr;

    /**
     * Min. peptides
     */
    private Double minPeptides;

    /**
     * Min. raror + unique peptides
     */
    private Double minRaror;

    /**
     * Min. unique peptides
     */
    private Double minUnique;

    // Group parameter

    private String groupType;

    // Standard 参数

    private Integer multiplicity;

    private Integer maxLabeledAa;
    /**
     * label ","分隔
     */
    private List<String> lightLabels;

    /**
     * label ","分隔
     */
    private List<String> mediumLabels;

    /**
     * label ","分隔
     */
    private List<String> heavyLabels;


    // Report ion MS2 参数

    private String isobaricLabels;

    // TIMS-DDA 参数

    private Double timsHalfWidth;
    private Double timsStep;
    private Double timsResolution;
    private Double timsMin;
    private Boolean timsRem;
    private Boolean timsColl;
    private Boolean reporterIons;

    /**
     * Variable modifications
     */
    private List<String> variableMods;

    /**
     * Fixed modifications
     */
    private List<String> fixMods;

    /**
     * Instrument type
     */
    private String instrumentType;

    /**
     * Digestion mode
     */
    private String digestionMode;

    /**
     * Max missed
     */
    private Double maxMissed;

    /**
     * Enzyme
     */
    private List<String> enzymes;
}
