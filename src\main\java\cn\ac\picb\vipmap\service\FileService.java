package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.file.vo.PathParamVO;
import cn.ac.picb.file.vo.TreeNode;
import cn.ac.picb.vipmap.client.FileServiceClient;
import cn.ac.picb.vipmap.vo.CurrentUser;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class FileService {

    private final FileServiceClient fileServiceClient;

    public List<TreeNode> findFileTreeNodes(CurrentUser user, String parentPath) {
        PathParamVO vo = new PathParamVO();
        vo.setPath(parentPath);
        vo.setUsername(user.getUsername());
        CommonResult<List<TreeNode>> result = fileServiceClient.fileTreeNodes(vo);
        result.checkError();
        return result.getData();
    }
}
