package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.rnaseq.dto.RnaseqTaskDTO;
import cn.ac.picb.rnaseq.po.RnaseqTaskPO;
import cn.ac.picb.rnaseq.vo.*;
import cn.ac.picb.vipmap.client.RnaseqServiceClient;
import feign.Response;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class RnaseqServiceClientFallback implements RnaseqServiceClient {

    private final static String SERVER_NAME = "rnaseq-service";


    @Override
    public CommonResult<PageResult<RnaseqTaskPO>> findTaskPage(RnaseqTaskQueryVO rnaseqTaskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<RnaseqTaskPO> saveTask(RnaseqTaskParamVO rnaseqTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<RnaseqTaskPO> findById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<RnaseqTaskDTO> findDetailById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<RnaseqTaskPO> deleteById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadResult(String taskId, Integer step, String displayName) {
        return null;
    }

    @Override
    public CommonResult<Object> chartData(ChartParamVO chartParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadTemplateExcel() {
        return null;
    }

    @Override
    public CommonResult<List<RnaseqTaskInput>> uploadDataExcel(String s, MultipartFile multipartFile) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<SyncToNodeFileVo>> getToNodeList(String taskId, String username) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Boolean> syncToNode(RnaseqSyncToNodeVO dto) {
        return serverError(SERVER_NAME);

    }
}
