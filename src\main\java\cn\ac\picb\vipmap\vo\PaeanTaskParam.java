package cn.ac.picb.vipmap.vo;

import cn.ac.picb.paean.vo.PaeanFileVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PaeanTaskParam {

    @NotNull
    private List<PaeanFileVO> bamList;
    @NotBlank
    private String paeanMode;

    private PaeanFileVO gff;
    private PaeanFileVO se;
    private PaeanFileVO a3ss;
    private PaeanFileVO a5ss;
}
