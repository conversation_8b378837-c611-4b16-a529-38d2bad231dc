package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnaseq.dto.BaselineTaskDTO;
import cn.ac.picb.scrnaseq.po.BaselineTaskPO;
import cn.ac.picb.scrnaseq.vo.BaselineTaskParamVO;
import cn.ac.picb.scrnaseq.vo.TaskQueryVO;
import cn.ac.picb.vipmap.client.ScrnaseqBaselineServiceClient;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaseqBaselineServiceClientFallback implements ScrnaseqBaselineServiceClient {

    private final static String SERVER_NAME = "scrnaseq-service";

    @Override
    public CommonResult<PageResult<BaselineTaskDTO>> findBaselineTaskPage(TaskQueryVO taskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<BaselineTaskDTO>> findcompleteBaselineTask(String userId) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<BaselineTaskPO> saveBaselineTask(BaselineTaskParamVO baselineTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<BaselineTaskPO> findBaselineById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<BaselineTaskDTO> findBaselineDtoById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<BaselineTaskPO> deleteBaselineById(String s) {
        return serverError(SERVER_NAME);
    }
}
