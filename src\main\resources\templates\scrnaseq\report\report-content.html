<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<th:block th:with="
    reportTask=${vo.reportTask},
    genomicsTaskDTO=${vo.genomicsTaskDTO},
    genomicsTask=${vo.genomicsTaskDTO.genomicsTask},
    baselineTask=${vo.baselineTask},
    pagaTask=${vo.pagaTask},
    degTask=${vo.degTask},
    genesTask=${vo.genesTask},
    wgcnaTask=${vo.wgcnaTask}">

    <div class="form-group-box">
        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary mb-2">Parameters</a>
        <div class="collapse show" id="coll-1">
            <hr>
            <th:block th:if="${genomicsTask != null}">
                <a href="#coll-1-01" data-toggle="collapse" class="h6 text-primary ml-2 d-block">10X genomics</a>
                <div class="collapse show" id="coll-1-01">
                    <div class="pl-4 pt-2">
                        <div class="result-box">
                            <h5 class="mb-3 font-16"><span>Expression matrix</span></h5>
                            <th:block th:if="${genomicsTask.dataMode == 'fastq'}">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th>Sample ID</th>
                                            <th>I1</th>
                                            <th>R1</th>
                                            <th>R2</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="svo : ${genomicsTaskDTO.matrix}">
                                            <td th:text="${svo.sampleId}">HG00114</td>
                                            <td>
                                                <th:block th:each="file, sta : ${svo.row.i1}">
                                                    <span class="font-12" th:text="${file.path}">Sample1_S1_L001_R1_001.fastq.gz</span><br
                                                        th:unless="${sta.last}">
                                                </th:block>
                                            </td>
                                            <td>
                                                <th:block th:each="file, sta : ${svo.row.r1}">
                                                    <span class="font-12" th:text="${file.path}">Sample1_S1_L001_R1_001.fastq.gz</span><br
                                                        th:unless="${sta.last}">
                                                </th:block>
                                            </td>
                                            <td>
                                                <th:block th:each="file, sta : ${svo.row.r2}">
                                                    <span class="font-12" th:text="${file.path}">Sample1_S1_L001_R1_001.fastq.gz</span><br
                                                        th:unless="${sta.last}">
                                                </th:block>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary"
                                              th:text="${genomicsTask.species}">Homo sapiens（human）</span>
                                    </div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Version</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${genomicsTask.version}">GRCH38（hg38）</span>
                                    </div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${genomicsTask.mapMethod}">STAR</span>
                                    </div>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Counting</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary"
                                              th:text="${genomicsTask.countMethod}">cellranger</span>
                                    </div>
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Option</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:text="${genomicsTask.rangerMode}">count</span>
                                    </div>
                                </div>
                            </th:block>

                            <th:block th:if="${genomicsTask.dataMode == 'matrix'}">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th>Sample ID</th>
                                            <td>barcodes.tsv.gz</td>
                                            <td>features.tsv.gz</td>
                                            <td>matrix.mtx.gz</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="svo : ${genomicsTaskDTO.matrix}">
                                            <td th:text="${svo.sampleId}">HG00114</td>
                                            <td>
                                                <span class="font-12" th:text="${svo.row.barcode.path}">Sample1_S1_L001_R1_001.fastq.gz</span>
                                            </td>
                                            <td>
                                                <span class="font-12" th:text="${svo.row.barcode.path}">Sample1_S1_L001_R1_001.fastq.gz</span>
                                            </td>
                                            <td>
                                                <span class="font-12" th:text="${svo.row.barcode.path}">Sample1_S1_L001_R1_001.fastq.gz</span>

                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary"
                                              th:text="${genomicsTask.species}">Homo sapiens（human）</span>
                                    </div>
                                </div>
                            </th:block>

                            <th:block th:if="${genomicsTask.dataMode == 'csv'}">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-center table-middle">
                                        <thead>
                                        <tr class="thead-light">
                                            <th>Sample ID</th>
                                            <th>csv file</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="svo : ${genomicsTaskDTO.matrix}">
                                            <td th:text="${svo.sampleId}">HG00114</td>
                                            <td>
                                                <span class="font-12" th:text="${svo.row.csv.path}">Sample1_S1_L001_R1_001.fastq.gz</span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <h6 class="border-bottom pb-2 m-0">Mapping</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary"
                                              th:text="${genomicsTask.species}">Homo sapiens（human）</span>
                                    </div>
                                </div>
                            </th:block>

                            <h5 class="mb-1 font-16"><span>Post processing</span></h5>
                            <div class="form-group row align-items-center m-0">
                                <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                <div class="col-xl-3 col-lg-3 col-md-3">
                                    <span class="text-primary">Seurat</span>
                                </div>
                            </div>
                            <h6 class="border-bottom pb-2 m-0">Cell filter</h6>
                            <div class="form-group row align-items-center m-0">
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">number of nGene</label>
                                <div class="col-xl-2 col-lg-2col-md-4">
                                    <span class="text-primary">[[${genomicsTask.nGeneStart}]] - [[${genomicsTask.nGeneEnd}]]</span>
                                </div>
                                <label class="col-xl-3 col-lg-2 col-md-3 col-form-label pr-0">percentage of
                                    mitochondrial genes</label>
                                <div class="col-xl-2 col-lg-2col-md-4">
                                    <span class="text-primary">[[${genomicsTask.toPerStart}]] - [[${genomicsTask.toPerEnd}]]</span>
                                </div>
                            </div>
                            <h6 class="border-bottom pb-2 m-0">Clustering</h6>
                            <div class="form-group row align-items-center m-0">
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">resolution
                                    parameter</label>
                                <div class="col-xl-2 col-lg-2col-md-4">
                                    <span class="text-primary" th:text="${genomicsTask.resolution}">0.8</span>
                                </div>
                            </div>
                            <div class="form-group row align-items-baseline m-0"
                                 th:unless="${#strings.isEmpty(genomicsTask.TGene)}">
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">t-SNE genes</label>
                                <div class="col-xl-2 col-lg-2col-md-4">
                                    <th:block th:each="gene, sta : ${#strings.listSplit(genomicsTask.TGene, ';')}">
                                        <span class="text-primary" th:text="${gene}">MS4A1</span><br
                                            th:unless="${sta.last}">
                                    </th:block>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </th:block>
            <th:block th:if="${baselineTask != null}">
                <a href="#coll-1-02" data-toggle="collapse" class="h6 text-primary ml-2 d-block">Baseline</a>
                <div class="collapse show" id="coll-1-02">
                    <div class="pl-4 pt-2">
                        <div class="result-box">
                            <div class="table-responsive mt-2">
                                <table class="table table-bordered table-sm table-center table-middle mb-1">
                                    <thead>
                                    <tr class="thead-light">
                                        <th>Cluster Genomics ID</th>
                                        <th>Start time</th>
                                        <th>Status time</th>
                                        <th>Consuming</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td th:text="${genomicsTask.taskId}"></td>
                                        <td th:text="${#dates.format(genomicsTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${#dates.format(genomicsTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${genomicsTask.useTime}">26m55s</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="form-group row align-items-center m-0">
                                <label class="col-xl-3 col-lg-2 col-md-3 col-form-label pr-0">Number of principle
                                    components</label>
                                <div class="col-xl-2 col-lg-2 col-md-4">
                                    <span class="text-primary" th:text="${baselineTask.selectPca}">10</span>
                                </div>
                            </div>
                            <div class="form-group row align-items-center m-0">
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Annotation Method</label>
                                <div class="col-xl-2 col-lg-2 col-md-4">
                                    <span class="text-primary" th:text="${baselineTask.annMethod}">Elham A, et al</span>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm table-center table-middle">
                                    <thead>
                                    <tr class="thead-light">
                                        <th>Cell type</th>
                                        <th>Gene marker</th>
                                    </tr>
                                    </thead>
                                    <tbody th:with="types=${#strings.listSplit(baselineTask.cellType,';')}, markers=${#strings.listSplit(baselineTask.geneMarker,';')}">
                                    <tr th:each="idx: ${#numbers.sequence(0, types.size()-1)}">
                                        <td th:text="${types.get(idx)}">B Cells</td>
                                        <td>
                                            <span class="font-12" th:text="${markers.get(idx)}">CD19</span>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </th:block>
            <th:block th:if="${pagaTask !=null}">
                <a href="#coll-1-03" data-toggle="collapse" class="h6 text-primary ml-2 d-block">PAGA & Pseudotime
                    trajectory </a>
                <div class="collapse show" id="coll-1-03">
                    <div class="pl-4 pt-2">
                        <div class="result-box">
                            <div class="table-responsive mt-2">
                                <table class="table table-bordered table-sm table-center table-middle mb-1">
                                    <thead>
                                    <tr class="thead-light">
                                        <th>Cluster Baseline ID</th>
                                        <th>Start time</th>
                                        <th>Status time</th>
                                        <th>Consuming</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td th:text="${baselineTask.taskId}"></td>
                                        <td th:text="${#dates.format(baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${#dates.format(baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${baselineTask.useTime}">26m55s</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="form-group row align-items-center m-0">
                                <label class="col-xl-1 col-lg-1 col-md-3 col-form-label pr-0">neighbor</label>
                                <div class="col-xl-3 col-lg-3 col-md-4">
                                    <span class="text-primary" th:text="${pagaTask.neighbors}">value</span>
                                </div>
                                <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">threshold</label>
                                <div class="col-xl-3 col-lg-3 col-md-4">
                                    <span class="text-primary" th:text="${pagaTask.threshold}">value</span>
                                </div>
                            </div>
                            <div class="form-group row align-items-baseline m-0">
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Compare cluster</label>
                                <div class="col-xl-4 col-lg-4 col-md-4"
                                     th:with="clus=${#strings.listSplit(pagaTask.MCluster, ';')}">
                                    <th:block th:each="c, sta : ${clus}">
                                        <span class="text-primary" th:text="${c}">Cluster_1</span><br
                                            th:unless="${sta.last}">
                                    </th:block>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </th:block>
            <th:block th:if="${degTask !=null}">
                <a href="#coll-1-04" data-toggle="collapse" class="h6 text-primary ml-2 d-block">DEG & GSVA</a>
                <div class="collapse show" id="coll-1-04">
                    <div class="pl-4 pt-2">
                        <div class="result-box">
                            <div class="table-responsive mt-2">
                                <table class="table table-bordered table-sm table-center table-middle mb-1">
                                    <thead>
                                    <tr class="thead-light">
                                        <th>Cluster Baseline ID</th>
                                        <th>Start time</th>
                                        <th>Status time</th>
                                        <th>Consuming</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td th:text="${baselineTask.taskId}"></td>
                                        <td th:text="${#dates.format(baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${#dates.format(baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${baselineTask.useTime}">26m55s</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="form-group row align-items-baseline m-0" th:if="${degTask.degMode == 'single'}">
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Compare cluster</label>
                                <div class="col-xl-2 col-lg-2 col-md-4">
                                    <span class="text-primary" th:text="${degTask.cluster1}">Cluster_1</span>
                                </div>
                            </div>
                            <div class="form-group row align-items-baseline m-0" th:if="${degTask.degMode == 'multi'}">
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Compare cluster</label>
                                <div class="col-xl-2 col-lg-2 col-md-4"
                                     th:with="cluster1s=${#strings.listSplit(degTask.cluster1, ';')}">
                                    <th:block th:each="cluster1, sta : ${cluster1s}">
                                        <span class="text-primary" th:text="${cluster1}">Cluster_1</span><br
                                            th:unless="${sta.last}">
                                    </th:block>
                                </div>
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">with cluster</label>
                                <div class="col-xl-2 col-lg-2 col-md-4"
                                     th:with="cluster2s=${#strings.listSplit(degTask.cluster2, ';')}">
                                    <th:block th:each="cluster2, sta : ${cluster2s}">
                                        <span class="text-primary" th:text="${cluster2}">Cluster_1</span><br
                                            th:unless="${sta.last}">
                                    </th:block>
                                </div>
                            </div>

                            <div class="form-group row align-items-center m-0">
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Fold change(log2)</label>
                                <div class="col-xl-2 col-lg-2 col-md-4">
                                    <span class="text-primary" th:text="${degTask.cutfc}">0.05</span>
                                </div>
                                <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">FDR</label>
                                <div class="col-xl-2 col-lg-2 col-md-4">
                                    <span class="text-primary" th:text="${degTask.cutp}">0.05</span>
                                </div>
                            </div>
                            <div class="form-group row align-items-center m-0">
                                <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Database</label>
                                <div class="col-xl-2 col-lg-2 col-md-4">
                                    <span class="text-primary" th:text="${degTask.annoDb}">REACTOME</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </th:block>
            <th:block th:if="${genesTask !=null}">
                <a href="#coll-1-05" data-toggle="collapse" class="h6 text-primary ml-2 d-block">Genes</a>
                <div class="collapse show" id="coll-1-05">
                    <div class="pl-4 pt-2">
                        <div class="result-box">
                            <div class="table-responsive mt-2">
                                <table class="table table-bordered table-sm table-center table-middle mb-1">
                                    <thead>
                                    <tr class="thead-light">
                                        <th>Cluster Baseline ID</th>
                                        <th>Start time</th>
                                        <th>Status time</th>
                                        <th>Consuming</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td th:text="${baselineTask.taskId}"></td>
                                        <td th:text="${#dates.format(baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${#dates.format(baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${baselineTask.useTime}">26m55s</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="form-group row align-items-center m-0">
                                <label class="col-xl-2 col-lg-2 col-md-2 col-form-label pr-0">Input your genes</label>
                                <div class="col-xl-10 col-lg-10 col-md-10">
                                    <span class="badge badge-primary"
                                          th:each="gene:${#strings.listSplit(genesTask.genes, ';')}" th:text="${gene}">CCL17</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </th:block>
            <th:block th:if="${wgcnaTask !=null}">
                <a href="#coll-1-06" data-toggle="collapse" class="h5 text-primary">WGCNA</a>
                <div class="collapse show" id="coll-1-06">
                    <div class="pl-4 pt-2">
                        <div class="result-box">
                            <div class="table-responsive mt-2">
                                <table class="table table-bordered table-sm table-center table-middle mb-1">
                                    <thead>
                                    <tr class="thead-light">
                                        <th>Cluster baseline ID</th>
                                        <th>Start time</th>
                                        <th>Status time</th>
                                        <th>Consuming</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td th:text="${baselineTask.taskId}"></td>
                                        <td th:text="${#dates.format(baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${#dates.format(baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                        <td th:text="${baselineTask.useTime}">26m55s</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>


                            <h6 class="border-bottom pb-2 m-2">Gene ontology</h6>
                            <div class="form-group row align-items-center m-0">
                                <label
                                        class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Species</label>
                                <div class="col-xl-3 col-lg-3 col-md-3">
                                    <span class="text-primary" th:if="${wgcnaTask.species} == 'human'">Homo sapiens (human)</span>
                                    <span class="text-primary" th:if="${wgcnaTask.species} == 'mouse'">Mus musculus（mouse）</span>
                                </div>
                                <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method</label>
                                <div class="col-xl-3 col-lg-3 col-md-3"><span class="text-primary">Fisher’s
                                                    exact test </span></div>

                            </div>

                            <h6 class="border-bottom pb-2 m-2" style="margin-top: 30px;">Differential
                                Analysis</h6>
                            <div class="form-group row align-items-center m-0">
                                <div class="col-lg-3">
                                    <label>Power</label>
                                    <span class="text-primary" style="margin-left: 20px;"
                                          th:text="${wgcnaTask.power}">15</span>
                                </div>
                                <div class="col-lg-3">
                                    <label>netWorkType</label>
                                    <span class="text-primary" style="margin-left: 20px;"
                                          th:text="${wgcnaTask.networkType}">signed</span>
                                </div>
                                <div class="col-lg-3">
                                    <label class="col-form-label pr-0">minModuleSize</label>
                                    <span class="text-primary" style="margin-left: 20px;"
                                          th:text="${wgcnaTask.minModuleSize}">10</span>
                                </div>
                                <div class="col-lg-3">
                                    <label>mergeCutHeight</label>
                                    <span class="text-primary" style="margin-left: 20px;"
                                          th:text="${wgcnaTask.mergeCutHeight}">0.2</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </th:block>
        </div>
    </div>
    <div class="form-group-box">
        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
        <div class="collapse show tabs-ana" id="coll-2">
            <ul class="nav nav-pills" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" data-toggle="tab" href="#tabs-a01" role="tab" aria-selected="true">
                        Cluster
                    </a>
                </li>
                <li class="nav-item" th:if="${#strings.listSplit(genomicsTask.sampleIds,',').size() > 1 }">
                    <a class="nav-link" data-toggle="tab" href="#tabs-a02" role="tab" aria-selected="false">
                        Sample
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#tabs-a03" role="tab" aria-selected="false">
                        Gene
                    </a>
                </li>
                <li class="nav-item" th:if="${baselineTask != null}">
                    <a class="nav-link" data-toggle="tab" href="#tabs-a04" role="tab" aria-selected="false">
                        Cell type
                    </a>
                </li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane active" id="tabs-a01" role="tabpanel">
                    <div class="p-2 text-center">
                        <div id="chart-01" style="width: 800px;height: 800px" th:if="${baselineTask == null}"></div>
                        <div id="chart-062" style="width: 800px;height: 800px" th:if="${baselineTask != null}"></div>
                    </div>
                </div>
                <div class="tab-pane" id="tabs-a02" role="tabpanel"
                     th:if="${#strings.listSplit(genomicsTask.sampleIds,',').size() > 1 }">
                    <div class="p-2 text-center">
                        <div id="chart-02" style="width: 800px;height: 800px" th:if="${baselineTask == null}"></div>
                        <div id="chart-05" style="width: 800px;height: 800px" th:if="${baselineTask != null}"></div>
                    </div>
                </div>
                <div class="tab-pane" id="tabs-a03" role="tabpanel">
                    <form action="" class="form-inline">
                        <div class="form-group">
                            <select id="gene-select" class="form-control form-control-sm"></select>
                        </div>
                    </form>
                    <div class="p-2 text-center">
                        <div id="chart-04" style="width: 800px;height: 800px"></div>
                    </div>
                </div>
                <div class="tab-pane" id="tabs-a04" role="tabpanel" th:if="${baselineTask != null}">
                    <div class="p-2 text-center">
                        <div id="chart-061" style="width: 800px;height: 800px"></div>
                    </div>
                </div>
            </div>
            <th:block th:if="${pagaTask !=null}">
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        <div class="p-2 text-center">
                            <h6 class="font-weight-bold text-center mb-2">PAGA</h6>
                            <div id="chart-08" style="width: 100%;height: 500px"></div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="p-2 text-center">
                            <h6 class="font-weight-bold text-center mb-2">Pseudotime trajectory</h6>
                            <div id="chart-09" class="row" style="width: 100%;height: 500px"></div>
                        </div>
                    </div>
                </div>
            </th:block>
            <th:block th:if="${degTask !=null}">
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        <div class="p-2 text-center">
                            <h6 class="font-weight-bold text-center mb-2">Volcano Plot</h6>
                            <div id="chart-10" style="width: 100%;height: 500px"></div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="p-2 text-center">
                            <h6 class="font-weight-bold text-center mb-2">Gene Set Enrichment</h6>
                            <div id="chart-11" style="width: 100%;height: 500px"></div>
                        </div>
                    </div>
                </div>
            </th:block>
            <th:block th:if="${wgcnaTask !=null}">
                <hr>
                <div class="row">
                    <div class="col-md-12">
                        <div class="p-2 text-center">
                            <div class="col-lg-12">
                                <div id="chart-14" style="width: 100%;height: 400px"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="p-2 text-center">
                            <div class="col-lg-12">
                                <div id="chart-17" style="width: 100%;height: 500px"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="p-2 text-center">
                            <div id="chart-15" style="width: 800px;height: 600px"></div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="p-2 text-center">
                            <div id="chart-16-box"></div>
                            <div id="chart-16" style="width: 800px;height: 600px"></div>
                        </div>
                    </div>
                </div>
            </th:block>
            <div class="p-2 text-right">
                <th:block th:if="${reportTask == null}">
                    <a href="javascript:;" onclick="addReport(this)" class="btn btn-primary">Report results</a>
                </th:block>
                <th:block th:if="${reportTask != null}">
                    <a th:if="${reportTask.status == 2}"
                       th:href="@{/analhysis/scrnaseq/report/download(taskId=${reportTask.taskId})}"
                       class="btn btn-primary">download results</a>
                    <a th:if="${reportTask.status == 1}"
                       th:href="@{/analysis/scrnaseq/report/taskDetail(id=${reportTask.id})}" class="btn btn-primary">view
                        task</a>
                </th:block>
            </div>
        </div>
    </div>

    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>

    <!--  cluster 标签  -->
    <script th:if="${baselineTask == null}">
      $(document).ready(function () {
        initChart01()
      })

      function initChart01 () {
        if (!document.getElementById('chart-01')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-01'))

        drawChart(myChart)

        function drawChart () {
          myChart.clear()
          $.ajax({
            url: '/analysis/scrnaseq/genomics/[[${genomicsTask.id}]]/1',
            beforeSend: function () {
              $("#chart-01").next().remove()
              $("#chart-01").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.code === 601) {
                $("#chart-01").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-01").hide()
                return
              }

              var data = result.data
              var legendData = []
              var series = []

              for (var group in data) {
                legendData.push(group)
                series.push({
                  name: group,
                  data: data[group],
                  type: 'scatter',
                  symbolSize: 5,
                  cursor: 'default',
                  itemStyle: {
                    color: getClusterColor(group)
                  }
                })
              }

              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                legend: {
                  data: legendData
                },
                xAxis: {
                  name: 'tSNE_1',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameLocation: 'center',
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },
                yAxis: {
                  name: 'tSNE_2',
                  nameLocation: 'center',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },
                series: series
              })
            }
          })
        }
      }
    </script>
    <script th:if="${baselineTask != null}">
      $(document).ready(function () {
        initChart062()
      })

      function initChart062 () {
        if (!document.getElementById('chart-062')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-062'))

        drawChart(myChart)

        function drawChart () {
          myChart.clear()
          $.ajax({
            url: '/analysis/scrnaseq/baseline/[[${baselineTask.id}]]/62',
            beforeSend: function () {
              $("#chart-062").next().remove()
              $("#chart-062").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.code === 601) {
                $("#chart-062").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-062").hide()
                return
              }

              var data = result.data
              var legendData = []
              var series = []

              for (var group in data) {
                legendData.push(group)
                series.push({
                  name: group,
                  data: data[group],
                  type: 'scatter',
                  symbolSize: 5,
                  cursor: 'default',
                  itemStyle: {
                    color: getClusterColor(group)
                  }
                })
              }

              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                legend: {
                  data: legendData
                },
                xAxis: {
                  name: 'tSNE_1',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameLocation: 'center',
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },
                yAxis: {
                  name: 'tSNE_2',
                  nameLocation: 'center',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },
                series: series
              })
            }
          })
        }
      }
    </script>
    <!--  sample 标签  -->
    <script th:if="${#strings.listSplit(genomicsTask.sampleIds,',').size() > 1 and baselineTask == null }">
      $(document).ready(function () {
        initChart02()
      })

      function initChart02 () {
        if (!document.getElementById('chart-02')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-02'))

        drawChart(myChart)

        function drawChart () {
          myChart.clear()
          $.ajax({
            url: '/analysis/scrnaseq/genomics/[[${genomicsTask.id}]]/2',
            beforeSend: function () {
              $("#chart-02").next().remove()
              $("#chart-02").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.code === 601) {
                $("#chart-02").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-02").hide()
                return
              }

              var data = result.data
              var legendData = []
              var series = []

              for (var group in data) {
                legendData.push(group)
                series.push({
                  name: group,
                  data: data[group],
                  type: 'scatter',
                  symbolSize: 5,
                  cursor: 'default'
                })
              }

              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                legend: {
                  data: legendData
                },
                xAxis: {
                  name: 'tSNE_1',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameLocation: 'center',
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },
                yAxis: {
                  name: 'tSNE_2',
                  nameLocation: 'center',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },
                series: series
              })

              $(function () {
                window.onresize = function () {
                  myChart.resize()
                }
              })
            }
          })
        }
      }
    </script>
    <script th:if="${#strings.listSplit(genomicsTask.sampleIds,',').size() > 1 and baselineTask != null }">
      $(document).ready(function () {
        initChart05()
      })

      function initChart05 () {
        if (!document.getElementById('chart-05')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-05'))

        drawChart(myChart)

        function drawChart () {
          myChart.clear()
          $.ajax({
            url: '/analysis/scrnaseq/baseline/[[${baselineTask.id}]]/5',
            beforeSend: function () {
              $("#chart-05").next().remove()
              $("#chart-05").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.code === 601) {
                $("#chart-05").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-05").hide()
                return
              }

              var data = result.data, sData = data.sData, xData = data.xData, sd = []
              for (var item in sData) {
                sd.push({
                  name: item,
                  type: 'bar',
                  stack: 'batch_level',
                  data: sData[item],
                  itemStyle: {
                    color: getClusterColor(item)
                  }
                })
              }
              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                tooltip: {
                  trigger: 'axis',
                  axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                    type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                  }
                },
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '3%',
                  containLabel: true
                },
                xAxis: [
                  {
                    name: 'sample',
                    nameLocation: 'center',
                    nameGap: 20,
                    type: 'category',
                    data: xData
                  }
                ],
                yAxis: [
                  {
                    name: 'fraction of cell type',
                    nameGap: 25,
                    nameLocation: 'center',
                    type: 'value'
                  }
                ],
                series: sd
              })

              $(function () {
                window.onresize = function () {
                  myChart.resize()
                }
              })
            }
          })
        }
      }
    </script>

    <!--  gene 标签  -->
    <script th:if="${baselineTask == null}">
      function initOption () {
        $.ajax({
          url: '/analysis/scrnaseq/report/getGeneNames',
          data: {
            genomicsId: '[[${genomicsTask.taskId}]]'
          },
          beforeSend: function () {
            $("#gene-select").html('')
          },
          success: function (result) {
            var data = result.data

            var html = []
            var i = 0

            for (var group in data) {
              html.push('<optgroup label="' + group + ' genes">')
              data[group].forEach(function (name) {
                if (i == 0) {
                  html.push('<option selected data-type="' + group + '" data-val="' + name + '">' + name + '</option>')
                } else {
                  html.push('<option data-type="' + group + '" data-val="' + name + '">' + name + '</option>')
                }
                i++
              })
              html.push('</optgroup>')
            }
            $("#gene-select").html(html.join(''))

            initChart()
          }
        })
      }

      function initChart () {
        var type = $("#gene-select option:selected").data('type')
        var gene = $("#gene-select option:selected").data('val')

        $.ajax({
          url: '/analysis/scrnaseq/report/geneData',
          data: {
            genomicsId: '[[${genomicsTask.taskId}]]',
            type: type,
            gene: gene
          },
          success: function (result) {
            var data = result.data
            var d = data.map(function (p) {
              if (p[2] == 0) {
                return {
                  value: [p[0], p[1], p[2]],
                  itemStyle: {
                    color: 'rgb(110,184,202)'
                  }
                }
              } else {
                return {
                  value: [p[0], p[1], p[2]],
                  itemStyle: {
                    color: 'rgba(42, 6, 141,' + (0.5 + p[2] / 10) + ')'
                  }
                }
              }
            })
            var option = {
              title: {
                text: gene
              },
              tooltip: {
                trigger: 'axis',
                axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                  type: 'cross'        // 默认为直线，可选为：'line' | 'shadow'
                },
                formatter: function (params) {
                  return params[0].data.value[2]
                }
              },
              toolbox: {
                show: true,
                feature: {
                  saveAsImage: {}
                }
              },
              xAxis: {
                name: 'tSNE_1',
                nameTextStyle: {
                  fontWeight: 'bold'
                },
                nameLocation: 'center',
                nameGap: 35,
                splitLine: {
                  show: false
                }
              },
              yAxis: {
                name: 'tSNE_2',
                nameLocation: 'center',
                nameTextStyle: {
                  fontWeight: 'bold'
                },
                nameGap: 35,
                splitLine: {
                  show: false
                }
              },
              series: [{
                data: d,
                type: 'scatter',
                symbolSize: 5,
                cursor: 'default'
              }]
            }
            var myChart = echarts.init(document.getElementById('chart-04'))
            myChart.clear()
            myChart.setOption(option)

            $(function () {
              window.onresize = function () {
                myChart.resize()
              }
            })
          }
        })
      }

      $(document).ready(function () {
        initOption()
        $("#gene-select").on('change', initChart)
      })
    </script>
    <script th:if="${baselineTask != null and genesTask == null}">
      function initOption () {
        $.ajax({
          url: '/analysis/scrnaseq/report/getGeneNames',
          data: {
            genomicsId: '[[${genomicsTask.taskId}]]',
            baselineId: '[[${baselineTask.taskId}]]'
          },
          beforeSend: function () {
            $("#gene-select").html('')
          },
          success: function (result) {
            var data = result.data

            var html = []
            var i = 0
            for (var group in data) {
              html.push('<optgroup label="' + group + ' genes">')
              data[group].forEach(function (name) {
                if (i == 0) {
                  html.push('<option selected data-type="' + group + '" data-val="' + name + '">' + name + '</option>')
                } else {
                  html.push('<option data-type="' + group + '" data-val="' + name + '">' + name + '</option>')
                }
                i++
              })
              html.push('</optgroup>')
            }
            $("#gene-select").html(html.join(''))

            initChart()
          }
        })
      }

      function initChart () {
        var type = $("#gene-select option:selected").data('type')
        var gene = $("#gene-select option:selected").data('val')

        $.ajax({
          url: '/analysis/scrnaseq/report/geneData',
          data: {
            genomicsId: '[[${genomicsTask.taskId}]]',
            baselineId: '[[${baselineTask.taskId}]]',
            type: type,
            gene: gene
          },
          success: function (result) {
            var data = result.data
            var d = data.map(function (p) {
              if (p[2] == 0) {
                return {
                  value: [p[0], p[1], p[2]],
                  itemStyle: {
                    color: 'rgb(110,184,202)'
                  }
                }
              } else {
                return {
                  value: [p[0], p[1], p[2]],
                  itemStyle: {
                    color: 'rgba(42, 6, 141,' + (0.5 + p[2] / 10) + ')'
                  }
                }
              }
            })
            var option = {
              title: {
                text: gene
              },
              tooltip: {
                trigger: 'axis',
                axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                  type: 'cross'        // 默认为直线，可选为：'line' | 'shadow'
                },
                formatter: function (params) {
                  return params[0].data.value[2]
                }
              },
              toolbox: {
                show: true,
                feature: {
                  saveAsImage: {}
                }
              },
              xAxis: {
                name: 'tSNE_1',
                nameTextStyle: {
                  fontWeight: 'bold'
                },
                nameLocation: 'center',
                nameGap: 35,
                splitLine: {
                  show: false
                }
              },
              yAxis: {
                name: 'tSNE_2',
                nameLocation: 'center',
                nameTextStyle: {
                  fontWeight: 'bold'
                },
                nameGap: 35,
                splitLine: {
                  show: false
                }
              },
              series: [{
                data: d,
                type: 'scatter',
                symbolSize: 5,
                cursor: 'default'
              }]
            }
            var myChart = echarts.init(document.getElementById('chart-04'))
            myChart.clear()
            myChart.setOption(option)

            $(function () {
              window.onresize = function () {
                myChart.resize()
              }
            })
          }
        })
      }

      $(document).ready(function () {
        initOption()
        $("#gene-select").on('change', initChart)
      })
    </script>
    <script th:if="${baselineTask != null and genesTask != null}">
      function initOption () {
        $.ajax({
          url: '/analysis/scrnaseq/report/getGeneNames',
          data: {
            genomicsId: '[[${genomicsTask.taskId}]]',
            baselineId: '[[${baselineTask.taskId}]]',
            genesId: '[[${genesTask.taskId}]]'
          },
          beforeSend: function () {
            $("#gene-select").html('')
          },
          success: function (result) {
            var data = result.data

            var html = []
            var i = 0
            for (var group in data) {
              html.push('<optgroup label="' + group + ' genes">')
              data[group].forEach(function (name) {
                if (i == 0) {
                  html.push('<option selected data-type="' + group + '" data-val="' + name + '">' + name + '</option>')
                } else {
                  html.push('<option data-type="' + group + '" data-val="' + name + '">' + name + '</option>')
                }
                i++
              })
              html.push('</optgroup>')
            }
            $("#gene-select").html(html.join(''))

            initChart()
          }
        })
      }

      function initChart () {
        var type = $("#gene-select option:selected").data('type')
        var gene = $("#gene-select option:selected").data('val')

        $.ajax({
          url: '/analysis/scrnaseq/report/geneData',
          data: {
            genomicsId: '[[${genomicsTask.taskId}]]',
            baselineId: '[[${baselineTask.taskId}]]',
            genesId: '[[${genesTask.taskId}]]',
            type: type,
            gene: gene
          },
          success: function (result) {
            var data = result.data
            var d = data.map(function (p) {
              if (p[2] == 0) {
                return {
                  value: [p[0], p[1], p[2]],
                  itemStyle: {
                    color: 'rgb(110,184,202)'
                  }
                }
              } else {
                return {
                  value: [p[0], p[1], p[2]],
                  itemStyle: {
                    color: 'rgba(42, 6, 141,' + (0.5 + p[2] / 10) + ')'
                  }
                }
              }
            })
            var option = {
              title: {
                text: gene
              },
              tooltip: {
                trigger: 'axis',
                axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                  type: 'cross'        // 默认为直线，可选为：'line' | 'shadow'
                },
                formatter: function (params) {
                  return params[0].data.value[2]
                }
              },
              toolbox: {
                show: true,
                feature: {
                  saveAsImage: {}
                }
              },
              xAxis: {
                name: 'tSNE_1',
                nameTextStyle: {
                  fontWeight: 'bold'
                },
                nameLocation: 'center',
                nameGap: 35,
                splitLine: {
                  show: false
                }
              },
              yAxis: {
                name: 'tSNE_2',
                nameLocation: 'center',
                nameTextStyle: {
                  fontWeight: 'bold'
                },
                nameGap: 35,
                splitLine: {
                  show: false
                }
              },
              series: [{
                data: d,
                type: 'scatter',
                symbolSize: 5,
                cursor: 'default'
              }]
            }
            var myChart = echarts.init(document.getElementById('chart-04'))
            myChart.clear()
            myChart.setOption(option)

            $(function () {
              window.onresize = function () {
                myChart.resize()
              }
            })
          }
        })
      }

      $(document).ready(function () {
        initOption()
        $("#gene-select").on('change', initChart)
      })
    </script>

    <!--  celltype 标签  -->
    <script th:if="${baselineTask != null}">
      $(document).ready(function () {
        initChart061()
      })

      function initChart061 () {
        if (!document.getElementById('chart-061')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-061'))

        drawChart(myChart)

        function drawChart () {
          myChart.clear()
          $.ajax({
            url: '/analysis/scrnaseq/baseline/[[${baselineTask.id}]]/61',
            beforeSend: function () {
              $("#chart-061").next().remove()
              $("#chart-061").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.code === 601) {
                $("#chart-061").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-061").hide()
                return
              }

              var data = result.data
              var legendData = []
              var series = []

              for (var group in data) {
                legendData.push(group)
                series.push({
                  name: group,
                  data: data[group],
                  type: 'scatter',
                  symbolSize: 5,
                  cursor: 'default'
                })
              }

              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                legend: {
                  data: legendData
                },
                xAxis: {
                  name: 'tSNE_1',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameLocation: 'center',
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },
                yAxis: {
                  name: 'tSNE_2',
                  nameLocation: 'center',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },
                series: series
              })

              $(function () {
                window.onresize = function () {
                  myChart.resize()
                }
              })
            }
          })
        }
      }
    </script>

    <!--  第二大块  -->
    <script th:if="${pagaTask != null}">
      $(document).ready(function () {
        initChart08()
        initChart09()
      })

      function initChart09 () {
        if (!document.getElementById('chart-09')) {
          return
        }

        drawChart()

        function drawChart () {
          let myChart1, myChart2
          $.ajax({
            url: '/analysis/scrnaseq/paga/[[${vo.pagaTask.id}]]/9',
            beforeSend: function () {
              $("#chart-09").empty()
              $('#chart-09').append(`
                            <div id="chart-09-1" class="col-lg-6"
                                 style="width: 50%;height: 500px"></div>
                            <div id="chart-09-2" class="col-lg-6"
                                 style="width: 50%;height: 500px"></div>
                        `)
              $("#chart-09").show()

              myChart1 = echarts.init(document.getElementById('chart-09-1'))
              myChart2 = echarts.init(document.getElementById('chart-09-2'))
              myChart1.clear()
              myChart2.clear()
              myChart1.showLoading()
              myChart2.showLoading()
            },
            complete: function () {
              myChart1.hideLoading()
              myChart2.hideLoading()
            },
            success: function (result) {
              if (result.error) {
                $("#chart-09").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-09").hide()
                return
              }

              var data = result.data

              var lines = data.lines, nodes = data.nodes, points = data.points

              var series1 = []
              var lineSeries = lines.map(function (item) {
                var data = []
                for (var i = 0; i < 2; i++) {
                  data.push([item.lineX[i], item.lineY[i]])
                }
                return {
                  data: data,
                  type: 'line',
                  symbol: 'none',
                  lineStyle: {
                    color: '#585656'
                  }
                }
              })

              var pointData = points.map(function (item) {
                return [item.x, item.y, item.name]
              })
              var pointSerie = {
                name: 'point',
                data: pointData,
                type: 'scatter',
                symbolSize: 20,
                color: '#000',
                label: {
                  show: true,
                  formatter: function (param) {
                    return param.data[2]
                  },
                  position: 'top'
                }
              }

              let nodeMap = {}
              nodes.sort((a, b) => a.name.localeCompare(b.name))
              nodes.forEach(it => {
                nodeMap[it.name] = nodeMap[it.name] || []
                nodeMap[it.name].push([it.x, it.y, it.y])
              })
              var nodeSeries = []
              for (let k in nodeMap) {
                nodeSeries.push({
                  name: k,
                  data: nodeMap[k],
                  type: 'scatter',
                  symbolSize: 6,
                  color: getClusterColor(k),
                  // emphasis: {
                  //     label: {
                  //         show: false,
                  //         formatter: function (param) {
                  //             return param.data[2];
                  //         },
                  //         position: 'top'
                  //     }
                  // }
                })
              }

              series1.push(...lineSeries, pointSerie, ...nodeSeries)
              myChart1.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                legend: {
                  data: Object.keys(nodeMap)
                },
                xAxis: {
                  name: 'Component 1',
                  nameGap: 25,
                  nameLocation: 'center',
                  type: 'value'
                },
                yAxis: {
                  name: 'Component 2',
                  nameGap: 25,
                  nameLocation: 'center',
                  type: 'value'
                },
                grid: {
                  left: '4%',
                  right: '4%',
                  containLabel: true
                },
                series: series1
              })

              var series2 = []
              series2.push(...lineSeries, pointSerie, {
                type: 'scatter',
                symbolSize: 6,
                data: nodes.map(it => [it.x, it.y, it.pseudotime]),
                emphasis: {
                  label: {
                    show: true,
                    formatter: function (param) {
                      return param.data[2]
                    },
                    position: 'top'
                  }
                }
              })
              myChart2.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                title: {
                  text: 'Pseudotime',
                  left: '8%',
                  top: '1%',
                  textStyle: {
                    fontSize: 12
                  }
                },
                visualMap: {
                  min: 0,
                  max: Math.ceil(Math.max(...nodes.map(it => it.pseudotime))),
                  seriesIndex: series2.length - 1,
                  dimension: 2,
                  precision: 1,
                  calculable: true,
                  orient: 'horizontal',
                  left: 'center',
                  top: '0%',
                  inRange: {
                    color: ['#2fbce2', '#053061']
                  }
                },
                xAxis: {
                  name: 'Component 1',
                  nameGap: 25,
                  nameLocation: 'center',
                  type: 'value'
                },
                yAxis: {
                  name: 'Component 2',
                  nameGap: 25,
                  nameLocation: 'center',
                  type: 'value'
                },
                grid: {
                  left: '4%',
                  right: '4%',
                  containLabel: true
                },
                series: series2
              })
            }
          })
        }
      }

      function initChart08 () {
        if (!document.getElementById('chart-08')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-08'))

        drawChart(myChart)

        function drawChart () {
          myChart.clear()
          $.ajax({
            url: '/analysis/scrnaseq/paga/[[${pagaTask.id}]]/8',
            beforeSend: function () {
              $("#chart-08").next().remove()
              $("#chart-08").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.code === 601) {
                $("#chart-08").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-08").hide()
                return
              }

              var data = result.data, lines = data.lines, nodes = data.nodes

              var series = lines.map(function (item) {
                var data = []
                for (var i = 0; i < 2; i++) {
                  data.push([item.lineX[i], item.lineY[i]])
                }
                return {
                  data: data,
                  type: 'line',
                  color: 'rgba(0,0,0, 0.3)',
                  lineStyle: {
                    width: item.width * 20
                  }
                }
              })

              var nodeData = nodes.map(function (item, idx) {
                return {
                  value: [item.x, item.y, item.name, item.weight],
                  itemStyle: {
                    color: getClusterColor(item.name)
                  }
                }
              })
              var s = {
                name: 'point',
                data: nodeData,
                type: 'scatter',
                symbolSize: function (data) {
                  return data[3] * 200
                },
                label: {
                  show: true,
                  formatter: function (param) {
                    return param.data.value[2]
                  },
                  position: 'top'
                }
              }
              series.push(s)

              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                xAxis: {
                  type: 'value',
                  show: false
                },
                yAxis: {
                  type: 'value',
                  show: false
                },
                grid: {
                  left: '3%',
                  right: '4%',
                  bottom: '3%',
                  containLabel: true
                },
                series: series
              })

            }
          })
        }
      }
    </script>
    <!--  第三大块  -->
    <script th:if="${degTask != null}">
      $(document).ready(function () {
        initChart10()
        initChart11()
      })

      function initChart10 () {
        if (!document.getElementById('chart-10')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-10'))

        drawChart(myChart)

        function drawChart () {
          myChart.clear()
          $.ajax({
            url: '/analysis/scrnaseq/deg/[[${degTask.id}]]/10',
            beforeSend: function () {
              $("#chart-10").next().remove()
              $("#chart-10").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.code === 601) {
                $("#chart-10").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-10").hide()
                return
              }
              var data = result.data
              var nodes = data.data, cutp = data.cutp, cutfc = data.cutfc
              var nodeData = nodes.map(function (item) {
                return {
                  value: [item.x, item.y, item.name],
                  itemStyle: {
                    color: item.color
                  }
                }
              })
              var points = {
                type: 'scatter',
                data: nodeData,
                symbolSize: 6,
              }
              var line = {
                type: 'line',
                data: [],
                markLine: {
                  data: [
                    {
                      name: 'x=Fold change(log2)',
                      symbol: 'none',
                      xAxis: cutfc,
                      lineStyle: {
                        color: '#ff0000'
                      }
                    },
                    {
                      name: 'x=-Fold change(log2)',
                      symbol: 'none',
                      xAxis: -cutfc,
                      lineStyle: {
                        color: '#ff0000'
                      }
                    },
                    {
                      name: 'y=-log10(FDR)',
                      symbol: 'none',
                      yAxis: -Math.log10(cutp),
                      lineStyle: {
                        color: '#ff0000'
                      }
                    }
                  ]
                }
              }

              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                xAxis: {
                  name: 'log2(Fold change)',
                  nameGap: 25,
                  nameLocation: 'center',
                },
                yAxis: {
                  name: '-log10(FDR)',
                  nameGap: 35,
                  nameLocation: 'center',
                },
                grid: {
                  left: '3%',
                  right: '4%',
                  containLabel: true
                },
                tooltip: {
                  formatter: function (params) {
                    return params.data.value[2]
                  }
                },
                series: [
                  points,
                  line
                ]
              })
            }
          })
        }
      }

      function initChart11 () {
        if (!document.getElementById('chart-11')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-11'))

        drawChart(myChart)

        function drawChart () {
          myChart.clear()
          $.ajax({
            url: '/analysis/scrnaseq/deg/[[${degTask.id}]]/11',
            beforeSend: function () {
              $("#chart-11").next().remove()
              $("#chart-11").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.code === 601) {
                $("#chart-11").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-11").hide()
                return
              }
              var data = result.data
              var xData = data.xData, yData = data.yData, sData = data.data

              var seriesData = sData.map(function (item) {
                return [item[1], item[0], item[2] || '-']
              })

              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                tooltip: {
                  position: 'top'
                },
                animation: false,
                grid: {
                  height: '50%',
                  left: '30%'
                },
                xAxis: {
                  type: 'category',
                  data: xData,
                  splitArea: {
                    show: true
                  },
                  axisLabel: {
                    interval: 0,
                    rotate: 90
                  },
                },
                yAxis: {
                  type: 'category',
                  data: yData,
                  splitArea: {
                    show: true
                  },
                  axisLabel: {
                    interval: 0,
                    fontSize: 10,
                    formatter: function (params) {
                      if (params.length > 40) {
                        return params.substring(0, 40) + '...'
                      } else {
                        return params
                      }
                    }
                  },
                  triggerEvent: true
                },
                visualMap: {
                  min: -1,
                  max: 1,
                  dimension: 2,
                  precision: 3,
                  calculable: true,
                  orient: 'horizontal',
                  left: 'center',
                  bottom: '15%',
                  inRange: {
                    color: ['#053061', '#2fbce2', '#f5f5f5', '#e7783c', '#69001f']
                  }
                },
                series: [{
                  type: 'heatmap',
                  data: seriesData,
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }]
              })

              myChart.on('mouseover', function (params) {
                if (params.componentType === 'yAxis') {
                  var tt = $('#tip')
                  tt.html(params.value)
                  tt.css('left', params.event.event.offsetX + 30)
                  tt.css('top', params.event.event.offsetY + 550)
                  tt.show()
                }
              })
              myChart.on('mouseout', function () {
                $('#tip').hide()
              })
            }
          })
        }
      }
    </script>

    <script th:if="${wgcnaTask!=null}">
      $(document).ready(function () {
        initChart14()
        initChart17()
        initChart15()
        initChart16()
      })

      function initChart17 () {
        if (!document.getElementById('chart-17')) {
          return
        }
        let chart = echarts.init(document.getElementById('chart-17'))
        chart.clear()

        $.ajax({
          url: '/analysis/scrnaseq/wgcna/[[${vo.wgcnaTask.id}]]/17',
          async: false,
          beforeSend: function () {
            $("#chart-17").next().remove()
            $("#chart-17").show()
            chart.showLoading()
          },
          complete: function () {
            chart.hideLoading()
          },
          success: function (res) {
            if (res.code != 200) {
              $("#chart-17").after('<div class="bg-light text-muted p-3">无分析结果</div>')
              $("#chart-17").hide()
              return
            }
            let { indexMap, geneMap, treeStr } = res.data

            function sum (arr) {
              return arr.reduce(function (prev, curr, idx, arr) {
                return prev + curr
              })
            }

            function parse_newick (s) {
              let ancestors = [], tree = {}, subtree = {}
              let tokens = s.split(/\s*(;|\(|\)|,|:)\s*/)
              for (let i = 0; i < tokens.length; i++) {
                let token = tokens[i]
                switch (token) {
                  case '(': // new children
                    subtree = {}
                    tree.children = [subtree]
                    ancestors.push(tree)
                    tree = subtree
                    break
                  case ',': // another branch
                    subtree = {}
                    ancestors[ancestors.length - 1].children.push(subtree)
                    tree = subtree
                    break
                  case ')': // optional name next
                    tree = ancestors.pop()
                    break
                  case ':': // optional length next
                    break
                  default:
                    let c = tokens[i - 1]
                    if (['(', ')', ','].indexOf(c) !== -1) {
                      if (token.startsWith('"') && token.startsWith('"')) {
                        token = token.substr(1, token.length - 2)
                      }

                      if (token != null && token != '') {
                        tree.index = token
                        tree.name = indexMap[tree.index]
                        tree.color = geneMap[tree.name]
                      }
                    } else if (c === ':') {
                      tree.height = parseFloat(token)
                    }
                }
              }
              return tree
            }

            let tree = parse_newick(treeStr)
            tree.height = 1

            let nodes = [], lines = [], minY = 1

            function getInternalNode (d) {
              const l = []
              d.forEach((it) => {
                let x = it.x
                if (x === undefined) {
                  if (Array.isArray(it.children) && it.children.length > 1) {
                    x = getInternalNode(it.children)
                  }
                }
                l.push(x)
              })
              return sum(l) / l.length
            }

            function genLeafNode (tree) {
              if (tree.children === undefined) {
                tree.x = nodes.length
                tree.leaf = true
                nodes.push({ index: tree.index, name: tree.name, height: tree.height, color: tree.color })
              }
              if (Array.isArray(tree.children) && tree.children.length > 1) {
                tree.children.forEach(it => {
                  genLeafNode(it)
                })
                tree.x = getInternalNode(tree.children)
              }
            }

            genLeafNode(tree)

            function genTreeCoords (childs, parentX, parentH, result) {
              childs.forEach(item => {
                let itemY = item.leaf ? item.height : parentH - item.height
                if (itemY < minY) {
                  minY = itemY
                }
                const coords = [
                  [parentX + 0.5, parentH],
                  [item.x + 0.5, parentH],
                  [item.x + 0.5, itemY]
                ]
                result.push({
                  coords: coords
                })

                if (item.children != null && item.children.length > 0) {
                  genTreeCoords(item.children, item.x, itemY, result)
                }
              })
            }

            genTreeCoords(tree.children, tree.x, tree.height, lines)

            let barData = []
            for (let i = 0; i < nodes.length; i++) {
              barData.push({
                node: nodes[i],
                value: [i, 1],
                itemStyle: {
                  color: nodes[i].color,
                }
              })
            }

            chart.setOption({
              grid: [
                {
                  top: '10%',
                  height: '62%',
                  left: '100px',
                  right: '50px'
                }, {
                  top: '75%',
                  bottom: '40px',
                  left: '100px',
                  right: '50px'
                  // height: '20%'
                }
              ],
              toolbox: {
                show: true,
                feature: {
                  saveAsImage: {}
                }
              },
              title: {
                text: 'Cluster Dendrogram',
                left: 'center'
              },
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'cross',
                  snap: true,
                  label: {
                    show: false,
                    precision: 0,
                  }
                },
                formatter: function (param) {
                  let node = param[0].data.node
                  return `${param[0].marker} ${node.name}: ${node.height}`
                  // return `${param[0].marker} ${JSON.stringify(node)}`
                }
              },
              axisPointer: {
                link: { xAxisIndex: 'all' },
              },
              dataZoom: [
                {
                  type: 'slider',
                  xAxisIndex: [0, 1],
                  labelPrecision: 0,
                  minValueSpan: 1,
                  showDetail: false,
                },
                {
                  type: 'slider',
                  yAxisIndex: [0],
                  // xAxisIndex: [0, 1],
                  orient: 'vertical',
                  showDetail: false,
                }
              ],
              xAxis: [
                {
                  gridIndex: 0,
                  type: 'value',
                  min: 0,
                  max: nodes.length,
                  axisLine: { show: false },
                  axisTick: { show: false },
                  axisLabel: { show: false },
                  splitLine: { show: false },
                },
                {
                  gridIndex: 1,
                  type: 'category',
                  axisLine: { show: false },
                  axisTick: { show: false },
                  axisLabel: { show: false },
                  splitLine: { show: false },
                }
              ],
              yAxis: [
                {
                  gridIndex: 0,
                  type: 'value',
                  inverse: false,
                  scale: true,
                  min: Math.floor(minY / 0.05) * 0.05,
                  max: 1,
                  name: 'Height',
                  nameLocation: 'center',
                  nameGap: 70,
                  nameTextStyle: {
                    size: 12,
                    fontWeight: 'bold',
                  },
                  axisLabel: {
                    formatter: function (value) {
                      return value.toFixed(2)
                    }
                  },
                  // axisLine: {show: false},
                  // axisTick: {show: false},
                  splitLine: { show: false },
                }, {
                  gridIndex: 1,
                  type: 'value',
                  name: 'Module colors',
                  nameLocation: 'center',
                  nameGap: 10,
                  nameRotate: 0,
                  nameTextStyle: {
                    size: 12,
                    fontWeight: 'bold',
                  },
                  axisLine: { show: false },
                  axisTick: { show: false },
                  axisLabel: { show: false },
                  splitLine: { show: false },
                }
              ],
              series: [
                {
                  type: 'lines',
                  xAxisIndex: 0,
                  yAxisIndex: 0,
                  coordinateSystem: 'cartesian2d',
                  polyline: true,
                  data: lines,
                  silent: true,
                  large: true,    // 大数据渲染优化
                  lineStyle: {
                    type: 'solid',
                    color: '#000'
                  }
                },
                {
                  type: 'bar',
                  xAxisIndex: 1,
                  yAxisIndex: 1,
                  barWidth: '100%',
                  clip: true,
                  silent: true,
                  data: barData,
                }
              ]
            })
          }
        })
      }

      function initChart14 () {
        if (!document.getElementById('chart-14')) {
          return
        }
        var power = echarts.init(document.getElementById('chart-14'))
        power.clear()
        var result
        //$.ajaxSettings.async = false;
        $.ajax({
          url: '/analysis/scrnaseq/wgcna/[[${vo.wgcnaTask.id}]]/14',
          async: false,
          beforeSend: function () {
            $("#chart-14").next().remove()
            $("#chart-14").show()
            power.showLoading()
          },
          complete: function () {
            power.hideLoading()
          },
          success: function (res) {
            if (res.code != 200) {
              $("#chart-14").after('<div class="bg-light text-muted p-3">无分析结果</div>')
              $("#chart-14").hide()
              return
            }
            result = res.data
          }
        })
        var show1 = []
        var show2 = []
        for (let i = 0; i < result.powerNumber.length; i++) {
          show1.push([result.powerNumber[i], result.correlation[i]])
          show2.push([result.powerNumber[i], result.connectivity[i]])
        }
        var dataAll = [show1, show2]

        option = {
          toolbox: {
            show: true,
            feature: {
              saveAsImage: {}
            }
          },
          grid: [
            { left: '7%', top: '7%', width: '38%', bottom: '100px' },
            { right: '7%', top: '7%', width: '38%', bottom: '100px' }

          ],
          tooltip: {
            formatter: '{a}: ({c})'
          },
          title: [
            {
              text: 'Scale independence',
              left: '15%',
              textStyle: {
                lineHeight: 12,
              }
            },
            {
              text: 'Mean connectivity',
              right: '17%',
              textStyle: {
                lineHeight: 12,
              }
            }
          ],
          xAxis: [
            {
              gridIndex: 0,
              name: 'soft Threshold(power)',
              nameLocation: 'center',
              nameGap: 50,
              nameTextStyle: {
                size: 12,
                fontWeight: 'bold',
              },
              min: 0
            },
            {
              gridIndex: 1,
              name: 'soft Threshold(power)',
              nameLocation: 'center',
              nameGap: 50,
              nameTextStyle: {
                size: 12,
                fontWeight: 'bold',
              },
              min: 0
            },
          ],
          yAxis: [
            {
              gridIndex: 0,
              name: 'Scale Free Topology Model Fit,signed R^2',
              nameLocation: 'center',
              nameGap: 50,
              nameTextStyle: {
                fontWeight: 'bold',
              },
              namePotate: 90,

            },
            {
              gridIndex: 1,
              name: 'Mean Connectivity',
              nameLocation: 'center',
              nameGap: 50,
              nameTextStyle: {
                fontWeight: 'bold',
              },
              namePotate: 90,
              axisLine: {
                show: false,
                width: 5
              }
            }
          ],
          series: [
            {
              name: 'Scale independence',
              type: 'scatter',
              xAxisIndex: 0,
              yAxisIndex: 0,
              data: dataAll[0],
            },
            {
              name: 'Mean connectivity',
              type: 'scatter',
              xAxisIndex: 1,
              yAxisIndex: 1,
              data: dataAll[1],
            }
          ]
        }
        power.setOption(option)
      }

      function initChart15 () {
        if (!document.getElementById('chart-15')) {
          return
        }
        var dom = document.getElementById("chart-15")
        var myChart = echarts.init(dom)

        myChart.clear()
        var result
        $.ajax({
          url: '/analysis/scrnaseq/wgcna/[[${vo.wgcnaTask.id}]]/15',
          async: false,
          beforeSend: function () {
            $("#chart-15").next().remove()
            $("#chart-15").show()
            myChart.showLoading()
          },
          complete: function () {
            myChart.hideLoading()
          },
          success: function (res) {
            if (res.code != 200) {
              $("#chart-15").after('<div class="bg-light text-muted p-3">无分析结果</div>')
              $("#chart-15").hide()
              return
            }
            result = res.data
          }
        })

        const hours = result.x
        const days = result.y
        const data = result.data

        var sd = data.map(function (item) {
          return [item[1], item[0], item[2] || '-']
        })

        option = {
          toolbox: {
            show: true,
            feature: {
              saveAsImage: {}
            }
          },
          title: {
            text: 'Correlation between module and cell type',
            left: 'center',
            textStyle: {
              lineHeight: 12,
            }
          },

          tooltip: {
            position: 'top'
          },
          grid: {
            bottom: '100px',
            top: '10%'
          },
          xAxis: {
            type: 'category',
            data: hours,
            splitArea: {
              show: true
            },
            axisLabel: {
              interval: 0,
              rotate: 90
            }
          },
          yAxis: {
            type: 'category',
            data: days,
            splitArea: {
              show: true
            },
            axisLabel: {
              interval: 0
            },
            inverse: true
          },
          visualMap: {
            min: 0,
            max: 1,
            dimension: 2,
            precision: 8,
            text: ['1', '0'],
            //calculable: true,
            orient: 'vertical',
            left: 'right',
            //bottom: '15%',
            top: '15%',
            inRange: {
              color: ['#5398ea', '#eedb93', '#ea4242']
            }
          },
          series: [{
            type: 'heatmap',
            data: sd,
          }]
        }

        myChart.setOption(option)
      }

      function initChart16 () {
        if (!document.getElementById('chart-16')) {
          return
        }
        var dom = document.getElementById("chart-16")
        var myChart = echarts.init(dom, null, { width: 800, height: 500 })
        myChart.clear()
        var result
        $.ajax({
          url: '/analysis/scrnaseq/wgcna/[[${vo.wgcnaTask.id}]]/16',
          async: false,
          beforeSend: function () {
            $("#chart-16").next().remove()
            $("#chart-16").show()
            myChart.showLoading()
          },
          complete: function () {
            myChart.hideLoading()
          },
          success: function (res) {
            if (res.code != 200) {
              $("#chart-16").after('<div class="bg-light text-muted p-3">无分析结果</div>')
              $("#chart-16").hide()
              return
            }
            result = res.data
          }
        })

        const dataBJ = result.data
        let max1 = 0
        let max2 = 0
        for (i = 0; i < dataBJ.length; i++) {
          if (dataBJ[i][2] > max1) {
            max1 = dataBJ[i][2]
          }
          if (dataBJ[i][3] > max2) {
            max2 = dataBJ[i][3]
          }
        }
        const itemStyle = {
          opacity: 0.8,
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowColor: 'rgb(9,8,8)'
        }
        option = {
          // toolbox: {
          //     show: true,
          //     feature: {
          //         saveAsImage: {}
          //     }
          // },
          title: {
            text: 'Gene ontology analysis',
            left: 'center'
          },

          grid: {
            left: '50%',
            show: true
          },
          tooltip: {
            backgroundColor: 'rgb(89,89,218)',
            formatter: function (param) {
              var value = param.value
              // prettier-ignore
              return 'Cluster：' + value[0] + '<br>' +
                'Description：' + value[1] + '<br>' +
                'GeneRatio：' + value[2] + '<br>' +
                'p.adjust：' + value[3] + '<br>'
            }
          },
          xAxis: {
            type: 'category',
            name: 'Cluster',
            nameLocation: 'center',
            boundaryGap: false,
            nameGap: 30,
            nameTextStyle: {
              fontSize: 13,
            },
            //max: 31,
            splitLine: {
              show: true
            }
          },
          yAxis: {
            type: 'category',
            boundaryGap: false,
            offset: 10,
            splitLine: {
              show: true
            },
          },
          visualMap: [
            {
              type: 'piecewise',
              left: 'right',
              top: '10%',
              dimension: 2,
              precision: 2,
              min: 0,
              max: 0.25,
              text: ['GeneRatio'],
              pieces: [
                {
                  gt: 0.20,
                  lte: 0.25,
                  symbol: 'circle',
                  symbolSize: 20
                },
                {
                  gt: 0.15,
                  lte: 0.20,
                  symbol: 'circle',
                  symbolSize: 17
                },
                {
                  gt: 0.10,
                  lte: 0.15,
                  symbol: 'circle',
                  symbolSize: 15
                },
                {
                  gt: 0.05,
                  lte: 0.10,
                  symbol: 'circle',
                  symbolSize: 12
                },
                {
                  gt: 0,
                  lte: 0.05,
                  symbol: 'circle',
                  symbolSize: 10,
                },
              ],
              itemWidth: 0,
              hoverLink: false,
              inRange: {
                symbol: 'circle',
                symbolSize: [5, 30],
              },
            },
            {
              left: 'right',
              bottom: '5%',
              dimension: 3,
              precision: 8,
              min: 0,
              max: max2,
              itemHeight: 120,
              text: ['p.adjust'],
              textGap: 30,
              inRange: {
                color: ['#f90020', '#ba009e', '#2809fc']
              },
            }
          ],
          series: [
            {
              type: 'scatter',
              itemStyle: itemStyle,
              data: dataBJ
            }
          ]
        }
        intoScatter()
        myChart.setOption(option)
      }

      function intoScatter () {
        const html =
          '<div class="d-flex justify-content-between"\n' +
          '                                             style="height: 100px;position: absolute;left: 760px; top: 80px;">\n' +
          '                                            <div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 10px; height: 10px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 12px; height: 12px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 15px; height: 15px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 17px; height: 17px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center">\n' +
          '                                                    <div class="circle"\n' +
          '                                                         style="width: 20px; height: 20px;margin-top: 25%"></div>\n' +
          '                                                </div>\n' +
          '                                            </div>\n' +
          '                                            <div class="ml-2">\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 10px; height: 10px;">0.05</div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 12px; height: 12px;margin-top: 30%">0.10</div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 15px; height: 15px;margin-top: 30%">0.15</div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 17px; height: 17px;margin-top: 40%">0.20</div>\n' +
          '                                                </div>\n' +
          '                                                <div style="height: 20px;text-align: center;">\n' +
          '                                                    <div style="width: 20px; height: 20px;margin-top: 30%">0.25</div>\n' +
          '                                                </div>\n' +
          '                                            </div>\n'
        var box = document.getElementById("chart-16-box")
        box.innerHTML = html
      }
    </script>


    <script>
      $("a[data-toggle='tab']").on("shown.bs.tab", function (e) {
        window.onresize()
      })

      function addReport (_this) {
        if ($("#genomic-select").val() === '') {
          layer.msg('please select genomic task')
          return
        }
        if ($(_this).data('loading') == 'true') {
          return
        }
        $(_this).data('loading', 'true')

        let data = {}
        if (genomicsId) {
          data.genomicsId = genomicsId
        }
        if (baselineId) {
          data.baselineId = baselineId
        }
        if (pagaId) {
          data.pagaId = pagaId
        }
        if (degId) {
          data.degId = degId
        }
        if (genesId) {
          data.genesId = genesId
        }
        if (wgcnaId) {
          data.wgcnaId = wgcnaId
        }

        $.ajax({
          url: '/analysis/scrnaseq/report/save',
          method: 'post',
          data: data,
          success: function (result) {
            layer.msg('submit success')
            var id = result.data.taskId
            // console.log(id)
            setTimeout(function () {
              var _context_path = $("meta[name='_context_path']").attr("content")
              window.location.href = $.trim(_context_path) + '/analysis/scrnaseq/report?taskId=' + id
            }, 2000)
          },
          complete: function () {
            $(_this).data('loading', 'false')
          }
        })
      }
    </script>
</th:block>
</html>
