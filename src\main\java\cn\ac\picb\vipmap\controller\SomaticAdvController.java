package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.somatic.dto.SomaticAdvTaskDTO;
import cn.ac.picb.somatic.enums.SomaticTaskStatus;
import cn.ac.picb.somatic.vo.*;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.SomaticAdvService;
import cn.ac.picb.vipmap.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/analysis/somatic-adv")
@RequiredArgsConstructor
public class SomaticAdvController {

    private final SomaticAdvService somaticAdvService;
    private final AppProperties appProperties;

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") SomaticTaskSearchVO queryVO, @ModelAttribute("pageParam") CustomPageParam pageParam, Model model) {
        PageResult<SomaticAdvTaskDTO> pageResult = somaticAdvService.findPage(user, queryVO, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = SomaticTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);

        SomaticTaskIdVO somaticTaskIdVO = somaticAdvService.findSomaticAdvIdInfo(user.getId());
        model.addAttribute("somaticTaskIdVO", somaticTaskIdVO);
        return "somatic-adv/list";
    }

    @RequestMapping("/form")
    public String form(CurrentUser user, @ModelAttribute("query") SomaticTaskSearchVO queryVO, @ModelAttribute PageParam pageParam, Model model) {
        SomaticBatchQueryVO somaticBatchQueryVO = somaticAdvService.findAllDoneBatchQueryInfo(user);
        model.addAttribute("somaticBatchQueryVO", somaticBatchQueryVO);

        PageResult<SomaticAdvTaskDTO> pageResult = somaticAdvService.findBatchPage(user, queryVO, pageParam);
        model.addAttribute("pageResult", pageResult);
        return "somatic-adv/form";
    }

    @RequestMapping("/createAdvTask")
    @ResponseBody
    public CommonResult<String> createAdvTask(CurrentUser user, @Validated SomaticAdvTaskParam param) {
        String taskId = somaticAdvService.createAdvTask(user, param);
        return success(taskId);
    }

    @RequestMapping("/form-adv/{id}")
    public String formAdv(CurrentUser user, @PathVariable("id") String id, Model model) {
        SomaticAdvTaskVO somaticAdvTaskVO = somaticAdvService.findInitTaskVO(user, id);
        model.addAttribute("somaticAdvTaskVO", somaticAdvTaskVO);
        return "somatic-adv/form-adv";
    }


    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        somaticAdvService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        SomaticAdvTaskVO vo = somaticAdvService.findAdvTaskVO(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = SomaticTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "somatic-adv/detail";
    }

    @RequestMapping("/getChartData")
    @ResponseBody
    public CommonResult<Object> chartData(@Validated SomaticAdvChartParamVO param) {
        Object data = somaticAdvService.getChartData(param);
        return success(data);
    }

    @RequestMapping("/getAdvImg")
    @SneakyThrows
    public ResponseEntity<byte[]> getAdvImg(String taskId, String name) {
        return somaticAdvService.getAdvImg(taskId, name);
    }

    @RequestMapping("/validateMafFile")
    @ResponseBody
    public CommonResult<SomaticAdvValidateResultVO> validateMafFile(CurrentUser user, @Validated SomaticFileVO mafFileVo) {
        return success(somaticAdvService.validateMafFile(mafFileVo, user));
    }


}
