package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.strnaseq.dto.StrnaseqDTO;
import cn.ac.picb.strnaseq.po.StrnaseqTaskPO;
import cn.ac.picb.strnaseq.vo.ChartParamVO;
import cn.ac.picb.strnaseq.vo.StrnaseqTaskParamVO;
import cn.ac.picb.strnaseq.vo.StrnaseqTaskQueryVO;
import cn.ac.picb.vipmap.client.StrnaseqServiceClient;
import feign.Response;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class StrnaseqServiceClientFallback implements StrnaseqServiceClient {

    private final static String SERVER_NAME = "strnaseq-service";

    @Override
    public CommonResult<StrnaseqTaskPO> saveTask(StrnaseqTaskParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PageResult<StrnaseqTaskPO>> findTaskPage(StrnaseqTaskQueryVO queryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<StrnaseqTaskPO> deleteTask(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<StrnaseqDTO> findTaskDtoById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public ResponseEntity<byte[]> getImage(String taskId, String path) {
        return null;
    }

    @Override
    public Response downloadFile(String taskId, String path, String displayName) {
        return null;
    }

    @Override
    public CommonResult<Object> getChartData(ChartParamVO vo) {
        return serverError(SERVER_NAME);
    }
}
