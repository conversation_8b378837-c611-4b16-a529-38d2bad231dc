package cn.ac.picb.vipmap.vo;

import cn.ac.picb.common.framework.vo.PageParam;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;


public class CustomPageParam extends PageParam {

    @NotNull(message = "每页条数不能为空")
    @Range(min = 1, max = 100, message = "条数范围为 [1, 100]")
    private int size = 10;

    public Integer getSize() {
        return size;
    }

    public CustomPageParam setSize(Integer size) {
        this.size = size;
        return this;
    }
}
