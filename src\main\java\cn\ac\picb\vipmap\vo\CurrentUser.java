package cn.ac.picb.vipmap.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "cas_user", indexes = {
        @Index(name = "index_id", columnList = "id", unique = true),
        @Index(name = "index_user_name", columnList = "user_name", unique = true),
})
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CurrentUser {

    @Id
    @Column(name = "id", unique = true, nullable = false, length = 36)
    private String id;

    private String name;

    @Column(name = "user_name")
    private String username;

    private String role;

    public CurrentUser(String id, String name, String username) {
        this.id = id;
        this.name = name;
        this.username = username;
    }
}
