eureka:
  client:
    service-url:
      defaultZone: http://peer1:7901/eureka/
    initial-instance-info-replication-interval-seconds: 10 # 将实例信息变更同步到 Eureka Server的初始延迟时间 ，默认为40秒
    registry-fetch-interval-seconds: 5 # 拉取服务注册信息频率
  instance:
    metadata-map:
      management.context-path: ${server.servlet.context-path}/actuator

server:
  port: 8082
  tomcat:
    max-http-form-post-size: 5MB
  ssl:
    # 本地测试必须使用https才能使用node系统的cas单点登录
    enabled: true
    # keytool -genkeypair -alias tomcat_https -keypass 123456 -keyalg RSA -keysize 1024 -validity 365 -keystore "D:\develop\server\cert\tomcat_https.keystore" -storepass 123456
    key-store: classpath:tomcat_https.keystore
    key-store-type: JKS
    key-alias: tomcat_https
    key-password: 123456
    key-store-password: 123456
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************
    username: root
    password: Lfgzs@2021
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    database: mysql
    hibernate:
      ddl-auto: update
    show-sql: true

logging:
  file:
    path: D:/Users/<USER>/test/logs/vipmap.log

app:
  #  windows本地的hosts里面添加一条记录127.0.0.1 local.biosino.org 才可以使用cas登录
  vipmap: https://local.biosino.org:8082/vipmap
  pdmsHost: http://local.biosino.org:8081/pdms
  modules: rnaseq;ase;circrna;paean;scrnaseq;scrnasmartseq;strnaseq;somatic;germline;methychip;wgbs;proteomics

# RDR登录配置
rdr:
  login_page: https://dev.biosino.org/keep-9hos/system/#/login?redirect=https://local.biosino.org:8082/vipmap/home
  api:
    url: https://dev.biosino.org/keep-9hos-api
    check_user_login: ${rdr.api.url}/auth/checkLogin
    get_user_info_by_token: ${rdr.api.url}/auth/getUserInfoByToken
    get_user_info_by_user_name_and_password: ${rdr.api.url}/auth/getUserInfoByUserNameAndPassword
    logout_url: ${rdr.api.url}/auth/logout

