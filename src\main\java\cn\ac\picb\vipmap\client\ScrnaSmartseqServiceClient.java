package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnasmartseq.client.ScrnaSmartseqServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaSmartseqServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(value = "scrnasmartseq-service", fallback = ScrnaSmartseqServiceClientFallback.class)
public interface ScrnaSmartseqServiceClient extends ScrnaSmartseqServiceApi {

}
