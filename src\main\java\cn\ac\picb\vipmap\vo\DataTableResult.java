package cn.ac.picb.vipmap.vo;

import org.springframework.data.domain.Page;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 本类用于定义 jQuery DataTables 插件的返回信息，参考：
 * <p>
 * https://datatables.net/manual/server-side#Returned-data
 *
 * <AUTHOR>
 */
public class DataTableResult<T> {

    /**
     * draw计数器，该对象是对-的响应，该响应来自作为数据请求的一部分发送的draw参数。
     * 注意，出于安全考虑，强烈建议将此参数转换为整数，而不是简单地将它在draw参数中发送的内容回传给客户机，以防止跨站点脚本攻击(XSS)
     */
    private Integer draw;
    /**
     * datatable 中用于计算分页 和显示用的 数据总条数
     */
    private Long recordsTotal;
    private Long recordsFiltered;
    /**
     * 数据
     */
    private Collection<T> data;
    /**
     * 查询失败消息
     */
    private String error;

    /**
     * 额外希望传递的值
     */
    private Map<String, Object> extra = new LinkedHashMap<>(0);

    /**
     * 不允许 new
     */
    private DataTableResult() {
    }

    public static <T> DataTableResult<T> success(Integer draw, Page<T> page) {
        return success(draw, page.getTotalElements(), page.getTotalElements(), page.getContent());
    }

    public static <T> DataTableResult<T> success(Integer draw, Long recordsTotal, Long recordsFiltered, Collection<T> data) {
        return new DataTableResult<T>()
                .setDraw(draw)
                .setRecordsTotal(recordsTotal)
                .setRecordsFiltered(recordsFiltered)
                .setData(data);
    }

    public static <T> DataTableResult<T> failed(Integer draw, String message) {
        return new DataTableResult<T>()
                .setDraw(draw)
                .setError(message)
                .setRecordsTotal(0L)
                .setRecordsFiltered(0L)
                .setData(null);
    }

    public Integer getDraw() {
        return draw;
    }

    public Long getRecordsTotal() {
        return recordsTotal;
    }

    public Long getRecordsFiltered() {
        return recordsFiltered;
    }

    public Collection<T> getData() {
        return data;
    }

    public Map<String, Object> getExtra() {
        return extra;
    }

    public String getError() {
        return error;
    }

    private DataTableResult<T> setDraw(Integer draw) {
        this.draw = draw;
        return this;
    }

    private DataTableResult<T> setRecordsTotal(Long recordsTotal) {
        this.recordsTotal = recordsTotal;
        return this;
    }

    private DataTableResult<T> setRecordsFiltered(Long recordsFiltered) {
        this.recordsFiltered = recordsFiltered;
        return this;
    }

    private DataTableResult<T> setData(Collection<T> data) {
        this.data = data;
        return this;
    }

    public DataTableResult<T> setExtra(Map<String, Object> extra) {
        this.extra = extra;
        return this;
    }

    public DataTableResult<T> appendExtra(String key, Object data) {
        this.extra.put(key, data);
        return this;
    }

    private DataTableResult<T> setError(String error) {
        this.error = error;
        return this;
    }
}
