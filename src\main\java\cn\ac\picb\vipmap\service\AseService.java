package cn.ac.picb.vipmap.service;


import cn.ac.picb.ase.po.AseTaskPO;
import cn.ac.picb.ase.vo.AseTaskInput;
import cn.ac.picb.ase.vo.AseTaskParamVO;
import cn.ac.picb.ase.vo.AseTaskQueryVO;
import cn.ac.picb.ase.vo.AseTaskVO;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.vipmap.client.AseServiceClient;
import cn.ac.picb.vipmap.mapper.AseMapper;
import cn.ac.picb.vipmap.vo.AseTaskParam;
import cn.ac.picb.vipmap.vo.AseTaskSearchVO;
import cn.ac.picb.vipmap.vo.CurrentUser;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2022/3/1 14:58
 */
@Service
@RequiredArgsConstructor
public class AseService {

    private final AseServiceClient aseServiceClient;

    public AseTaskPO createTask(CurrentUser user, AseTaskParam param) {
        AseTaskParamVO vo = AseMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<AseTaskPO> result = aseServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public Response downloadTemplate() {
        return aseServiceClient.downloadTemplateExcel();
    }

    public List<AseTaskInput> uploadTemplate(MultipartFile file, CurrentUser user) {
        CommonResult<List<AseTaskInput>> result = aseServiceClient.uploadTemplate(user.getUsername(), file);
        result.checkError();
        return result.getData();
    }

    public PageResult<AseTaskPO> findPage(CurrentUser user, AseTaskSearchVO search, PageParam pageParam) {
        AseTaskQueryVO queryVO = AseMapper.INSTANCE.convert(search);
        queryVO.setUserId(user.getId());
        queryVO.setPage(pageParam.getPage());
        queryVO.setSize(pageParam.getSize());
        CommonResult<PageResult<AseTaskPO>> result = aseServiceClient.findTaskPage(queryVO);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<AseTaskPO> result = aseServiceClient.deleteById(id);
        result.checkError();
    }

    public AseTaskVO findTaskVO(String id) {
        CommonResult<AseTaskVO> result = aseServiceClient.findDetailById(id);
        result.checkError();
        return result.getData();
    }

    public Response downloadResult(String taskId, String displayName) {
        return aseServiceClient.downloadResult(taskId, displayName);
    }
}
