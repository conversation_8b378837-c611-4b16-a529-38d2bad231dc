package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.proteomics.po.ProteomicsTaskPO;
import cn.ac.picb.proteomics.vo.ProteomicsTaskParamVO;
import cn.ac.picb.proteomics.vo.ProteomicsTaskQueryVO;
import cn.ac.picb.proteomics.vo.ProteomicsValidateResultVO;
import cn.ac.picb.vipmap.client.ProteomicsServiceClient;
import feign.Response;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ProteomicsServiceClientFallback implements ProteomicsServiceClient {

    private final static String SERVER_NAME = "proteomics-service";

    @Override

    public CommonResult<PageResult<ProteomicsTaskPO>> findTaskPage(ProteomicsTaskQueryVO proteomicsTaskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<String> saveTask(ProteomicsTaskParamVO proteomicsTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ProteomicsTaskPO> findById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<ProteomicsTaskPO> deleteById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadTemplateExcel() {
        return null;
    }

    @Override
    public CommonResult<List<ProteomicsValidateResultVO>> uploadTemplateExcel(MultipartFile multipartFile, String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadResult(String taskId, String displayName) {
        return null;
    }
}
