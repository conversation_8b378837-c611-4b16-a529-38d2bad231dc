package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnaseq.client.ScrnaseqPagaServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqPagaServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "scrnaseq-service", contextId = "paga", fallback = ScrnaseqPagaServiceClientFallback.class)
public interface ScrnaseqPagaServiceClient extends ScrnaseqPagaServiceApi {

}
