package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.somatic.dto.SomaticAdvTaskDTO;
import cn.ac.picb.somatic.enums.SomaticTaskStatus;
import cn.ac.picb.somatic.po.SomaticAdvTaskPO;
import cn.ac.picb.somatic.vo.*;
import cn.ac.picb.vipmap.client.SomaticServiceClient;
import cn.ac.picb.vipmap.mapper.SomaticMapper;
import cn.ac.picb.vipmap.vo.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.validation.ValidationException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SomaticAdvService {

    private final SomaticServiceClient somaticServiceClient;

    public PageResult<SomaticAdvTaskDTO> findPage(CurrentUser user, SomaticTaskSearchVO queryVO, CustomPageParam pageParam) {
        SomaticTaskQueryVO vo = SomaticMapper.INSTANCE.convertToQueryVO(queryVO);
        vo.setUserId(user.getId());
        vo.setPage(pageParam.getPage());
        vo.setSize(pageParam.getSize());

        CommonResult<PageResult<SomaticAdvTaskDTO>> result = somaticServiceClient.findAdvTaskPage(vo);
        result.checkError();
        return result.getData();
    }


    public String createAdvTask(CurrentUser user, SomaticAdvTaskParam param) {
        final String id = param.getId();
        if (StrUtil.isBlank(id)) {
            final SomaticFileVO mafFileVo = param.getMafFileVo();
            final List<String> runNameList = param.getRunNameList();
            if (mafFileVo == null && CollUtil.isEmpty(runNameList)) {
                throw new ValidationException("Parameters must not be empty");
            }
        } else {
            final String taskName = param.getTaskName();
            if (StrUtil.isBlank(taskName)) {
                throw new ValidationException("Advanced analysis Task Name must not be empty");
            }
            final Integer interestedGenesTop = param.getInterestedGenesTop();
            final String interestedGenesCustom = param.getInterestedGenesCustom();
            if (interestedGenesTop == null && StrUtil.isBlank(interestedGenesCustom)) {
                throw new ValidationException("Interested Genes must not be empty");
            }
        }

        SomaticAdvTaskParamVO vo = SomaticMapper.INSTANCE.convertToAdvVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<String> result = somaticServiceClient.saveAdvTask(vo);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<SomaticAdvTaskPO> result = somaticServiceClient.deleteAdvById(id);
        result.checkError();
    }

    public SomaticAdvTaskVO findAdvTaskVO(String id) {
        SomaticAdvTaskVO vo = new SomaticAdvTaskVO();
        CommonResult<SomaticAdvTaskDTO> result = somaticServiceClient.findAdvDetailById(id);
        result.checkError();
        SomaticAdvTaskDTO dto = result.getData();
        vo.setTask(dto);

        /*SomaticAdvTaskPO task = dto.getAdvTask();
        if (task.getStatus().equals(SomaticTaskStatus.complete.getCode())) {
            CommonResult<SomaticTaskDetailVO> taskDetailVO = somaticServiceClient.findTaskDetailVO(task.getTaskId());
            taskDetailVO.checkError();
            SomaticTaskDetailVO data = taskDetailVO.getData();
            vo.setDetailVO(data);
        }
        */
        return vo;
    }


    public SomaticBatchQueryVO findAllDoneBatchQueryInfo(CurrentUser user) {
        final CommonResult<SomaticBatchQueryVO> allDoneBatchId = somaticServiceClient.findAllDoneBatchQueryInfo(user.getId());
        return allDoneBatchId != null ? allDoneBatchId.getData() : null;
    }

    public PageResult<SomaticAdvTaskDTO> findBatchPage(CurrentUser user, SomaticTaskSearchVO queryVO, PageParam pageParam) {
        final SomaticTaskQueryVO vo = SomaticService.initFindPageParam(user, queryVO, pageParam);

        CommonResult<PageResult<SomaticAdvTaskDTO>> result = somaticServiceClient.findBatchTaskPage(vo);
        result.checkError();
        return result.getData();
    }


    public SomaticAdvTaskVO findInitTaskVO(CurrentUser user, String id) {
        CommonResult<SomaticAdvTaskDTO> result = somaticServiceClient.findAdvDetailById(id);
        result.checkError();
        SomaticAdvTaskDTO dto = result.getData();
        final String userId = dto.getAdvTask().getUserId();
        if (!userId.equals(user.getId())) {
            return null;
        }
        final Integer status = dto.getAdvTask().getStatus();
        if (!SomaticTaskStatus.init.getCode().equals(status)) {
            throw new RuntimeException("Illegal task status");
        }
        SomaticAdvTaskVO vo = new SomaticAdvTaskVO();
        vo.setTask(dto);
        return vo;
    }

    public Object getChartData(SomaticAdvChartParamVO param) {
        CommonResult<Object> result = somaticServiceClient.chartAdvData(param);
        result.checkError();
        return result.getData();
    }

    public ResponseEntity<byte[]> getAdvImg(String taskId, String name) {
        return somaticServiceClient.getAdvImg(taskId, name);
    }

    public SomaticAdvValidateResultVO validateMafFile(SomaticFileVO mafFileVo, CurrentUser user) {
        final CommonResult<SomaticAdvValidateResultVO> result = somaticServiceClient.validateMafFile(mafFileVo, user.getUsername());
        result.checkError();
        return result.getData();
    }

    public SomaticTaskIdVO findSomaticAdvIdInfo(String userId) {
        return somaticServiceClient.findSomaticAdvIdInfo(userId).getData();
    }
}
