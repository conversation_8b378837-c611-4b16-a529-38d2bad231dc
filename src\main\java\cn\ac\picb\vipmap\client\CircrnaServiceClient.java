package cn.ac.picb.vipmap.client;

import cn.ac.picb.circrna.client.CircrnaServiceApi;
import cn.ac.picb.vipmap.client.fallback.CircrnaServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/3/1 14:59
 */
@FeignClient(value = "rnaseq-circrna-service", fallback = CircrnaServiceClientFallback.class)
public interface CircrnaServiceClient extends CircrnaServiceApi {
}
