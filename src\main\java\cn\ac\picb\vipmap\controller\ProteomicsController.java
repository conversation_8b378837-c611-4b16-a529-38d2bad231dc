package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.proteomics.enums.*;
import cn.ac.picb.proteomics.po.ProteomicsTaskPO;
import cn.ac.picb.proteomics.vo.ProteomicsValidateResultVO;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.ProteomicsService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.ProteomicsTaskParam;
import cn.ac.picb.vipmap.vo.ProteomicsTaskSearchVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/analysis/proteomics")
@RequiredArgsConstructor
public class ProteomicsController {

    private final ProteomicsService proteomicsService;
    private final AppProperties appProperties;

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") ProteomicsTaskSearchVO queryVO, @ModelAttribute PageParam pageParam, Model model) {
        PageResult<ProteomicsTaskPO> pageResult = proteomicsService.findPage(user, queryVO, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = ProteomicsTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "proteomics/list";
    }

    @RequestMapping("/form")
    public String form(Model model) {
        final List<String> databases = Arrays.stream(DatabaseType.values()).map(DatabaseType::getDesc).collect(Collectors.toList());
        model.addAttribute("databases", databases);

        final List<String> standardLabels = Arrays.stream(StandardLabel.values()).map(StandardLabel::getDesc).collect(Collectors.toList());
        model.addAttribute("standardLabels", standardLabels);

        final List<String> isobaricLabels = Arrays.stream(IsobaricLabel.values()).map(IsobaricLabel::getDesc).collect(Collectors.toList());
        model.addAttribute("isobaricLabels", isobaricLabels);

        final List<String> instrumentTypes = Arrays.stream(InstrumentType.values()).map(InstrumentType::getDesc).collect(Collectors.toList());
        model.addAttribute("instrumentTypes", instrumentTypes);

        final Map<Integer, DigestionMode> digestionModes = Arrays.stream(DigestionMode.values()).collect(Collectors.toMap(DigestionMode::getCode, Function.identity()));
        model.addAttribute("digestionModes", digestionModes);
        return "proteomics/form";
    }

    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated ProteomicsTaskParam param) {
        String taskId = proteomicsService.createTask(user, param);
        return success(taskId);
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        proteomicsService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        ProteomicsTaskPO task = proteomicsService.findById(id);
        model.addAttribute("task", task);

        Map<Integer, String> codeDescMap = ProteomicsTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "proteomics/detail";
    }

    @RequestMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemplate() {
        Response response = proteomicsService.downloadTemplate();
        ResponseUtil.download(response);
    }

    @RequestMapping("/uploadTemplate")
    @ResponseBody
    public CommonResult<List<ProteomicsValidateResultVO>> uploadTemplate(CurrentUser user, MultipartFile file) {
        List<ProteomicsValidateResultVO> vos = proteomicsService.uploadTemplate(file, user);
        return success(vos);
    }

    @RequestMapping("/result/{id}")
    @ResponseBody
    public void downloadResult(@PathVariable("id") String id) {
        Response response = proteomicsService.downloadResult(id);
        ResponseUtil.download(response);
    }
}
