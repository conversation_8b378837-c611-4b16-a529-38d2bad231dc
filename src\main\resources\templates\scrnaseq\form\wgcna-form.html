<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link type="text/css" rel="stylesheet" th:href="@{/select2/select2.min.css}"/>
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-advanced-wgcna')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq</h4>

                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/form/wgcna}" class="active">Add Task</a>
                            <a th:href="@{/analysis/scrnaseq/list(type='advanced')}">Task list</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <form id="form" class="form-custom form-inline form-task mt-2" style="padding: 0 15px;">
                            <div class="form-group-box min-width" style="border-top: transparent;">
                                <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">
                                    Input</a>
                                <div class="collapse show" id="coll-1">
                                    <div class="pl-4 pt-2">
                                        <h6 class="text-primary border-bottom pb-2">Search</h6>
                                        <input type="hidden" name="type" value="baseline">
                                        <div class="d-flex flex-wrap pl-4 pt-2">
                                            <div class="form-group row align-items-center">
                                                <label class="cmx-2 font-12">Cluster
                                                    Description ID</label>
                                                <div class="col-xl-4 col-lg-4 col-md-4 search">
                                                    <input class="form-control form-control-sm width-100 "
                                                           name="searchTaskId"
                                                           type="text">
                                                </div>
                                            </div>
                                            <div class="basic-name form-group">
                                                <label class="mx-2 font-12">Task Name</label>
                                                <div class="col-xl-4 col-lg-4 col-md-4 search">
                                                    <input class="form-control form-control-sm width-100"
                                                           name="searchTaskName" type="text"
                                                           onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                </div>
                                            </div>
                                            <div class="form-group ml">
                                                <label class="mx-2 font-12">Time</label>
                                                <div class="input-daterange input-group">
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="start"/>
                                                    <div class="input-group-append">
                                                            <span class="input-group-text"><i
                                                                    class="fa fa-calendar"></i></span>
                                                    </div>
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="end"/>
                                                    <div class="input-group-append">
                                                            <span class="input-group-text"><i
                                                                    class="fa fa-calendar"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" id="search-btn" onclick="filterTask()"
                                                    class="btn btn-primary btn-sm h-31">Search
                                            </button>
                                        </div>
                                        <div id="table-result">
                                            <table
                                                    class="table table-bordered table-sm table-center table-middle font-12">
                                                <colgroup>
                                                    <col width="30px"></col>
                                                    <col width="80px"></col>
                                                    <col width="110px"></col>
                                                    <col width="100px"></col>
                                                    <col width="100px"></col>
                                                    <col width="100px"></col>
                                                    <col width="100px"></col>
                                                    <col width="100px"></col>
                                                    <col width="90px"></col>
                                                    <col width="90px"></col>
                                                    <col width="90px"></col>
                                                </colgroup>
                                                <thead>
                                                <tr class="thead-light">
                                                    <th></th>
                                                    <th>Parent ID</th>
                                                    <th>Parent Name</th>
                                                    <th>Parent Type</th>
                                                    <th>Task ID</th>
                                                    <th>Task Name</th>
                                                    <th>Task Type</th>
                                                    <th>Speices</th>
                                                    <th>Start time</th>
                                                    <th>Status time</th>
                                                    <th>Consuming</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr class="w-100">
                                                    <td colspan="11" class="text-center">
                                                        <div class="spinner-border text-muted"></div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>

                                        </div>


                                        <h6 class="text-primary border-bottom pb-2 mt-4">Select</h6>
                                        <div class="pl-4 pt-2">
                                            <div class="form-group row align-items-center mb-0">
                                                <span>WGCNA will be done based on Cluster Description:<span
                                                        class="text-primary" id="baselineIdSpan"></span></span>
                                            </div>

                                            <div class="basic-name form-group row align-items-center mb-0"
                                                 style="margin-top: 8px;">
                                                <label>Current Task Name</label>
                                                <input class="form-control input-name validate[required]"
                                                       name="taskName" type="text"
                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group-box min-width not-bb">
                                <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                                <div class="collapse show" id="coll-2">
                                    <div class="form-group row" style="padding-top: 15px;">
                                        <div class="form-group-box min-width"
                                             style="border-top: transparent;padding-left: 50px;">
                                            <a href="#coll-11" data-toggle="collapse" class="h6 text-primary">Gene
                                                ontology</a>
                                            <div class="collapse show" id="coll-11">
                                                <div class="col-xl-4 col-lg-4 col-md-4">
                                                    <div class="form-group row align-items-center"
                                                         style="margin: 10px;width: 100%;">
                                                        <label class="cmx-2">Species</label>
                                                        <div class="col-xl-4 col-lg-4 col-md-4"
                                                             style="margin-left: 120px;position: relative;left: 10px;">
                                                            <div class="input-group">
                                                                <select class="form-control select2 form-control-sm"
                                                                        style="width: 300px;" name="species">
                                                                    <option value="human">Homo sapiens（human）&nbsp;&nbsp;&nbsp;
                                                                    </option>
                                                                    <option value="mouse">Mus musculus（mouse）&nbsp;&nbsp;&nbsp;
                                                                    </option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group row align-items-center"
                                                         style="margin: 10px;">
                                                        <label class="cmx-2">Method</label>
                                                        <div class="col-xl-4 col-lg-4 col-md-4"
                                                             style="margin-left: 110px;position: relative;left: 30px;">
                                                            <div
                                                                    class="custom-control custom-radio custom-control-inline">
                                                                <input type="radio" class="custom-control-input"
                                                                       id="am1" checked name="method"
                                                                       style="width: 200px;" value="1">
                                                                <label for="am1" class="custom-control-label"
                                                                       style="width: 130px;">Fisher’s exact
                                                                    test</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group-box min-width not-bb" style="padding-left: 50px;">
                                            <a href="#coll-22" data-toggle="collapse"
                                               class="h6 text-primary">Differential Analysis</a>

                                            <div class="collapse show" id="coll-22">
                                                <div class="pl-4 pt-2">
                                                    <div class="form-group row align-items-center"
                                                         style="margin: 10px;">
                                                        <label class="cmx-2">Power</label>
                                                        <a class="text-danger ml-1" data-toggle="tooltip"
                                                           data-placement="top"
                                                           title="soft-thresholding power for building scale-free network construction">
                                                            <i class="fa fa-question-circle"></i>
                                                        </a>
                                                        <div class="col-xl-4 col-lg-4 col-md-4"
                                                             style="margin-left: 115px;">
                                                            <div class="input-group">
                                                                <div class="input-group mt-1 mb-1">
                                                                    <input style="width: 200px;" type="number"
                                                                           class="form-control power validate[required]"
                                                                           value="15" name="power"
                                                                           oninput="if(value>40)value='';if(value<1)value='' ">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group row align-items-center"
                                                         style="margin: 10px;">
                                                        <label class="cmx-2 ">Network type</label>
                                                        <a class="text-danger ml-1" data-toggle="tooltip"
                                                           data-placement="top"
                                                           title="determines whether a correlation, or a distance network will be calculated">
                                                            <i class="fa fa-question-circle"></i>
                                                        </a>
                                                        <div class="col-xl-4 col-lg-4 col-md-4"
                                                             style="margin-left: 73px;">
                                                            <div class="input-group">
                                                                <select class="form-control select2 form-control-sm validate[required]"
                                                                        name="networkType"
                                                                        style="width: 200px;">
                                                                    <option value="signed">signed</option>
                                                                    <option value="unsigned">unsigned</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group row align-items-center"
                                                         style="margin: 10px;">
                                                        <label class="cmx-2 ">Minimum module size</label>
                                                        <a class="text-danger ml-1" data-toggle="tooltip"
                                                           data-placement="top"
                                                           title="minimum number of genes contained in a module">
                                                            <i class="fa fa-question-circle"></i>
                                                        </a>
                                                        <div class="col-xl-4 col-lg-4 col-md-4"
                                                             style="margin-left: 20px;">
                                                            <div class="input-group mt-1 mb-1"
                                                                 style="width: 200px;">
                                                                <input type="number" name="minModuleSize"
                                                                       class="form-control minmodulesize validate[required]"
                                                                       value=10
                                                                       oninput="if(value>100)value='';if(value<1)value='' ">
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group row align-items-center"
                                                         style="margin: 10px;">
                                                        <label class="cmx-2 ">merge cutoff height</label>
                                                        <a class="text-danger ml-1" data-toggle="tooltip"
                                                           data-placement="top"
                                                           title="dendrogram cut height for module merging">
                                                            <i class="fa fa-question-circle"></i>
                                                        </a>
                                                        <div class="col-xl-4 col-lg-4 col-md-4"
                                                             style="margin-left: 35px;">
                                                            <div style="width: 200px;" class="input-group mb-1">
                                                                <input type="number" name="mergeCutHeight"
                                                                       class="form-control mergecutheight validate[required] validate[maxSize[8]]"
                                                                       value=0.2
                                                                       oninput="if(value>1)value='';if(value<0)value=''"
                                                                       step="0.1"/>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <!--                提交-->
                <div class="text-center w-100">
                    <a href="javascript:void(0)" id="submitBtn" onclick="save()"
                       class="btn btn-outline-primary btn-custom"><span>Submit</span><i
                            class="fa fa-long-arrow-right"></i></a>
                </div>
            </main>
        </div>

        <!-- 原本树形结构 -->

    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script th:src="@{/js/cellType.js}"></script>
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-zh_CN.js}"></script>
    <script th:src="@{/select2/select2.min.js}"></script>
    <script th:src="@{/js/adv-filter-table.js}"></script>
    <script>
      function save () {
        if (!$("#form").validationEngine('validate')) {
          return
        }
        var sout = $("#form").serialize()
        // console.log(sout)
        $('#submitBtn').removeAttr('onclick')
        $.ajax({
          url: "/analysis/scrnaseq/saveWganc",
          data: $("#form").serialize(),
          type: "post",
          dataType: 'json',
          success: function (result) {
            if (result.success) {
              window.location.href = '[[@{/analysis/scrnaseq/list}]]?type=advanced'
            } else {
              layer.msg(result.message)
              $('#submitBtn').attr('onclick', 'save()')
            }
          }
        })
      }
    </script>
</th:block>
