package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.methychip.po.MethyChipTaskPO;
import cn.ac.picb.methychip.vo.*;
import cn.ac.picb.vipmap.client.MethyChipServiceClient;
import feign.Response;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:19
 */
@Component
public class MethyChipServiceClientFallback implements MethyChipServiceClient {
    private final static String SERVER_NAME = "methychip-service";

    @Override
    public CommonResult<MethyChipTaskPO> saveTask(MethyChipTaskParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadTemplateExcel() {
        return null;
    }

    @Override
    public CommonResult<List<MethyChipTaskInput>> uploadTemplate(String username, MultipartFile excel) {
        return serverError(SERVER_NAME);

    }

    @Override
    public CommonResult<PageResult<MethyChipTaskPO>> findTaskPage(MethyChipTaskQueryVO query) {
        return serverError(SERVER_NAME);

    }

    @Override
    public CommonResult<MethyChipTaskPO> deleteById(String id) {
        return serverError(SERVER_NAME);

    }

    @Override
    public CommonResult<MethyChipTaskVO> findDetailById(String id) {
        return serverError(SERVER_NAME);

    }

    @Override
    public Response downloadResult(String taskId, String displayName) {
        return null;
    }

    @Override
    public CommonResult<Object> chartData(ChartParamVO param) {
        return serverError(SERVER_NAME);
    }
}
