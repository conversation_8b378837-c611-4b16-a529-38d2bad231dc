[{"id": "1", "shape": "lane", "site": "start", "position": {"x": -300, "y": 60}, "label": "fastq, fq.gz, bam, txt, xls"}, {"id": "2", "shape": "lane", "position": {"x": -100, "y": 60}, "label": "html,JPG"}, {"id": "3", "shape": "lane", "position": {"x": 100, "y": 60}, "label": "bam"}, {"id": "4", "shape": "lane", "position": {"x": 300, "y": 60}, "label": "xls,txt,tsv,vcf"}, {"id": "5", "shape": "lane", "position": {"x": 500, "y": 60}, "label": "xls,txt,tsv,vcf"}, {"id": "6", "shape": "lane", "position": {"x": 700, "y": 60}, "label": "xls, txt, tsv, vcf, jpg"}, {"id": "7", "shape": "lane", "site": "end", "position": {"x": 900, "y": 60}, "label": "png,pdf"}, {"id": "RNASeq-DEG", "shape": "lane-polygon", "position": {"x": -260, "y": 150}, "label": "RNASeq-DEG", "attrs": {"text2": {"text": "rawdata"}}, "parent": "1", "data": {"state": "R1", "to": "rnaseq"}, "ports": [{"id": "RNASeq-DEG-right", "group": "right"}]}, {"id": "RNASeq-DEG2", "shape": "lane-polygon", "position": {"x": -260, "y": 230}, "label": "RNASeq-DEG", "attrs": {"text2": {"text": "bamfile"}}, "data": {"state": "R2", "to": "rnaseq"}, "parent": "1", "ports": [{"id": "RNASeq-DEG2-right", "group": "right"}]}, {"id": "Start2", "shape": "lane-polygon", "position": {"x": -260, "y": 310}, "label": "RNASeq-ASE", "attrs": {"text2": {"text": "rawdata"}}, "parent": "1", "data": {"state": "R3", "to": "ase"}, "ports": [{"id": "Start2-right", "group": "right"}]}, {"id": "Start3", "shape": "lane-polygon", "position": {"x": -260, "y": 390}, "label": "RNASeq-CircRNA", "attrs": {"text2": {"text": "rawdata"}}, "parent": "1", "data": {"state": "R4"}, "ports": [{"id": "Start3-right", "group": "right"}]}, {"id": "Start4", "shape": "lane-polygon", "position": {"x": -260, "y": 470}, "label": "WES/WGS-somatic", "attrs": {"text2": {"text": "rawdata"}}, "parent": "1", "data": {"state": "R5"}, "ports": [{"id": "Start4-right", "group": "right"}]}, {"id": "Start5", "shape": "lane-polygon", "position": {"x": -260, "y": 550}, "label": "WES/WGS-grmline", "attrs": {"text2": {"text": "rawdata"}}, "parent": "1", "data": {"state": "R6"}}, {"id": "Start6", "shape": "lane-polygon", "position": {"x": -260, "y": 630}, "label": "ChIP-Seq", "attrs": {"text2": {"text": "rawdata"}}, "parent": "1", "data": {"state": "R7"}}, {"id": "Start7", "shape": "lane-polygon", "position": {"x": -260, "y": 710}, "label": "ScRNA-Seq", "attrs": {"text2": {"text": "rawdata"}}, "ports": [{"id": "RNASeq-DEG-right", "group": "right"}], "parent": "1", "data": {"state": "R8"}}, {"id": "Start8", "shape": "lane-polygon", "position": {"x": -260, "y": 790}, "label": "ScRNA-Seq", "attrs": {"text2": {"text": "count matrix"}}, "parent": "1", "data": {"state": "R9"}}, {"id": "NGSQCToolkit", "shape": "lane-rect", "position": {"x": -40, "y": 235}, "label": "NGSQCToolkit", "parent": "2", "data": {"state": ["R1", "R3", "R5"]}, "ports": [{"id": "NGSQCToolkit-left", "group": "left"}, {"id": "NGSQCToolkit-right", "group": "right"}]}, {"id": "Trimmomatic", "shape": "lane-rect", "position": {"x": -40, "y": 400}, "data": {"state": ["R3"]}, "ports": [{"id": "Trimmomatic-left", "group": "left"}, {"id": "Trimmomatic-right", "group": "right"}], "label": "Trimmomatic", "parent": "2"}, {"id": "FastQc", "shape": "lane-rect", "position": {"x": -40, "y": 530}, "data": {"version": "v1.5"}, "label": "FastQc", "parent": "2"}, {"id": "trim_galore", "shape": "lane-rect", "position": {"x": -40, "y": 640}, "label": "trim_galore", "parent": "2"}, {"id": "Cell Ranger2", "shape": "lane-rect", "position": {"x": -40, "y": 720}, "label": "Cell Ranger", "parent": "2"}, {"id": "HISAT2", "shape": "lane-rect", "position": {"x": 160, "y": 160}, "data": {"state": ["R1"], "version": "v1.5"}, "label": "HISAT2", "parent": "3", "ports": [{"id": "HISAT2-left", "group": "left"}, {"id": "HISAT2-right", "group": "right"}]}, {"id": "STAR3", "shape": "lane-rect", "position": {"x": 160, "y": 235}, "label": "STAR", "parent": "3", "data": {"state": ["R1", "R3", "R5"]}, "ports": [{"id": "STAR3-left", "group": "left"}, {"id": "STAR3-right", "group": "right"}]}, {"id": "RSEM:STAR", "shape": "lane-rect", "position": {"x": 160, "y": 310}, "label": "RSEM:STAR", "parent": "3", "ports": [{"id": "RSEM:STAR-left", "group": "left"}, {"id": "RSEM:STAR-right", "group": "right"}]}, {"id": "MapSplice", "shape": "lane-rect", "position": {"x": 160, "y": 380}, "data": {"version": "v1.5"}, "label": "MapSplice", "parent": "3", "ports": [{"id": "MapSplice-left", "group": "left"}, {"id": "MapSplice-right", "group": "right"}]}, {"id": "BWA", "shape": "lane-rect", "position": {"x": 160, "y": 450}, "label": "BWA", "parent": "3"}, {"id": "Marker Duplicates", "shape": "lane-rect", "position": {"x": 170, "y": 510}, "label": "Marker \nDuplicates", "parent": "3"}, {"id": "GATK4:BQSR", "shape": "lane-rect", "position": {"x": 170, "y": 580}, "data": {"version": "v1.5"}, "label": "GATK4:BQSR", "parent": "3"}, {"id": "Bowtie2", "shape": "lane-rect", "position": {"x": 160, "y": 650}, "label": "Bowtie2", "parent": "3"}, {"id": "Cell Ranger:STAR", "shape": "lane-rect", "position": {"x": 160, "y": 730}, "label": "Cell Ranger:\nSTAR", "parent": "3"}, {"id": "featureCounts", "shape": "lane-rect", "position": {"x": 360, "y": 130}, "data": {"state": ["R1", "R2", "R3"], "version": "v1.5"}, "label": "featureCounts", "parent": "4", "ports": [{"id": "featureCounts-left", "group": "left"}, {"id": "featureCounts-right", "group": "right"}]}, {"id": "HTSeq", "shape": "lane-rect", "position": {"x": 360, "y": 200}, "label": "HTSeq", "parent": "4", "data": {"state": ["R1", "R2", "R3"]}, "ports": [{"id": "HTSeq-left", "group": "left"}, {"id": "HTSeq-right", "group": "right"}]}, {"id": "RSEM", "shape": "lane-rect", "position": {"x": 360, "y": 270}, "data": {"state": ["R1", "R3"]}, "label": "RSEM", "parent": "4", "ports": [{"id": "RSEM-left", "group": "left"}, {"id": "RSEM-right", "group": "right"}]}, {"id": "GATK4:FiterV<PERSON>ts", "shape": "lane-rect", "position": {"x": 560, "y": 550}, "data": {"version": "v1.5"}, "label": "GATK4:\nFiterV<PERSON>ts", "parent": "5"}, {"id": "GATK4: VQSR", "shape": "lane-rect", "position": {"x": 560, "y": 610}, "label": "GATK4: VQSR", "parent": "5"}, {"id": "<PERSON><PERSON><PERSON>", "shape": "lane-rect", "position": {"x": 560, "y": 800}, "data": {"version": "v1.5"}, "label": "<PERSON><PERSON><PERSON>", "parent": "5"}, {"id": "DESeq2", "shape": "lane-rect", "position": {"x": 760, "y": 200}, "data": {"state": ["R1", "R2", "R3"]}, "label": "DESeq2", "parent": "6", "ports": [{"id": "DESeq2-left", "group": "left"}, {"id": "DESeq2-right", "group": "right"}]}, {"id": "GOKEGGEnrichment", "shape": "lane-rect", "position": {"x": 760, "y": 270}, "data": {"state": ["R1", "R2", "R3"]}, "label": "GO/KEGG\nEnrichment", "parent": "6", "ports": [{"id": "GOKEGGEnrichment-left", "group": "left"}, {"id": "GOKEGGEnrichment-right", "group": "right"}]}, {"id": "Heatmap", "shape": "lane-rect-end", "position": {"x": 960, "y": 150}, "label": "Heatmap", "parent": "7", "data": {"state": ["R1", "R2", "R3"], "version": "v1.5"}, "ports": [{"id": "Heatmap-left", "group": "left"}, {"id": "Heatmap-right", "group": "right"}]}, {"id": "VolcanoPlot", "shape": "lane-rect-end", "position": {"x": 960, "y": 200}, "data": {"state": ["R1", "R2", "R3"]}, "label": "VolcanoPlot", "parent": "7", "ports": [{"id": "VolcanoPlot-left", "group": "left"}, {"id": "VolcanoPlot-right", "group": "right"}]}, {"id": "Maplot", "shape": "lane-rect-end", "position": {"x": 960, "y": 250}, "data": {"version": "v1.5", "state": ["R1", "R2", "R3"]}, "label": "Maplot", "parent": "7", "ports": [{"id": "Maplot-left", "group": "left"}, {"id": "Maplot-right", "group": "right"}]}, {"id": "RNASeq-1", "shape": "lane-edge", "source": {"cell": "RNASeq-DEG", "port": "RNASeq-DEG-right"}, "target": {"cell": "NGSQCToolkit", "port": "NGSQCToolkit-left"}}, {"id": "RNASeq-2", "shape": "lane-edge", "source": {"cell": "RNASeq-DEG", "port": "RNASeq-DEG-right"}, "target": {"cell": "Trimmomatic", "port": "Trimmomatic-left"}}, {"id": "NGSQ-1", "shape": "lane-edge", "source": {"cell": "NGSQCToolkit", "port": "NGSQCToolkit-right"}, "target": {"cell": "STAR3", "port": "STAR3-left"}}, {"id": "NGSQ-2", "shape": "lane-edge", "source": {"cell": "NGSQCToolkit", "port": "NGSQCToolkit-right"}, "target": {"cell": "HISAT2", "port": "HISAT2-left"}}, {"id": "NGSQ-3", "shape": "lane-edge", "source": {"cell": "NGSQCToolkit", "port": "NGSQCToolkit-right"}, "target": {"cell": "RSEM:STAR", "port": "RSEM:STAR-left"}}, {"id": "NGSQ-4", "shape": "lane-edge", "source": {"cell": "NGSQCToolkit", "port": "NGSQCToolkit-right"}, "target": {"cell": "MapSplice", "port": "MapSplice-left"}}, {"id": "HISAT2-1", "shape": "lane-edge", "source": {"cell": "HISAT2", "port": "HISAT2-right"}, "target": {"cell": "featureCounts", "port": "featureCounts-left"}}, {"id": "HISAT2-2", "shape": "lane-edge", "source": {"cell": "HISAT2", "port": "HISAT2-right"}, "target": {"cell": "HTSeq", "port": "HTSeq-left"}}, {"id": "STAR3-1", "shape": "lane-edge", "source": {"cell": "STAR3", "port": "STAR3-right"}, "target": {"cell": "featureCounts", "port": "featureCounts-left"}}, {"id": "STAR3-2", "shape": "lane-edge", "source": {"cell": "STAR3", "port": "STAR3-right"}, "target": {"cell": "HTSeq", "port": "HTSeq-left"}}, {"id": "STAR3-3", "shape": "lane-edge", "source": {"cell": "STAR3", "port": "STAR3-right"}, "target": {"cell": "RSEM", "port": "RSEM-left"}}, {"id": "featureCounts-1", "shape": "lane-edge", "source": {"cell": "featureCounts", "port": "featureCounts-right"}, "target": {"cell": "DESeq2", "port": "DESeq2-left"}}, {"id": "HTSeq-1", "shape": "lane-edge", "source": {"cell": "HTSeq", "port": "HTSeq-right"}, "target": {"cell": "DESeq2", "port": "DESeq2-left"}}, {"id": "RSEM-1", "shape": "lane-edge", "source": {"cell": "RSEM", "port": "RSEM-right"}, "target": {"cell": "DESeq2", "port": "DESeq2-left"}}, {"id": "DESeq2-1", "shape": "lane-edge", "source": "DESeq2", "target": "GOKEGGEnrichment"}, {"id": "HTSeq-1", "shape": "lane-edge", "source": {"cell": "HTSeq", "port": "HTSeq-right"}, "target": {"cell": "DESeq2", "port": "DESeq2-left"}}, {"id": "DESeq2-4", "shape": "lane-edge", "source": {"cell": "DESeq2", "port": "DESeq2-right"}, "target": {"cell": "Heatmap", "port": "Heatmap-left"}}, {"id": "DESeq2-2", "shape": "lane-edge", "source": {"cell": "DESeq2", "port": "DESeq2-right"}, "target": {"cell": "VolcanoPlot", "port": "VolcanoPlot-left"}}, {"id": "DESeq2-3", "shape": "lane-edge", "source": {"cell": "DESeq2", "port": "DESeq2-right"}, "target": {"cell": "Maplot", "port": "Maplot-left"}}, {"id": "RNASeq-DEG2-1", "shape": "lane-edge", "vertices": [{"x": 230, "y": 300}], "source": {"cell": "RNASeq-DEG2", "port": "RNASeq-DEG2-right"}, "target": {"cell": "HTSeq", "port": "HTSeq-left"}}, {"id": "RNASeq-DEG2-2", "shape": "lane-edge", "vertices": [{"x": 200, "y": 300}], "source": {"cell": "RNASeq-DEG2", "port": "RNASeq-DEG2-right"}, "target": {"cell": "featureCounts", "port": "featureCounts-left"}}, {"id": "Start2-1", "shape": "lane-edge", "vertices": [{"x": -100, "y": 300}], "source": {"cell": "Start2", "port": "Start2-right"}, "target": {"cell": "NGSQCToolkit", "port": "NGSQCToolkit-left"}}, {"id": "Start2-2", "shape": "lane-edge", "source": {"cell": "Start2", "port": "Start2-right"}, "target": {"cell": "Trimmomatic", "port": "Trimmomatic-left"}}, {"id": "Start7-1", "shape": "lane-edge", "source": "Start7", "target": "Cell Ranger2"}, {"id": "Start8-1", "shape": "lane-edge", "source": "Start8", "target": "<PERSON><PERSON><PERSON>"}, {"id": "Cell Ranger2-1", "shape": "lane-edge", "source": "Cell Ranger2", "target": "Cell Ranger:STAR"}, {"id": "Input Data", "shape": "lane-top", "site": "start", "position": {"x": -300, "y": 60}, "label": "Input Data"}, {"id": "Quality Control", "shape": "lane-top", "position": {"x": -100, "y": 60}, "label": "Quality Control"}, {"id": "Mapping", "shape": "lane-top", "position": {"x": 100, "y": 60}, "label": "Mapping"}, {"id": "Quantitative & Qualitative", "shape": "lane-top", "position": {"x": 300, "y": 60}, "label": "Quantitative \n& Qualitative"}, {"id": "Normalization & Filtration", "shape": "lane-top", "position": {"x": 500, "y": 60}, "label": "Normalization \n& Filtration"}, {"id": "Basic analysis", "shape": "lane-top", "position": {"x": 700, "y": 60}, "label": "Normalization \n& Filtration"}, {"id": "Visualization", "shape": "lane-top", "site": "end", "position": {"x": 900, "y": 60}, "label": "Visualization"}]