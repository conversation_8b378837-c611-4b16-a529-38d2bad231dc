package cn.ac.picb.vipmap.controller;

import cn.ac.picb.common.framework.util.DownloadUtils;
import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.paean.dto.PaeanTaskDTO;
import cn.ac.picb.paean.enums.PaeanTaskStatus;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.PaeanService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.PaeanSearchVO;
import cn.ac.picb.vipmap.vo.PaeanTaskParam;
import cn.hutool.core.io.IoUtil;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/analysis/paean")
@RequiredArgsConstructor
public class PaeanController {

    private final PaeanService paeanService;
    private final AppProperties appProperties;

    @RequestMapping("/demo")
    public String demo() {
        return "paean/demo";
    }

    @RequestMapping("/downloadDemo")
    @SneakyThrows
    public void downloadDemo(HttpServletRequest request, HttpServletResponse response) {
        ClassPathResource resource = new ClassPathResource("demo/paean/SRR536344.tar.gz");
        InputStream is = resource.getInputStream();
        byte[] bytes = IoUtil.readBytes(is);
        DownloadUtils.download(request, response, bytes, "SRR536344.tar.gz");
    }

    @RequestMapping("/list")
    public String list(@ModelAttribute("query") PaeanSearchVO vo, PageParam pageParam, CurrentUser user, Model model) {
        PageResult<PaeanTaskDTO> pageResult = paeanService.findPage(user, vo, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = PaeanTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "paean/list";
    }

    @RequestMapping("/form")
    public String form() {
        return "paean/form";
    }

    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated PaeanTaskParam param) {
        String taskId = paeanService.createTask(user, param);
        return success(taskId);
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        paeanService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/batchDelete")
    @ResponseBody
    public CommonResult<Boolean> batchDelete(@RequestParam("ids[]") String[] ids) {
        paeanService.batchDeleteTask(ids);
        return success(true);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        PaeanTaskDTO vo = paeanService.findTaskDetail(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = PaeanTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "paean/detail";
    }

    @RequestMapping("/download")
    @ResponseBody
    public void downloadResult(String taskId) {
        Response response = paeanService.downloadResult(taskId);
        ResponseUtil.download(response);
    }
}
