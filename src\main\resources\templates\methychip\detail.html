<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('methylation-beadchip-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Methylation BeadChip</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                </div>
                <th:block th:switch="${vo.task.status}">
                    <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Analysis Error</p>
                    </div>

                    <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Prepared</p>
                    </div>
                    <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="4">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Analysis done</p>
                    </div>

                    <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="*">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                        <p class="m-0" th:text="${codeDescMap.get(vo.task.status)}">Data prepared</p>
                    </div>
                </th:block>

                <ul class="detail-info row mb-2">
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Task ID：</span>
                        <div th:text="${vo.task.taskId}">AA20200722001</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Task Name：</span>
                        <div th:text="${vo.task.taskName}">AA20200722001</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Start Time：</span>
                        <div th:text="${#dates.format(vo.task.createTime,'yyyy-MM-dd HH:mm:ss')}">2020-07-22</div>
                    </li>
                    <li class="col-xl-6 col-lg-6"><span class="text-muted">Consuming：</span>
                        <div th:text="${vo.task.useTime}">42分</div>
                    </li>
                </ul>
                <div class="form-group-box">
                    <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                    <div class="collapse show" id="coll-1">
                        <div class="pt-2 pl-2">
                            <div class="result-box">
                                <h6 class="border-bottom pb-2 m-0">Select Sample</h6>
                                <div class="table-responsive mb-3">
                                    <table class="table table-bordered table-sm table-middle mb-0">
                                        <thead class="thead-light">
                                        <tr>
                                            <td style="min-width: 120px" class="text-center">
                                                Data_Directory
                                            </td>
                                            <td style="min-width: 120px" class="text-center">
                                                Sample_Name
                                            </td>
                                            <td style="min-width: 120px" class="text-center">
                                                Sample_Plate
                                            </td>
                                            <td style="min-width: 120px" class="text-center">
                                                Sample_Group
                                            </td>
                                            <td style="min-width: 120px" class="text-center">Pool_ID
                                            </td>
                                            <td style="min-width: 120px" class="text-center">Project
                                            </td>
                                            <td style="min-width: 120px" class="text-center">
                                                Sample_Well
                                            </td>
                                            <td style="min-width: 120px" class="text-center">
                                                Sentrix_ID
                                            </td>
                                            <td style="min-width: 120px" class="text-center">
                                                Sentrix_Position
                                            </td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr th:each="input : ${vo.inputs}">
                                            <td class="text-center" th:text="${input.dataDirectory.path}"></td>
                                            <td class="text-center" th:text="${input.sampleName}"></td>
                                            <td class="text-center" th:text="${input.samplePlate}"></td>
                                            <td class="text-center" th:text="${input.sampleGroup}"></td>
                                            <td class="text-center" th:text="${input.poolId}"></td>
                                            <td class="text-center" th:text="${input.project}"></td>
                                            <td class="text-center" th:text="${input.sampleWell}"></td>
                                            <td class="text-center" th:text="${input.sentrixId}"></td>
                                            <td class="text-center" th:text="${input.sentrixPosition}"></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Microarray Type</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Type: </label>
                                    <div class="col-xl-6 col-lg-3 col-md-3">
                                        <span class="text-primary"
                                              th:if="${vo.task.arrayType == '450K'}">Infinium HumanMethylation450 BeadChip(450K)</span>
                                        <span class="text-primary"
                                              th:if="${vo.task.arrayType == 'EPIC'}">Infinium MethylationEPIC BeadChip(850K)</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Normalization</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">Method：</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" th:if="${vo.task.normMethod == 'BMIQ'}">BMIQ</span>
                                        <span class="text-primary" th:if="${vo.task.normMethod == 'SWAN'}">SWAN</span>
                                        <span class="text-primary" th:if="${vo.task.normMethod == 'PBC'}">PBC</span>
                                        <span class="text-primary"
                                              th:if="${vo.task.normMethod == 'FunctionalNormalize'}">FunctionalNormalize</span>
                                    </div>
                                </div>

                                <h6 class="border-bottom pb-2 m-0">Differentially Methodlated Probes</h6>
                                <div class="form-group row align-items-center m-0">
                                    <label class="col-xl-1 col-lg-2 col-md-2 col-form-label pr-0">p.adjust <</label>
                                    <div class="col-xl-3 col-lg-3 col-md-3">
                                        <span class="text-primary" id="cstat"
                                              th:text="${vo.task.adjPVal}">0.001</span>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group-box" th:if="${vo.task.status == 4}">
                    <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                    <div class="collapse show" id="coll-2">
                        <ul class="detail-info row ml-2 mt-3 mb-2">
                            <th:block th:if="${vo.task.status== 4}">
                                <li class="col-4"><p class="h6">Result download：</p>
                                    <div>
                                        <a th:href="@{/analysis/methychip/download(taskId=${vo.task.taskId})}"><i
                                                class="fa fa-download mr-1"></i>Download</a>
                                    </div>
                                </li>
                            </th:block>
                        </ul>
                    </div>
                    <h5 class="font-weight-bold text-center pb-2 m-0 mt-3" id="ma-title">MDS: Raw Data<br/>1000 most
                        variable positions</h5>
                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            <div class="d-flex justify-content-center">
                                <div id="chart-18" style="width: 100%;height: 400px"></div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3 mt-3">
                        <h6 class="text-muted ml-4">Group：</h6>
                        <select class="custom-select w-auto" id="group">
                            <option th:each="item : ${vo.groupTitleMap}"
                                    th:value="${item.key}"
                                    th:text="${item.key}"
                                    th:title="${item.value}"></option>
                        </select>
                    </div>
                    <h5 class="font-weight-bold text-center pb-2 m-0 mt-3" id="chart-19-title">differentially expressed
                        genes heatmap</h5>
                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            <div class="d-flex justify-content-center">
                                <div id="chart-19" style="width: 100%; height: 600px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>
    <script th:inline="javascript">

      $(document).ready(function () {
        initChart18()
        initChart19()

        $('#group').on('change', function () {
          initChart19()
        })
      })

      function initChart18 () {
        if (!document.getElementById('chart-18')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-18'))

        drawChart(myChart)

        function drawChart (chart) {
          chart.clear()
          chart.clear()

          $.ajax({
            url: '/analysis/methychip/chartData',
            data: {
              'taskId': [[${vo.task.taskId}]],
              'chartNo': 18,
            },
            beforeSend: function () {
              $("#chart-18").next().remove()
              $("#chart-18").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.error) {
                layer.msg(result.message)
                return
              }
              if (result.code !== 200) {
                $('#chart-18-title').show()
                $("#chart-18").hide()
                $("#chart-18").after('<div class="bg-light text-muted p-3">No Results</div>')
                return
              }
              $('#chart-18-title').hide()
              $("#chart-18").show()
              $("#chart-18").next().remove()

              // console.log(result)
              var data = result.data
              var legendData = []
              var series = []

              for (var group in data) {
                legendData.push(group)

                series.push({
                  name: group,
                  data: data[group].map(it => Object.values(it)),
                  type: 'scatter',
                  symbolSize: 10,
                  cursor: 'default',
                  itemStyle: {
                    color: getClusterColor(Object.keys(data).indexOf(group))
                  },
                  encode: {
                    x: 2,
                    y: 3
                  }
                })
              }
              // console.log(legendData, series)

              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                legend: {
                  data: legendData
                },
                // title: {
                //   text: 'MDS: Raw Data',
                //   left: "center",
                //   subtext: '1000 most variable positions',
                //   textStyle: {
                //     fontSize: 20
                //   },
                //   subtextStyle: {
                //     fontSize: 15
                //   }
                // },
                xAxis: {
                  name: '',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameLocation: 'center',
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },
                yAxis: {
                  name: '',
                  nameLocation: 'center',
                  nameTextStyle: {
                    fontWeight: 'bold'
                  },
                  nameGap: 35,
                  splitLine: {
                    show: false
                  }
                },

                tooltip: {
                  trigger: 'item',
                  axisPointer: {
                    type: 'shadow'
                  },
                  formatter: function (params) {
                    // console.log(params, params.data)
                    //<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#c23531;"></span>
                    return params.data[1]
                  }
                },
                series: series
              })
            }
          })
        }
      }

      function initChart19 () {
        if (!document.getElementById('chart-19')) {
          return
        }
        var myChart = echarts.init(document.getElementById('chart-19'))

        drawChart(myChart)

        function drawChart () {
          myChart.clear()
          $.ajax({
            url: '/analysis/methychip/chartData',
            data: {
              'taskId': [[${vo.task.taskId}]],
              'chartNo': 19,
              'group': $('#group').val()
            },
            beforeSend: function () {
              $("#chart-19").next().remove()
              $("#chart-19").show()
              myChart.showLoading()
            },
            complete: function () {
              myChart.hideLoading()
            },
            success: function (result) {
              if (result.error) {
                $('#chart-19-title').text($('#group option:selected').attr('title'))
                $('#chart-19-title').show()
                $("#chart-19").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                $("#chart-19").hide()
                return
              }
              $('#chart-19-title').hide()
              var data = result.data
              // console.log(data)
              var xData = data.xData, yData = data.yData, sData = data.data

              var seriesData = sData.map(function (item) {
                return [item[1], item[0], item[2] || '-']
              })

              myChart.setOption({
                toolbox: {
                  show: true,
                  feature: {
                    saveAsImage: {}
                  }
                },
                tooltip: {
                  position: 'top',
                  formatter: function (params) {
                    // console.log(params)
                    var value = Number.parseFloat(params.data[2]).toPrecision(3)
                    var p = Math.floor(Math.log(value) / Math.LN10)
                    var n = value * Math.pow(10, -p)
                    return `<b>Sample: </b>${params.name}<br/>
                            <b>ID: </b>${yData[params.data[1]]}<br/>
                            <b>Value: </b>${('' + n).substring(0, 4) + 'e' + p}<br/>`
                  }
                },
                title: {
                  text: $('#group option:selected').attr("title"),
                  left: "center",
                  textStyle: {
                    fontSize: 20
                  },
                },
                animation: false,
                grid: {
                  left: '3%',
                  right: '10%',
                  bottom: '12%',
                  containLabel: true
                },
                xAxis: {
                  type: 'category',
                  data: xData,
                  splitArea: {
                    show: true
                  },
                  // axisLabel: {
                  //   interval: 0,
                  //   rotate: 90
                  // },
                },
                yAxis: {
                  show: false,
                  type: 'category',
                  data: yData,
                  splitArea: {
                    show: true
                  },
                  triggerEvent: true
                },
                visualMap: {
                  min: 0,
                  max: 1,
                  align: 'bottom',
                  dimension: 2,
                  precision: 2,
                  calculable: true,
                  orient: 'vertical',
                  right: '0%',
                  align: 'left',
                  top: '40%',
                  inRange: {
                    color: ['#411d5a', '#335f88', '#54b968', '#dcd138']
                  }
                },
                series: [{
                  type: 'heatmap',
                  data: seriesData,
                  emphasis: {
                    itemStyle: {
                      shadowBlur: 10,
                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                  }
                }]
              })

              myChart.on('mouseover', function (params) {
                if (params.componentType === 'yAxis') {
                  var tt = $('#tip')
                  tt.html(params.value)
                  tt.css('left', params.event.event.offsetX + 30)
                  tt.css('top', params.event.event.offsetY + 550)
                  tt.show()
                }
              })
              myChart.on('mouseout', function () {
                $('#tip').hide()
              })
            }
          }
            )
        }
      }

    </script>
</th:block>
</html>
