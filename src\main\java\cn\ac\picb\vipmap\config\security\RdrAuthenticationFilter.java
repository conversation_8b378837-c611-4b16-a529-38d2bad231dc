package cn.ac.picb.vipmap.config.security;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * VipMap自定义认证过滤器
 * 参考RdrAuthenticationFilter实现，用于处理Token认证逻辑
 *
 * <AUTHOR>
 */
@Slf4j
public class RdrAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    public final String USER_PWD = "DEFAULT_PWD";

    protected RdrAuthenticationFilter(String defaultFilterProcessesUrl) {
        super(defaultFilterProcessesUrl);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException, IOException {
        String token = RdrUserDetailsService.obtainToken(request, response);

        // TOKEN != null 获取用户名
        if (StrUtil.isBlank(token)) {
            throw new RuntimeException("用户未登录");
        }

        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(token, this.USER_PWD);
        this.setDetails(request, authRequest);
        return this.getAuthenticationManager().authenticate(authRequest);
    }

    protected void setDetails(HttpServletRequest request, UsernamePasswordAuthenticationToken authRequest) {
        authRequest.setDetails(this.authenticationDetailsSource.buildDetails(request));
    }
}
