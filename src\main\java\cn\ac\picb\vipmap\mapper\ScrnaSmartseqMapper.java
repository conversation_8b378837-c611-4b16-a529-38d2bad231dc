package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskParamVO;
import cn.ac.picb.scrnasmartseq.vo.ScrnaSmartseqTaskQueryVO;
import cn.ac.picb.rnaseq.vo.RnaseqTaskQueryVO;
import cn.ac.picb.vipmap.vo.RnaseqTaskSearchVO;
import cn.ac.picb.vipmap.vo.ScrnaSmartseqTaskParam;
import cn.ac.picb.vipmap.vo.ScrnaSmartseqTaskSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ScrnaSmartseqMapper {

    ScrnaSmartseqMapper INSTANCE = Mappers.getMapper(ScrnaSmartseqMapper.class);

    ScrnaSmartseqTaskQueryVO convert(ScrnaSmartseqTaskSearchVO search);

    ScrnaSmartseqTaskParamVO convertToVO(ScrnaSmartseqTaskParam param);
}
