package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.somatic.dto.SomaticTaskDTO;
import cn.ac.picb.somatic.enums.SomaticTaskStatus;
import cn.ac.picb.somatic.po.SomaticTaskPO;
import cn.ac.picb.somatic.vo.*;
import cn.ac.picb.vipmap.client.SomaticServiceClient;
import cn.ac.picb.vipmap.mapper.SomaticMapper;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.SomaticTaskParam;
import cn.ac.picb.vipmap.vo.SomaticTaskSearchVO;
import cn.ac.picb.vipmap.vo.SomaticTaskVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SomaticService {

    private final SomaticServiceClient somaticServiceClient;

    public PageResult<SomaticTaskDTO> findPage(CurrentUser user, SomaticTaskSearchVO queryVO, PageParam pageParam) {
        SomaticTaskQueryVO vo = initFindPageParam(user, queryVO, pageParam);

        CommonResult<PageResult<SomaticTaskDTO>> result = somaticServiceClient.findTaskPage(vo);
        result.checkError();
        return result.getData();
    }

    public static final SomaticTaskQueryVO initFindPageParam(CurrentUser user, SomaticTaskSearchVO queryVO, PageParam pageParam) {
        SomaticTaskQueryVO vo = SomaticMapper.INSTANCE.convertToQueryVO(queryVO);
        vo.setUserId(user.getId());
        vo.setPage(pageParam.getPage());
        vo.setSize(pageParam.getSize());
        return vo;
    }

    public String createTask(CurrentUser user, SomaticTaskParam param) {
        SomaticTaskParamVO vo = SomaticMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<String> result = somaticServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<SomaticTaskPO> result = somaticServiceClient.deleteById(id);
        result.checkError();
    }

    public SomaticTaskVO findTaskVO(String id) {
        SomaticTaskVO vo = new SomaticTaskVO();
        CommonResult<SomaticTaskDTO> result = somaticServiceClient.findDetailById(id);
        result.checkError();
        SomaticTaskDTO dto = result.getData();
        vo.setTask(dto);

        SomaticTaskPO task = dto.getTask();
        if (task.getStatus().equals(SomaticTaskStatus.complete.getCode())) {
            CommonResult<SomaticTaskDetailVO> taskDetailVO = somaticServiceClient.findTaskDetailVO(task.getTaskId());
            taskDetailVO.checkError();
            SomaticTaskDetailVO data = taskDetailVO.getData();
            vo.setDetailVO(data);
        }
        return vo;
    }

    public ResponseEntity<byte[]> getBaseQualityImg(String taskId, String name) {
        return somaticServiceClient.getBaseQualityImg(taskId, name);
    }

    public ResponseEntity<byte[]> getGcContentImg(String taskId, String name) {
        return somaticServiceClient.getGcContentImg(taskId, name);
    }

    public ResponseEntity<byte[]> getGenomeFractionCoverageImg(String taskId, String name) {
        return somaticServiceClient.getGenomeFractionCoverageImg(taskId, name);
    }

    public Response downloadVcf(String taskId, String runName) {
        return somaticServiceClient.downloadVcf(taskId, runName, "");
    }

    public Response downloadAnnotatedFile(String taskId, String runName) {
        return somaticServiceClient.downloadAnnotatedFile(taskId, runName, "");
    }

    public Response downloadTemplate() {
        return somaticServiceClient.downloadTemplateExcel();
    }

    public List<SomaticValidateResultVO> uploadTemplate(MultipartFile file, CurrentUser user) {
        final CommonResult<List<SomaticValidateResultVO>> result = somaticServiceClient.uploadTemplateExcel(file, user.getUsername());
        result.checkError();
        return result.getData();
    }

    public SomaticTaskIdVO findSomaticIdInfo(String userId) {
        return somaticServiceClient.findSomaticIdInfo(userId).getData();
    }

}
