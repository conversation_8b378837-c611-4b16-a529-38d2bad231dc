<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <!-- 引入 AntV X6 的 CSS 样式文件 -->
    <link rel="stylesheet" th:href="@{/js/x6/index.css}"/>
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="card card-custom">
            <div class="card-body">
                <div class="d-flex flex-column">
                    <div class="text-intro">
                        <h5>ViPMAP is a visualization integrated precision medicine analytics platform, designed for
                            systematically and comprehensively analyzing high-throughput sequencing data via web
                            submission. Standardized analysis pipelines (as belows) will continue to be released
                            including RNASeq, scRNA-Seq, ChIP-Seq, WES/WGS and etc. Raw fastq data is acceptable for
                            routine primary analysis (Pipelines). Besides, several common result files can also be
                            submitted for follow-up advance personalized analysis (Tools). ViPMAP is free for
                            non-commercial users. It should be noted that because some project files are larger and
                            require higher computing resource allocation, it is recommended to contact us in
                            advance.</h5>
                    </div>
                    <!--<img th:src="@{/images/pic-home.png}" class="img-fluid" alt="">-->
                    <div id="container" style="width: 100%; height: 1500px; position: relative">
                        <div id="tooltipText" class="tooltiptext"></div>
                    </div>
                    <!--<button type="button" onclick="exprotData()">导出数据</button>-->

                    <h6 class="mt-4 mb-1 text-center">Standardized analysis pipelines and framework for diverse omics
                        data formats</h6>
                </div>
            </div>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <!-- 引入 AntV X6 的 JavaScript 文件 -->
    <script th:src="@{/js/x6/index.js}"></script>
    <!-- 注册节点类型，定义节点样式等 -->
    <script th:src="@{/js/x6/register-node.js}"></script>
    <script th:src="@{/js/tinycolor-min.js}"></script>
    <script>
        var _context_path = $("meta[name='_context_path']").attr("content")
        const graph = new X6.Graph({
            container: document.getElementById('container'),
            interacting: {
                nodeMovable: false,
            },
            connecting: {
                connector: {
                    name: 'smooth',
                    args: {
                        direction: 'H',
                    },
                },
            },
            // draggable: true, // 禁止拖动
            // 启用网格
            grid: true,
            // 禁用拖拽画布
            panning: false,
            // 禁用缩放画布
            zooming: false,
            // 禁用滚动条
            scroller: {
                enabled: false,
            },
        });
        graph.on('nodeTo', ({view, e, node}) => {
            e.stopPropagation();
            let to = node.store.data.data.to
            // console.log(node.store.data.data.to);
            window.open(`${_context_path}/${to}`, '_blank');
        });

        graph.on('node:mouseenter', ({e, node, view}) => {
            //鼠标进入开启悬浮窗
            if (node.shape !== 'lane' && node.shape !== 'lane-top') {
                var text = document.getElementById('tooltipText');
                if (node.data && node.data.version !== undefined) {
                    text.innerHTML = 'version:' + node.data.version;
                    text.style.display = 'block';
                    const x = node.store.data.position.x;
                    const y = node.store.data.position.y;
                    text.style.left = `${x + 300}px`;
                    text.style.top = `${y - 20}px`;
                } else {
                    text.style.display = 'none';
                }
            }
        });

        graph.on('node:mouseleave', ({node}) => {
            var text = document.getElementById('tooltipText');
            text.style.display = 'none';
        });

        // 点击高亮节点
        let currentNodes = [];
        let originalColor = new Map();
        let curEdge = [];

        function reset() {
            if (currentNodes.length !== 0) {
                currentNodes.forEach((n) => {
                    let originalColorsForCurrentNode = originalColor.get(n);
                    n.attr('body', {
                        fill: originalColorsForCurrentNode.fill,
                        stroke: originalColorsForCurrentNode.stroke,
                        strokeWidth: 1,
                    });
                });
            }
            if (curEdge.length !== 0) {
                curEdge.forEach((edge) => {
                    edge.attr('line', {
                        stroke: '#d7d5d5',
                        strokeWidth: 1,
                    });
                });
            }
        }

        graph.on('node:click', ({e, x, y, node, view}) => {
            // 恢复已经被点击过的节点和边的原始 fill 和 stroke 颜色
            reset()
            if (node.shape !== 'lane-polygon' && node.shape !== 'lane-polygon2' && node.shape !== 'lane-rect-left') {
                currentNodes = []
                return
            }
            //点击第一列已高亮的节点将其清空
            if (currentNodes.indexOf(node) !== -1) {
                currentNodes = []
                return
            }
            const nodesArray = []
            const state = node.data.state
            state.forEach(r => {
                graph.getNodes().forEach(node => {
                    if (node.data && node.data.state !== undefined && node.data.state.includes(r)) {
                        nodesArray.push(node)
                    }
                })
            })
            const nodesToHighlight = Array.from(new Set(nodesArray));
            // 存储高亮节点
            currentNodes = nodesToHighlight.slice();
            nodesToHighlight.forEach((node) => {
                // 获取节点的 fill 和 stroke
                let fill = node.attr('body/fill');
                let stroke = node.attr('body/stroke');
                const originalColorsForNode = {fill, stroke};
                if (!originalColor.has(node)) {
                    originalColor.set(node, originalColorsForNode);
                }
                // 使用 tinycolor2 计算加深后的颜色值
                let newFill = tinycolor(fill).darken(20).toString();
                let newStroke = tinycolor(stroke).darken(20).toString();
                // 修改节点的 fill 和 stroke
                node.attr('body', {
                    fill: newFill,
                    stroke: newStroke,
                    strokeWidth: 3,

                });
                const edges = graph.getEdges().filter((edge) => {
                    const source = edge.getSourceCell();
                    const target = edge.getTargetCell();
                    return (
                        nodesToHighlight.includes(source) &&
                        nodesToHighlight.includes(target)
                    );
                });
                // 存储边
                curEdge = edges.slice();
                edges.forEach((edge) => {
                    edge.attr('line', {
                        stroke: 'gray', // 将线条颜色加深为红色
                        strokeWidth: 2, // 将线条宽度加粗
                    });
                });
            });

        });

        let originData = [];
        const cells = [];
        fetch(`${_context_path}/data/swimlane.json`)
            .then((response) => response.json())
            .then((data) => {
                originData = JSON.parse(JSON.stringify(data));
                data.forEach((item) => {
                    if (item.shape === 'lane-edge') {
                        cells.push(graph.createEdge(item));
                    } else {
                        if (item.site) {
                            let fill = ''
                            if (item.shape === 'lane' || item.shape === 'lane-right') {
                                fill = item.site === 'start' ? '#60A917' : '#745E87'
                                const node = graph.createNode(item)
                                node.attr('name-rect', {
                                    fill: fill,
                                });
                                node.attr('rect2', {
                                    fill: fill,
                                });
                                cells.push(node);
                            } else {
                                fill = '#C4C4C4'
                                const node = graph.createNode(item)
                                node.attr('body', {
                                    fill: fill,
                                    stroke: '#ADADAD',
                                });
                                cells.push(node);
                            }
                        } else {
                            cells.push(graph.createNode(item));
                        }
                    }
                });
                graph.resetCells(cells);
                graph.zoomToFit({padding: 10, maxScale: 1});
            });

        graph.on('edge:mouseup', ({view, e, node}) => {
            const id = view.cell.id;
            const vertices = view.cell.vertices;
            originData.forEach((u) => {
                if (u.id === id) {
                    u.vertices = vertices;
                }
            });
        });
        graph.on('node:mouseup', ({view, e, node}) => {


            const {clientX, clientY} = e;
            const {x, y} = graph.clientToLocal(clientX, clientY);
            console.log(`{"x":${Math.floor(x)},"y":${Math.floor(y)}}`);
            const id = view.cell.id;
            const position = view.cell.store.data.position;
            originData.forEach((u) => {
                if (u.id === id) {
                    u.position.x = position.x
                    u.position.y = position.y
                }
            });
        });
        graph.on('edge:connected', ({edge}) => {
            originData.push({
                id: edge.id,
                shape: "lane-edge",
                source: edge.source,
                target: edge.target
            })
            // console.log('Edge connected:', edge);
        });

        function exprotData() {
            console.log(graph.toJSON());
            console.log(JSON.stringify(originData));
        }
    </script>
</th:block>


<style type="text/css">
    .tooltiptext {
        /* width: 120px; */
        background-color: black;
        color: #fff;
        border-radius: 6px;
        padding: 5px 15px;
        position: absolute;
        z-index: 1;
        display: none;
        font-size: 14px;
        /*transition: all 10s ease-out;*/
    }

    .tooltiptext::before {
        content: "";
        position: absolute;
        z-index: 1;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: black transparent transparent transparent;
    }
</style>
</html>
