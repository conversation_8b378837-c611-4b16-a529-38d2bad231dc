pipeline {

  agent any

    environment {
        DOCKER_CREDENTIAL_ID = 'dev-biosino-dockerhub-builder'
        KUBECONFIG_CREDENTIAL_ID = 'kubecfg-analysis-admin'
        REGISTRY = 'dev.biosino.org'
        DOCKERHUB_NAMESPACE = 'analysis'
        APP_NAME = 'vipmap-app'
        GITLAB_ADDRESS = 'dev.biosino.org/git'
        WAR_PKG = ""
        BUILD_DATE = sh(script: "date +%Y%m%d", returnStdout: true)
    }


    stages {

        stage ('checkout scm') {
            steps {
                checkout(scm)
            }
        }

        stage('build'){
            steps {
                echo "starting deploy....."
                sh "mvn  clean package -Dmaven.test.skip=true -P prod -U"
                sh 'docker build -f .ci/Dockerfile${WAR_PKG} -t $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:SNAPSHOT-$BRANCH_NAME-$BUILD_ID-$BUILD_DATE .'

            }
        }

        stage('tag && push'){

            steps {
                withCredentials([usernamePassword(passwordVariable : 'DOCKER_PASSWORD' ,usernameVariable : 'DOCKER_USERNAME' ,credentialsId : "$DOCKER_CREDENTIAL_ID" ,)]) {
                    sh 'echo "$DOCKER_PASSWORD" | docker login $REGISTRY -u "$DOCKER_USERNAME" --password-stdin'
                }
                  sh 'docker tag  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:SNAPSHOT-$BRANCH_NAME-$BUILD_ID-$BUILD_DATE $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:v$BUILD_ID-$BUILD_DATE'
                  sh 'docker push  $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:v$BUILD_ID-$BUILD_DATE'
            }
        }

        stage('deploy to dev') {
          steps {
            sh 'bash .ci/build-onlytag.sh'
            withKubeConfig( [ credentialsId: "$KUBECONFIG_CREDENTIAL_ID" ] ) {
               sh "kubectl -n analysis apply -f app_build.yaml --insecure-skip-tls-verify"
            }
          }
        }

    }
}
