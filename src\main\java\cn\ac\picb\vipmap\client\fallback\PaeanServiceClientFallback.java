package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.paean.dto.PaeanTaskDTO;
import cn.ac.picb.paean.po.PaeanTaskPO;
import cn.ac.picb.paean.vo.PaeanTaskParamVO;
import cn.ac.picb.paean.vo.PaeanTaskQueryVO;
import cn.ac.picb.vipmap.client.PaeanServiceClient;
import feign.Response;
import org.springframework.stereotype.Component;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class PaeanServiceClientFallback implements PaeanServiceClient {

    private final static String SERVER_NAME = "paean-service";

    @Override
    public CommonResult<PageResult<PaeanTaskDTO>> findTaskPage(PaeanTaskQueryVO paeanTaskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<String> saveTask(PaeanTaskParamVO paeanTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PaeanTaskPO> findById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PaeanTaskDTO> findDetailById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PaeanTaskPO> deleteById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Boolean> deleteByIds(String[] strings) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadResult(String s, String s1) {
        return null;
    }
}
