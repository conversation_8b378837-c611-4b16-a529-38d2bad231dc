<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-paean-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Paean</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/paean/form}" class="active">Add Task</a>
                            <a th:href="@{/analysis/paean/list}">Task List</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade">
                            <form id="form" class="py-2 px-4">
                                <div class="form-group row mb-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0 text-right"><span class="text-danger"><b>*</b></span> BAM File</label>
                                    <div class="col-xl-10 col-lg-9 col-md-8 td-input px-15">
                                        <div class="input-group input-group-sm">
                                            <div class="input-group-prepend">
                                                <button onclick="showFileModal(this, '.bam')" class="btn btn-primary btn-sm" type="button">Select</button>
                                            </div>
                                            <div class="input-group-prepend bam">
                                              <span class="input-group-text">
                                                <em class="seled"></em>
                                              </span>
                                            </div>
                                        </div>
                                        <div class="form-text text-muted">
                                            <i class="fa fa-info-circle mr-1"></i>Upload your RNA-seq BAM file and select
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row align-items-center mb-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0 text-right"><span class="text-danger"><b>*</b></span> Mode</label>
                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" class="custom-control-input" id="pe1" name="mode" value="2">
                                            <label for="pe1" class="custom-control-label">Pair-end</label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" class="custom-control-input" id="pe2" name="mode" value="1">
                                            <label for="pe2" class="custom-control-label">Single-end</label>
                                        </div>
                                    </div>
                                </div>
                                <h5 class="font-weight-bold font-16 pb-2 mb-2 border-bottom text-muted">Annotation</h5>
                                <div class="form-text text-muted pl-5 ml-4">
                                    <i class="fa fa-info-circle mr-1"></i>Paean‘s default annotation or upload your annotation file and select
                                </div>
                                <div class="form-group row align-items-center mb-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0 text-right">GFF File</label>
                                    <div class="col-xl-8 col-lg-9 col-md-8 td-input px-15">
                                        <div class="d-flex">
                                            <button onclick="selectDefault(this)" type="button" class="btn btn-primary btn-sm text-nowrap mr-2">
                                                Use Default
                                            </button>
                                            <div class="input-group input-group-sm">
                                                <div class="input-group-prepend">
                                                    <button onclick="showFileModal(this)" class="btn btn-secondary btn-sm" type="button">Select</button>
                                                </div>
                                                <div class="input-group-prepend gff">
                                                    <span class="input-group-text"><em class="seled"></em></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row align-items-center mb-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0 text-right">SE File</label>
                                    <div class="col-xl-8 col-lg-9 col-md-8 td-input px-15">
                                        <div class="d-flex">
                                            <button onclick="selectDefault(this)" type="button" class="btn btn-primary btn-sm text-nowrap mr-2">
                                                Use Default
                                            </button>
                                            <div class="input-group input-group-sm">
                                                <div class="input-group-prepend">
                                                    <button onclick="showFileModal(this)" class="btn btn-secondary btn-sm" type="button">Select</button>
                                                </div>
                                                <div class="input-group-prepend se">
                                                    <span class="input-group-text"><em class="seled"></em></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row align-items-center mb-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0 text-right">A3SS File</label>
                                    <div class="col-xl-8 col-lg-9 col-md-8 td-input px-15">
                                        <div class="d-flex">
                                            <button onclick="selectDefault(this)" type="button" class="btn btn-primary btn-sm text-nowrap mr-2">
                                                Use Default
                                            </button>
                                            <div class="input-group input-group-sm">
                                                <div class="input-group-prepend">
                                                    <button onclick="showFileModal(this)" class="btn btn-secondary btn-sm" type="button">Select</button>
                                                </div>
                                                <div class="input-group-prepend a3ss">
                                                    <span class="input-group-text"><em class="seled"></em></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row align-items-center mb-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0 text-right">A5SS File</label>
                                    <div class="col-xl-8 col-lg-9 col-md-8 td-input px-15">
                                        <div class="d-flex">
                                            <button onclick="selectDefault(this)" type="button" class="btn btn-primary btn-sm text-nowrap mr-2">
                                                Use Default
                                            </button>
                                            <div class="input-group input-group-sm">
                                                <div class="input-group-prepend">
                                                    <button onclick="showFileModal(this)" class="btn btn-secondary btn-sm" type="button">Select</button>
                                                </div>
                                                <div class="input-group-prepend a5ss">
                                                    <span class="input-group-text"><em class="seled"></em></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row align-items-center mb-0">
                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0 text-right">RI File</label>
                                    <div class="col-xl-8 col-lg-9 col-md-8 td-input px-15">
                                        <div class="d-flex">
                                            <button onclick="selectDefault(this)" type="button" class="btn btn-primary btn-sm text-nowrap mr-2">
                                                Use Default
                                            </button>
                                            <div class="input-group input-group-sm">
                                                <div class="input-group-prepend">
                                                    <button onclick="showFileModal(this)" class="btn btn-secondary btn-sm" type="button">Select</button>
                                                </div>
                                                <div class="input-group-prepend ri">
                                                    <span class="input-group-text"><em class="seled"></em></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center my-3">
                                    <a href="javascript:void(0);" onclick="submitForm(this)" class="btn btn-outline-primary btn-custom">
                                        <span>Start Your Paean job!</span><i class="fa fa-long-arrow-right"></i>
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <div id="file-modal"></div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>
    <script>
        $("#file-modal").fileModal('/analysis/fileTree');

        var _selectBtn;

        function showFileModal(_this, ext) {
            var func;
            if (ext) {
                func = function (data) {
                    return !data.name || /.*\.bam/.test(data.name);
                }
            }
            _selectBtn = _this;
            var selectIds = [];
            $(_this).parent().next().find("em.seled:first").find("b.text-primary").each(function () {
                selectIds.push($(this).find("input[type=hidden]:eq(0)").val());
            });
            $("#file-modal").trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: selectIds,
                func: func
            });
        }

        $("#file-modal").on('__SELECT_FILES__', function (e, data) {
            var nodes = data.nodes || []
            if (nodes.length === 0) {
                return;
            }
            var html = [];
            $.each(nodes, function (i, node) {
                var filePath = node.path;
                var fileName = node.name;
                var fileSize = node.size;
                html.push('<b class="text-primary lg" data-toggle="tooltip" title="' + fileName + '">' + fileName + '');
                html.push('<input type="hidden" value="' + filePath + '">');
                html.push('<input type="hidden" value="' + fileName + '">');
                html.push('<input type="hidden" value="' + fileSize + '">');
                html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>');
                html.push('</b>');
            });
            $(_selectBtn).parent().next().find("em.seled:first").html(html.join(''));
            $('[data-toggle="tooltip"]').tooltip();
            $(_selectBtn).removeClass('btn-secondary').addClass('btn-primary');
            $(_selectBtn).parent().parent().prev().removeClass('btn-primary').addClass('btn-secondary');
        });

        function removeFile(_this) {
            var parents = $(_this).parents("em.seled");
            $(_this).parent().parent().remove();
            if (parents.find('b.lg').length === 0) {
                var btn = $(parents).parents("div.d-flex:eq(0)").find("button");
                if (btn) {
                    btn.removeClass('btn-secondary').addClass('btn-primary')
                }
            }
        }

        function selectDefault(_this) {
            $(_this).removeClass('btn-secondary').addClass('btn-primary');
            $(_this).next().find('button').removeClass('btn-primary').addClass('btn-secondary')
            $(_this).next().find("em.seled").html('');
        }

        function submitForm(_this) {
            var formData = new FormData();
            var bamLength = $("div.bam").find("b.text-primary").length;
            if (bamLength <= 0) {
                layer.msg("please select BAM file");
                return;
            } else {
                $("div.bam").find("b.text-primary").each(function (index) {
                    formData.append("bamList[" + index + "].path", $(this).find("input[type=hidden]:eq(0)").val());
                    formData.append("bamList[" + index + "].name", $(this).find("input[type=hidden]:eq(1)").val());
                    formData.append("bamList[" + index + "].size", $(this).find("input[type=hidden]:eq(2)").val());
                })
            }

            var paeanMode = $(":radio[name='mode']:checked").val();
            if (!paeanMode) {
                layer.msg("please select mode");
                return;
            } else {
                formData.append("paeanMode", paeanMode);
            }

            var gffLength = $("div.gff").find("input[type=hidden]").length;
            if (gffLength === 3) {
                formData.append("gff.path", $("div.gff").find("input[type=hidden]:eq(0)").val());
                formData.append("gff.name", $("div.gff").find("input[type=hidden]:eq(1)").val());
                formData.append("gff.size", $("div.gff").find("input[type=hidden]:eq(2)").val());
            } else if (gffLength > 3) {
                layer.msg("please select one GFF file");
                return;
            }
            var seLen = $("div.se").find("input[type=hidden]").length;
            if (seLen === 3) {
                formData.append("se.path", $("div.se").find("input[type=hidden]:eq(0)").val());
                formData.append("se.name", $("div.se").find("input[type=hidden]:eq(1)").val());
                formData.append("se.size", $("div.se").find("input[type=hidden]:eq(2)").val());
            } else if (seLen > 3) {
                layer.msg("please select one SE file");
                return;
            }
            var a3ssLen = $("div.a3ss").find("input[type=hidden]").length;
            if (a3ssLen === 3) {
                formData.append("a3ss.path", $("div.a3ss").find("input[type=hidden]:eq(0)").val());
                formData.append("a3ss.name", $("div.a3ss").find("input[type=hidden]:eq(1)").val());
                formData.append("a3ss.size", $("div.a3ss").find("input[type=hidden]:eq(2)").val());
            } else if (a3ssLen > 3) {
                layer.msg("please select one A3SS file");
                return;
            }
            var a5ssLen = $("div.a5ss").find("input[type=hidden]").length;
            if (a5ssLen === 3) {
                formData.append("a5ss.path", $("div.a5ss").find("input[type=hidden]:eq(0)").val());
                formData.append("a5ss.name", $("div.a5ss").find("input[type=hidden]:eq(1)").val());
                formData.append("a5ss.size", $("div.a5ss").find("input[type=hidden]:eq(2)").val());
            } else if (a5ssLen === 3) {
                layer.msg("please select one a5ss FILE");
                return;
            }
            var riLen = $("div.ri").find("input[type=hidden]").length;
            if (riLen === 3) {
                formData.append("ri.path", $("div.ri").find("input[type=hidden]:eq(0)").val());
                formData.append("ri.name", $("div.ri").find("input[type=hidden]:eq(1)").val());
                formData.append("ri.size", $("div.ri").find("input[type=hidden]:eq(2)").val());
            } else if (riLen === 3) {
                layer.msg("please select one ri FILE");
                return;
            }
            if ($(_this).data('loading') == 'true') {
                return;
            }
            $(_this).data('loading', 'true');

            $.ajax({
                url: '/analysis/paean/createTask',
                method: 'post',
                dataType: 'json',
                contentType: false,
                processData: false,
                data: formData,
                success: function (result) {
                    if (result.success) {
                        layer.msg('submit success');
                        var batch = result.data;
                        setTimeout(function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            window.location.href = $.trim(_context_path) + '/analysis/paean/list?batch=' + batch;
                        }, 2000);
                    } else {
                        layer.msg(result.message);
                    }
                },
                complete: function () {
                    $(_this).data('loading', 'false');
                }
            });
        }
    </script>
</th:block>
</html>
