<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/bootstrap-tagsinput.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('somatic-adv-analysis')}"></div>
            <main th:with="currTaskPo=${somaticAdvTaskVO?.task?.advTask}">
                <h4 class="border-bottom pb-2 mb-3">WES/WGS-somatic Advanced Analysis</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/somatic-adv/form-adv/{id}(id=${id})}" class="active">Add Task</a>
                            <a th:href="@{/analysis/somatic-adv/list}">Task List</a>
                        </div>
                    </div>
                    <div class="list-group tab-content"
                         th:with="runNames=${somaticAdvTaskVO?.task?.runNames}, basicAnalysisIds=${somaticAdvTaskVO?.task?.basicAnalysisIds}">
                        <div class="tab-pane active show fade"
                             th:with="fromBasicAnalysis=${not #lists.isEmpty(runNames)}">
                            <div class="p-2">
                                <form action="" class="form-custom">
                                    <div class="form-group-box border-top-0">
                                        <h6 class="text-primary border-bottom pb-2">Input file</h6>
                                        <input type="hidden" name="id" th:value="${id}">
                                        <input type="hidden" name="fromBasicAnalysis" th:value="${fromBasicAnalysis}">

                                        <th:block th:unless="${fromBasicAnalysis}">
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-1 col-lg-3 col-md-3 col-form-label pr-0">maf
                                                    file</label>
                                                <div class="col-xl-5 col-lg-6 col-md-6">
                                                    <span class="text-primary"
                                                          th:text="${somaticAdvTaskVO?.task?.mafFileVo?.name}">xxxxxxxxxx</span>
                                                </div>
                                            </div>
                                        </th:block>

                                        <th:block th:if="${fromBasicAnalysis}">
                                            <div class="form-group row align-items-center m-0">
                                                <label class="col-xl-2 col-lg-3 col-md-3 col-form-label pr-0">Basic
                                                    Analysis
                                                    Task ID </label>
                                                <div class="col-xl-5 col-lg-6 col-md-6">
                                                    <span th:text="${currTaskPo?.basicBatchId}" class="text-primary">HG00114</span>
                                                </div>
                                            </div>

                                            <a href="#coll-1" data-toggle="collapse"
                                               class="h6 text-primary ml-2 d-block">Run Name</a>
                                            <div class="collapse show" id="coll-1">
                                                <div class="pl-4 pt-2">
                                                    <div class="result-box">
                                                        <div class="table-responsive my-2"
                                                             th:with="colSize=5, dataSize=${#lists.size(runNames)}">
                                                            <table class="table table-bordered table-sm table-center table-middle mb-1">
                                                                <tbody>
                                                                <th:block th:if="${dataSize gt colSize}"
                                                                          th:each="item,status:${runNames}">
                                                                    <p th:remove="tag"
                                                                       th:utext="${(status.index+1)%colSize==1 ? '&lt;tr&gt;':''}"/>
                                                                    <td>
                                                                        <a th:href="@{/analysis/somatic/{id}(id=${basicAnalysisIds.get(status.index)})}"
                                                                           th:text="${item}">RunName 1</a>
                                                                    </td>
                                                                    <p th:remove="tag"
                                                                       th:utext="${(status.index+1)%colSize==0 ? '&lt;/tr&gt;':''}"/>
                                                                </th:block>

                                                                <th:block th:unless="${dataSize gt colSize}">
                                                                    <tr>
                                                                        <td th:each="colIdx,status:${#numbers.sequence(1,colSize)}">
                                                                            <a th:if="${colIdx le dataSize}"
                                                                               th:text="${runNames.get(status.index)}"
                                                                               th:href="@{/analysis/somatic/{id}(id=${basicAnalysisIds.get(status.index)})}"></a>

                                                                            <span th:unless="${colIdx le dataSize}"> &nbsp;</span>
                                                                        </td>
                                                                    </tr>
                                                                </th:block>

                                                                </tbody>
                                                            </table>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </th:block>
                                        <hr>

                                        <div class="form-group row align-items-center">
                                            <label class="col-xl-3 col-lg-4 col-md-4 col-form-label pr-0">Advanced
                                                analysis Task Name</label>
                                            <div class="col-xl-4 col-lg-2 col-md-2">
                                                <input name="taskName" th:value="${currTaskPo?.taskName}" type="text"
                                                       class="form-control">
                                            </div>
                                        </div>

                                        <div th:unless="${fromBasicAnalysis}" class="form-group row align-items-center">
                                            <label class="col-xl-1 col-lg-3 col-md-4 col-form-label pr-0">Species</label>
                                            <div class="col-xl-3 col-lg-3 col-md-8">
                                                <select name="species" class="form-control"
                                                        onchange="changeVersion(this)">
                                                    <option value="">Choose..</option>
                                                    <option value="Homo sapiens">Homo sapiens(Human)</option>
                                                </select>
                                            </div>
                                            <label class="col-xl-1 col-lg-3 col-md-4 col-form-label">Version</label>
                                            <div class="col-xl-3 col-lg-3 col-md-4">
                                                <select name="specVersion"
                                                        th:with="currSpecVersion=${currTaskPo?.refVersion}"
                                                        class="form-control">
                                                    <!--/*
                                                    <option value="">Choose..</option>
                                                    <option th:selected="${#strings.equals(currSpecVersion,'hg38')}"
                                                            value="hg38">hg38(GRCh38)
                                                    </option>
                                                    <option th:selected="${#strings.equals(currSpecVersion,'hg19')}"
                                                            value="hg19">hg19(GRCh37)
                                                    </option>
                                                    */-->
                                                </select>
                                            </div>
                                        </div>

                                        <h6 class="text-primary border-bottom pb-2">Genomic Profiles & Statistically
                                            Significant Pairwise Relationships</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Interested
                                                    Genes</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="radio-collapse">
                                                        <div class="d-flex row align-items-center mb-2">
                                                            <div class="col-auto">
                                                                <div class="custom-control custom-radio mb-1">
                                                                    <input type="radio" id="gs1" name="interestedGenes"
                                                                           value="top"
                                                                           th:checked="${currTaskPo?.interestedGenesTop ne null} or (${currTaskPo?.interestedGenesTop eq null} and ${#strings.isEmpty(currTaskPo?.interestedGenesCustom)})"
                                                                           class="custom-control-input">
                                                                    <label class="custom-control-label"
                                                                           for="gs1">Top </label>
                                                                </div>
                                                            </div>
                                                            <div class="col-auto py-0">
                                                                <input name="interestedGenesTop" type="number" min="1"
                                                                       max="50"
                                                                       th:value="${#strings.isEmpty(currTaskPo?.interestedGenesTop)?'20':currTaskPo?.interestedGenesTop}"
                                                                       class="form-control width-100">
                                                            </div>
                                                            <div class="col-auto px-0">
                                                                genes
                                                                <span class="pl-1">
                                                                        <a href="javascript:;" class="text-danger"
                                                                           data-container="body" data-html="true"
                                                                           data-trigger="focus" data-toggle="popover"
                                                                           data-placement="top"
                                                                           data-content="Please enter a positive integer from 1 to 50."><i
                                                                                class="fa fa-question-circle"></i></a>
                                                                    </span>
                                                            </div>
                                                        </div>
                                                        <div class="d-flex row align-items-center">
                                                            <div class="col-auto">
                                                                <div class="custom-control custom-radio mb-1">
                                                                    <input type="radio" id="gs2" name="interestedGenes"
                                                                           value="custom"
                                                                           th:checked="${currTaskPo?.interestedGenesTop eq null} and ${not #strings.isEmpty(currTaskPo?.interestedGenesCustom)}"
                                                                           class="custom-control-input">
                                                                    <label class="custom-control-label" for="gs2">Custom
                                                                        gene sets</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div data-id="gs2">
                                                        <div class="tags_add">
                                                            <input name="interestedGenesCustom" type="text"
                                                                   class="form-control"
                                                                   th:value="${currTaskPo?.interestedGenesCustom}"
                                                                   data-role="tagsinput">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Tumor Mutational Burden(TMB)</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Capture
                                                    Size</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="d-flex radio-collapse py-2">
                                                        <div class="custom-control custom-radio custom-control-inline mr-5">
                                                            <input type="radio" value="Null"
                                                                   class="custom-control-input" id="cs1"
                                                                   name="captureSize"
                                                                   th:checked="${#strings.equals(currTaskPo?.captureSize,'Null')} or ${#strings.isEmpty(currTaskPo?.captureSize)}">
                                                            <label for="cs1" class="custom-control-label">Null</label>
                                                        </div>

                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" value="" class="custom-control-input"
                                                                   id="cs2"
                                                                   th:checked="${not #strings.equals(currTaskPo?.captureSize,'Null')} and ${not #strings.isEmpty(currTaskPo?.captureSize)}"
                                                                   name="captureSize">
                                                            <label for="cs2" class="custom-control-label">
                                                                <span class="d-flex align-items-center"><input
                                                                        type="number" name="captureSizeVal"
                                                                        th:value="${currTaskPo?.captureSize}"
                                                                        class="form-control form-control-sm width-100"> <span
                                                                        class="pl-1">Mb</span> </span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Driver Gene</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="d-flex radio-collapse py-2">
                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" value="oncodriveCLUST"
                                                                   class="custom-control-input" id="dg1"
                                                                   th:checked="${#strings.equals(currTaskPo?.driverGeneMethod,'oncodriveCLUST')} or ${#strings.isEmpty(currTaskPo?.driverGeneMethod)}"
                                                                   name="driverGeneMethod">
                                                            <label for="dg1"
                                                                   class="custom-control-label">oncodriveCLUST</label>
                                                        </div>

                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" value="MutsigCV"
                                                                   class="custom-control-input" id="dg2"
                                                                   th:checked="${#strings.equals(currTaskPo?.driverGeneMethod,'MutsigCV')}"
                                                                   name="driverGeneMethod">
                                                            <label for="dg2"
                                                                   class="custom-control-label">MutsigCV</label>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Mutation Signature Analysis</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Database</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="d-flex radio-collapse py-2">
                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" value="legacy"
                                                                   class="custom-control-input" id="msa1"
                                                                   th:checked="${#strings.equals(currTaskPo?.dataBase,'legacy')} or ${#strings.isEmpty(currTaskPo?.dataBase)}"
                                                                   name="dataBase">
                                                            <label for="msa1" class="custom-control-label">legacy <a
                                                                    href="javascript:;" class="text-danger"
                                                                    data-container="body" data-html="true"
                                                                    data-trigger="focus" data-toggle="popover"
                                                                    data-placement="top"
                                                                    data-content="It includes 30 signaures"><i
                                                                    class="fa fa-question-circle"></i></a></label>
                                                        </div>

                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" value="SBS" class="custom-control-input"
                                                                   id="msa2"
                                                                   th:checked="${#strings.equals(currTaskPo?.dataBase,'SBS')}"
                                                                   name="dataBase">
                                                            <label for="msa2" class="custom-control-label">SBS <a
                                                                    href="javascript:;" class="text-danger"
                                                                    data-container="body" data-html="true"
                                                                    data-trigger="focus" data-toggle="popover"
                                                                    data-placement="top"
                                                                    data-content="It includes updated/refined 65 signatures"><i
                                                                    class="fa fa-question-circle"></i></a></label>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </form>

                            </div>

                            <div class="text-center mb-3">
                                <a href="javascript:void(0);" onclick="submitForm(this)"
                                   class="btn btn-outline-primary btn-custom"><span>Advanced Analysis</span><i
                                        class="fa fa-long-arrow-right"></i></a>
                            </div>
                        </div>

                    </div>
                </div>
            </main>
        </div>
    </div>

</div>

<th:block layout:fragment="custom-script">
    <script>
        $(document).ready(function () {
            $('.radio-collapse input[type=radio]').click(function () {
                $(this).parents('.radio-collapse').find('input').each(function () {
                    var id = $(this).attr('id')
                    if (id) {
                        $('[data-id="' + id + '"]').addClass('d-none')
                    }
                })
                var selfId = $(this).attr('id')
                $('[data-id="' + selfId + '"]').removeClass('d-none')
            });
        });

        function changeVersion(_this) {
            // 先清空第二个
            let specVersionObj = $('select[name="specVersion"]');
            specVersionObj.empty();
            let option;
            if ('Homo sapiens' == $(_this).val()) {
                option = `<option value="hg38">hg38(GRCh38)</option>
                    <option value="hg19">hg19(GRCh37)</option>`;
            } else {
                option = `<option value="">Choose..</option>`;
            }
            specVersionObj.append($(option));
        }


        function submitForm(_this) {
            let formData = new FormData();

            let taskName = $.trim($('input[name="taskName"]').val());
            if (taskName.length == 0) {
                layer.msg("Advanced analysis Task Name must not be empty");
                return false;
            }
            formData.append('taskName', taskName);

            const maxGenesCount = 50;
            let interestedGenes = $('input[name="interestedGenes"]:checked').val();
            if ('custom' == interestedGenes) {
                let interestedGenesCustom = $.trim($('input[name="interestedGenesCustom"]').val());
                if ($.trim(interestedGenesCustom).length == 0) {
                    layer.msg("Please input at least one custom gene");
                    return false;
                }
                if (interestedGenesCustom.split(',').length > maxGenesCount) {
                    layer.msg("The amount of the Custom gene must be a positive integer from 1 to " + maxGenesCount);
                    return false;
                }
                formData.append('interestedGenesCustom', interestedGenesCustom);
            } else {
                let interestedGenesTop = $.trim($('input[name="interestedGenesTop"]').val());
                // 正整数
                let intReg = /^[1-9]\d*$/;

                if (!intReg.test(interestedGenesTop) || interestedGenesTop < 1 || interestedGenesTop > maxGenesCount) {
                    layer.msg("Interested Genes Top must be a positive integer from 1 to " + maxGenesCount);
                    return false;
                }
                formData.append('interestedGenesTop', interestedGenesTop);
            }

            let captureSize = $('input[name="captureSize"]:checked').val();
            if ('Null' !== captureSize) {
                captureSize = $.trim($('input[name="captureSizeVal"]').val());
                // Can only fill in non negative numbers，非负数字
                let numReg = /^\d+(\.{0,1}\d+){0,1}$/;
                if (!numReg.test(captureSize)) {
                    layer.msg("Illegal capture size value");
                    return false;
                }
            }
            formData.append('captureSize', captureSize);

            let fromBasicAnalysis = $('input[name="fromBasicAnalysis"]').val();
            if (fromBasicAnalysis !== 'true') {
                formData.append('refVersion', $.trim($('select[name="specVersion"]').val()));
            }

            formData.append('driverGeneMethod', $.trim($('input[name="driverGeneMethod"]:checked').val()));
            formData.append('dataBase', $.trim($('input[name="dataBase"]:checked').val()));
            formData.append('id', $('input[name="id"]').val());

            if ($(_this).data('loading') == 'true') {
                return;
            }
            $(_this).data('loading', 'true');

            $.ajax({
                url: '/analysis/somatic-adv/createAdvTask',
                dataType: 'json',
                type: 'post',
                processData: false,
                contentType: false,
                data: formData,
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg('submit success');
                        var id = result.data;
                        setTimeout(function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            window.location.href = $.trim(_context_path) + '/analysis/somatic-adv/list?id=' + id;
                        }, 2000);
                    }
                },
                complete: function () {
                    $(_this).data('loading', 'false');
                }
            });
        }

    </script>
</th:block>
</html>
