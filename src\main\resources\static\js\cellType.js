/**
 *  cell_type
 *  code使用值，value基因值
 * @type {Array}
 */
var CELL_TYPES = [
    {
        "code": 'B cells(kappa)',
        "value": ['BANK1', 'CD19', 'CD74', 'CD79A', 'CD79B', 'CXCR5', 'IGKC', 'MS4A1', 'PTPRC', 'VIM']
    },
    {
        "code": 'B cells(lambda)',
        "value": ['BANK1', 'CD19', 'CD74', 'CD79A', 'CD79B', 'CXCR5', 'IGLC2', 'IGLC3', 'MS4A1', 'PTPRC', 'VIM']
    },
    {
        "code": 'Cytotoxic T cells',
        "value": ['CCL5', 'CD2', 'CD3D', 'CD3E', 'CD3G', 'CD8A', 'CD8B', 'EOMES', 'GZMA', 'NKG7', 'PTPRC', 'TRAC', 'VIM']
    },
    {
        "code": 'CD4 T cells',
        "value": ['CD2', 'CD3D', 'CD3E', 'CD3G', 'CD4', 'IL7R', 'PTPRC', 'TRAC', 'VIM']
    },
    {
        "code": 'Tfh',
        "value": ['CD2', 'CD3D', 'CD3E', 'CD3G', 'CD4', 'CXCR5', 'ICA1', 'ICOS', 'PDCD1', 'PTPRC', 'ST8SIA1', 'TNFRSF4', 'TRAC', 'VIM']
    },
    {
        "code": 'Monocyte/Macrophage',
        "value": ['CD14', 'CD33', 'CD4', 'FCGR3A', 'ITGAM', 'ITGAX', 'LYZ', 'PTPRC', 'VIM']
    },
    {
        "code": 'Epithelial cells',
        "value": ['CDH1', 'EPCAM', 'KRT8', 'WFDC2']
    },
    {
        "code": 'Vascular smooth muscle cells',
        "value": ['ACTA2', 'COL1A1', 'COL3A1', 'MCAM', 'MYH11', 'MYLK', 'PLN', 'SERPINH1', 'VIM']
    },
    {
        "code": 'Endothelial cells',
        "value": ['CDH5', 'CLEC14A', 'EMCN', 'MCAM', 'PECAM1', 'SERPINH1', 'VIM', 'VWF']
    },
    {
        "code": 'Stromal cell',
        "value": ['COL5A1', 'KLF6', 'LUM']
    }
];
