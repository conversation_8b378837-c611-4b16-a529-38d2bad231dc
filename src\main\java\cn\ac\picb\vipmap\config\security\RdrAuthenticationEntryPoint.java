package cn.ac.picb.vipmap.config.security;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * VipMap认证入口点实现
 * 参考RdrAuthenticationEntryPointImpl，处理未认证请求的重定向
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class RdrAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final RdrProperties rdrProperties;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException, ServletException {

        log.debug("用户未认证，重定向到登录页面: {}", rdrProperties.getRdrLoginPage());

        // 重定向到RDR登录页面
        response.sendRedirect(rdrProperties.getRdrLoginPage());
    }
}
