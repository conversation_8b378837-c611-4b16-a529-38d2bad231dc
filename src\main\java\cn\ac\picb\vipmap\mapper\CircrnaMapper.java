package cn.ac.picb.vipmap.mapper;


import cn.ac.picb.circrna.vo.CircrnaTaskParamVO;
import cn.ac.picb.circrna.vo.CircrnaTaskQueryVO;
import cn.ac.picb.vipmap.vo.CircrnaTaskParam;
import cn.ac.picb.vipmap.vo.CircrnaTaskSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:14
 */
@Mapper
public interface CircrnaMapper {
    CircrnaMapper INSTANCE = Mappers.getMapper(CircrnaMapper.class);

    CircrnaTaskParamVO convertToVO(CircrnaTaskParam param);

    CircrnaTaskQueryVO convert(CircrnaTaskSearchVO search);
}
