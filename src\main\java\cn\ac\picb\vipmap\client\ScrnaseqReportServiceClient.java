package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnaseq.client.ScrnaseqReportServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqPagaServiceClientFallback;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqReportServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "scrnaseq-service", contextId = "report", fallback = ScrnaseqReportServiceClientFallback.class)
public interface ScrnaseqReportServiceClient extends ScrnaseqReportServiceApi {

}
