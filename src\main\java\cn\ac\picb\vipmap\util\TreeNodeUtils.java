package cn.ac.picb.vipmap.util;

import cn.ac.picb.scrnaseq.enums.ScrnaseqTaskType;
import cn.ac.picb.scrnaseq.enums.TreeNodeType;
import cn.ac.picb.scrnaseq.po.*;
import cn.ac.picb.scrnaseq.vo.TaskTreeNodeDataVO;
import cn.ac.picb.scrnaseq.vo.TaskTreeNodeVO;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import lombok.experimental.UtilityClass;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@UtilityClass
public class TreeNodeUtils {

    public TaskTreeNodeVO taskToNode(GenomicsTaskPO task) {
        double v = 0.2;
        int i = (int) v;
        return TaskTreeNodeVO.builder()
                .key(task.getId())
                .title(task.getTaskId())
                .data(
                        TaskTreeNodeDataVO.builder()
                                .id(task.getId())
                                .nodeType(TreeNodeType.genomics.name())
                                .type(ScrnaseqTaskType.genomics.name())
                                .taskId(task.getTaskId())
                                .time(task.getUpdateTime())
                                .status(task.getStatus())
                                .build()
                )
                .folder(true)
                .lazy(true)
                .expanded(false)
                .build();
    }

    public TaskTreeNodeVO taskToNode(BaselineTaskPO task) {
        return TaskTreeNodeVO.builder()
                .key(task.getId())
                .title(task.getTaskId())
                .data(
                        TaskTreeNodeDataVO.builder()
                                .parentId(task.getParentId())
                                .id(task.getId())
                                .nodeType(TreeNodeType.baseline.name())
                                .type(ScrnaseqTaskType.baseline.name())
                                .taskId(task.getTaskId())
                                .time(task.getUpdateTime())
                                .status(task.getStatus())
                                .build()
                )
                .folder(true)
                .lazy(true)
                .expanded(false)
                .build();
    }

    public TaskTreeNodeVO folder(ScrnaseqTaskType type, String baselineId) {
        Map<ScrnaseqTaskType, String> map = MapUtil.builder(new HashMap<ScrnaseqTaskType, String>(3))
                .put(ScrnaseqTaskType.paga, "PAGA & Pseudotime trajectory")
                .put(ScrnaseqTaskType.deg, "DEG & GSVA")
                .put(ScrnaseqTaskType.genes, "Genes")
                .build();

        return TaskTreeNodeVO.builder()
                .key(IdUtil.randomUUID())
                .title(map.get(type))
                .data(TaskTreeNodeDataVO.builder().id(baselineId).nodeType(TreeNodeType.folder.name()).type(type.name()).build())
                .folder(true)
                .lazy(true)
                .expanded(false)
                .build();
    }

    public TaskTreeNodeVO taskToNode(GenesTaskPO task) {
        return TaskTreeNodeVO.builder()
                .key(task.getId())
                .title(task.getTaskId())
                .data(
                        TaskTreeNodeDataVO.builder()
                                .parentId(task.getParentId())
                                .id(task.getId())
                                .nodeType(TreeNodeType.genes.name())
                                .type(ScrnaseqTaskType.genes.name())
                                .taskId(task.getTaskId())
                                .time(task.getUpdateTime())
                                .status(task.getStatus())
                                .build()
                )
                .build();
    }

    public TaskTreeNodeVO taskToNode(DegTaskPO task) {
        return TaskTreeNodeVO.builder()
                .key(task.getId())
                .title(task.getTaskId())
                .data(
                        TaskTreeNodeDataVO.builder()
                                .parentId(task.getParentId())
                                .id(task.getId())
                                .nodeType(TreeNodeType.deg.name())
                                .type(ScrnaseqTaskType.deg.name())
                                .taskId(task.getTaskId())
                                .time(task.getUpdateTime())
                                .status(task.getStatus())
                                .build()
                ).build();
    }
    public TaskTreeNodeVO taskToNode(WgcnaTaskPO task) {
        return TaskTreeNodeVO.builder()
                .key(task.getId())
                .title(task.getTaskId())
                .data(
                        TaskTreeNodeDataVO.builder()
                                .parentId(task.getParentId())
                                .id(task.getId())
                                .nodeType(TreeNodeType.wgcna.name())
                                .type(ScrnaseqTaskType.wgcna.name())
                                .taskId(task.getTaskId())
                                .time(task.getUpdateTime())
                                .status(task.getStatus())
                                .build()
                ).build();
    }

    public TaskTreeNodeVO taskToNode(PagaTaskPO task) {
        return TaskTreeNodeVO.builder()
                .key(task.getId())
                .title(task.getTaskId())
                .data(
                        TaskTreeNodeDataVO.builder()
                                .parentId(task.getParentId())
                                .id(task.getId())
                                .nodeType(TreeNodeType.paga.name())
                                .type(ScrnaseqTaskType.paga.name())
                                .taskId(task.getTaskId())
                                .time(task.getUpdateTime())
                                .status(task.getStatus())
                                .build()
                ).build();
    }
}
