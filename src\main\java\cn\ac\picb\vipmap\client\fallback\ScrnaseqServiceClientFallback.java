package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.scrnaseq.vo.TaskListTreeVO;
import cn.ac.picb.scrnaseq.vo.TaskQueryVO;
import cn.ac.picb.scrnaseq.vo.TaskTreeNodeVO;
import cn.ac.picb.scrnaseq.vo.TaskTreeQueryVO;
import cn.ac.picb.vipmap.client.ScrnaseqServiceClient;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaseqServiceClientFallback implements ScrnaseqServiceClient {

    private final static String SERVER_NAME = "scrnaseq-service";

    @Override
    public CommonResult<List<TaskTreeNodeVO>> findTreeData(TaskTreeQueryVO taskTreeQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<TaskListTreeVO>> findAllTaskWithTree(TaskQueryVO query) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<TaskListTreeVO>> findAllCompleteTaskWithTree(TaskQueryVO query) {
        return serverError(SERVER_NAME);
    }

    @Override
    public String getVersion() {
        return "error";
    }


}
