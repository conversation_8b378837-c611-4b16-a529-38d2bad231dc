<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link type="text/css" rel="stylesheet" th:href="@{/select2/select2.min.css}"/>
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-advanced-deg')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/form/deg}" class="active">Add Task</a>
                            <a th:href="@{/analysis/scrnaseq/list(type='advanced')}">Task list</a>
                        </div>
                    </div>
                    <div id="form-content">
                        <form id="form" class="form-custom form-inline form-task mt-2" style="padding: 0 15px;">
                            <div class="form-group-box min-width" style="border-top: transparent;">
                                <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">
                                    Input</a>
                                <div class="collapse show" id="coll-1">
                                    <div class="pl-4 pt-2">
                                        <h6 class="text-primary border-bottom pb-2">Search</h6>
                                        <input type="hidden" name="type" value="baseline">
                                        <div class="d-flex flex-wrap pl-4 pt-2">
                                            <div class="form-group row align-items-center">
                                                <label class="cmx-2 font-12">Cluster
                                                    Description ID</label>
                                                <div class="col-xl-4 col-lg-4 col-md-4 search">
                                                    <input class="form-control form-control-sm width-100 "
                                                           name="searchTaskId"
                                                           type="text">
                                                </div>
                                            </div>
                                            <div class="basic-name form-group">
                                                <label class="mx-2 font-12">Task Name</label>
                                                <div class="col-xl-4 col-lg-4 col-md-4 search">
                                                    <input class="form-control form-control-sm width-100"
                                                           name="searchTaskName" type="text"
                                                           onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                </div>
                                            </div>
                                            <div class="form-group ml">
                                                <label class="mx-2 font-12">Time</label>
                                                <div class="input-daterange input-group">
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="start"/>
                                                    <div class="input-group-append">
                                                            <span class="input-group-text"><i
                                                                    class="fa fa-calendar"></i></span>
                                                    </div>
                                                    <input type="text" class="form-control form-control-sm"
                                                           name="end"/>
                                                    <div class="input-group-append">
                                                            <span class="input-group-text"><i
                                                                    class="fa fa-calendar"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button" id="search-btn" onclick="filterTask()"
                                                    class="btn btn-primary btn-sm h-31">Search
                                            </button>
                                        </div>
                                        <div id="table-result">
                                            <table
                                                    class="table table-bordered table-sm table-center table-middle font-12">
                                                <colgroup>
                                                    <col width="30px"></col>
                                                    <col width="80px"></col>
                                                    <col width="110px"></col>
                                                    <col width="100px"></col>
                                                    <col width="100px"></col>
                                                    <col width="100px"></col>
                                                    <col width="100px"></col>
                                                    <col width="100px"></col>
                                                    <col width="90px"></col>
                                                    <col width="90px"></col>
                                                    <col width="90px"></col>
                                                </colgroup>
                                                <thead>
                                                <tr class="thead-light">
                                                    <th></th>
                                                    <th>Parent ID</th>
                                                    <th>Parent Name</th>
                                                    <th>Parent Type</th>
                                                    <th>Task ID</th>
                                                    <th>Task Name</th>
                                                    <th>Task Type</th>
                                                    <th>Speices</th>
                                                    <th>Start time</th>
                                                    <th>Status time</th>
                                                    <th>Consuming</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr class="w-100">
                                                    <td colspan="11" class="text-center">
                                                        <div class="spinner-border text-muted"></div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>

                                        </div>


                                        <h6 class="text-primary border-bottom pb-2 mt-4">Select</h6>
                                        <div class="pl-4 pt-2">
                                            <div class="form-group row align-items-center mb-0">
                                                <span>DEG and Enrichment will be done based on Cluster Description:<span
                                                        class="text-primary" id="baselineIdSpan"></span></span>
                                            </div>

                                            <div class="basic-name form-group row align-items-center mb-0"
                                                 style="margin-top: 8px;">
                                                <label>Current Task Name</label>
                                                <input class="form-control input-name validate[required]"
                                                       name="taskName" type="text"
                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group-box min-width not-bb">
                                <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                                <div class="collapse show min-width" id="coll-2">
                                    <div class="pl-4 pt-2">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-2 row">
                                                    <label class="col-form-label col-xl-5 col-lg-6 col-md-5 pr-0">
                                                        <span style="margin: 0 0 0 -60px;">Compare cluster</span>
                                                    </label>
                                                    <div class="col-xl-7 col-lg-6 col-md-7">
                                                        <div class="item-cluster d-flex align-items-center mb-2">
                                                            <select class="custom-select mClusterSelect1   validate[required]"
                                                                    onchange="changeClusterSelect1()"
                                                                    style="width: 100%">
                                                                <option value="">Please select ID
                                                                </option>
                                                            </select>
                                                            <div class="ml-1 cc-control d-none">
                                                                <i class="fa fa-plus-circle text-primary ml-1"
                                                                   onclick="addCluster(this)"></i>
                                                                <i class="fa fa-minus-circle text-muted ml-1"
                                                                   onclick="delCluster(this)"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6" id="clusters-radio">
                                                <div class="custom-control custom-radio">
                                                    <input type="radio" class="custom-control-input validate[required]"
                                                           id="clusters1" name="degMode" value="single" checked>
                                                    <label for="clusters1" class="custom-control-label">
                                                        <span style="margin: 0 0 0 -270px;">with the resest
                                                                clusters</span>
                                                    </label>
                                                </div>
                                                <div class="custom-control custom-radio">
                                                    <input type="radio" class="custom-control-input validate[required]"
                                                           id="clusters2" name="degMode" value="multi">
                                                    <label for="clusters2" class="custom-control-label">
                                                        <span style="margin: 0 0 0 -325px;">or with
                                                                cluster</span>
                                                    </label>
                                                </div>
                                                <div class="form-group mb-2 row d-none" id="clustersOther">
                                                    <label class="col-form-label col-xl-5 col-lg-6 col-md-5 pr-0">cluster</label>
                                                    <div class="col-xl-7 col-lg-6 col-md-7">
                                                        <div class="item-cluster d-flex align-items-center mb-2">
                                                            <select class="custom-select mClusterSelect2 validate[required]"
                                                                    onchange="changeClusterSelect2()">
                                                                <option value="">Please select ID
                                                                </option>
                                                            </select>
                                                            <div class="ml-1 cc-control d-none">
                                                                <i class="fa fa-plus-circle text-primary ml-1"
                                                                   onclick="addCluster(this)"></i>
                                                                <i class="fa fa-minus-circle text-muted ml-1"
                                                                   onclick="delCluster(this)"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group row mb-3">
                                                    <label class="col-form-label col-xl-5 col-lg-6 col-md-5 pr-0">
                                                        <span style="margin: 0 0 0 -50px;">Fold
                                                            change(log2)</span>
                                                    </label>
                                                    <div class="col-xl-7 col-lg-6 col-md-7">
                                                        <input type="text"
                                                               style="width: 100%"
                                                               class="form-control validate[[required],custom[number]]"
                                                               name="cutfc">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group row mb-3">
                                                    <label class="col-form-label col-xl-5 col-lg-6 col-md-5 pr-0">
                                                        <span style="margin: 0 0 0 -135px;">FDR</span>
                                                    </label>
                                                    <div class="col-xl-7 col-lg-6 col-md-7">
                                                        <input type="text"
                                                               class="form-control validate[[required],custom[number],min[0],max[1]]"
                                                               name="cutp">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group row mb-3">
                                                    <label class="col-form-label col-xl-5 col-lg-6 col-md-5 pr-0">
                                                        <span style="margin: 0 0 0 -102px;">Database</span>
                                                    </label>
                                                    <div class="col-xl-7 col-lg-6 col-md-7">
                                                        <select style="width: 100%;"
                                                                class="custom-select validate[required]" name="annoDb">
                                                            <option value="">Choose</option>
                                                            <option value="KEGG">KEGG</option>
                                                            <option value="REACTOME">REACTOME</option>
                                                            <option value="BIOCARTA">BIOCARTA</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center w-100">
                                <a href="javascript:void(0)" id="submitBtn" onclick="save()"
                                   class="btn btn-outline-primary btn-custom">
                                    <span>Submit</span><i class="fa fa-long-arrow-right"></i>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script th:src="@{/js/cellType.js}"></script>
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-zh_CN.js}"></script>
    <script th:src="@{/select2/select2.min.js}"></script>
    <script th:src="@{/js/adv-filter-table.js}"></script>
    <script>

      function save () {
        if (!$("#form").validationEngine('validate')) {
          return
        }
        var mCluster = []
        var flag = false
        //mCluste不能重复
        $(".mClusterSelect1").each(function (i) {
          $(this).attr("name", "cluster1List[" + i + "]")
          var mClusterSelect1 = $(this).find("option:selected").val()
          if (mCluster.indexOf(mClusterSelect1) > -1) {
            flag = true
            return
          }
          mCluster.push(mClusterSelect1)
        })
        var degMode = $("input[name='degMode']:checked").val()
        if (degMode == "multi") {
          $(".mClusterSelect2").each(function (i) {
            $(this).attr("name", "cluster2List[" + i + "]")
            var mClusterSelect2 = $(this).find("option:selected").val()
            if (mCluster.indexOf(mClusterSelect2) > -1) {
              flag = true
              return
            }
            mCluster.push(mClusterSelect2)
          })

        }
        if (flag) {
          layer.msg("Compare cluster can't be duplicate")
          return
        }

        // 禁用按钮防止重复提交
        $('#submitBtn').removeAttr('onclick')
        $.ajax({
          url: "/analysis/scrnaseq/saveDeg",
          data: $("#form").serialize(),
          type: "post",
          dataType: 'json',
          success: function (result) {
            if (result.success) {
              window.location.href = '[[@{/analysis/scrnaseq/list}]]?type=advanced'
            } else {
              layer.msg(result.message)
              $('#submitBtn').attr('onclick', 'save()')
            }
          }
        })
      }

      function addCluster (_this) {
        var clone = $(_this).parents('.item-cluster').clone()
        $(_this).parents('.item-cluster').after(clone)
      }

      function delCluster (_this) {
        if ($('.item-cluster').length > 1) {
          $(_this).parents('.item-cluster').remove()
        }
      }

      $('#clusters-radio input[type=radio]').click(function () {
        var id = $(this).attr('id')
        if (id == 'clusters1') {
          $('.cc-control').removeClass('d-flex').addClass('d-none')
          $('.item-cluster:first-child').nextAll().remove()
          $('#clustersOther').addClass('d-none')
        } else {
          $('.cc-control').removeClass('d-none').addClass('d-flex')
          $('#clustersOther').removeClass('d-none')
        }
      })

      function changeClusterSelect1 () {
        let set = new Set()
        $('.mClusterSelect1 option:selected').each(function () {
          set.add($(this).val())
        })
        $('.mClusterSelect1 option').each(function () {
          $(this).show()
          for (let item of set) {
            if ($(this).val() === item && $(this).val() !== '') {
              $(this).hide()
            }
          }
        })
      }

      function changeClusterSelect2 () {
        let set = new Set()
        $('.mClusterSelect2 option:selected').each(function () {
          set.add($(this).val())
        })
        $('.mClusterSelect2 option').each(function () {
          $(this).show()
          for (let item of set) {
            if ($(this).val() === item && $(this).val() !== '') {
              $(this).hide()
            }
          }
        })
      }
    </script>
</th:block>
</html>
