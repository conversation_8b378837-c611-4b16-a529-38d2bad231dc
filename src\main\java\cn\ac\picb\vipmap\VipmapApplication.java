package cn.ac.picb.vipmap;

import cn.ac.picb.vipmap.config.AppProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableConfigurationProperties(AppProperties.class)
@EnableFeignClients(basePackages = {"cn.ac.picb.*.client"})
public class VipmapApplication {

    public static void main(String[] args) {
        SpringApplication.run(VipmapApplication.class, args);
    }

}
