package cn.ac.picb.vipmap.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "app")
@Data
public class AppProperties {

    /**
     * 分析流程语言 en / zh_CN
     */
    private String locale = "en";

    private String cas = "https://www.biosino.org/node-cas";

    private String imac = "https://www.biosino.org/iMAC";

    private String vipmap = "https://www.biosino.org/vipmap";
//    private String vipmap = "https://localhost:8082/vipmap";

    private String pdms = "https://www.biosino.org/fm";
//    private String pdms = "http://localhost:8080/fm";

    private String bmdc = "https://www.biosino.org/bmdcRegist";

    private String ftp = "sftp://fms.biosino.org:44399";

    private String pheatmap = "https://www.biosino.org/vipmap/BioCharts/api";

    private String modules = "rnaseq;ase;circrna;paean;scrnaseq;scrnasmartseq;strnaseq;somatic;germline;methychip;wgbs;proteomics";
}
