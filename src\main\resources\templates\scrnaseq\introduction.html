<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-introduction')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Introduction</h4>
                <div class="tool-box">
                    <div class="tool-title">Summary</div>
                    <div class="tool-content">
                        <p class="text-muted">Single cell RNA sequecing(scRNA-seq) provides a high-throughput way to
                            study gene expression profile for one single cell in order to keep unique cell
                            signal away from being concealed by group.</p>
                        <p class="text-muted">For this models, we will provide a pipeline to analyse scRNA-seq dataset
                            from 10X Genomics as below.</p>
                        <p class="text-center"><img th:src="@{/images/pic-scrna.png}" alt=""></p>
                        <h5 class="font-16"><b>Pipeline</b></h5>
                        <p class="pl-3 mb-1"><b>10X genomics</b></p>
                        <p class="pl-3 text-muted">This pipeline starts by reading in the raw fastq data and return a
                            unique molecular identified(UMI) count matrix.</p>
                        <p class="pl-3 text-muted">Then we use the count matrix for post preprocessing. During post
                            preprocessing, the input count matrix will be filtered, normalized and reduced
                            dimension first. </p>
                        <h5 class="font-16"><b>Tools</b></h5>
                        <p class="pl-3 mb-1"><b>Clustering and basic analysis</b></p>
                        <p class="pl-3 text-muted">For filtered expression matrix, choose appropriate principle
                            components for t-SNE cluster and detect the possible cell type of each cluster by
                            measuring the expression level of marker genes.</p>
                        <p class="pl-3 text-muted">After annotate each cluster, calculate the correlation between
                            clusters and reveal the cell proportion of each cluster.</p>
                        <p class="pl-3 mb-1"><b>graph abstraction and pseudotime trajectory analysis</b></p>
                        <p class="pl-3 text-muted">After clustering and basic analysis, we provide an interpretable
                            graph-like map of the arising data manifold by estimation connectivity of
                            manifold partitions to resolve differentiation relations among cells.</p>
                        <p class="pl-3 text-muted">Besides, pseudotime trajectory is available to show the way of cells
                            transition from one functional "state" to another by learning the sequence
                            of gene expression changes each cell must go through as part of a dynamic biological
                            process. </p>
                        <p class="pl-3 mb-1"><b>DEG and enrichment analysis</b></p>
                        <p class="pl-3 text-muted">Calculate different expression genes(DEG) for chosen clusters and
                            estimates variation of pathway activity of this DEGs over a sample population
                            in an unsupervised manner.</p>
                        <p class="pl-3 mb-1"><b>Expression and correlation analysis</b></p>
                        <p class="pl-3 text-muted">Visualise the distribution and the probability density of scRNA-seq
                            expression data for chosen genes in different clusters and calculate the
                            correlation of cells through the chosen genes.</p>
                        <p class="pl-3 mb-1"><b>WGCNA analysis</b></p>
                        <p class="pl-3 text-muted">Weighted Gene Co-expression Network Analysis (WGCNA) analyzed gene
                            expression patterns in multiple cells, which can cluster genes with similar expression
                            patterns. And analyze the correlation between modules and cell types.
                        </p>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">

</th:block>
</html>
