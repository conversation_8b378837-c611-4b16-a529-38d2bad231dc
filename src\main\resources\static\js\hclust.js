/**
 * hclust v1.0.2
 * builder author: yb
 * https://www.npmjs.com/package/hclust
 */
(()=>{var e={565:(e,t,n)=>{const{sum:r,dotProduct:a,pearson:i,spearman:l}=n(914),{min:u,max:o,abs:c,pow:s,acos:f,PI:p}=Math;function m(e,t){return a(e,t)/(s(a(e,e),.5)*s(a(t,t),.5))}e.exports={euclidean:function(e,t){let n=e.map(((n,r)=>s(e[r]-t[r],2)));return s(r(n),.5)},maximum:function(e,t){let n=e.map(((n,r)=>c(e[r]-t[r])));return o(...n)},canberra:function(e,t){let n=e.map(((n,r)=>c(e[r]-t[r])/(c(e[r])+c(t[r]))));return r(n)},manhattan:function(e,t){let n=e.map(((n,r)=>c(e[r]-t[r])));return r(n)},percent:function(e,t,n){let a=e.map(((n,r)=>c(e[r]-t[r])));return 100*(n-r(a))/n},cosine:m,angular:function(e,t){return 2*f(m(e,t))/p},pearson:function(e,t){return 1-i(e,t)},spearman:function(e,t){return 1-l(e,t)}}},914:e=>{function t(e){return e.reduce(((e,t)=>e+t))}function n(e,t){return e.map(((n,r)=>e[r]*t[r]))}function r(e,r){return t(n(e,r))}function a(e){return t(e)/e.length}function i(e){let t=a(e);return e.map((e=>e-t))}function l(e){let n=a(e);return t(e.map((e=>Math.pow(e-n,2))))/e.length}function u(e){return Math.pow(l(e),.5)}function o(e){let n=[...e].sort(((e,t)=>e-t)),r=e.map((e=>n.indexOf(e)+1)),a={};for(let e of[...new Set(r)]){let n=r.filter((t=>t===e)).length;a[e]=1===n?e:t([...Array(n).keys()].map((t=>t+e)))/n}return r=r.map((e=>a[e])),r}e.exports={sum:t,product:n,dotProduct:r,mean:a,residuals:i,variation:l,sd:u,flattenDeep:function e(t){return t.reduce(((t,n)=>Array.isArray(n)?t.concat(e(n)):t.concat(n)),[])},intersection:function(e,t){return e.filter((e=>-1!==t.indexOf(e)))},arrayToRanks:o,pearson:function(e,t){return r(i(e),i(t))/(u(e)*u(t)*e.length)},spearman:function(e,n){let r=o(e),a=o(n);return 1-t(r.map(((e,t)=>Math.pow(r[t]-a[t],2))))/(Math.pow(e.length,3)-e.length)*6}}},10:(e,t,n)=>{const{flattenDeep:r,intersection:a,sum:i}=n(914),l=n(974),{min:u,max:o}=Math,c={single:e=>u(...e),complete:e=>o(...e),average:e=>i(e)/e.length};e.exports=function(e,t="euclidean",n="average",i="pairwise",o,s){let f=l(e,t,i,o,s),p=[],m=[...Array(e.length).keys()].map((e=>[e]));for(;1!==m.length;){let e=[],t=0;for(;t<m.length;){let i,l,u=t+1;for(;u<m.length;){i=r(m[t]),l=r(m[u]);let o=[m[t],m[u]],s=f.filter((e=>a(e.elements,i).length>0&&a(e.elements,l).length>0));s=s.map((e=>e.distance)),s=c[n](s),e.push({elements:o,distance:s,indices:[t,u]}),u+=1}t+=1}let i=e.map((e=>e.distance)),l=u(...i),o=e[i.indexOf(l)];m=m.filter(((e,t)=>!o.indices.includes(t))),delete o.indices,p.unshift(o),m.push(o.elements)}return p}},974:(e,t,n)=>{const{flattenDeep:r}=n(914),{min:a,max:i}=Math,l=n(565);e.exports=function(e,t="euclidean",n,u,o){let c,s=[],f=0;if(u&&o&&(c=(o-u)*e.length),!c&&"percent"===t){let t=r(e);c=(i(...t)-a(...t))*e.length}for(;f<e.length;){let r=f+1;for(;r<e.length;){let a=[f,r],i=e[f],u=e[r];if("pairwise"===n&&i.length&&u.length){let e=i.map(((e,t)=>null===i[t]&&null===u[t]));i=i.filter(((t,n)=>!e[n])),u=u.filter(((t,n)=>!e[n]))}if(!i.length||!u.length)return!1;let o=l[t](i,u,c);s.push({elements:a,distance:o}),r+=1}f+=1}return s}}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}(()=>{let e=n(10);window.hclust=e})()})();