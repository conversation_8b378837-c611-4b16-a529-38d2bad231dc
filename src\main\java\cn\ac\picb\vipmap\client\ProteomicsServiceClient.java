package cn.ac.picb.vipmap.client;

import cn.ac.picb.proteomics.client.ProteomicsServiceApi;
import cn.ac.picb.vipmap.client.fallback.ProteomicsServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(value = "proteomics-service", fallback = ProteomicsServiceClientFallback.class)
public interface ProteomicsServiceClient extends ProteomicsServiceApi {
}
