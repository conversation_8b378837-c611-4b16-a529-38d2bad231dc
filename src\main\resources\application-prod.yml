eureka:
  client:
    service-url:
      defaultZone: http://discovery-service:7901/eureka/
    initial-instance-info-replication-interval-seconds: 10 # 将实例信息变更同步到 Eureka Server的初始延迟时间 ，默认为40秒
    registry-fetch-interval-seconds: 5 # 拉取服务注册信息频率
  instance:
    metadata-map:
      management.context-path: ${server.servlet.context-path}/actuator

spring:
  thymeleaf:
    cache: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************
    username: analysis
    password: analysis
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    database: mysql
    hibernate:
      ddl-auto: update
    show-sql: false

# RDR登录配置
rdr:
  login_page: https://crdc.9hospital.com.cn:1443/system/#/login?redirect=https://crdc.9hospital.com.cn:1443/bda
  api:
    url: https://crdc.9hospital.com.cn:1443/9hos-api
    check_user_login: ${rdr.api.url}/auth/checkLogin
    get_user_info_by_token: ${rdr.api.url}/auth/getUserInfoByToken
    get_user_info_by_user_name_and_password: ${rdr.api.url}/auth/getUserInfoByUserNameAndPassword
    logout_url: ${rdr.api.url}/auth/logout

logging:
  file:
    path: /data/logs/vipmap.log
  level:
    com.netflix: warn

server:
  tomcat:
    max-http-form-post-size: 5MB
