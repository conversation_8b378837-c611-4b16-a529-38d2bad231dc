package cn.ac.picb.vipmap.service;


import cn.ac.picb.circrna.po.CircrnaTaskPO;
import cn.ac.picb.circrna.vo.CircrnaTaskInput;
import cn.ac.picb.circrna.vo.CircrnaTaskParamVO;
import cn.ac.picb.circrna.vo.CircrnaTaskQueryVO;
import cn.ac.picb.circrna.vo.CircrnaTaskVO;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.vipmap.client.CircrnaServiceClient;
import cn.ac.picb.vipmap.mapper.CircrnaMapper;
import cn.ac.picb.vipmap.vo.CircrnaTaskParam;
import cn.ac.picb.vipmap.vo.CircrnaTaskSearchVO;
import cn.ac.picb.vipmap.vo.CurrentUser;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2022/3/1 14:58
 */
@Service
@RequiredArgsConstructor
public class CircrnaService {

    private final CircrnaServiceClient circrnaServiceClient;

    public CircrnaTaskPO createTask(CurrentUser user, CircrnaTaskParam param) {
        CircrnaTaskParamVO vo = CircrnaMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<CircrnaTaskPO> result = circrnaServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public Response downloadTemplate() {
        return circrnaServiceClient.downloadTemplateExcel();
    }

    public List<CircrnaTaskInput> uploadTemplate(MultipartFile file, CurrentUser user) {
        CommonResult<List<CircrnaTaskInput>> result = circrnaServiceClient.uploadTemplate(user.getUsername(), file);
        result.checkError();
        return result.getData();
    }

    public PageResult<CircrnaTaskPO> findPage(CurrentUser user, CircrnaTaskSearchVO search, PageParam pageParam) {
        CircrnaTaskQueryVO queryVO = CircrnaMapper.INSTANCE.convert(search);
        queryVO.setUserId(user.getId());
        queryVO.setPage(pageParam.getPage());
        queryVO.setSize(pageParam.getSize());
        CommonResult<PageResult<CircrnaTaskPO>> result = circrnaServiceClient.findTaskPage(queryVO);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<CircrnaTaskPO> result = circrnaServiceClient.deleteById(id);
        result.checkError();
    }

    public CircrnaTaskVO findTaskVO(String id) {
        CommonResult<CircrnaTaskVO> result = circrnaServiceClient.findDetailById(id);
        result.checkError();
        return result.getData();
    }

    public Response downloadResult(String taskId, String displayName) {
        return circrnaServiceClient.downloadResult(taskId, displayName);
    }
}
