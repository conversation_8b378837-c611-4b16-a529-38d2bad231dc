<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/bootstrap-tagsinput.css}">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis-genomics')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/form/genomics}" class="active">Add Task</a>
                            <a th:href="@{/analysis/scrnaseq/list(type='genomics')}">Task list</a>
                        </div>
                    </div>
                    <div id="form-content">
                        <form action="" class="form-custom" id="genomics_form" style="padding: 0 15px;">
                            <div class="form-group-box">
                                <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Expression matrix</a>
                                <div class="collapse show" id="coll-1">
                                    <div class="pl-4 pt-2">
                                        <div class="form-group row pl-3">
                                            <label>Current Task Name</label>
                                            <div class="col-xl-4 col-lg-3 col-md-8">
                                                <input class="form-control input-name validate[required]"
                                                       name="taskName"
                                                       type="text"
                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="custom-control custom-radio mb-1">
                                                <input type="radio" id="file1" name="dataMode" value="fastq"
                                                       class="custom-control-input validate[required]">
                                                <label class="custom-control-label" for="file1"><span
                                                        class="text-primary">fastq file</span></label>
                                            </div>
                                            <div class="desc py-2 mb-2 border-bottom" id="fastq">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-middle table-center">
                                                        <thead class="thead-light">
                                                        <tr>
                                                            <td width="150">RunName</td>
                                                            <td>I1</td>
                                                            <td>R1</td>
                                                            <td>R2</td>
                                                            <td width="50"></td>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="fastq_tbody">
                                                        <tr>
                                                            <td style="padding: 0;"><input
                                                                    class="form-control RunName validate[required]"
                                                                    name="runName"
                                                                    type="text"
                                                                    onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="showFileModal(this)">Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend ">
                                              <span class="input-group-text">
                                                <em class="seled"
                                                    data-errormessage-value-missing="* Please select a file"></em>
                                              </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="showFileModal(this)">Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend">
                                              <span class="input-group-text">
                                                <em class="seled validate[required]"
                                                    data-errormessage-value-missing="* Please select a file"></em>
                                              </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="showFileModal(this)">Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend">
                                              <span class="input-group-text">
                                                <em class="seled validate[required]"
                                                    data-errormessage-value-missing="* Please select a file"></em>
                                              </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td><i class="fa fa-plus-square text-primary"
                                                                   onclick="addRow(this)"></i><i
                                                                    class="fa fa-minus-square text-muted ml-1"
                                                                    onclick="removeRow(this)"></i></td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <h6 class="text-primary border-bottom pb-2">Mapping</h6>
                                                <div class="pl-4 pt-2">
                                                    <div class="form-group row align-items-center select_species">
                                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Species</label>
                                                        <div class="col-xl-4 col-lg-3 col-md-8 ml--30">
                                                            <select class="form-control validate[required]"
                                                                    name="species">
                                                                <option value="human">Homo sapiens（human）</option>
                                                                <option value="mouse">Mus musculus (mouse)</option>
                                                            </select>
                                                        </div>
                                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label">Version</label>
                                                        <div class="col-xl-4 col-lg-3 col-md-4">
                                                            <select class="form-control validate[required]"
                                                                    name="version">
                                                                <option value="hg38">hg38 (GRCh38)</option>
                                                                <option value="hg19">hg19 (GRCh37)</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-xl-10 offset-xl-2">
                                                            <span class="d-block pt-2">Or if you want to use your own genome please contact us</span>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row align-items-center mb-0">
                                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                        <div class="col-xl-10 col-lg-9 col-md-8 ml--30">
                                                            <div class="custom-control custom-radio custom-control-inline">
                                                                <input type="radio" class="custom-control-input"
                                                                       id="mapMethod" name="mapMethod" value="STAR"
                                                                       checked>
                                                                <label for="mapMethod"
                                                                       class="custom-control-label">STAR</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <h6 class="text-primary border-bottom pb-2">Counting</h6>
                                                <div class="pl-4 pt-2">
                                                    <div class="form-group row align-items-center mb-0">
                                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                        <div class="col-xl-10 col-lg-9 col-md-8 ml--30">
                                                            <div class="custom-control custom-radio custom-control-inline">
                                                                <input type="radio" class="custom-control-input"
                                                                       id="countMethod" name="countMethod"
                                                                       value="cellranger" checked>
                                                                <label for="countMethod" class="custom-control-label">cellranger</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row align-items-center mb-0">
                                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Option</label>
                                                        <div class="col-xl-10 col-lg-9 col-md-8 ml--30">
                                                            <div class="custom-control custom-radio custom-control-inline">
                                                                <input type="radio"
                                                                       class="custom-control-input validate[required]"
                                                                       id="op1" name="rangerMode" value="Count">
                                                                <label for="op1"
                                                                       class="custom-control-label">Count</label>
                                                            </div>
                                                            <div class="custom-control custom-radio custom-control-inline">
                                                                <input type="radio"
                                                                       class="custom-control-input validate[required]"
                                                                       id="op2" name="rangerMode" value="Aggr">
                                                                <label for="op2"
                                                                       class="custom-control-label">Aggregation</label>
                                                            </div>
                                                            <div class="custom-control custom-radio custom-control-inline">
                                                                <input type="radio"
                                                                       class="custom-control-input validate[required]"
                                                                       id="op3" name="rangerMode" value="Advanced"
                                                                       checked>
                                                                <label for="op3"
                                                                       class="custom-control-label">Advanced</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="custom-control custom-radio mb-1">
                                                <input type="radio" id="file2" name="dataMode" value="matrix"
                                                       class="custom-control-input validate[required]">
                                                <label class="custom-control-label" for="file2"><span
                                                        class="text-primary">feature-barcode matrix from cellranger output</span></label>
                                            </div>
                                            <div class="desc py-2 mb-2 border-bottom" id="matrix">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-middle table-center">
                                                        <thead class="thead-light">
                                                        <tr>
                                                            <td width="150">RunName</td>
                                                            <td>barcodes.tsv.gz</td>
                                                            <td>features.tsv.gz</td>
                                                            <td>matrix.mtx.gz</td>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="matrix_tbody">
                                                        <tr>
                                                            <td style="padding: 0;"><input
                                                                    class="form-control RunName validate[required]"
                                                                    name="runName"
                                                                    type="text"
                                                                    onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="show10xMatrixFileModal(this,'barcodes.tsv.gz')">
                                                                            Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend ">
                                              <span class="input-group-text">
                                                <em class="seled validate[required]"
                                                    data-errormessage-value-missing="* Please select a file"></em>
                                              </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="show10xMatrixFileModal(this,'features.tsv.gz')">
                                                                            Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend">
                                              <span class="input-group-text">
                                                <em class="seled validate[required]"
                                                    data-errormessage-value-missing="* Please select a file"></em>
                                              </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="show10xMatrixFileModal(this,'matrix.mtx.gz')">
                                                                            Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend">
                                              <span class="input-group-text">
                                                <em class="seled validate[required]"
                                                    data-errormessage-value-missing="* Please select a file"></em>
                                              </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <h6 class="text-primary border-bottom pb-2">Mapping</h6>
                                                <div class="pl-4 pt-2">
                                                    <div class="form-group row align-items-center">
                                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Species</label>
                                                        <div class="col-xl-4 col-lg-3 col-md-8 ml--30">
                                                            <select class="form-control validate[required]"
                                                                    name="species">
                                                                <option value="human">Homo sapiens（human）</option>
                                                                <option value="mouse">Mus musculus (mouse)</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="custom-control custom-radio mb-1">
                                                <input type="radio" id="file3" name="dataMode" value="csv"
                                                       class="custom-control-input validate[required]">
                                                <label class="custom-control-label" for="file3"><span
                                                        class="text-primary">gene-cell matrix of .csv file</span>
                                                </label>
                                            </div>
                                            <div class="desc py-2 mb-2" id="csv">
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-middle table-center"
                                                           style="width:500px">
                                                        <thead class="thead-light">
                                                        <tr>
                                                            <td width="150">RunName</td>
                                                            <td>.csv file</td>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="csv_tbody">
                                                        <tr>
                                                            <td style="padding: 0;"><input
                                                                    class="form-control RunName validate[required]"
                                                                    name="runName"
                                                                    type="text"

                                                                    onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="showRawMatrixFileModal(this)">
                                                                            Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend ">
                                              <span class="input-group-text">
                                                <em class="seled validate[required]"
                                                    data-errormessage-value-missing="* Please select a file"></em>
                                              </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>

                                                </div>
                                                <div class="pl-4 pt-2">
                                                    <div class="form-group row align-items-center">
                                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Species</label>
                                                        <div class="col-xl-4 col-lg-3 col-md-8 ml--30">
                                                            <select class="form-control validate[required]"
                                                                    name="species">
                                                                <option value="human">Homo sapiens（human）</option>
                                                                <option value="mouse">Mus musculus (mouse)</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group-box">
                                <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Post processing</a>
                                <div class="collapse show" id="coll-2">
                                    <div class="pl-4 pt-2">
                                        <div class="form-group row align-items-center mb-0">
                                            <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                            <div class="col-xl-10 col-lg-9 col-md-8 ml--30">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input type="checkbox" class="custom-control-input" id="md3"
                                                           name="md3" checked>
                                                    <label for="md3" class="custom-control-label">Seurat</label>
                                                </div>
                                            </div>
                                        </div>
                                        <h6 class="text-primary border-bottom py-2">Cell filter</h6>
                                        <div class="form-group row align-items-center mb-2">
                                            <div class="col-xl-10 col-lg-10 col-md-10">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input type="checkbox" class="custom-control-input" id="ngene"
                                                           checked>
                                                    <label for="ngene" class="custom-control-label">number of
                                                        nGene</label>
                                                </div>
                                                <div class="group-inner">
                                                    <div class="input-group">
                                                        <input type="text"
                                                               class="form-control validate[custom[integer],min[200],max[2500]]"
                                                               name="nGeneStart" value="200">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text">-</span>
                                                        </div>
                                                        <input type="text"
                                                               class="form-control validate[custom[integer],min[200],max[2500]]"
                                                               name="nGeneEnd" value="2500">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group row align-items-center mb-2">
                                            <div class="col-xl-10 col-lg-10 col-md-10">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input type="checkbox" class="custom-control-input" id="pomg"
                                                           checked>
                                                    <label for="pomg" class="custom-control-label">percentage of
                                                        mitochondrial genes</label>
                                                </div>
                                                <div class="group-inner">
                                                    <div class="input-group">
                                                        <input type="text"
                                                               class="form-control validate[custom[number],min[0],max[1]]"
                                                               name="toPerStart" value="0.01">
                                                        <div class="input-group-append">
                                                            <span class="input-group-text">-</span>
                                                        </div>
                                                        <input type="text"
                                                               class="form-control validate[custom[number],min[0],max[1]]"
                                                               name="toPerEnd" value="0.99">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <h6 class="text-primary border-bottom py-2">Clustering</h6>
                                        <div class="form-group row align-items-center mb-2">
                                            <div class="col-xl-4 col-lg-4 col-md-5">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input type="checkbox" class="custom-control-input" id="rp1"
                                                           checked>
                                                    <label for="rp1" class="custom-control-label">resolution
                                                        parameter</label>
                                                </div>
                                            </div>
                                            <div class="col-xl-4 col-lg-4 col-md-5">
                                                <input type="text" class="form-control validate[custom[number]]"
                                                       name="resolution" value="0.8">
                                            </div>
                                        </div>
                                        <div class="form-group row mb-2">
                                            <div class="col-xl-4 col-lg-4 col-md-5">
                                                <div class="custom-control custom-checkbox custom-control-inline">
                                                    <input type="checkbox" class="custom-control-input" id="tgenes"
                                                           value="0.8">
                                                    <label for="tgenes" class="custom-control-label">t-SNE genes</label>
                                                </div>
                                            </div>
                                            <div class="col-xl-4 col-lg-4 col-md-5">
                                                <div class="tags_add">
                                                    <input type="text" class="form-control" name="tGenes"
                                                           disabled="disabled">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="text-center">
                            <button onclick="uploadMetadata()" class="btn btn-outline-primary btn-custom">
                                <span>Submit</span><i class="fa fa-long-arrow-right"></i></button>
                        </div>
                        <div id="file-modal"></div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-zh_CN.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>
    <script>
      $(document).ready(function () {
        $('#file-modal').fileModal('/analysis/fileTree')
        $('input[name=\'dataMode\']').click(function () {
          var this_exp1 = $(this).val()
          $('div.desc').hide()
          $('#' + this_exp1).show()
        })
        $('select[name=\'species\']').on('change', function () {
          var versionSelect = $(this).parents('div.select_species').find('select[name="version"]')
          if (!versionSelect) {
            return
          }
          if ($(this).val() === 'human') {
            versionSelect.html('<option value="hg38">hg38 (GRCh38)</option><option value="hg19">hg19 (GRCh37)</option>')
          } else if ($(this).val() === 'mouse') {
            versionSelect.html('<option value="mm10">mm10</option>')
          }
        })

        $('#ngene').on('change', function () {
          if ($(this).is(':checked')) {
            $('input[name=nGeneStart]').removeAttr('disabled')
            $('input[name=nGeneEnd]').removeAttr('disabled')
          } else {
            $('input[name=nGeneStart]').val('').attr('disabled', 'disabled')
            $('input[name=nGeneEnd]').val('').attr('disabled', 'disabled')
          }
        })
        $('#pomg').on('change', function () {
          if ($(this).is(':checked')) {
            $('input[name=toPerStart]').removeAttr('disabled')
            $('input[name=toPerEnd]').removeAttr('disabled')
          } else {
            $('input[name=toPerStart]').val('').attr('disabled', 'disabled')
            $('input[name=toPerEnd]').val('').attr('disabled', 'disabled')
          }
        })
        $('#rp1').on('change', function () {
          if ($(this).is(':checked')) {
            $('input[name=resolution]').removeAttr('disabled')
          } else {
            $('input[name=resolution]').val('').attr('disabled', 'disabled')
          }
        })
        $('#tgenes').on('change', function () {
          if ($(this).is(':checked')) {
            $('input[name=tGenes]').prop('disabled', false).tagsinput()
          } else {
            $('input[name=tGenes]').prop('disabled', true)
            $('input[name=tGenes]').tagsinput('removeAll')
            $('input[name=tGenes]').tagsinput('destroy')
          }
        })
        $('#md3').on('click', function () {
          if ($(this).prop('checked')) {
            $('#ngene').prop('checked', true).change()
            $('#pomg').prop('checked', true).change()
          } else {
            $('#ngene').prop('checked', false).change()
            $('#pomg').prop('checked', false).change()
          }
        })
      })

      var _selectBtn

      $('#file-modal').on('__SELECT_FILES__', function (e, data) {
        var nodes = data.nodes || []
        if (nodes.length === 0) {
          $(_selectBtn).parent().next().find('em.seled:first').addClass('validate[required]')
          return
        }

        var html = []
        $.each(nodes, function (i, node) {
          var filePath = node.path
          var fileName = node.name
          html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '')
          html.push('<input type="hidden" value="' + filePath + '">')
          html.push('<input type="hidden" value="' + fileName + '">')
          html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
          html.push('</b>')
        })

        $(_selectBtn).parent().next().find('em.seled:first').html(html.join(''))
        $(_selectBtn).parent().next().find('em.seled:first').removeClass('validate[required]')
        $('[data-toggle="tooltip"]').tooltip()
      })

      function showFileModal (_this) {
        _selectBtn = _this

        var selectIds = []
        $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
          selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
        })
        $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
          selectIds: selectIds
        })
      }

      function show10xMatrixFileModal (_this, name) {
        _selectBtn = _this

        var selectIds = []
        $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
          selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
        })
        $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
          selectIds: selectIds,
          func: function (data) {
            return data.name === name
          }
        })
      }

      function showRawMatrixFileModal (_this) {
        _selectBtn = _this

        var selectIds = []
        $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
          selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
        })
        $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
          selectIds: selectIds,
          func: function (data) {
            return /.*\.csv/.test(data.name)
          }
        })
      }

      function removeFile (_this) {
        $(_this).parent().parent().remove()
      }

      function addRow (_this) {
        var length = $('#files_tbody').find('tr').length
        if (length >= 5) {
          return
        }
        var trClone = $(_this).parents('tbody:first').find('tr').eq(0).clone(true)
        trClone.find('em.seled').html('')
        $(_this).parents('tbody:first').append(trClone)
      }

      function removeRow (_this) {
        if ($(_this).parents('tbody:first').find('tr').length < 2) {
          return
        }
        $(_this).parents('tr:first').remove()
      }

      function uploadMetadata () {
        if (!$('#genomics_form').validationEngine('validate')) {
          return
        }

        // 禁用按钮防止重复提交
        $('#submitBtn').removeAttr('onclick')
        var formData = new FormData()
        var dataMode = $(':radio[name="dataMode"]:checked').val()
        formData.append('dataMode', dataMode)
        formData.append('taskName', $('input[name="taskName"]').val())

        if (dataMode === 'fastq') {
          $('#fastq_tbody').find('tr').each(function (index) {
            formData.append('files[' + index + '].runName', $(this).find('td:eq(0)').find('input[name="runName"]').val())
            $(this).find('td:eq(1)').find('b.text-primary').each(function (i) {
              formData.append('files[' + index + '].i1[' + i + '].path', $(this).find('input[type=hidden]:eq(0)').val())
              formData.append('files[' + index + '].i1[' + i + '].name', $(this).find('input[type=hidden]:eq(1)').val())
            })

            $(this).find('td:eq(2)').find('b.text-primary').each(function (i) {
              formData.append('files[' + index + '].r1[' + i + '].path', $(this).find('input[type=hidden]:eq(0)').val())
              formData.append('files[' + index + '].r1[' + i + '].name', $(this).find('input[type=hidden]:eq(1)').val())
            })

            $(this).find('td:eq(3)').find('b.text-primary').each(function (i) {
              formData.append('files[' + index + '].r2[' + i + '].path', $(this).find('input[type=hidden]:eq(0)').val())
              formData.append('files[' + index + '].r2[' + i + '].name', $(this).find('input[type=hidden]:eq(1)').val())
            })
          })
          formData.append('species', $('#fastq').find('select[name=\'species\']').val() || '')
          formData.append('version', $('#fastq').find('select[name=\'version\']').val() || '')
          formData.append('mapMethod', $('#fastq').find(':radio[name="mapMethod"]:checked').val() || '')
          formData.append('countMethod', $('#fastq').find(':radio[name="countMethod"]:checked').val() || '')
          formData.append('rangerMode', $('#fastq').find(':radio[name="rangerMode"]:checked').val() || '')
        } else if (dataMode === 'matrix') {
          $('#matrix_tbody').find('tr').each(function (index) {
            formData.append('files[' + index + '].runName', $(this).find('td:eq(0)').find('input[name="runName"]').val())
            $(this).find('td:eq(1)').find('b.text-primary').each(function () {
              formData.append('files[' + index + '].barcode.path', $(this).find('input[type=hidden]:eq(0)').val())
              formData.append('files[' + index + '].barcode.name', $(this).find('input[type=hidden]:eq(1)').val())
            })

            $(this).find('td:eq(2)').find('b.text-primary').each(function () {
              formData.append('files[' + index + '].feature.path', $(this).find('input[type=hidden]:eq(0)').val())
              formData.append('files[' + index + '].feature.name', $(this).find('input[type=hidden]:eq(1)').val())
            })

            $(this).find('td:eq(3)').find('b.text-primary').each(function () {
              formData.append('files[' + index + '].matrix.path', $(this).find('input[type=hidden]:eq(0)').val())
              formData.append('files[' + index + '].matrix.name', $(this).find('input[type=hidden]:eq(1)').val())
            })
          })
          formData.append('species', $('#matrix').find('select[name=\'species\']').val() || '')
          formData.append('version', 'hg38')
          formData.append('mapMethod', 'Count')
        } else {
          $('#csv_tbody').find('tr').each(function (index) {
            formData.append('files[' + index + '].runName', $(this).find('td:eq(0)').find('input[name="runName"]').val())
            $(this).find('td:eq(1)').find('b.text-primary').each(function (i) {
              formData.append('files[' + index + '].csv.path', $(this).find('input[type=hidden]:eq(0)').val())
              formData.append('files[' + index + '].csv.name', $(this).find('input[type=hidden]:eq(1)').val())
            })
          })
          formData.append('species', $('#csv').find('select[name=\'species\']').val() || '')
          formData.append('version', 'hg38')
          formData.append('mapMethod', 'Count')
        }

        if ($('#ngene').prop('checked')) {
          formData.append('nGeneStart', $('input[name=\'nGeneStart\']').val())
          formData.append('nGeneEnd', $('input[name=\'nGeneEnd\']').val())
        }
        if ($('#pomg').prop('checked')) {
          formData.append('toPerStart', $('input[name=\'toPerStart\']').val())
          formData.append('toPerEnd', $('input[name=\'toPerEnd\']').val())
        }

        if ($('#rp1').prop('checked')) {
          formData.append('resolution', $('input[name=\'resolution\']').val())
        }
        if ($('#tgenes').prop('checked')) {
          formData.append('tGenes', $('input[name=\'tGenes\']').val())
        }

        $.ajax({
          url: '/analysis/scrnaseq/saveGenomics',
          method: 'post',
          dataType: 'json',
          contentType: false,
          processData: false,
          data: formData,
          success: function (result) {
            if (result.success) {
              window.location.href = '[[@{/analysis/scrnaseq/list}]]?type=genomics'
            } else {
              layer.msg(result.message)
              $('#submitBtn').attr('onclick', 'uploadMetadata()')
            }
          }
        })
      }
    </script>
</th:block>
</html>
