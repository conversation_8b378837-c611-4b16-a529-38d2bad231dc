<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<nav class="side-bar" th:fragment="sideBar (menu)">
    <h5><i class="fa fa-sitemap text-muted"></i> Analysis </h5>
    <ul class="level-1 mb-3">

        <li th:if="${@modules.contains('rnaseq')}">
            <a href="javascript:void(0);"
               th:classappend="${#strings.startsWith(menu,'rnaseq') ? 'active' : ''}">RNA-Seq-DEG</a>

        </li>


        <li th:if="${@modules.contains('ase')}">
            <a href="javascript:void(0);"
               th:classappend="${#strings.startsWith(menu,'ase-a') ? 'active' : ''}">RNA-Seq-ASE-rMATS</a>

        </li>

        <li th:if="${@modules.contains('circrna')}">
            <a href="javascript:void(0);"
               th:classappend="${#strings.startsWith(menu,'circrna') ? 'active' : ''}">RNA-Seq-circRNA</a>

        </li>

        <li th:if="${@modules.contains('paean')}">
            <a href="javascript:void(0);"
               th:classappend="${#strings.startsWith(menu,'paean') ? 'active' : ''}">RNA-Seq-ASE-Paean</a>
        </li>

        <li th:if="${@modules.contains('scrnaseq')}">
            <a href="#nav-toggle-3"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'scrnaseq') ? '' : 'collapsed'}">scRNA-Seq_10X</a>
            <div id="nav-toggle-3" th:classappend="${#strings.startsWith(menu,'scrnaseq') ? '' : 'collapse'}">
                <ul class="level-2">

                    <li th:classappend="${menu == 'scrnaseq-analysis-genomics'}?'active'">
                        <a th:href="@{/analysis/scrnaseq/form/genomics}">Basic Analysis</a>
                    </li>
                    <li th:classappend="${menu == 'scrnaseq-advanced'}?'active'">
                        <a href="#nav-toggle-3-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'scrnaseq-advanced') ? '' : 'collapsed'}">
                            <a th:href="@{/analysis/scrnaseq/list(type='advanced')}">Advanced Analysis</a>
                        </a>
                        <div id="nav-toggle-3-1"
                             th:classappend="${#strings.startsWith(menu,'scrnaseq-advanced') ? '' : 'collapse'}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'scrnaseq-advanced-baseline'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/baseline}">Cluster Description</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-paga'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/paga}" style="font-size: 12px">Pseudotime
                                    Trajectory</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-deg'}?'active font-13'"
                                ><a
                                        th:href="@{/analysis/scrnaseq/form/deg}">DEG and Enrichment</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-genes'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/genes}">Gene Description</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-wgcna'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/wgcna}">WGCNA</a></li>
                            </ul>
                        </div>
                    </li>
                    <li th:classappend="${menu == 'scrnaseq-report-add'}?'active'">
                        <a th:href="@{/analysis/scrnaseq/report/form}">Report</a>
                    </li>
                </ul>
            </div>
        </li>

        <li th:if="${@modules.contains('scrnasmartseq')}">
            <a href="javascript:void(0);"
               th:classappend="${#strings.startsWith(menu,'scrnasmartseq') ? 'ac' : 'collapsed'}">Smart-Seq</a>

        </li>

        <li th:if="${@modules.contains('strnaseq')}">
            <a href="#nav-toggle-10"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'strnaseq') ? '' : 'collapsed'}">stRNA-Seq</a>
            <div id="nav-toggle-10" th:classappend="${#strings.startsWith(menu,'strnaseq') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li><a href="#nav-toggle-8-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'strnaseq-10x') ? 'collapsed' : ''}"> 10X Genomics
                        Visium</a>
                        <div id="nav-toggle-8-1"
                             th:classappend="${#strings.startsWith(menu,'strnaseq-10x') ? 'collapse' : ''}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'strnaseq-introduction'}?'active'"><a
                                        th:href="@{/strnaseq}">Introduction</a></li>
                                <li th:classappend="${menu == 'strnaseq-analysis'}?'active'"><a
                                        th:href="@{/analysis/strnaseq/form}">Basic Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </li>

        <li th:if="${@modules.contains('somatic')}">
            <a href="javascript:void(0);"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'somatic') ? '' : 'collapsed'}">WES/WGS-somatic</a>
            <div id="nav-toggle-4" th:classappend="${#strings.startsWith(menu,'somatic') ? '' : 'collapse'}">
                <ul class="level-2">
                    <!--/*
                    <li th:classappend="${menu == 'somatic-introduction'}?'active'">
                        <a th:href="@{/somatic}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'somatic-analysis'}?'active'">
                        <a th:href="@{/analysis/somatic/form}">Basic Analysis</a>
                    </li>
                    <li th:classappend="${menu == 'somatic-adv-analysis'}?'active'">
                        <a th:href="@{/analysis/somatic-adv/form}">Advanced Analysis</a>
                    </li>
                    */-->

                    <li><a href="#nav-toggle-4-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'somatic-cnvs') ? 'collapsed' : ''}">SNVs+indels</a>
                        <div id="nav-toggle-4-1"
                             th:classappend="${#strings.startsWith(menu,'somatic-cnvs') ? 'collapse' : ''}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'somatic-introduction'}?'active'"><a
                                        th:href="@{/somatic}">Introduction</a></li>
                                <li th:classappend="${menu == 'somatic-analysis'}?'active'"><a
                                        th:href="@{/analysis/somatic/form}">Basic Analysis</a></li>
                                <li th:classappend="${menu == 'somatic-adv-analysis'}?'active'"><a
                                        th:href="@{/analysis/somatic-adv/form}">Advanced Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                    <li>
                        <a href="#nav-toggle-4-2" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'somatic-cnvs') ? '' : 'collapsed'}">CNVs</a>
                        <div id="nav-toggle-4-2"
                             th:classappend="${#strings.startsWith(menu,'somatic-cnvs') ? '' : 'collapse'}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'somatic-cnvs-introduction'}?'active'"><a
                                        th:href="@{/somatic-cnvs}">Introduction</a></li>
                                <li th:classappend="${menu == 'somatic-cnvs-analysis'}?'active'"><a
                                        th:href="@{/analysis/somatic-cnvs/form}">Basic Analysis</a></li>
                            </ul>
                        </div>
                    </li>

                </ul>
            </div>
        </li>

        <li th:if="${@modules.contains('germline')}">
            <a href="javascript:void(0);"
               th:classappend="${#strings.startsWith(menu,'germline') ? 'active' : ''}">WES/WGS-germline</a>

        </li>

        <li th:if="${@modules.contains('methychip')}">
            <a href="javascript:void(0);"
               th:classappend="${#strings.startsWith(menu,'methychip') ? 'active' : ''}">Methylation
                BeadChip</a>

        </li>

        <li th:if="${@modules.contains('wgbs')}">
            <a href="javascript:void(0);"
               th:classappend="${#strings.startsWith(menu,'wgbs') ? 'active' : ''}">WGBS</a>

        </li>

        <li th:if="${@modules.contains('proteomics')}">
            <a href="javascript:void(0);"
               th:classappend="${#strings.startsWith(menu,'proteomics') ? 'active' : ''}">Proteomics</a>
        </li>
    </ul>
</nav>
</html>
