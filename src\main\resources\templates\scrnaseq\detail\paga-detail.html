<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">PAGA & Pseudotime trajectory</h4>
                <div class="tool-box">
                    <div class="tool-title mb-3">View Result</div>
                    <th:block th:switch="${taskVo.pagaTask.status}">
                        <div class="alert alert-info alert-with-icon alert-dismissible" role="alert" th:case="1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Prepared</p>
                        </div>
                        <div class="alert alert-success alert-with-icon alert-dismissible" role="alert" th:case="2">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Data prepared</p>
                        </div>
                        <div class="alert alert-dark alert-with-icon alert-dismissible" role="alert" th:case="3">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis done</p>
                        </div>
                        <div class="alert alert-danger alert-with-icon alert-dismissible" role="alert" th:case="-1">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                            <span class="alert-icon-wrap"><i class="fa fa-exclamation-circle fa-lg"></i></span>
                            <p class="m-0">Analysis Error</p>
                        </div>
                    </th:block>
                    <div class="form-group-box">
                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Parameters</a>
                        <div class="collapse show" id="coll-1">
                            <div class="pl-4 pt-2">
                                <div class="result-box">
                                    <div class="table-responsive mt-2">
                                        <table class="table table-bordered table-sm table-center table-middle mb-1">
                                            <thead>
                                            <tr class="thead-light">
                                                <th>Cluster Baseline ID</th>
                                                <th>Start time</th>
                                                <th>Status time</th>
                                                <th>Consuming</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td th:text="${taskVo.baselineTask.taskId}"></td>
                                                <td th:text="${#dates.format(taskVo.baselineTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${#dates.format(taskVo.baselineTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                                <td th:text="${taskVo.baselineTask.useTime}">26m55s</td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="form-group row align-items-center m-0">
                                        <label class="col-xl-1 col-lg-1 col-md-3 col-form-label pr-0">neighbor</label>
                                        <div class="col-xl-3 col-lg-3 col-md-4">
                                            <span class="text-primary" th:text="${taskVo.pagaTask.neighbors}">value</span>
                                        </div>
                                        <label class="col-xl-1 col-lg-2 col-md-3 col-form-label pr-0">threshold</label>
                                        <div class="col-xl-3 col-lg-3 col-md-4">
                                            <span class="text-primary" th:text="${taskVo.pagaTask.threshold}">value</span>
                                        </div>
                                    </div>
                                    <div class="form-group row align-items-baseline m-0">
                                        <label class="col-xl-2 col-lg-2 col-md-3 col-form-label pr-0">Compare cluster</label>
                                        <div class="col-xl-4 col-lg-4 col-md-4" th:with="clus=${#strings.listSplit(taskVo.pagaTask.MCluster, ';')}">
                                            <th:block th:each="c, sta : ${clus}">
                                                <span class="text-primary" th:text="${c}">Cluster_1</span><br th:unless="${sta.last}">
                                            </th:block>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group-box" th:if="${taskVo.pagaTask.status == 2}">
                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Analysis Result</a>
                        <div class="collapse show" id="coll-2">
                            <div class="tool-content">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">PAGA</h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='PAGA',runName =${taskVo.pagaTask.taskId})}"><i class="fa fa-download text-primary"></i></a>-->
                                        </div>
                                        <div id="chart-08" style="width: 100%;height: 500px"></div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="flex-grow-1 font-weight-bold border-top pb-2 text-center mb-0">Pseudotime trajectory </h6>
                                            <!--                                            <a th:href="@{/analysis/scrnaseq/common/download(code='pseudotime',runName =${taskVo.pagaTask.taskId})}"><i class="fa fa-download text-primary"></i></a>-->
                                        </div>
                                        <div id="chart-09" class="row"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tool-box">
                    <div class="tool-title">Reference</div>
                    <div class="tool-content">
                        <ul class="r-list">
                            <li>1. cell,2019,a cellular taxonomy of the bone marrow stroma in homeostasis and leukemia
                            </li>
                            <li>2. Nature Biotechnology,2014, The dynamics and regulators of cell fate decisions are
                                revealed by pseudotemporal ordering of single cells
                            </li>
                        </ul>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/echarts.min.js}"></script>
    <script th:src="@{/js/ecStat.js}"></script>
    <script th:src="@{/js/dataTool.min.js}"></script>
    <script>
        $(document).ready(function () {
            initChart08();
            initChart09();
        })

        function initChart09() {
            if (!document.getElementById('chart-09')) {
                return;
            }

            drawChart();

            function drawChart() {
                let myChart1, myChart2;
                $.ajax({
                    url: '/analysis/scrnaseq/paga/[[${taskVo.pagaTask.id}]]/9',
                    beforeSend: function () {
                        $("#chart-09").empty();
                        $('#chart-09').append(`
                            <div id="chart-09-1" class="col-lg-6"
                                 style="width: 50%;height: 500px"></div>
                            <div id="chart-09-2" class="col-lg-6"
                                 style="width: 50%;height: 500px"></div>
                        `)
                        $("#chart-09").show();

                        myChart1 = echarts.init(document.getElementById('chart-09-1'));
                        myChart2 = echarts.init(document.getElementById('chart-09-2'));
                        myChart1.clear();
                        myChart2.clear();
                        myChart1.showLoading();
                        myChart2.showLoading();
                    },
                    complete: function () {
                        myChart1.hideLoading();
                        myChart2.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-09").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-09").hide();
                            return;
                        }

                        var data = result.data;

                        var lines = data.lines, nodes = data.nodes, points = data.points;

                        var series1 = [];
                        var lineSeries = lines.map(function (item) {
                            var data = []
                            for (var i = 0; i < 2; i++) {
                                data.push([item.lineX[i], item.lineY[i]])
                            }
                            return {
                                data: data,
                                type: 'line',
                                symbol: 'none',
                                lineStyle: {
                                    color: '#585656'
                                }
                            }
                        })

                        var pointData = points.map(function (item) {
                            return [item.x, item.y, item.name]
                        })
                        var pointSerie = {
                            name: 'point',
                            data: pointData,
                            type: 'scatter',
                            symbolSize: 20,
                            color: '#000',
                            label: {
                                show: true,
                                formatter: function (param) {
                                    return param.data[2];
                                },
                                position: 'top'
                            }
                        }

                        let nodeMap = {};
                        nodes.sort((a, b) => a.name.localeCompare(b.name))
                        nodes.forEach(it => {
                            nodeMap[it.name] = nodeMap[it.name] || [];
                            nodeMap[it.name].push([it.x, it.y, it.y])
                        })
                        var nodeSeries = [];
                        for (let k in nodeMap) {
                            nodeSeries.push({
                                name: k,
                                data: nodeMap[k],
                                type: 'scatter',
                                symbolSize: 6,
                                color: getClusterColor(k),
                                // emphasis: {
                                //     label: {
                                //         show: false,
                                //         formatter: function (param) {
                                //             return param.data[2];
                                //         },
                                //         position: 'top'
                                //     }
                                // }
                            })
                        }

                        series1.push(...lineSeries, pointSerie, ...nodeSeries)
                        myChart1.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            legend: {
                                data: Object.keys(nodeMap)
                            },
                            xAxis: {
                                name: 'Component 1',
                                nameGap: 25,
                                nameLocation: 'center',
                                type: 'value'
                            },
                            yAxis: {
                                name: 'Component 2',
                                nameGap: 25,
                                nameLocation: 'center',
                                type: 'value'
                            },
                            grid: {
                                left: '4%',
                                right: '4%',
                                containLabel: true
                            },
                            series: series1
                        });

                        var series2 = [];
                        series2.push(...lineSeries, pointSerie, {
                            type: 'scatter',
                            symbolSize: 6,
                            data: nodes.map(it => [it.x, it.y, it.pseudotime]),
                            emphasis: {
                                label: {
                                    show: true,
                                    formatter: function (param) {
                                        return param.data[2];
                                    },
                                    position: 'top'
                                }
                            }
                        })
                        myChart2.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            title: {
                                text: 'Pseudotime',
                                left: '8%',
                                top: '1%',
                                textStyle: {
                                    fontSize: 12
                                }
                            },
                            visualMap: {
                                min: 0,
                                max: Math.ceil(Math.max(...nodes.map(it => it.pseudotime))),
                                seriesIndex: series2.length - 1,
                                dimension: 2,
                                precision: 1,
                                calculable: true,
                                orient: 'horizontal',
                                left: 'center',
                                top: '0%',
                                inRange: {
                                    color: ['#2fbce2', '#053061']
                                }
                            },
                            xAxis: {
                                name: 'Component 1',
                                nameGap: 25,
                                nameLocation: 'center',
                                type: 'value'
                            },
                            yAxis: {
                                name: 'Component 2',
                                nameGap: 25,
                                nameLocation: 'center',
                                type: 'value'
                            },
                            grid: {
                                left: '4%',
                                right: '4%',
                                containLabel: true
                            },
                            series: series2
                        });
                    }
                })
            }
        }

        function initChart08() {
            if (!document.getElementById('chart-08')) {
                return;
            }
            var myChart = echarts.init(document.getElementById('chart-08'));

            drawChart(myChart);

            function drawChart() {
                myChart.clear();
                $.ajax({
                    url: '/analysis/scrnaseq/paga/[[${taskVo.pagaTask.id}]]/8',
                    beforeSend: function () {
                        $("#chart-08").next().remove();
                        $("#chart-08").show();
                        myChart.showLoading();
                    },
                    complete: function () {
                        myChart.hideLoading();
                    },
                    success: function (result) {
                        if (result.error) {
                            $("#chart-08").after('<div class="bg-light text-muted p-3">无分析结果</div>')
                            $("#chart-08").hide();
                            return;
                        }

                        var data = result.data, lines = data.lines, nodes = data.nodes;

                        var series = lines.map(function (item) {
                            var data = []
                            for (var i = 0; i < 2; i++) {
                                data.push([item.lineX[i], item.lineY[i]])
                            }
                            return {
                                data: data,
                                type: 'line',
                                color: 'rgba(0,0,0, 0.3)',
                                lineStyle: {
                                    width: item.width * 20
                                }
                            }
                        })

                        var nodeData = nodes.map(function (item, idx) {
                            return {
                                value: [item.x, item.y, item.name, item.weight],
                                itemStyle: {
                                    color: getClusterColor(item.name)
                                }
                            }
                        })
                        var s = {
                            name: 'point',
                            data: nodeData,
                            type: 'scatter',
                            symbolSize: function (data) {
                                return data[3] * 200;
                            },
                            label: {
                                show: true,
                                formatter: function (param) {
                                    return param.data.value[2];
                                },
                                position: 'top'
                            }
                        }
                        series.push(s)

                        myChart.setOption({
                            toolbox: {
                                show: true,
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            xAxis: {
                                type: 'value',
                                show: false
                            },
                            yAxis: {
                                type: 'value',
                                show: false
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            series: series
                        })

                    }
                })
            }
        }
    </script>
</th:block>
</html>
