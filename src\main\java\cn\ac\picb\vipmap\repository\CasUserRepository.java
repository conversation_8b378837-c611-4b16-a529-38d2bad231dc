package cn.ac.picb.vipmap.repository;

import cn.ac.picb.vipmap.vo.CurrentUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface CasUserRepository extends JpaRepository<CurrentUser, String>, JpaSpecificationExecutor<CurrentUser> {

    Optional<CurrentUser> findFirstByUsername(String username);
}
