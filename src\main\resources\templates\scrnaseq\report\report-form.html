<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-report-add')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/report/form}" class="active">Add Report</a>
                            <a th:href="@{/analysis/scrnaseq/report}">Report List</a>
                        </div>
                    </div>
                    <div id="form-content">
                        <form class="form-custom" style="padding: 0 15px;" id="search-form">
                            <div class="form-group-box not-bb" style="border-top: transparent;" id="show-bb">
                                <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Input</a>

                                <div class="collapse show" id="coll-1">
                                    <div class="pl-4 pt-2">
                                        <h6 class="text-primary border-bottom pb-2">select task</h6>
                                        <div class="d-flex flex-wrap pl-4 pt-2">
                                            <div class="basic-name form-group row align-items-center ml-1">
                                                <label class="mx-2 font-12 ">Task Name of Basic Analysis</label>
                                                <div class="col-xl-4 col-lg-4 col-md-4 search">
                                                    <input class="form-control form-control-sm width-100 ml--16"
                                                           name="taskName" type="text"
                                                           onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                </div>
                                            </div>
                                            <div class="form-group ml-1 row align-items-center width-345">
                                                <label class="mx-2 font-12">Time</label>
                                                <div class="input-daterange input-group width-300">
                                                    <input type="text"
                                                           class="form-control form-control-sm max-width-97"
                                                           name="start"/>
                                                    <div class="input-group-append">
                                                            <span class="input-group-text"><i
                                                                    class="fa fa-calendar"></i></span>
                                                    </div>
                                                    <input type="text"
                                                           class="form-control form-control-sm max-width-97"
                                                           name="end"/>
                                                    <div class="input-group-append">
                                                            <span class="input-group-text"><i
                                                                    class="fa fa-calendar"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <button type="button"
                                                    class="btn btn-primary btn-sm h-31" id="search-btn"
                                                    onclick="filterTask()">Search
                                            </button>
                                        </div>
                                        <div class="table-result table-responsive" style="min-width: 890px;">
                                            <table id="tree-report"
                                                   class="table table-bordered table-sm table-center table-middle font-12 table-striped mb-0 treetable">
                                                <colgroup>
                                                    <col width="320px">
                                                    </col>

                                                    <col width="100px">
                                                    </col>
                                                    <col width="100px">
                                                    </col>
                                                    <col width="128px">
                                                    </col>

                                                    <col width="128px">
                                                    </col>
                                                    <col width="100px">
                                                    </col>

                                                </colgroup>
                                                <thead>
                                                <tr class="thead-light">
                                                    <th rowspan="5">Task ID</th>
                                                    <th>Task Name</th>
                                                    <th>Species</th>
                                                    <th>Start time</th>
                                                    <th>Status time</th>
                                                    <th>Consuming</th>

                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td colspan="6" style="text-align: center;">
                                                        <div class="spinner-border text-muted"></div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <div class="text-center pt-2" id="view">
                                            <button type="button" onclick="submitForm()"
                                                    class="btn btn-primary btn-sm h-31 ptb-2">View
                                            </button>
                                        </div>
                                        <div class="text-center pt-4 pb-2 br" id="checkedInfo"
                                             style="background-color: #e5f3fb;display: none">

                                        </div>
                                        <div id="content" style="display:none;"></div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-zh_CN.js}"></script>
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script>
      let genomicsId = null
      let baselineId = null
      let pagaId = null
      let degId = null
      let genesId = null
      let wgcnaId = null

      $(document).ready(function () {

        $("#view").click(function () {
          $(".view-show").show()
          $("#view button").addClass("btn-light")
          $('#show-bb').removeClass('not-bb')
        })

        $('.input-daterange').datepicker({
          format: "yyyy/mm/dd",
          toggleActive: true,
          autoclose: true,
          todayHighlight: true
        })
        filterTask()
      })

      function filterTask () {
        // 禁用按钮防止重复点击
        $('#search-btn').attr('disabled', true)
        $.ajax({
          url: "/analysis/scrnaseq/findAllCompleteTaskWithTree",
          data: $('#search-form').serialize(),
          type: 'post',
          dataType: 'json',
          success: function (result) {
            if (result.success) {
              source = getSource(result.data)
              obtainToReportTable(source)
            } else {
              layer.alert(result.message, { icon: 2 })
            }
            setTimeout(() => { $('#search-btn').attr('disabled', false)}, 1000)
          }
        })
      }

      function obtainToReportTable (source) {
        $('.table-result').html(`<table id="tree-report"
                                                   class="table table-bordered table-sm table-center table-middle font-12 table-striped mb-0 treetable">
                                                <colgroup>
                                                    <col width="320px">
                                                    </col>

                                                    <col width="100px">
                                                    </col>
                                                    <col width="100px">
                                                    </col>
                                                    <col width="128px">
                                                    </col>

                                                    <col width="128px">
                                                    </col>
                                                    <col width="100px">
                                                    </col>

                                                </colgroup>
                                                <thead>
                                                <tr class="thead-light">
                                                    <th rowspan="5">Task ID</th>
                                                    <th>Task Name</th>
                                                    <th>Species</th>
                                                    <th>Start time</th>
                                                    <th>Status time</th>
                                                    <th>Consuming</th>

                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td colspan="6" style="text-align: center;">
                                                        <div class="spinner-border text-muted"></div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>`)
        if (source.length === 0) {
          $('#tree-report').find('tbody').html(`<tr>
                                                    <td colspan="6" style="text-align: center;">
                                                        No data.
                                                    </td>
                                                </tr>`)
          return
        }

        $('#tree-report').find('tbody').html(`<tr>
                                                    <td style="text-align: left;">
                                                    </td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </tr>`)

        $("#tree-report").fancytree({
          extensions: ["table"],
          checkbox: false,
          generateIds: true,
          table: {
            indentation: 20,
          },
          source: source,

          renderColumns: function (event, data) {
            var node = data.node,
              $tdList = $(node.tr).find("> td")
            // (index #0 is rendered by fancytree by adding the checkbox)
            $tdList.eq(0).text(node.data.TaskId)
            $tdList.eq(1).text(node.data.TaskName)
            $tdList.eq(2).text(node.data.Speices)
            $tdList.eq(3).text(node.data.StartTime)
            $tdList.eq(4).text(node.data.StatusTime)
            $tdList.eq(5).text(node.data.Consuming)

          }
        })
      }

      function getSource (data) {
        let source = []
        data.forEach(it => {
          let item = {}
          obtainToNewTree(it, item)
          source.push(item)
        })
        return source
      }

      function obtainToNewTree (node, _newNode) {
        // node => n
        _newNode.title = generateTitle(node)
        _newNode.TaskName = node.taskName !== null ? node.taskName : ''
        _newNode.Species = node.species !== null ? node.species : ''
        _newNode.StartTime = node.createTime !== null ? node.createTime : ''
        _newNode.StatusTime = node.updateTime !== null ? node.updateTime : ''
        _newNode.Consuming = node.useTime !== null ? node.useTime : ''

        if (node.children === null || node.children.length === 0) {
          _newNode.expanded = false
          _newNode.folder = false
        } else {
          _newNode.expanded = true
          _newNode.folder = true
          _newNode.children = []
          node.children.forEach(it => {
            let child = {}
            _newNode.children.push(child)
            obtainToNewTree(it, child)
          })
        }
      }

      function generateTitle (node) {
        if (!node) {
          return ''
        }
        var nodeType = node.type
        var html = []

        switch (nodeType) {
          case "genomics":
            html.push(`<div nodeType="genomics"  id="${node.id}" taskId="${node.taskId}" parentId="${node.parentId}">
                            ${node.children !== null && node.children.length !== 0 ? '' : `<input type="radio" onclick="changeRadio(this)" nodeType="genomics" name="tree-input1" class="left" id="left-${node.id}-${node.taskId}">`}
                            Basic Analysis&nbsp;&nbsp;
                            <span class='badge badge-primary font-weight-normal'>${node.taskId}</span>
                       </div>`)
            break
          case "baseline":
            html.push(`<div nodeType="baseline"  id="${node.id}" taskId="${node.taskId}" parentId="${node.parentId}">
                            ${node.children !== null && node.children.length !== 0 ? '' : `<input type="radio" onclick="changeRadio(this)" nodeType="baseline" name="tree-input1" class="left" id="left-${node.id}-${node.taskId}">`}
                            Cluster Description&nbsp;&nbsp;
                            <span class='badge badge-primary font-weight-normal'>${node.taskId}</span>
                            </div>`)
            break
          case "paga":
            html.push(`<div nodeType="paga"  id="${node.id}" taskId="${node.taskId}" parentId="${node.parentId}">
                            ${node.children !== null && node.children.length !== 0 ? '' : `<input type="radio" onclick="changeRadio(this)" nodeType="paga" name="tree-input2" class="root" id="root-${node.id}-${node.taskId}">`}
                            Pseudotime Trajectory&nbsp;&nbsp;
                            <span class='badge badge-primary font-weight-normal'>${node.taskId}</span>
                        </div>`)
            break
          case "deg":
            html.push(`<div nodeType="deg"  id="${node.id}" taskId="${node.taskId}" parentId="${node.parentId}">
                            ${node.children !== null && node.children.length !== 0 ? '' : `<input type="radio"  onclick="changeRadio(this)" nodeType="deg" name="tree-input3" class="root" id="root-${node.id}-${node.taskId}">`}
                            DEG and Enrichment &nbsp;&nbsp;
                            <span class='badge badge-primary font-weight-normal'>${node.taskId}</span>
                        </div>`)
            break
          case "genes":
            html.push(`<div nodeType="genes"  id="${node.id}" taskId="${node.taskId}" parentId="${node.parentId}">
                            ${node.children !== null && node.children.length !== 0 ? '' : `<input type="radio" onclick="changeRadio(this)" nodeType="genes" name="tree-input4" class="root" id="root-${node.id}-${node.taskId}">`}
                            Gene Description&nbsp;&nbsp;
                            <span class='badge badge-primary font-weight-normal'>${node.taskId}</span>
                       </div>`)
            break
          case "wgcna":
            html.push(`<div nodeType="wgcna"  id="${node.id}" taskId="${node.taskId}" parentId="${node.parentId}">
                            ${node.children !== null && node.children.length !== 0 ? '' : `<input type="radio" onclick="changeRadio(this)" nodeType="wgcna" name="tree-input5" class="root" id="root-${node.id}-${node.taskId}">`}
                            WGCNA&nbsp;&nbsp;
                            <span class='badge badge-primary font-weight-normal'>${node.taskId}</span>
                       </div>`)
            break
          default:
            html.push('')
            break
        }
        return html.join('')
      }

      /* Handle custom checkbox clicks */
      $("#tree-report").on("click", "input[name=like]", function (e) {
        var node = $.ui.fancytree.getNode(e),
          $input = $(e.target)

        e.stopPropagation()  // prevent fancytree activate for this row
        if ($input.is(":checked")) {
          alert("like " + node)
        } else {
          alert("dislike " + node)
        }
      })

      function changeRadio (_this) {
        let checkedState = $(_this).attr('checked')
        let checkedID = $(_this).attr('id')
        if ($(_this).attr('class') === 'root') {
          if (checkedState === 'checked') {
            $(_this).attr('checked', false)
            $(_this).prop('checked', false)
            if ($("input[id^=root-]:checked").length === 0) {
              $(_this).parents("tr").siblings("tr").find("input[id^=left-]").attr('disabled', false)
            } else {
              $(_this).parents("tr").siblings("tr").find("input[id^=left-]").attr('disabled', true)
            }
          } else {

            $(_this).parents("tr").siblings("tr").find("input[id^=left-]").attr('disabled', true)
            $(_this).attr('checked', true)
          }
        } else {
          if (checkedState === 'checked') {
            $(_this).parents("tr").siblings("tr").find("input[id^=root-]").attr('disabled', false)
            $(_this).parents("tr").siblings("tr").find("input[id^=left-]").attr('disabled', false)
            $(_this).attr('checked', false)
            $(_this).prop('checked', false)
          } else {
            $(_this).parents("tr").siblings("tr").find("input[id^=root-]").attr('disabled', true)
            $(_this).attr('disabled', false)
            $(_this).parents("tr").siblings("tr").find("input[id^=left-]").attr('disabled', true)
            $(_this).attr('checked', true)
          }
        }
        getCheckedInfo()
      }

      function getCheckedInfo () {

        let genomicsTaskId
        let baselineTaskId
        let pagaTaskId
        let degTaskId
        let genesTaskId
        let wgcnaTaskId

        genomicsId = null
        baselineId = null
        pagaId = null
        degId = null
        genesId = null
        wgcnaId = null

        $(':radio:checked').each(function (index, _this) {

          let _parDiv = $(_this).parent()
          let nodeType = $(_parDiv).attr('nodeType')
          let id = $(_parDiv).attr('id')
          let taskId = $(_parDiv).attr('taskId')
          let parentId = $(_parDiv).attr('parentId')

          let pid

          switch (nodeType) {
            case "genomics":
              genomicsId = id
              genomicsTaskId = taskId
              break
            case "baseline":
              baselineId = id
              baselineTaskId = taskId
              genomicsId = $(`div[id="${parentId}"]`).attr('id')
              genomicsTaskId = $(`div[id="${parentId}"]`).attr('taskId')
              break
            case "paga":
              pagaId = id
              pagaTaskId = taskId
              baselineId = $(`div[id="${parentId}"]`).attr('id')
              baselineTaskId = $(`div[id="${parentId}"]`).attr('taskId')
              pid = $(`div[id="${parentId}"]`).attr('parentId')
              genomicsId = $(`div[id="${pid}"]`).attr('id')
              genomicsTaskId = $(`div[id="${pid}"]`).attr('taskId')
              break
            case "deg":
              degId = id
              degTaskId = taskId
              baselineId = $(`div[id="${parentId}"]`).attr('id')
              baselineTaskId = $(`div[id="${parentId}"]`).attr('taskId')
              pid = $(`div[id="${parentId}"]`).attr('parentId')
              genomicsId = $(`div[id="${pid}"]`).attr('id')
              genomicsTaskId = $(`div[id="${pid}"]`).attr('taskId')
              break
            case "genes":
              genesId = id
              genesTaskId = taskId
              baselineId = $(`div[id="${parentId}"]`).attr('id')
              baselineTaskId = $(`div[id="${parentId}"]`).attr('taskId')
              pid = $(`div[id="${parentId}"]`).attr('parentId')
              genomicsId = $(`div[id="${pid}"]`).attr('id')
              genomicsTaskId = $(`div[id="${pid}"]`).attr('taskId')
              break
            case "wgcna":
              wgcnaId = id
              wgcnaTaskId = taskId
              baselineId = $(`div[id="${parentId}"]`).attr('id')
              baselineTaskId = $(`div[id="${parentId}"]`).attr('taskId')
              pid = $(`div[id="${parentId}"]`).attr('parentId')
              genomicsId = $(`div[id="${pid}"]`).attr('id')
              genomicsTaskId = $(`div[id="${pid}"]`).attr('taskId')
              break
            default:
              break
          }
        })
        let info = []
        if (genomicsId && genomicsId !== undefined) {
          info.push(`<p>*You have selected tasks: Basic Analysis <span
                                                    class='badge badge-primary font-weight-normal'>${genomicsTaskId}</span>
                  </p>`)
        }
        if (baselineId && genomicsId !== undefined) {
          info.push(`<p>Cluster Description <span
                                                        class='badge badge-primary font-weight-normal'>${baselineTaskId}</span>
                                            </p>`)
        }
        if (pagaId && pagaId !== undefined) {
          info.push(`<p>Pseudotime Trajectory <span
                                                        class='badge badge-primary font-weight-normal'>${pagaTaskId}</span>
                                            </p>`)
        }
        if (degId && degId !== undefined) {
          info.push(`<p>DEG and Enrichment <span
                                                        class='badge badge-primary font-weight-normal'>${degTaskId}</span>
                                            </p>`)
        }
        if (genesId && genesId !== undefined) {
          info.push(`<p>Gene Description <span
                                                        class='badge badge-primary font-weight-normal'>${genesTaskId}</span>
                                            </p>`)
        }
        if (wgcnaId && wgcnaId !== undefined) {
          info.push(`<p>WGCNA <span
                                                        class='badge badge-primary font-weight-normal'>${wgcnaTaskId}</span>
                                            </p>`)
        }

        $('#checkedInfo').html(info.join(''))
        if (info.length !== 0) {
          $('#checkedInfo').show()
        } else {
          $('#checkedInfo').hide()
        }
      }

      function submitForm () {
        if (!genomicsId) {
          layer.msg('please select genomic task')
          return
        }
        let data = {}
        if (genomicsId) {
          data.genomicsId = genomicsId
        }
        if (baselineId) {
          data.baselineId = baselineId
        }
        if (pagaId) {
          data.pagaId = pagaId
        }
        if (degId) {
          data.degId = degId
        }
        if (genesId) {
          data.genesId = genesId
        }
        if (wgcnaId) {
          data.wgcnaId = wgcnaId
        }
        // console.log(data)
        $.ajax({
          url: '/analysis/scrnaseq/report/getSelectData',
          type: 'post',
          data: data,
          success: function (res) {
            $("#content").html(res)
            $("#content").show()
          }
        })
      }
    </script>
</th:block>
</html>
