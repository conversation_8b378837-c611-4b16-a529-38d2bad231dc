<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
<head>
    <meta charset="UTF-8">
    <meta name="_context_path" th:content="${#strings.defaultString(#httpServletRequest.getContextPath(), ' ')}"/>

    <title>Visualization integrated Precision Medicine Analytics Platform</title>
    <link type="text/css" rel="stylesheet" th:href="@{/bootstrap-4.3.1-dist/css/bootstrap.min.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/font-awesome-4.7.0/css/font-awesome.min.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/css/select2.min.css}"/>
    <link rel="stylesheet" th:href="@{/js/datatables/datatables.min.css}">
    <th:block layout:fragment="custom-style"></th:block>
    <link type="text/css" rel="stylesheet" th:href="@{/css/style.css}"/>
    <script th:src="@{/js/jquery-3.4.1.min.js}"></script>
    <script th:src="@{/js/setTokenFromHashToCookie.js}"></script>
</head>
<body>
<header>
    <div class="container-fulid">
        <div class="d-flex justify-content-between align-items-center">
            <div class="logo">
                <a th:href="@{/home}" class="logo-text"
                   alt="Visualization integrated Precision Medicine Analytics Platform "
                   title="Visualization integrated Precision Medicine Analytics Platform ">
                    <span class="logo-bda">BDA</span>
                </a>
            </div>

            <div class="d-flex align-items-center">
                <!-- 平台切换导航 -->
                <div class="platform-nav mr-3">
                    <div class="nav-select active" data-platform="precision">
                        精准医学大数据分析平台
                    </div>
                    <div class="nav-select" data-platform="microbe" th:onclick="'window.location.href=\'' + ${@urlMap.get('imac')} + '\''">
                        微生物云分析平台
                    </div>
                </div>

                <ul class="header-user" sec:authorize="isAnonymous()">
                    <li><a th:href="@{${@urlMap.get('loginPage')}}"><i
                            class="fa fa-sign-in"></i> Sign in</a></li>
                </ul>
                <div class="header-login" sec:authorize="isAuthenticated()">
                <a class="btn btn-light btn-sm"><i class="fa fa-user-circle-o mr-1"></i>[[${#authentication.principal.username}]]</a>
                <div class="login-card">
                    <div class="login-info">
                        <a th:href="@{${@urlMap.get('bmdc') + '/setting/member?memberId=' + #authentication.principal.id}}">
                            <h5 class="font-weight-bold">[[${#authentication.principal.username}]]</h5>
                        </a>
                        <div class="ml-auto">
                            <a th:href="@{/logout}" class="btn btn-light btn-sm"><i class="fa fa-power-off"></i></a>
                        </div>
                    </div>
                    <div class="login-content">
                        <div class="row row-sm">
                            <div th:if="${@modules.contains('rnaseq')}" class="col-12">
                                <a th:href="@{/analysis/rnaseq/list}" class="lc-box">RNA-Seq-DEG</a>
                            </div>
                            <div th:if="${@modules.contains('ase')}" class="col-12">
                                <a th:href="@{/analysis/ase/list}" class="lc-box">RNA-Seq-ASE</a>
                            </div>
                            <div th:if="${@modules.contains('paean')}" class="col-12">
                                <a th:href="@{/analysis/paean/list}" class="lc-box">RNA-Seq-ASE-GPU</a>
                            </div>
                            <div th:if="${@modules.contains('circrna')}" class="col-12">
                                <a th:href="@{/analysis/circrna/list}" class="lc-box">RNA-Seq-CircleRNA</a>
                            </div>
                            <div th:if="${@modules.contains('scrnasmartseq')}" class="col-12">
                                <a th:href="@{/analysis/scrnaSmartseq/list}" class="lc-box">RNA-Seq-SmartSeq</a>
                            </div>
                            <div th:if="${@modules.contains('somatic')}" class="col-12">
                                <a th:href="@{/analysis/somatic/list}" class="lc-box">DNA-Seq-Somatic-SNV</a>
                            </div>
                            <div th:if="${@modules.contains('somatic')}" class="col-12">
                                <a th:href="@{/analysis/somatic-cnvs/list}" class="lc-box">DNA-Seq-Somatic-CNV</a>
                            </div>
                            <div th:if="${@modules.contains('germline')}" class="col-12">
                                <a th:href="@{/analysis/germline/list}" class="lc-box">DNA-Seq-Germline</a>
                            </div>
                            <div th:if="${@modules.contains('wgbs')}" class="col-12">
                                <a th:href="@{/analysis/wgbs/list}" class="lc-box">Methylation-WGBS</a>
                            </div>
                            <div th:if="${@modules.contains('methychip')}" class="col-12">
                                <a th:href="@{/analysis/methychip/list}" class="lc-box">Methylation-BeadChip</a>
                            </div>
                            <div th:if="${@modules.contains('scrnaseq')}" class="col-12">
                                <a th:href="@{/analysis/scrnaseq/all}" class="lc-box">scRNA-Seq-10X</a>
                            </div>
                            <div th:if="${@modules.contains('strnaseq')}" class="col-12">
                                <a th:href="@{/analysis/strnaseq/list}" class="lc-box">stRNA-Seq-10X</a>
                            </div>
                            <div th:if="${@modules.contains('proteomics')}" class="col-12">
                                <a th:href="@{/analysis/proteomics/list}" class="lc-box">Proteomics</a>
                            </div>
                            <div sec:authorize="hasRole('ADMIN')" class="col-12">
                                <a th:href="@{/admin/list}" class="lc-box">Admin</a>
                            </div>
                            <div class="col-12">
                                <a th:href="@{${@urlMap.get('pdms') + '/user/home'}}" class="lc-box">My Data</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="menu">
        <div class="container">
            <nav class="navbar navbar-expand-lg">
                <button class="navbar-toggler" type="button" data-toggle="collapse"
                        data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav justify-content-between">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/home}">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="javascript:void(0);">Analysis</a>
                            <ul class="dropdown-menu">
                                <li th:if="${@modules.contains('rnaseq')}"><a th:href="@{/rnaseq}">RNA-Seq-DEG</a></li>
                                <li th:if="${@modules.contains('ase')}"><a th:href="@{/ase}">RNA-Seq-ASE</a></li>
                                <li th:if="${@modules.contains('paean')}"><a th:href="@{/paean}">RNA-Seq-ASE-GPU</a>
                                <li th:if="${@modules.contains('circrna')}"><a
                                        th:href="@{/circrna}">RNA-Seq-CircleRNA</a>
                                </li>
                                </li>
                                <li th:if="${@modules.contains('scrnasmartseq')}"><a th:href="@{/scrnasmartseq}">RNA-Seq-SmartSeq</a>
                                <li th:if="${@modules.contains('somatic')}"><a
                                        th:href="@{/somatic}">DNA-Seq-Somatic-SNV</a>
                                </li>
                                <li th:if="${@modules.contains('somatic')}"><a th:href="@{/somatic-cnvs}">DNA-Seq-Somatic-CNV</a>
                                </li>
                                <li th:if="${@modules.contains('germline')}"><a
                                        th:href="@{/germline}">DNA-Seq-Germline</a></li>
                                </li>
                                <li th:if="${@modules.contains('wgbs')}"><a th:href="@{/wgbs}">Methylation-WGBS</a></li>
                                <li th:if="${@modules.contains('methychip')}"><a th:href="@{/methychip}">Methylation-BeadChip</a>
                                </li>
                                <li th:if="${@modules.contains('scrnaseq')}"><a th:href="@{/scrnaseq}">scRNA-Seq-10X</a>
                                </li>
                                <li th:if="${@modules.contains('strnaseq')}"><a th:href="@{/strnaseq}">stRNA-seq-10X</a>
                                </li>
                                <li th:if="${@modules.contains('proteomics')}"><a
                                        th:href="@{/proteomics}">Proteomics</a></li>
                            </ul>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="javascript:void(0);">About</a>
                            <ul class="dropdown-menu">
                                <li><a th:href="@{/guidelines}">Guidelines</a></li>
                                <li><a th:href="@{/contactUs}">Contact us</a></li>
                            </ul>
                        </li>
                        <!--<li class="nav-item">
                            <a class="nav-link" href="javascript:void(0);">Links</a>
                            <ul class="dropdown-menu">
                                <li><a href="https://www.biosino.org/ideas/">iDeas</a></li>
                                <li><a href="javascript:void(0);">Cell Markers</a></li>
                            </ul>
                        </li>-->
                    </ul>
                </div>
            </nav>
        </div>
    </div>
</header>
<div layout:fragment="content"></div>
<!--<footer class="pt-3 pb-3">
    <div class="container-fulid">
        <h6 class="text-center text-muted m-0">© 2021 Shanghai Institute of Nutrition and Health, CAS</h6>
    </div>
</footer>-->
<div class="upload-sd">
    <a th:href="@{${@urlMap.get('pdms') + '/user/home'}}" target="_blank" class="my-sd">Submit Data</a>
</div>
<script th:src="@{/bootstrap-4.3.1-dist/js/bootstrap.bundle.min.js}"></script>
<script th:src="@{/js/layer/layer.js}"></script>
<script th:src="@{/js/main.js}"></script>
<script th:src="@{/js/settings.js}"></script>
<script th:src="@{/js/bootstrap-datepicker.js}"></script>
<script th:src="@{/js/bootstrap-tagsinput.js}"></script>
<script th:src="@{/js/datatables/datatables.min.js}"></script>
<script th:src="@{/select2/select2.min.js}"></script>
<script>
  var pheatmap_renderer_server = "[[${@urlMap.get('pheatmap')}]]"

  // 平台切换功能
  $(document).ready(function() {
    $('.platform-nav .nav-select').click(function() {
      // 移除所有active类
      $('.platform-nav .nav-select').removeClass('active');
      // 给当前点击的元素添加active类
      $(this).addClass('active');
    });
  });

</script>
<th:block layout:fragment="custom-script"></th:block>
</body>
</html>
