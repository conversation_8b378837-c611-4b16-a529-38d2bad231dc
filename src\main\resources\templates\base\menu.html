<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<nav class="side-bar" th:fragment="sideBar (menu)">
    <h5><i class="fa fa-sitemap text-muted"></i> Analysis </h5>
    <ul class="level-1 mb-3">

        <li>
            <a href="#nav-toggle-t1"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'rnaseq') ? '' : 'collapsed'}"
            >RNA-Seq</a>
            <div id="nav-toggle-t1" th:classappend="${#strings.startsWith(menu,'rnaseq') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t1-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'rnaseq-deg') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'rnaseq-deg') ? 'active' : ''}">DEG</a>
                        </a>
                        <div id="nav-toggle-t1-1"
                             th:classappend="${#strings.startsWith(menu,'rnaseq-deg') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'rnaseq-deg-introduction'}?'active'">
                                    <a th:href="@{/rnaseq}">Introduction</a>
                                </li>
                                <li th:classappend="${menu == 'rnaseq-deg-analysis'}?'active'">
                                    <a th:href="@{/analysis/rnaseq/form}">Start Analysis</a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t1-2" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'rnaseq-ase') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'rnaseq-ase') ? 'active' : ''}">ASE</a>
                        </a>
                        <div id="nav-toggle-t1-2"
                             th:classappend="${#strings.startsWith(menu,'rnaseq-ase') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'rnaseq-ase-introduction'}?'active'">
                                    <a th:href="@{/ase}">Introduction</a>
                                </li>
                                <li th:classappend="${menu == 'rnaseq-ase-analysis'}?'active'">
                                    <a th:href="@{/analysis/ase/form}">Start Analysis</a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>

                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t1-3" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'rnaseq-paean') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'rnaseq-paean') ? 'active' : ''}">ASE-GPU</a>
                        </a>
                        <div id="nav-toggle-t1-3"
                             th:classappend="${#strings.startsWith(menu,'rnaseq-paean') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'rnaseq-paean-introduction'}?'active'">
                                    <a th:href="@{/paean}">Introduction</a>
                                </li>
                                <li th:classappend="${menu == 'rnaseq-paean-demo'}?'active'">
                                    <a th:href="@{/analysis/paean/demo}">Demo</a>
                                </li>
                                <li th:classappend="${menu == 'rnaseq-paean-analysis'}?'active'">
                                    <a th:href="@{/analysis/paean/form}">Start Analysis</a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t1-4" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'rnaseq-circrna') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'rnaseq-circrna') ? 'active' : ''}">CircleRNA</a>
                        </a>
                        <div id="nav-toggle-t1-4"
                             th:classappend="${#strings.startsWith(menu,'rnaseq-circrna') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'rnaseq-circrna-introduction'}?'active'">
                                    <a th:href="@{/circrna}">Introduction</a>
                                </li>
                                <li th:classappend="${menu == 'rnaseq-circrna-analysis'}?'active'">
                                    <a th:href="@{/analysis/circrna/form}">Start Analysis</a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t1-5" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'rnaseq-scrnasmartseq') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'rnaseq-scrnasmartseq') ? 'active' : ''}">SmartSeq</a>
                        </a>
                        <div id="nav-toggle-t1-5"
                             th:classappend="${#strings.startsWith(menu,'rnaseq-scrnasmartseq') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'rnaseq-scrnasmartseq-introduction'}?'active'">
                                    <a th:href="@{/scrnasmartseq}">Introduction</a>
                                </li>
                                <li th:classappend="${menu == 'rnaseq-scrnasmartseq-analysis'}?'active'">
                                    <a th:href="@{/analysis/scrnaSmartseq/form}">Start Analysis</a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </li>
        <li>
            <a href="#nav-toggle-t2"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'dnaseq') ? '' : 'collapsed'}"
            >DNA-Seq</a>
            <div id="nav-toggle-t2" th:classappend="${#strings.startsWith(menu,'dnaseq') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t2-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'dnaseq-somatic') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'dnaseq-somatic') ? 'active' : ''}">Somatic-SNV</a>
                        </a>
                        <div id="nav-toggle-t2-1"
                             th:classappend="${#strings.startsWith(menu,'dnaseq-somatic') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'dnaseq-somatic-introduction'}?'active'"><a
                                        th:href="@{/somatic}">Introduction</a></li>
                                <li th:classappend="${menu == 'dnaseq-somatic-analysis'}?'active'"><a
                                        th:href="@{/analysis/somatic/form}">Basic Analysis</a></li>
                                <li th:classappend="${menu == 'dnaseq-somatic-adv-analysis'}?'active'"><a
                                        th:href="@{/analysis/somatic-adv/form}">Advanced Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t2-2" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'dnaseq-somatic-cnvs') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'dnaseq-somatic-cnvs') ? 'active' : ''}">Somatic-CNV</a>
                        </a>
                        <div id="nav-toggle-t2-2"
                             th:classappend="${#strings.startsWith(menu,'dnaseq-somatic-cnvs') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'dnaseq-somatic-cnvs-introduction'}?'active'"><a
                                        th:href="@{/somatic-cnvs}">Introduction</a></li>
                                <li th:classappend="${menu == 'dnaseq-somatic-cnvs-analysis'}?'active'"><a
                                        th:href="@{/analysis/somatic-cnvs/form}">Basic Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t2-3" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'dnaseq-germline') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'dnaseq-germline') ? 'active' : ''}">Germline</a>
                        </a>
                        <div id="nav-toggle-t2-3"
                             th:classappend="${#strings.startsWith(menu,'dnaseq-germline') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'dnaseq-germline-introduction'}?'active'"><a
                                        th:href="@{/germline}">Introduction</a></li>
                                <li th:classappend="${menu == 'dnaseq-germline-analysis'}?'active'"><a
                                        th:href="@{/analysis/germline/form}">Start Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </li>

        <li>
            <a href="#nav-toggle-t3"
               data-toggle="collapse" th:classappend="${#strings.startsWith(menu,'methylation') ? '' : 'collapsed'}"
            >Methylation</a>
            <div id="nav-toggle-t3" th:classappend="${#strings.startsWith(menu,'methylation') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t3-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'methylation-wgbs') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'methylation-wgbs') ? 'active' : ''}">WGBS</a>
                        </a>
                        <div id="nav-toggle-t3-1"
                             th:classappend="${#strings.startsWith(menu,'methylation-wgbs') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'methylation-wgbs-introduction'}?'active'"><a
                                        th:href="@{/wgbs}">Introduction</a></li>
                                <li th:classappend="${menu == 'methylation-wgbs-analysis'}?'active'"><a
                                        th:href="@{/analysis/wgbs/form}">Start Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
                <ul class="level-2">
                    <li>
                        <a href="#nav-toggle-t3-2" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'methylation-beadchip') ? '' : 'collapsed'}">
                            <a th:classappend="${#strings.startsWith(menu,'methylation-beadchip') ? 'active' : ''}">BeadChip</a>
                        </a>
                        <div id="nav-toggle-t3-2"
                             th:classappend="${#strings.startsWith(menu,'methylation-beadchip') ? '' : 'collapse'}"
                        >
                            <ul class="level-2">
                                <li th:classappend="${menu == 'methylation-beadchip-introduction'}?'active'"><a
                                        th:href="@{/methychip}">Introduction</a></li>
                                <li th:classappend="${menu == 'methylation-beadchip-analysis'}?'active'"><a
                                        th:href="@{/analysis/methychip/form}">Start Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </li>

        <li th:if="${@modules.contains('scrnaseq')}">
            <a href="#nav-toggle-3"
               data-toggle="collapse"
               th:classappend="${#strings.startsWith(menu,'scrnaseq') ? '' : 'collapsed'}">scRNA-Seq</a>
            <div id="nav-toggle-3" th:classappend="${#strings.startsWith(menu,'scrnaseq') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'scrnaseq-introduction'}?'active'">
                        <a th:href="@{/scrnaseq}">Introduction</a>
                    </li>
                    <!--<li th:classappend="${menu == 'scrnaseq-analysis'}?'active'">
                        <a th:href="@{/analysis/scrnaseq/all}">Start Analysis</a>
                    </li>-->
                    <li th:classappend="${menu == 'scrnaseq-analysis-genomics'}?'active'">
                        <a th:href="@{/analysis/scrnaseq/form/genomics}">Basic Analysis</a>
                    </li>
                    <li th:classappend="${menu == 'scrnaseq-advanced'}?'active'">
                        <a href="#nav-toggle-3-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'scrnaseq-advanced') ? '' : 'collapsed'}">
                            <a th:href="@{/analysis/scrnaseq/list(type='advanced')}">Advanced Analysis</a>
                        </a>
                        <div id="nav-toggle-3-1"
                             th:classappend="${#strings.startsWith(menu,'scrnaseq-advanced') ? '' : 'collapse'}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'scrnaseq-advanced-baseline'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/baseline}">Cluster Description</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-paga'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/paga}" style="font-size: 12px">Pseudotime
                                    Trajectory</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-deg'}?'active font-13'"
                                ><a
                                        th:href="@{/analysis/scrnaseq/form/deg}">DEG and Enrichment</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-genes'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/genes}">Gene Description</a></li>
                                <li th:classappend="${menu == 'scrnaseq-advanced-wgcna'}?'active'"><a
                                        th:href="@{/analysis/scrnaseq/form/wgcna}">WGCNA</a></li>
                            </ul>
                        </div>
                    </li>
                    <li th:classappend="${menu == 'scrnaseq-report-add'}?'active'">
                        <a th:href="@{/analysis/scrnaseq/report/form}">Report</a>
                    </li>
                </ul>
            </div>
        </li>

        <li th:if="${@modules.contains('strnaseq')}"><a href="#nav-toggle-10"
                                                        data-toggle="collapse"
                                                        th:classappend="${#strings.startsWith(menu,'strnaseq') ? '' : 'collapsed'}">stRNA-Seq</a>
            <div id="nav-toggle-10" th:classappend="${#strings.startsWith(menu,'strnaseq') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li><a href="#nav-toggle-8-1" data-toggle="collapse"
                           th:classappend="${#strings.startsWith(menu,'strnaseq-10x') ? 'collapsed' : ''}"> 10X Genomics
                        Visium</a>
                        <div id="nav-toggle-8-1"
                             th:classappend="${#strings.startsWith(menu,'strnaseq-10x') ? 'collapse' : ''}">
                            <ul class="level-2">
                                <li th:classappend="${menu == 'strnaseq-introduction'}?'active'"><a
                                        th:href="@{/strnaseq}">Introduction</a></li>
                                <li th:classappend="${menu == 'strnaseq-analysis'}?'active'"><a
                                        th:href="@{/analysis/strnaseq/form}">Basic Analysis</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </li>

        <li th:if="${@modules.contains('proteomics')}"><a href="#nav-toggle-6"
                                                          data-toggle="collapse"
                                                          th:classappend="${#strings.startsWith(menu,'proteomics') ? '' : 'collapsed'}">Proteomics</a>
            <div id="nav-toggle-6" th:classappend="${#strings.startsWith(menu,'proteomics') ? '' : 'collapse'}">
                <ul class="level-2">
                    <li th:classappend="${menu == 'proteomics-introduction'}?'active'">
                        <a th:href="@{/proteomics}">Introduction</a>
                    </li>
                    <li th:classappend="${menu == 'proteomics-analysis'}?'active'">
                        <a th:href="@{/analysis/proteomics/form}">Start Analysis</a>
                    </li>
                </ul>
            </div>
        </li>
    </ul>
</nav>
</html>
