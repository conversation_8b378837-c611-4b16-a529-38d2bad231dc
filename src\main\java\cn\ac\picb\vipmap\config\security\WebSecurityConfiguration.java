package cn.ac.picb.vipmap.config.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.web.filter.ForwardedHeaderFilter;

/**
 * <AUTHOR>
 */
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfiguration extends WebSecurityConfigurerAdapter {

    @Autowired
    private RdrProperties rdrProperties;

    @Autowired
    private RdrUserDetailsService rdrUserDetailsService;

    @Bean
    FilterRegistrationBean<ForwardedHeaderFilter> forwardedHeaderFilter() {
        FilterRegistrationBean<ForwardedHeaderFilter> filterRegBean = new FilterRegistrationBean<>();
        filterRegBean.setFilter(new ForwardedHeaderFilter());
        filterRegBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return filterRegBean;
    }

    /**
     * 解决 无法直接注入 AuthenticationManager
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    /**
     * 定义认证用户信息获取来源，密码校验规则等
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        super.configure(auth);
        auth.authenticationProvider(rdrAuthenticationProvider());
    }

    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers("/css/**", "/images/**", "/js/**", "/select2/**", "/font-awesome-4.7.0/**", "/bootstrap-4.3.1-dist/**");
    }

    /**
     * 定义安全策略
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                .csrf().disable()
                .authorizeRequests()
                .antMatchers("/analysis/**").authenticated()
                .antMatchers("/admin/**").authenticated()
                .antMatchers("/actuator/**").permitAll()
                .and()
                .exceptionHandling()
                .authenticationEntryPoint(rdrAuthenticationEntryPoint())
                .and()
                .headers().frameOptions().disable()
                .and()
                .addFilterBefore(rdrLogoutFilter(), LogoutFilter.class)
                .addFilterBefore(rdrAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
    }

    @Bean
    public SavedRequestAwareAuthenticationSuccessHandler rdrSuccessHandler() {
        return new RdrSuccessHandler(rdrProperties);
    }

    @Bean
    public RdrAuthenticationFilter rdrAuthenticationFilter() throws Exception {
        RdrAuthenticationFilter rdrAuthenticationFilter = new RdrAuthenticationFilter("/login");
        rdrAuthenticationFilter.setAuthenticationManager(authenticationManagerBean());
        rdrAuthenticationFilter.setAuthenticationSuccessHandler(rdrSuccessHandler());
        return rdrAuthenticationFilter;
    }

    @Bean
    public AuthenticationProvider rdrAuthenticationProvider() {
        return new RdrAuthenticationProvider(rdrUserDetailsService);
    }

    @Bean
    public LogoutSuccessHandler rdrLogoutSuccessHandler() {
        return new RdrLogoutSuccessHandler(rdrProperties);
    }

    @Bean
    public LogoutHandler rdrLogoutHandler() {
        return new RdrLogoutHandler(rdrProperties);
    }

    @Bean
    public LogoutFilter rdrLogoutFilter() {
        LogoutFilter logoutFilter = new LogoutFilter(rdrLogoutSuccessHandler(), rdrLogoutHandler());
        logoutFilter.setFilterProcessesUrl("/logout");
        return logoutFilter;
    }

    @Bean
    public RdrAuthenticationEntryPoint rdrAuthenticationEntryPoint() {
        return new RdrAuthenticationEntryPoint(rdrProperties);
    }
}
