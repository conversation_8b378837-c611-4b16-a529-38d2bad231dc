package cn.ac.picb.vipmap.controller;


import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.methychip.enums.MethyChipTaskStatus;
import cn.ac.picb.methychip.po.MethyChipTaskPO;
import cn.ac.picb.methychip.vo.ChartParamVO;
import cn.ac.picb.methychip.vo.MethyChipTaskInput;
import cn.ac.picb.methychip.vo.MethyChipTaskVO;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.MethyChipService;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.MethyChipTaskParam;
import cn.ac.picb.vipmap.vo.MethyChipTaskSearchVO;
import feign.Response;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR> Li
 * @date 2022/3/1 11:31
 */
@Controller
@RequestMapping("/analysis/methychip")
public class MethyChipController {

    private final MethyChipService methyChipService;
    private final AppProperties appProperties;

    public MethyChipController(MethyChipService methyChipService, AppProperties appProperties) {
        this.methyChipService = methyChipService;
        this.appProperties = appProperties;
    }

    /**
     * 转跳到ase任务创建页面
     */
    @RequestMapping("/form")
    public String form() {
        return "methychip/form";
    }

    /**
     * 下载模板文件
     */
    @RequestMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemplate() {
        Response response = methyChipService.downloadTemplate();
        ResponseUtil.download(response);
    }

    /**
     * 解析上传的文件
     */
    @RequestMapping("/uploadTemplate")
    @ResponseBody
    public CommonResult<List<MethyChipTaskInput>> uploadTemplate(CurrentUser user, MultipartFile file) {
        List<MethyChipTaskInput> vos = methyChipService.uploadTemplate(file, user);
        return success(vos);
    }

    /**
     * 创建MethyChip任务
     */
    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated MethyChipTaskParam param) {
        MethyChipTaskPO task = methyChipService.createTask(user, param);
        return success(task.getTaskId());
    }

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") MethyChipTaskSearchVO search, PageParam pageParam, Model model) {
        PageResult<MethyChipTaskPO> pageResult = methyChipService.findPage(user, search, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = MethyChipTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "methychip/list";
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        methyChipService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        MethyChipTaskVO vo = methyChipService.findTaskVO(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = MethyChipTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "methychip/detail";
    }

    @RequestMapping("/download")
    @ResponseBody
    public void downloadResult(String taskId) {
        Response response = methyChipService.downloadResult(taskId, "");
        ResponseUtil.download(response);
    }

    @RequestMapping("/chartData")
    @ResponseBody
    public CommonResult<Object> chartData(@Validated ChartParamVO param) {
        Object data = methyChipService.getChartData(param);
        return success(data);
    }

}
