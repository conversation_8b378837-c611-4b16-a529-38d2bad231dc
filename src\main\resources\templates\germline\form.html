<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('dnaseq-germline-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">WES/WGS-germline</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/germline/form}" class="active">Add Task</a>
                            <a th:href="@{/analysis/germline/list}">Task List</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade">
                            <div class="p-2">
                                <div class="d-flex mb-2">
                                    <input type="file" class="form-control form-control-sm w-50 mr-2" id="excel">
                                    <button onclick="uploadExcel()" type="button" class="btn btn-primary btn-sm mr-2 text-nowrap">上传</button>
                                    <a th:href="@{/analysis/germline/downloadTemplate}" class="btn btn-link btn-sm text-nowrap"><i class="fa fa-download mr-1"></i>下载模版</a>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm table-middle table-center">
                                        <thead class="thead-light">
                                        <tr>

                                            <td width="180">RunName</td>
                                            <td>R1(Read1)</td>
                                            <td>R2(Read2)</td>
                                            <td width="50"></td>
                                        </tr>
                                        </thead>
                                        <tbody id="sample-table">
                                        <tr>
                                            <td class="td-input">
                                                <input type="text" class="form-control text-center runName">
                                            </td>
                                            <td class="td-input">
                                                <div class="input-group input-group-sm">
                                                    <div class="input-group-prepend">
                                                        <button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button>
                                                    </div>
                                                    <div class="input-group-prepend">
                                                          <span class="input-group-text">
                                                            <em class="seled"></em>
                                                          </span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="td-input">
                                                <div class="input-group input-group-sm">
                                                    <div class="input-group-prepend">
                                                        <button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button>
                                                    </div>
                                                    <div class="input-group-prepend">
                                                          <span class="input-group-text">
                                                            <em class="seled"></em>
                                                          </span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <i class="fa fa-plus-square text-primary" onclick="addRow(this)"></i>
                                                <i class="fa fa-minus-square text-muted ml-1" onclick="removeRow(this)"></i>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="form-custom">
                                    <div class="form-group-box border-top-0">
                                        <h6 class="text-primary border-bottom pb-2">Quality Control</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row align-items-center mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input type="radio" class="custom-control-input" id="qc1" name="qcMethod" value="trimmomatic" checked>
                                                        <label for="qc1" class="custom-control-label">Trimmomatic</label>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Mapping</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row align-items-center">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Species</label>
                                                <div class="col-xl-4 col-lg-3 col-md-8">
                                                    <select class="form-control" name="species" id="species">
                                                        <option value="human" selected>Homo sapiens</option>
                                                    </select>
                                                </div>
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label">version</label>
                                                <div class="col-xl-2 col-lg-3 col-md-4">
                                                    <select class="form-control" name="refVersion" id="refVersion">
                                                        <option value="hg38" selected>hg38(GRCh38)</option>
                                                        <option value="hg38_alt">hg38+HLA</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-10 offset-xl-2">
                                                    <span class="d-block pt-2">
                                                       <small>Or if you want to use your own genome please contact us</small>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input type="radio" class="custom-control-input" id="md1" name="mappingMethod" value="bwa" checked>
                                                        <label for="md1" class="custom-control-label">BWA</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Germline Variances Calling</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row align-items-center mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input type="radio" class="custom-control-input" id="md2" name="varianceMethod" value="gatk4" checked>
                                                        <label for="md2" class="custom-control-label">GATK4 </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group row align-items-center mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Filtering Method</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input type="radio" class="custom-control-input" id="pn1" name="filteredMethod" value="Hardfilter" checked>
                                                        <label for="pn1" class="custom-control-label">Hard-filtering
                                                            <a href="javascript:;" class="text-danger" data-container="body" data-trigger="focus"
                                                               data-toggle="popover" data-placement="top"
                                                               data-content="Hard-filtering consists of choosing specific thresholds for one or more annotations and throwing out any variants that have annotation values above or below the set thresholds.">
                                                                <i class="fa fa-question-circle"></i>
                                                            </a>
                                                        </label>
                                                    </div>
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input type="radio" class="custom-control-input" id="pn2" name="filteredMethod" value="VQSR">
                                                        <label for="pn2" class="custom-control-label">VQSR
                                                            <a href="javascript:;" class="text-danger" data-container="body" data-trigger="focus"
                                                               data-toggle="popover" data-placement="top"
                                                               data-content="Using machine-learning algorithms to learn from the data what are the annotation profiles of good variants (true positives) and of bad variants (false positives) in a particular dataset.">
                                                                <i class="fa fa-question-circle"></i>
                                                            </a>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-xl-10 offset-xl-2">
                                                    <span class="d-block pt-2">
                                                        It is usually recommended to use VQSR if you have at least one whole genome or  <span class="text-danger">30</span> exomes.
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Statistics</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Regions file <a href="javascript:;" class="text-danger" data-container="body"
                                                                                                                              data-trigger="focus" data-toggle="popover" data-placement="top"
                                                                                                                              data-content="This file is used to calculate coverage.">
                                                    <i class="fa fa-question-circle"></i></a>
                                                </label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="d-flex radio-collapse py-2">
                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input" id="rf1" name="exon_bed" value="default" checked>
                                                            <label for="rf1" class="custom-control-label">Protein Coding Regions <a
                                                                    href="ftp://ftp.ncbi.nlm.nih.gov/pub/CCDS/current_human/CCDS.current.txt" target="_blank">CCDS</a> </label>
                                                        </div>
                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input" id="rf2" name="exon_bed" value="self">
                                                            <label for="rf2" class="custom-control-label">upload your own target regions <a href="javascript:;" class="text-danger"
                                                                                                                                            data-container="body" data-trigger="focus" data-html="true"
                                                                                                                                            data-toggle="popover" data-placement="top"
                                                                                                                                            data-content="The uploaded file should be bed or interval format, you can click <a target='_blank' href='https://gatk.broadinstitute.org/hc/en-us/articles/360035531852-Intervals-and-interval-lists'>here</a> to find more details."><i
                                                                    class="fa fa-question-circle"></i></a></label>
                                                        </div>
                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input" id="rf3" name="exon_bed" value="NONE">
                                                            <label for="rf3" class="custom-control-label">WGS</label>
                                                        </div>
                                                    </div>
                                                    <div class="d-none" data-id="rf2">
                                                        <div class="d-flex align-items-center td-input">
                                                            <span class="text-muted pr-2">Upload:</span>
                                                            <div class="input-group input-group-sm">
                                                                <div class="input-group-prepend">
                                                                    <button class="btn btn-outline-secondary btn-sm" type="button" onclick="showSelectFileModal(this)">Select</button>
                                                                </div>
                                                                <div class="input-group-prepend" id="exon_file_div">
                                                                      <span class="input-group-text">
                                                                        <em class="seled"></em>
                                                                      </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Annotation</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row align-items-center mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input type="radio" class="custom-control-input" id="md3" name="annomethod" value="ANNOVAR" checked>
                                                        <label for="md3" class="custom-control-label">ANNOVAR</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="text-center mb-3">
                                <a href="javascript:void(0)" onclick="submitForm(this)" class="btn btn-outline-primary btn-custom">
                                    <span>Submit</span><i class="fa fa-long-arrow-right"></i>
                                </a>
                            </div>
                        </div>

                    </div>
                </div>
            </main>
        </div>
    </div>
    <div id="file-modal"></div>
</div>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>

    <script>
        $("#file-modal").fileModal('/analysis/fileTree', 1);

        $('.radio-collapse input[type=radio]').click(function () {
            $(this).parents('.radio-collapse').find('input').each(function () {
                var id = $(this).attr('id')
                if (id) {
                    $('[data-id="' + id + '"]').addClass('d-none')
                }
            })
            var selfId = $(this).attr('id')
            $('[data-id="' + selfId + '"]').removeClass('d-none')
        });

        var _selectBtn;
        var _selectFile;
        $("#file-modal").on('__SELECT_FILES__', function (e, data) {
            var nodes = data.nodes || []
            if (nodes.length === 0) {
                return;
            }
            if (nodes.length > 1) {
                layer.msg("Please select only one file.");
                return;
            }

            var html = [];
            $.each(nodes, function (i, node) {
                var filePath = node.path;
                var fileName = node.name;
                var fileSize = node.size;

                html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '');
                html.push('<input type="hidden" value="' + filePath + '">');
                html.push('<input type="hidden" value="' + fileName + '">');
                html.push('<input type="hidden" value="' + fileSize + '">');
                html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>');
                html.push('</b>');
            });

            if (_selectFile) {
                if (nodes.length > 1) {
                    layer.msg("only one file can be selected");
                    return;
                }
                $(_selectFile).parent().next().find('em.seled:first').html(html.join(''));
            } else {
                $(_selectBtn).parent().next().find('em.seled:first').html(html.join(''));
            }
            $('[data-toggle="tooltip"]').tooltip();
        });

        function showFileModal(_this) {
            _selectBtn = _this;
            _selectFile = null;

            var selectIds = [];
            $(_this).parents("td:first").find("em").find("b.text-primary").each(function () {
                selectIds.push($(this).find("input[type=hidden]:eq(0)").val());
            });
            $("#file-modal").trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: selectIds,
                func: function (data) {
                    return !data.name || /.*\.fq$/i.test(data.name) || /.*\.gz$/i.test(data.name) || /.*\.fastq$/i.test(data.name) || /.*\.fastq\.gz$/i.test(data.name) || /.*\.fq\.gz$/i.test(data.name);
                }
            });
        }

        function showSelectFileModal(_this) {
            _selectBtn = null;
            _selectFile = _this;
            var selectId = $(_this).parent().next().find("input[type=hidden]:eq(0)").val();

            $("#file-modal").trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: [selectId]
            });
        }

        function addRow(_this) {
            var trClone = $(_this).parents("tbody:first").find("tr").eq(0).clone(true);
            trClone.find('em.seled').html("");
            trClone.find("input[type=text]").val('');
            $(_this).parents("tbody:first").append(trClone);
        }

        function removeRow(_this) {
            if ($(_this).parents("tbody:first").find("tr").length < 2) {
                $(_this).parents("tr:first").find('em.seled').html("");
                $(_this).parents("tr:first").find("input[type=text]").val('');
                return;
            }
            $(_this).parents("tr:first").remove();
        }

        function removeFile(_this) {
            $(_this).parent().parent().remove();
        }

        function validate() {
            var i = 0;
            var val_r1 = true;
            var groupSet = new Set();
            $("#sample-table").find("tr").each(function () {
                var runName = $(this).find("td:eq(0)").find('input.runName').val();
                if ($.trim(runName) != '') {
                    groupSet.add(runName);
                }
                i++;
                if ($(this).find("td:eq(1)").find('b.text-primary').length == 0) {
                    val_r1 = false
                }
            });
            if (groupSet.size == 0) {
                layer.msg("please input run name");
                return false;
            }
            if (i != groupSet.size) {
                layer.msg("run name must be unique");
                return false;
            }
            if (!val_r1) {
                layer.msg("please must select R1 file");
                return false;
            }
            return true;
        }

        function submitForm(_this) {
            var res = validate();
            if (!res) {
                return;
            }

            if ($(_this).data('loading') == 'true') {
                return;
            }
            var formData = new FormData();
            $("#sample-table").find("tr").each(function (index) {
                var runName = $(this).find("td:eq(0)").find('input.runName').val();
                formData.append("input[" + index + "].runName", runName);
                $(this).find("td:eq(1)").find('b.text-primary').each(function (i) {
                    formData.append("input[" + index + "].fFastq.path", $(this).find("input[type=hidden]:eq(0)").val());
                    formData.append("input[" + index + "].fFastq.name", $(this).find("input[type=hidden]:eq(1)").val());
                    formData.append("input[" + index + "].fFastq.size", $(this).find("input[type=hidden]:eq(2)").val());
                });

                $(this).find("td:eq(2)").find('b.text-primary').each(function (i) {
                    formData.append("input[" + index + "].rFastq.path", $(this).find("input[type=hidden]:eq(0)").val());
                    formData.append("input[" + index + "].rFastq.name", $(this).find("input[type=hidden]:eq(1)").val());
                    formData.append("input[" + index + "].rFastq.name", $(this).find("input[type=hidden]:eq(1)").val());
                });
            });

            var qcMethod = $("input[name='qcMethod']:checked").val() || '';
            var refVersion = $("select[name='refVersion']").val() || '';
            var mappingMethod = $("input[name='mappingMethod']:checked").val() || '';
            var varianceMethod = $("input[name='varianceMethod']:checked").val() || '';
            var filteredMethod = $("input[name='filteredMethod']:checked").val() || '';
            var exonBed = $("input[name='exon_bed']:checked").val() || '';
            var annoMethod = $("input[name='annomethod']:checked").val() || '';

            formData.append("qcMethod", qcMethod);
            formData.append("refVersion", refVersion);
            formData.append("mappingMethod", mappingMethod);
            formData.append("varianceMethod", varianceMethod);
            formData.append("filteredMethod", filteredMethod);
            formData.append("exonBed", exonBed);
            formData.append("annoMethod", annoMethod);

            if ('self' === exonBed) {
                if ($("#exon_file_div").find("input[type=hidden]").length === 0) {
                    layer.msg("please select target regions file");
                    return;
                }
                formData.append("exonFileVo.path", $("#exon_file_div").find("input[type=hidden]:eq(0)").val());
                formData.append("exonFileVo.name", $("#exon_file_div").find("input[type=hidden]:eq(1)").val());
                formData.append("exonFileVo.size", $("#exon_file_div").find("input[type=hidden]:eq(2)").val());
            }

            $(_this).data('loading', 'true');

            $.ajax({
                url: '/analysis/germline/createTask',
                dataType: 'json',
                type: 'post',
                processData: false,
                contentType: false,
                data: formData,
                success: function (result) {
                    if (result.success) {
                        layer.msg('submit success');
                        var id = result.data;
                        setTimeout(function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            window.location.href = $.trim(_context_path) + '/analysis/germline/list?taskId=' + id;
                        }, 2000);
                    } else {
                        layer.msg(result.message);
                    }
                },
                complete: function () {
                    $(_this).data('loading', 'false');
                }
            });
        }

        function uploadExcel() {
            if ($("#excel").val() === '') {
                layer.msg('请选择文件');
                return;
            }
            var formData = new FormData();
            formData.append('file', $('#excel')[0].files[0])
            $.ajax({
                url: '/analysis/germline/uploadTemplate',
                data: formData,
                dataType: 'json',
                type: 'post',
                async: false,
                processData: false,
                contentType: false,
                success: function (result) {
                    if (result.success) {
                        var data = result.data || [];
                        var trs = [];
                        $.each(data, function (idx, item) {
                            var html = ['<tr>'];
                            html.push('<td class="td-input"> <input type="text" class="form-control text-center runName" value="' + item.runName + '"></td>');

                            html.push('<td class="td-input">');
                            html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>');
                            html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">');
                            obtainTr(html, item.r1)
                            html.push('</em></span></div></td>');

                            html.push('<td class="td-input">');
                            html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>');
                            html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">');
                            obtainTr(html, item.r2)
                            html.push('</em></span></div></td>');
                            html.push('<td><i class="fa fa-plus-square text-primary" onclick="addRow(this)"></i><i class="fa fa-minus-square text-muted ml-1" onclick="removeRow(this)"></i></td>')
                            trs.push(html.join(''))
                        })
                        $("#sample-table").html(trs.join(''));
                    } else {
                        layer.msg(result.message);
                    }
                }
            })
        }

        function obtainTr(html, node) {
            if (!node) {
                return;
            }
            var filePath = node.path;
            var fileName = node.name;
            var fileSize = node.size;

            html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '');
            html.push('<input type="hidden" value="' + filePath + '">');
            html.push('<input type="hidden" value="' + fileName + '">');
            html.push('<input type="hidden" value="' + fileSize + '">');
            html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>');
            html.push('</b>');
        }
    </script>
</th:block>
</html>
