<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/validationEngine.jquery.css}">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
    <style>
        .regions {
            margin-right: 20px;
            color: #000000c2;
            font-size: 1.1rem;
        }
    </style>
</th:block>

<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('methylation-wgbs-analysis')}"></div>

            <main>
                <h4 class="border-bottom pb-2 mb-3">WGBS</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/wgbs/form}" class="active">Add Task</a>
                            <a th:href="@{/analysis/wgbs/list}">Task List</a>
                        </div>
                    </div>
                    <form id="form">
                        <div class="list-group tab-content">
                            <div class="tab-pane active show fade">
                                <div class="p-2">
                                    <div class="form-group row align-items-center mb-2">
                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Task
                                            Name</label>
                                        <div class="col-xl-10 col-lg-9 col-md-8">
                                            <input class="form-control width-300 input-name validate[required]"
                                                   name="taskName"
                                                   type="text"
                                                   onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                        </div>
                                    </div>
                                    <div class="form-group-box">

                                        <a href="#coll-1" data-toggle="collapse" class="h5 text-primary">Select
                                            Sample</a>
                                        <div class="collapse show" id="coll-1">
                                            <div class="pl-4 pt-2">
                                                <label>Select your input data by fill the following table or upload an
                                                    Excel file that includes the attributes</label>
                                                <div class="d-flex mb-2">
                                                    <input type="file" multiple name="" id="excel"
                                                           class="form-control form-control-sm w-50 mr-2">
                                                    <button type="button" onclick="uploadExcel()"
                                                            class="btn btn-primary btn-sm mr-2 text-nowrap">Upload
                                                    </button>
                                                    <a th:href="@{/analysis/wgbs/downloadTemplate}"
                                                       class="btn btn-link btn-sm text-nowrap"><i
                                                            class="fa fa-download mr-1"></i>Download template</a>
                                                </div>
                                                <div class="table-responsive">
                                                    <table class="table table-bordered table-sm table-middle mb-0">
                                                        <thead class="thead-light">
                                                        <tr>
                                                            <td width="200" class="text-center">Sample name</td>
                                                            <td class="text-center">
                                                                Select file
                                                                <a href="javascript:;"
                                                                   class="ml-2 text-danger"
                                                                   data-container="body"
                                                                   data-html="true"
                                                                   data-trigger="focus"
                                                                   data-toggle="popover"
                                                                   data-placement="right"
                                                                   data-content="The name of files in this directory should be XXX.fastq.gz"><i
                                                                        class="fa fa-question-circle"></i></a>
                                                            </td>
                                                            <td width="200" class="text-center">Group</td>
                                                            <td width="100"></td>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="sample-table">
                                                        <tr>
                                                            <td class="td-input">
                                                                <input type="text"
                                                                       class="form-control text-center rounded-0 sample"
                                                                       onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                                            </td>
                                                            <td class="td-input">
                                                                <div class="input-group input-group-sm">
                                                                    <div class="input-group-prepend">
                                                                        <button class="btn btn-primary btn-sm"
                                                                                type="button"
                                                                                onclick="showFileModal(this)">Select
                                                                        </button>
                                                                    </div>
                                                                    <div class="input-group-prepend">
                                                                  <span class="input-group-text">
                                                                    <em class="seled"></em>
                                                                  </span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="td-input">
                                                                <input type="text" class="group">
                                                            </td>
                                                            <td>
                                                                <button type="button" class="btn btn-primary btn-sm"
                                                                        onclick="addRow(this)"><i
                                                                        class="fa fa-plus"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-secondary btn-sm"
                                                                        onclick="removeRow(this)"><i
                                                                        class="fa fa-minus"></i></button>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-2" data-toggle="collapse" class="h5 text-primary">Quality
                                            Control</a>
                                        <div class="collapse show" id="coll-2">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <div class="col-3 custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input"
                                                                   id="Trimmomatic1" name="qcMethod" value="trimmomatic"
                                                                   checked>
                                                            <label for="Trimmomatic1" class="custom-control-label">Trimmomatic</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-3" data-toggle="collapse" class="h5 text-primary">Extraction of
                                            methylation locis/regions</a>
                                        <div class="collapse show" id="coll-3">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <div class="col-3 custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input"
                                                                   id="Bismark" name="regions" value="Bismark"
                                                                   checked>
                                                            <label for="Bismark"
                                                                   class="custom-control-label">Bismark</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="pl-4 pt-2">
                                                <a href="#" data-toggle="collapse"
                                                   class="regions">Mapping</a>
                                                <div class="collapse show">
                                                    <div class="pl-4 pt-2">
                                                        <div class="form-group row align-items-center">
                                                            <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Species</label>
                                                            <div class="col-xl-4 col-lg-3 col-md-8">
                                                                <select class="form-control" name="species"
                                                                        id="species">
                                                                    <option value="human">Homo sapiens（human）</option>
                                                                    <option value="mouse">Mus musculus（mouse）</option>
                                                                </select>
                                                            </div>
                                                            <div class="col-xl-2 col-lg-3 col-md-4">
                                                                <input class="form-control"
                                                                       style="background-color: #fff" name="specVersion"
                                                                       id="specVersion" readonly value="GRCh38">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="pl-4 pt-2">
                                                <a href="#" data-toggle="collapse" class="regions">Sequence
                                                    deduplication
                                                </a>
                                                <div class="col-3 custom-control custom-radio custom-control-inline">
                                                    <input type="radio" class="custom-control-input"
                                                           id="sd" value="True"
                                                           checked>
                                                    <label for="sd"
                                                           class="custom-control-label">True</label>
                                                </div>
                                            </div>
                                            <div class="pl-4 pt-2">
                                                <a href="#" data-toggle="collapse" class="regions">Extraction
                                                    of methylation information</a>
                                                <div class="col-3 custom-control custom-radio custom-control-inline">
                                                    <input type="radio" class="custom-control-input"
                                                           id="eomi" value="True"
                                                           checked>
                                                    <label for="eomi"
                                                           class="custom-control-label">True</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-5" data-toggle="collapse" class="h5 text-primary">Differential
                                            Analysis</a>
                                        <div class="collapse show" id="coll-5">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Comparison</label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-sm mb-2 w-auto">
                                                                <thead>
                                                                <tr>
                                                                    <th>control</th>
                                                                    <th>patient</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody id="comparison-tbody">
                                                                <tr>
                                                                    <td>
                                                                        <select class="custom-select custom-select-sm group-select groupA"></select>
                                                                    </td>
                                                                    <td>
                                                                        <select class="custom-select custom-select-sm group-select groupB"></select>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center mb-2">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">
                                                        P value
                                                        <a href="javascript:;"
                                                           class="ml-2 text-danger"
                                                           data-container="body"
                                                           data-html="true"
                                                           data-trigger="focus"
                                                           data-toggle="popover"
                                                           data-placement="top"
                                                           data-content="Valid: 0 < P value ≤ 0.05"><i
                                                                class="fa fa-question-circle"></i></a>
                                                    </label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <input type="number"
                                                               class="form-control width-100 input-double validate[min[0],max[0.05]]"
                                                               name="pvalue" value="0.05" min="0" max="0.05"
                                                               step="0.01">
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">
                                                        Delta
                                                        <a href="javascript:;"
                                                           class="ml-2 text-danger"
                                                           data-container="body"
                                                           data-html="true"
                                                           data-trigger="focus"
                                                           data-toggle="popover"
                                                           data-placement="top"
                                                           data-content="Valid: 0 ≤ Delta ≤ 0.5"><i
                                                                class="fa fa-question-circle"></i></a>
                                                    </label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <input type="number"
                                                               class="form-control width-100 input-double validate[min[0],max[0.1]]"
                                                               name="delta" value="0.1" min="0" max="0.5" step="0.01">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group-box">
                                        <a href="#coll-5" data-toggle="collapse" class="h5 text-primary">GO functional
                                            enrichment analysis
                                        </a>
                                        <div class="collapse show" id="coll-6">
                                            <div class="pl-4 pt-2">
                                                <div class="form-group row align-items-center mb-2">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">p-value
                                                        cutoff
                                                        <a href="javascript:;"
                                                           class="ml-2 text-danger"
                                                           data-container="body"
                                                           data-html="true"
                                                           data-trigger="focus"
                                                           data-toggle="popover"
                                                           data-placement="top"
                                                           data-content="Valid: 0 ≤ p-value cutoff ≤ 0.05"><i
                                                                class="fa fa-question-circle"></i></a>
                                                    </label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <input type="number"
                                                               class="form-control width-100 input-double validate[min[0],max[0.05]]"
                                                               name="pvalueCutoff" min="0" max="0.05" step="0.01"
                                                               value="0.05">
                                                    </div>
                                                </div>
                                                <div class="form-group row align-items-center mb-0">
                                                    <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">q-value
                                                        cutoff
                                                        <a href="javascript:;"
                                                           class="ml-2 text-danger"
                                                           data-container="body"
                                                           data-html="true"
                                                           data-trigger="focus"
                                                           data-toggle="popover"
                                                           data-placement="top"
                                                           data-content="Valid: 0 ≤ q-value cutoff ≤ 0.2"><i
                                                                class="fa fa-question-circle"></i></a>
                                                    </label>
                                                    <div class="col-xl-10 col-lg-9 col-md-8">
                                                        <input type="number"
                                                               class="form-control width-100 input-double validate[min[0],max[0.2]]"
                                                               name="qvalueCutoff" min="0" max="0.2" step="0.1"
                                                               value="0.2">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center my-3">
                                        <a href="javascript:void(0)" onclick="submitForm(this)"
                                           class="btn btn-outline-primary btn-custom"><span>Submit</span><i
                                                class="fa fa-long-arrow-right"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
    <div id="file-modal"></div>
</div>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/jquery.validationEngine.js}"></script>
    <script th:src="@{/js/jquery.validationEngine-zh_CN.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>

    <script>
        $('#file-modal').fileModal('/analysis/fileTree')

        var groupSet = new Set()

        $(document).ready(function () {
            $('#species').on('change', function () {
                var group = $('#species').val()
                if (group == 'human') {
                    $('#specVersion').val('GRCh38')
                } else {
                    $('#specVersion').val('GRCm38')
                }
            })

            $('.group').autocomplete({source: []})

            bindGroupChange()

            validate()
        })

        function validate() {
            $('.input-int').on('change', function () {
                var value = $.trim($(this).val())
                if (value === '') {
                    return
                }
                if (!/^[1-9]+[0-9]*]*$/.test(value)) {
                    layer.msg('please input number')
                    $(this).val('')
                }
            })

            $('.input-double').on('change', function () {
                var value = $.trim($(this).val())
                if (value === '') {
                    return
                }
                if (!/^[0-9]+.?[0-9]*]*$/.test(value)) {
                    layer.msg('please input number')
                    $(this).val('')
                }
            })
        }


        function initAutocomplete() {
            var instance = $('.group').autocomplete('instance')
            if (instance) {
                instance.destroy()
            }
            var groupOptions = []
            groupSet.forEach(function (item) {
                groupOptions.push(item)
            })
            $('.group').autocomplete({source: groupOptions})
        }

        function bindGroupChange() {

            $('.group').on('change', function () {
                groupSet = new Set()

                $('.group').each(function () {
                    var group = $(this).val()
                    if (/^[0-9]*$/.test(group)) {
                        layer.msg('Group cannot use pure numbers')
                        return
                    }
                    if (group) {
                        groupSet.add(group)
                    }
                })
                initAutocomplete()

                var options = ['<option value="">choose</option>']
                groupSet.forEach(function (item) {
                    options.push('<option value="' + item + '">' + item + '</option>')
                })
                $('select.group-select').html(options.join(''))
            })
        }

        function groupChange() {
            groupSet = new Set()
            $('.group').each(function () {
                var group = $(this).val()
                if (/^[0-9]*$/.test(group)) {
                    layer.msg('Group cannot use pure numbers')
                    return
                }
                if (group) {
                    groupSet.add(group)
                }
            })
            initAutocomplete()

            var options = ['<option value="">choose</option>']
            groupSet.forEach(function (item) {
                options.push('<option value="' + item + '">' + item + '</option>')
            })
            $('select.group-select').html(options.join(''))
        }

        function addRow(_this) {
            // $('.group').autocomplete('destroy')
            var clone = $(_this).parents('tr:first').clone()
            clone.find('input.sample').val('')
            clone.find('input.group').val('')
            clone.find('em.seled').html('')
            $(_this).parents('tbody').append(clone)

            // initAutocomplete()
            bindGroupChange()
            validate()
        }

        function removeRow(_this) {
            var rows = $(_this).parents('tbody').find('tr')

            var rowDiv = $(_this).parents('tr')
            if (rows.length > 1) {
                rowDiv.remove()
            } else {
                rowDiv.find('input.sample').val('')
                rowDiv.find('input.group').val('1')
                rowDiv.find('div.select-file').html('')
            }
        }


        var _selectBtn

        function showFileModal(_this) {
            _selectBtn = _this

            var selectIds = []
            $(_this).parents('td:first').find('em').find('b.text-primary').each(function () {
                selectIds.push($(this).find('input[type=hidden]:eq(0)').val())
            })
            $('#file-modal').trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: selectIds,
                func: function (data) {
                    return !data.name || /.*\.fastq\.gz/.test(data.name) || /.*\.fq\.gz/.test(data.name)
                }
            })
        }

        $('#file-modal').on('__SELECT_FILES__', function (e, data) {
            var nodes = data.nodes || []
            if (nodes.length === 0) {
                return
            }
            if (nodes.length > 2) {
                layer.msg('Up to 2 fastq files in "Select file"')
                return
            }

            var html = []
            $.each(nodes, function (i, node) {
                var filePath = node.path
                var fileName = node.name
                var fileSize = node.size

                html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '')
                html.push('<input type="hidden" value="' + filePath + '">')
                html.push('<input type="hidden" value="' + fileName + '">')
                html.push('<input type="hidden" value="' + fileSize + '">')
                html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
                html.push('</b>')
            })

            $(_selectBtn).parent().next().find('em.seled:first').html(html.join(''))
            $('[data-toggle="tooltip"]').tooltip()
        })

        function removeFile(_this) {
            $(_this).parent().parent().remove()
        }

        function validateRequired() {
            var flag = false
            var sampleIds = []
            $.each($('input.sample'), function () {
                var sampleId = $.trim($(this).val())
                if (sampleId === '') {
                    flag = true
                }
                sampleIds.push(sampleId)
            })
            if (isRepeat(sampleIds)) {
                layer.msg('Please check the sample name, the name within the same task needs to be uniquely.')
                return false
            }
            if (flag || sampleIds.length === 0) {
                layer.msg('sample name can not be empty')
                return false
            }

            let fileNumSet = new Set()
            var rows = $('#sample-table').find('tr')
            $.each(rows, function (i, item) {
                let index = 0
                $.each($(item).find('td:eq(1)').find('b.text-primary'), function (j, it) {
                    if (j > 2) {
                        layer.msg('Up to 2 fastq files in "Select file"')
                        return false
                    }
                    index = j + 1 > index ? j + 1 : index
                })
                fileNumSet.add(index)
            })

            if (fileNumSet.has(0)) {
                layer.msg('Select file can not be empty')
                return false
            }

            var groups = new Set()
            $.each($('input.group'), function () {
                var group = $.trim($(this).val())
                if (group === '' || /^[0-9]*$/.test(group)) {
                    flag = true
                }
                groups.add(group)
            })
            if (flag || groups.size === 0) {
                layer.msg('group can not be empty or use pure numbers')
                return false
            }
            console.log(groups)

            if (groups.size !== 2) {
                layer.msg('The number of groups should be 2')
                return false
            }

            $.each($('div.select-file'), function () {
                var size = $(this).find('div.btn-group').length
                if (size === 0) {
                    layer.msg('please select fastq file')
                    return false
                }
            })

            let compars = []
            let comparsFlag = true
            var rows = $('#comparison-tbody').find('tr')
            $.each(rows, function (i, item) {
                var g1 = $(item).find('select:eq(0)').val()
                var g2 = $(item).find('select:eq(1)').val()
                compars.push({'g1': g1, "g2": g2})
                if (g1 === '' || g2 === '') {
                    layer.msg('Comparison can not be empty')
                    comparsFlag = false
                    return false
                }
                if (g1 === g2) {
                    layer.msg('The groups to be compared must be different.')
                    comparsFlag = false
                    return false
                }
            })

            if (!comparsFlag) {
                return false
            }

            // console.log(compars)

            if (isRepeat(compars)) {
                layer.msg('Group pairs in the comparison list cannot be repeated.')
                return false
            }
            return true
        }

        function submitForm(_this) {
            if (!$('#form').validationEngine('validate')) {
                return
            }
            if (!validateRequired()) {
                return

            }

            var formData = new FormData()

            var rows = $('#sample-table').find('tr')
            $.each(rows, function (i, item) {
                var sample = $(item).find('td:eq(0)').find('input:eq(0)').val()
                formData.append('inputs[' + i + '].sample', sample)

                $.each($(item).find('td:eq(1)').find('b.text-primary'), function (j, it) {
                    var path = $(it).find('input:eq(0)').val()
                    var name = $(it).find('input:eq(1)').val()
                    var size = $(it).find('input:eq(2)').val()

                    formData.append('inputs[' + i + '].files[' + j + '].path', path)
                    formData.append('inputs[' + i + '].files[' + j + '].name', name)
                    formData.append('inputs[' + i + '].files[' + j + '].size', size)
                })

                var group = $(item).find('td:eq(2)').find('input:eq(0)').val()
                formData.append('inputs[' + i + '].group', group)

            })

            var taskName = $('input[name="taskName"]').val()
            var species = $('select[name="species"]').val() || ''
            var pvalue = $('input[name="pvalue"]').val() || ''
            var delta = $('input[name="delta"]').val() || ''
            var pvalueCutoff = $('input[name="pvalueCutoff"]').val() || ''
            var qvalueCutoff = $('input[name="qvalueCutoff"]').val() || ''


            if (pvalue == '' || !/^[0-9]+.?[0-9]*]*$/.test(pvalue)) {
                layer.msg('P value must be number')
                return
            }

            if (delta == '' || !/^[0-9]+.?[0-9]*]*$/.test(delta)) {
                layer.msg('Delta must be number')
                return
            }

            if (pvalueCutoff == '' || !/^[0-9]+.?[0-9]*]*$/.test(pvalueCutoff)) {
                layer.msg('p-value cutoff must be number')
                return
            }

            if (qvalueCutoff == '' || !/^[0-9]+.?[0-9]*]*$/.test(qvalueCutoff)) {
                layer.msg('q-value cutoff must be number')
                return
            }

            if (pvalue > 0.05 || pvalue <= 0) {
                layer.msg('P value must be: 0 < P value ≤ 0.05')
                return
            }

            if (delta > 0.5 || delta < 0) {
                layer.msg('Delta must be: 0 ≤ Delta ≤ 0.5')
                return
            }

            if (pvalueCutoff > 0.5 || pvalueCutoff < 0) {
                layer.msg('p-value cutoff must be: 0 ≤ p-value cutoff ≤ 0.05')
                return
            }

            if (qvalueCutoff > 0.5 || qvalueCutoff < 0) {
                layer.msg('q-value cutoff must be: 0 ≤ q-value cutoff ≤ 0.2')
                return
            }

            var rows = $('#comparison-tbody').find('tr')
            $.each(rows, function (i, item) {
                var g1 = $(item).find('select:eq(0)').val()
                var g2 = $(item).find('select:eq(1)').val()
                formData.append('control', g1)
                formData.append('patient', g2)
            })

            formData.append('taskName', taskName)
            formData.append('species', species)


            formData.append('pvalue', pvalue)
            formData.append('delta', delta)
            formData.append('pvalueCutoff', pvalueCutoff)
            formData.append('qvalueCutoff', qvalueCutoff)

            for (var [a, b] of formData.entries()) {
                console.log(a, b);
            }


            if ($(_this).data('loading') == 'true') {
                return
            }
            $(_this).data('loading', 'true')

            $.ajax({
                url: '/analysis/wgbs/createTask',
                dataType: 'json',
                type: 'post',
                processData: false,
                contentType: false,
                data: formData,
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg('submit success')
                        var id = result.data
                        setTimeout(function () {
                            var _context_path = $('meta[name="_context_path"]').attr('content')
                            window.location.href = $.trim(_context_path) + '/analysis/wgbs/list?taskId=' + id
                        }, 2000)
                    }
                },
                complete: function () {
                    $(_this).data('loading', 'false')
                }
            })
        }

        function uploadExcel() {
            if ($('#excel').val() === '') {
                layer.msg('please select a file')
                return
            }
            var formData = new FormData()
            formData.append('file', $('#excel')[0].files[0])
            $.ajax({
                url: '/analysis/wgbs/uploadTemplate',
                data: formData,
                dataType: 'json',
                type: 'post',
                async: false,
                processData: false,
                contentType: false,
                success: function (result) {
                    if (result.success) {
                        var data = result.data || []
                        if (data.length === 0) {
                            layer.msg('no data')
                            return
                        }
                        var trs = []
                        $.each(data, function (idx, item) {
                            console.log(item.pe)
                            var html = ['<tr>']
                            html.push('<td class="td-input">')
                            html.push('<input type="text" value="' + item.sample + '" class="form-control text-center rounded-0 sample" onkeyup="value=value.replace(/[^\\w\\.\\/]/ig,\'\')"></td>')
                            html.push('<td class="td-input">')
                            html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>')
                            html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">')
                            obtainTr(html, item.files)
                            html.push('</em></span></div></div></td>')

                            html.push('<td class="td-input"> <input type="text" class="group" onchange="groupChange()" value="' + item.group + '"> </td>')
                            html.push(`<td>
                               <button type="button" class="btn btn-primary btn-sm" onclick="addRow(this)"><i class="fa fa-plus"></i>
                               </button>
                               <button type="button" class="btn btn-secondary btn-sm" onclick="removeRow(this)"><i class="fa fa-minus"></i></button>
                           </td>`)
                            trs.push(html.join(''))
                        })
                        $('#sample-table').html(trs.join(''))
                        groupChange()
                        bindGroupChange()
                        // layer.msg("The data is imported successfully. Please check whether the table data result is correct")
                    } else {
                        layer.msg(result.message)
                    }
                }
            })
        }

        function obtainTr(html, nodes) {
            if (!nodes) {
                return
            }
            $.each(nodes, function (i, node) {
                var filePath = node.path
                var fileName = node.name
                var fileSize = node.size
                html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '')
                html.push('<input type="hidden" value="' + filePath + '">')
                html.push('<input type="hidden" value="' + fileName + '">')
                html.push('<input type="hidden" value="' + fileSize + '">')
                html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>')
                html.push('</b>')
            })
        }

    </script>
</th:block>
</html>
