package cn.ac.picb.vipmap.vo;

import cn.ac.picb.circrna.vo.CircrnaTaskInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:04
 */
@Data
public class CircrnaTaskParam {
    @NotBlank
    private String taskName;

    @NotNull
    private List<CircrnaTaskInput> inputs;

    @NotBlank
    private String qcMethod;

    @NotBlank
    private String species;

    @NotBlank
    private String specVersion;

    @NotBlank
    private String mappingMethod;

    @NotBlank
    private String circRNAMethod;


}
