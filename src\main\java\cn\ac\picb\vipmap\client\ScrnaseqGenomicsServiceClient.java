package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnaseq.client.ScrnaseqGenomicsServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqGenomicsServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "scrnaseq-service", contextId = "genomics", fallback = ScrnaseqGenomicsServiceClientFallback.class)
public interface ScrnaseqGenomicsServiceClient extends ScrnaseqGenomicsServiceApi {
}
