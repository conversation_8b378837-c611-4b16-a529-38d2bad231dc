$(document).ajaxSend(function (e, xhr, options) {
  var _context_path = $("meta[name='_context_path']").attr("content")
  if ($.trim(_context_path) != '') {
    var url = options.url

    if ($.trim(url).indexOf("http") != 0) {

      options.url = _context_path + url
    }
  }
})
$(document).ajaxError(function (e, xhr) {
  if (xhr.status == 403) {
    layer.msg('no permission')
  }
  if (xhr.status == 500) {
    var result = xhr.responseJSON
    layer.msg(result.message)
  }
})
$(document).ajaxSuccess(function (event, xhr, options) {
  if (options.dataType == 'json') {
    var result = xhr.responseJSON
    var code = result.code
    if (code && code !== 200) {
      layer.alert(result.message, { icon: 2 })
    }
  }
})

// echarts 配色
var colorArr = [
  "#c4ccd3",
  "#c23531",
  "#2f4554",
  "#61a0a8",
  "#d48265",
  "#91c7ae",
  "#749f83",
  "#ca8622",
  "#bda29a",
  "#6e7074",
  "#546570",
]

function getClusterColor (name) {
  let c
  if (Number(name) != null) {
    c = colorArr[Number(name) % colorArr.length]
  } else if (name.startsWith("cluster_")) {
    let n = Number(name.replace('cluster_', ''))
    if (n != null) {
      c = colorArr[n % colorArr.length]
    }
  }
  return c
}

// var pheatmap_renderer_server = 'http://**************:8081';
// var pheatmap_renderer_server = 'https://www.biosino.org/vipmap/BioCharts/api'

// var pheatmap_renderer_server = $("meta[name='_context_path']").attr("content") + '/BioCharts/api'
