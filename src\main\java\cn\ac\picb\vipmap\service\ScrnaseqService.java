package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.util.ServiceExceptionUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnaseq.dto.*;
import cn.ac.picb.scrnaseq.po.*;
import cn.ac.picb.scrnaseq.vo.*;
import cn.ac.picb.vipmap.client.*;
import cn.ac.picb.vipmap.enums.ScrnaseqType;
import cn.ac.picb.vipmap.mapper.ScrnaseqMapper;
import cn.ac.picb.vipmap.util.TreeNodeUtils;
import cn.ac.picb.vipmap.vo.*;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.ac.picb.vipmap.enums.ErrorCodeConstants.PARAMS_ERROR;

/**
 * <AUTHOR>
 */
@SuppressWarnings("DuplicatedCode")
@Service
@RequiredArgsConstructor
public class ScrnaseqService {

    private final ScrnaseqServiceClient scrnaseqServiceClient;
    private final ScrnaseqGenomicsServiceClient scrnaseqGenomicsServiceClient;
    private final ScrnaseqBaselineServiceClient scrnaseqBaselineServiceClient;
    private final ScrnaseqPagaServiceClient scrnaseqPagaServiceClient;
    private final ScrnaseqDegServiceClient scrnaseqDegServiceClient;
    private final ScrnaseqWgcnaServiceClient scrnaseqWgcnaServiceClient;
    private final ScrnaseqGenesServiceClient scrnaseqGenesServiceClient;
    private final ScrnaseqReportServiceClient scrnaseqReportServiceClient;
    private final ScrnaseqFileServiceClient scrnaseqFileServiceClient;

    public List<TaskTreeNodeVO> findTreeData(CurrentUser user, String parentId, String type) {
        TaskTreeQueryVO vo = new TaskTreeQueryVO();
        vo.setUserId(user.getId());
        vo.setParentId(parentId);
        vo.setType(type);

        CommonResult<List<TaskTreeNodeVO>> result = scrnaseqServiceClient.findTreeData(vo);
        result.checkError();
        return result.getData();
    }

    public GenomicsTaskDTO findGenomicsTaskDTOById(String parentId) {
        CommonResult<GenomicsTaskDTO> result = scrnaseqGenomicsServiceClient.findGenomicsDtoById(parentId);
        result.checkError();
        return result.getData();
    }

    public GenomicsTaskPO findGenomicsTaskById(String parentId) {
        CommonResult<GenomicsTaskPO> result = scrnaseqGenomicsServiceClient.findGenomicsById(parentId);
        result.checkError();
        return result.getData();
    }

    public BaselineTaskPO findBaselineTaskById(String parentId) {
        CommonResult<BaselineTaskPO> result = scrnaseqBaselineServiceClient.findBaselineById(parentId);
        result.checkError();
        return result.getData();
    }

    public BaselineTaskDTO findBaselineTaskDTOById(String parentId) {
        CommonResult<BaselineTaskDTO> result = scrnaseqBaselineServiceClient.findBaselineDtoById(parentId);
        result.checkError();
        return result.getData();
    }

    public PagaTaskPO findPagaTaskById(String id) {
        CommonResult<PagaTaskPO> result = scrnaseqPagaServiceClient.findPagaById(id);
        result.checkError();
        return result.getData();
    }

    public PagaTaskDTO findPagaTaskDTOById(String id) {
        CommonResult<PagaTaskDTO> result = scrnaseqPagaServiceClient.findPagaDtoById(id);
        result.checkError();
        return result.getData();
    }

    public DegTaskPO findDegTaskById(String id) {
        CommonResult<DegTaskPO> result = scrnaseqDegServiceClient.findDegById(id);
        result.checkError();
        return result.getData();
    }

    public DegTaskDTO findDegTaskDTOById(String id) {
        CommonResult<DegTaskDTO> result = scrnaseqDegServiceClient.findDegDtoById(id);
        result.checkError();
        return result.getData();
    }

    public WgcnaTaskDTO findWgcnaTaskDTOById(String id) {
        CommonResult<WgcnaTaskDTO> result = scrnaseqWgcnaServiceClient.findWgcnaById(id);
        result.checkError();
        return result.getData();
    }

    public WgcnaTaskPO findWgcnaTaskById(String id) {
        CommonResult<WgcnaTaskPO> result = scrnaseqWgcnaServiceClient.findWgcnaTaskById(id);
        result.checkError();
        return result.getData();
    }

    public GenesTaskPO findGenesTaskById(String id) {
        CommonResult<GenesTaskPO> result = scrnaseqGenesServiceClient.findGenesById(id);
        result.checkError();
        return result.getData();
    }

    public GenesTaskDTO findGenesTaskDTOById(String id) {
        CommonResult<GenesTaskDTO> result = scrnaseqGenesServiceClient.findGenesDtoById(id);
        result.checkError();
        return result.getData();
    }

    public TaskTreeNodeVO saveGenomics(CurrentUser user, ScrnaseqGenomicsTaskParam param) {
        GenomicsTaskParamVO vo = ScrnaseqMapper.INSTANCE.convertToGenomicsTaskParamVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<GenomicsTaskPO> result = scrnaseqGenomicsServiceClient.saveGenomicsTask(vo);
        result.checkError();
        GenomicsTaskPO data = result.getData();
        return TreeNodeUtils.taskToNode(data);
    }

    public TaskTreeNodeVO saveBaseline(CurrentUser user, ScrnaseqBaselineTaskParam param) {
        BaselineTaskParamVO vo = ScrnaseqMapper.INSTANCE.convertToBaselineTaskParamVO(param);
        vo.setUserId(user.getId());
        CommonResult<BaselineTaskPO> result = scrnaseqBaselineServiceClient.saveBaselineTask(vo);
        result.checkError();
        BaselineTaskPO data = result.getData();
        return TreeNodeUtils.taskToNode(data);
    }

    public TaskTreeNodeVO savePaga(CurrentUser user, ScrnaseqPagaTaskParam param) {
        PagaTaskParamVO vo = ScrnaseqMapper.INSTANCE.convertToPagaTaskParamVO(param);
        vo.setUserId(user.getId());
        CommonResult<PagaTaskPO> result = scrnaseqPagaServiceClient.savePagaTask(vo);
        result.checkError();
        PagaTaskPO data = result.getData();
        return TreeNodeUtils.taskToNode(data);
    }

    public TaskTreeNodeVO saveDeg(CurrentUser user, ScrnaseqDegTaskParam param) {
        DegTaskParamVO vo = ScrnaseqMapper.INSTANCE.convertToDegTaskParamVO(param);
        vo.setUserId(user.getId());
        CommonResult<DegTaskPO> result = scrnaseqDegServiceClient.saveDegTask(vo);
        result.checkError();
        DegTaskPO data = result.getData();
        return TreeNodeUtils.taskToNode(data);
    }

    public TaskTreeNodeVO saveWgcna(CurrentUser user, ScrnaseqWgcnaTaskParam param) {
        WgcnaTaskParamVo vo = ScrnaseqMapper.INSTANCE.convertToWgcnaTaskParamVO(param);
        vo.setUserId(user.getId());
        CommonResult<WgcnaTaskPO> result = scrnaseqWgcnaServiceClient.saveWgcnaTask(vo);
        result.checkError();
        WgcnaTaskPO data = result.getData();
        return TreeNodeUtils.taskToNode(data);
    }

    public TaskTreeNodeVO saveGenes(CurrentUser user, ScrnaseqGenesTaskParam param) {
        GenesTaskParamVO vo = ScrnaseqMapper.INSTANCE.convertToGenesTaskParamVO(param);
        vo.setUserId(user.getId());
        CommonResult<GenesTaskPO> result = scrnaseqGenesServiceClient.saveGenesTask(vo);
        result.checkError();
        GenesTaskPO data = result.getData();
        return TreeNodeUtils.taskToNode(data);
    }

    public PageResult<ScrnaseqTaskVO> findGenomicsPage(CurrentUser currentUser, ScrnaseqTaskSearchVO searchVO, PageParam pageable) {
        TaskQueryVO queryVO = ScrnaseqMapper.INSTANCE.convertToTaskQuery(searchVO);
        queryVO.setUserId(currentUser.getId());
        queryVO.setPage(pageable.getPage());
        queryVO.setSize(pageable.getSize());

        CommonResult<PageResult<GenomicsTaskPO>> result = scrnaseqGenomicsServiceClient.findGenomicsTaskPage(queryVO);
        result.checkError();
        PageResult<GenomicsTaskPO> data = result.getData();

        PageResult<ScrnaseqTaskVO> pageResult = new PageResult<>();
        pageResult.setContent(data.getContent().stream().map(task -> {
            ScrnaseqTaskVO vo = new ScrnaseqTaskVO();
            vo.setGenomicsTask(task);
            return vo;
        }).collect(Collectors.toList()));
        pageResult.setTotal(data.getTotal());
        return pageResult;
    }

    public PageResult<ScrnaseqTaskVO> findBaselinePage(CurrentUser currentUser, ScrnaseqTaskSearchVO searchVO, PageParam pageable) {
        TaskQueryVO queryVO = ScrnaseqMapper.INSTANCE.convertToTaskQuery(searchVO);
        queryVO.setUserId(currentUser.getId());
        queryVO.setPage(pageable.getPage());
        queryVO.setSize(pageable.getSize());
        CommonResult<PageResult<BaselineTaskDTO>> result = scrnaseqBaselineServiceClient.findBaselineTaskPage(queryVO);
        result.checkError();
        PageResult<BaselineTaskDTO> data = result.getData();

        PageResult<ScrnaseqTaskVO> pageResult = new PageResult<>();
        pageResult.setContent(data.getContent().stream().map(task -> {
            ScrnaseqTaskVO vo = new ScrnaseqTaskVO();
            vo.setBaselineTask(task.getBaselineTask());
            vo.setGenomicsTask(task.getGenomicsTask());
            return vo;
        }).collect(Collectors.toList()));
        pageResult.setTotal(data.getTotal());
        return pageResult;
    }

    public PageResult<ScrnaseqTaskVO> findPagaPage(CurrentUser currentUser, ScrnaseqTaskSearchVO searchVO, PageParam pageable) {
        TaskQueryVO queryVO = ScrnaseqMapper.INSTANCE.convertToTaskQuery(searchVO);
        queryVO.setUserId(currentUser.getId());
        queryVO.setPage(pageable.getPage());
        queryVO.setSize(pageable.getSize());
        CommonResult<PageResult<PagaTaskDTO>> result = scrnaseqPagaServiceClient.findPagaTaskPage(queryVO);
        result.checkError();
        PageResult<PagaTaskDTO> data = result.getData();

        PageResult<ScrnaseqTaskVO> pageResult = new PageResult<>();
        pageResult.setContent(data.getContent().stream().map(task -> {
            ScrnaseqTaskVO vo = new ScrnaseqTaskVO();
            vo.setPagaTask(task.getPagaTask());
            vo.setBaselineTask(task.getBaselineTask());
            return vo;
        }).collect(Collectors.toList()));
        pageResult.setTotal(data.getTotal());
        return pageResult;
    }

    public PageResult<ScrnaseqTaskVO> findDegPage(CurrentUser currentUser, ScrnaseqTaskSearchVO searchVO, PageParam pageable) {
        TaskQueryVO queryVO = ScrnaseqMapper.INSTANCE.convertToTaskQuery(searchVO);
        queryVO.setUserId(currentUser.getId());
        queryVO.setPage(pageable.getPage());
        queryVO.setSize(pageable.getSize());
        CommonResult<PageResult<DegTaskDTO>> result = scrnaseqDegServiceClient.findDegTaskPage(queryVO);
        result.checkError();
        PageResult<DegTaskDTO> data = result.getData();

        PageResult<ScrnaseqTaskVO> pageResult = new PageResult<>();
        pageResult.setContent(data.getContent().stream().map(task -> {
            ScrnaseqTaskVO vo = new ScrnaseqTaskVO();
            vo.setDegTask(task.getDegTask());
            vo.setBaselineTask(task.getBaselineTask());
            return vo;
        }).collect(Collectors.toList()));
        pageResult.setTotal(data.getTotal());
        return pageResult;
    }

    public PageResult<ScrnaseqTaskVO> findGenesPage(CurrentUser currentUser, ScrnaseqTaskSearchVO searchVO, PageParam pageable) {
        TaskQueryVO queryVO = ScrnaseqMapper.INSTANCE.convertToTaskQuery(searchVO);
        queryVO.setUserId(currentUser.getId());
        queryVO.setPage(pageable.getPage());
        queryVO.setSize(pageable.getSize());
        CommonResult<PageResult<GenesTaskDTO>> result = scrnaseqGenesServiceClient.findGenesTaskPage(queryVO);
        result.checkError();
        PageResult<GenesTaskDTO> data = result.getData();

        PageResult<ScrnaseqTaskVO> pageResult = new PageResult<>();
        pageResult.setContent(data.getContent().stream().map(task -> {
            ScrnaseqTaskVO vo = new ScrnaseqTaskVO();
            vo.setGenesTask(task.getGenesTask());
            vo.setBaselineTask(task.getBaselineTask());
            return vo;
        }).collect(Collectors.toList()));
        pageResult.setTotal(data.getTotal());
        return pageResult;
    }

    public TaskTreeNodeVO deleteTask(String type, String id) {
        ScrnaseqType scrnaseqType = EnumUtil.fromStringQuietly(ScrnaseqType.class, type);
        if (scrnaseqType == null) {
            throw ServiceExceptionUtil.exception(PARAMS_ERROR);
        }
        TaskTreeNodeVO node;
        switch (scrnaseqType) {
            case genomics:
                CommonResult<GenomicsTaskPO> genomicsById = scrnaseqGenomicsServiceClient.deleteGenomicsById(id);
                genomicsById.checkError();
                GenomicsTaskPO genomicsTask = genomicsById.getData();
                node = TreeNodeUtils.taskToNode(genomicsTask);
                break;
            case baseline:
                CommonResult<BaselineTaskPO> baselineById = scrnaseqBaselineServiceClient.deleteBaselineById(id);
                baselineById.checkError();
                BaselineTaskPO baselineTask = baselineById.getData();
                node = TreeNodeUtils.taskToNode(baselineTask);
                break;
            case paga:
                CommonResult<PagaTaskPO> pagaById = scrnaseqPagaServiceClient.deletePagaById(id);
                pagaById.checkError();
                PagaTaskPO pagaTask = pagaById.getData();
                node = TreeNodeUtils.taskToNode(pagaTask);
                break;
            case deg:
                CommonResult<DegTaskPO> degById = scrnaseqDegServiceClient.deleteDegById(id);
                degById.checkError();
                DegTaskPO degTask = degById.getData();
                node = TreeNodeUtils.taskToNode(degTask);
                break;
            case genes:
                CommonResult<GenesTaskPO> genesById = scrnaseqGenesServiceClient.deleteGenesById(id);
                genesById.checkError();
                GenesTaskPO genesTask = genesById.getData();
                node = TreeNodeUtils.taskToNode(genesTask);
                break;
            case wgcna:
                CommonResult<WgcnaTaskPO> wgcnaTaskById = scrnaseqWgcnaServiceClient.deleteWgcnaTaskById(id);
                wgcnaTaskById.checkError();
                WgcnaTaskPO task = wgcnaTaskById.getData();
                node = TreeNodeUtils.taskToNode(task);
                break;
            default:
                node = new TaskTreeNodeVO();
                break;
        }
        return node;
    }

    public ReportTaskPO deleteReportTask(String id) {
        CommonResult<ReportTaskPO> reportById = scrnaseqReportServiceClient.deleteReportById(id);
        reportById.checkError();
        return reportById.getData();
    }

    public PageResult<ReportTaskDTO> findReportPage(CurrentUser currentUser, ReportSearchVO searchVO, PageParam pageParam) {
        ReportQueryVO queryVO = ScrnaseqMapper.INSTANCE.convertToReportQuery(searchVO);
        queryVO.setUserId(currentUser.getId());
        queryVO.setPage(pageParam.getPage());
        queryVO.setSize(pageParam.getSize());
        CommonResult<PageResult<ReportTaskDTO>> result = scrnaseqReportServiceClient.findReportTaskPage(queryVO);
        result.checkError();
        return result.getData();
    }

    public ReportTaskPO saveReport(CurrentUser user, ScrnaseqReportTaskParam param) {
        ReportTaskCreateParamVO vo = ScrnaseqMapper.INSTANCE.convertToReportTaskCreateParamVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<ReportTaskPO> result = scrnaseqReportServiceClient.saveReportTask(vo);
        result.checkError();
        return result.getData();
    }

    public ReportTaskDTO findReportTaskDTOById(String id) {
        CommonResult<ReportTaskDTO> result = scrnaseqReportServiceClient.findReportDtoById(id);
        result.checkError();
        return result.getData();
    }

    public SelectTaskVO findTaskByParams(ScrnaseqSelectParam param, CurrentUser user) {
        SelectParamVO vo = ScrnaseqMapper.INSTANCE.convertToSelectParamVO(param);
        vo.setUserId(user.getId());

        CommonResult<SelectTaskVO> result = scrnaseqReportServiceClient.findTaskBySelectParams(vo);
        result.checkError();
        return result.getData();
    }

    public ReportTaskDTO getSelectData(CurrentUser user, ScrnaseqReportTaskParam param) {
        ReportTaskParamVO vo = ScrnaseqMapper.INSTANCE.convertToReportTaskParamVO(param);
        vo.setUserId(user.getId());
        CommonResult<ReportTaskDTO> result = scrnaseqReportServiceClient.findTaskResultByParams(vo);
        result.checkError();
        return result.getData();
    }

    public Map<String, List<String>> getGeneNames(ScrnaseqReportTaskParam param) {
        SelectedTaskVO vo = ScrnaseqMapper.INSTANCE.convertToSelectedTaskVO(param);
        CommonResult<Map<String, List<String>>> result = scrnaseqFileServiceClient.getGeneNamesByParams(vo);
        result.checkError();
        return result.getData();
    }

    public List<List<Double>> getGeneData(String type, String gene, ScrnaseqReportTaskParam param) {
        SelectedTaskVO vo = ScrnaseqMapper.INSTANCE.convertToSelectedTaskVO(param);
        vo.setType(type);
        vo.setGene(gene);
        CommonResult<List<List<Double>>> result = scrnaseqFileServiceClient.getGeneDataByParams(vo);
        result.checkError();
        return result.getData();
    }

    public Object getGenomicsChartData(GenomicsTaskPO task, Integer chartNo) {
        ChartParamVO vo = new ChartParamVO();
        vo.setTaskId(task.getTaskId());
        vo.setChartNo(chartNo);
        CommonResult<Object> result = scrnaseqFileServiceClient.getGenomicsChartData(vo);
        result.checkError();
        return result.getData();
    }

    public Object getBaselineChartData(BaselineTaskPO task, Integer chartNo) {
        ChartParamVO vo = new ChartParamVO();
        vo.setTaskId(task.getTaskId());
        vo.setChartNo(chartNo);
        CommonResult<Object> result = scrnaseqFileServiceClient.getBaselineChartData(vo);
        result.checkError();
        return result.getData();
    }

    public Object getPagaChartData(PagaTaskPO task, Integer chartNo) {
        ChartParamVO vo = new ChartParamVO();
        vo.setTaskId(task.getTaskId());
        vo.setChartNo(chartNo);
        CommonResult<Object> result = scrnaseqFileServiceClient.getPagaChartData(vo);
        result.checkError();
        return result.getData();
    }

    public Object getDegChartData(DegTaskPO task, Integer chartNo) {
        ChartParamVO vo = new ChartParamVO();
        vo.setTaskId(task.getTaskId());
        vo.setChartNo(chartNo);
        vo.setCutfc(task.getCutfc());
        vo.setCutp(task.getCutp());
        CommonResult<Object> result = scrnaseqFileServiceClient.getDegChartData(vo);
        result.checkError();
        return result.getData();
    }

    public Object getWgcnaChartData(WgcnaTaskPO task, Integer chartNo) {
        ChartParamVO vo = new ChartParamVO();
        vo.setTaskId(task.getTaskId());
        vo.setChartNo(chartNo);
        CommonResult<Object> result = scrnaseqFileServiceClient.getWgcnaChartData(vo);
        result.checkError();
        return result.getData();
    }

    public Object getGeneChartData(GenesTaskPO task, Integer chartNo) {
        ChartParamVO vo = new ChartParamVO();
        vo.setTaskId(task.getTaskId());
        vo.setChartNo(chartNo);
        CommonResult<Object> result = scrnaseqFileServiceClient.getGenesChartData(vo);
        result.checkError();
        return result.getData();
    }

    public ResponseEntity<byte[]> getGenesStackedViolinPdf(String taskId) {
        return scrnaseqFileServiceClient.getGenesStackedViolinPdf(taskId);
    }

    public Response downloadReportByTaskId(String taskId) {
        return scrnaseqFileServiceClient.downloadReportResult(taskId, "");
    }

    public List<AnnotationTabRowVO> findBaselineAnnotationTabRow(String taskId) {
        CommonResult<List<AnnotationTabRowVO>> result = scrnaseqFileServiceClient.findBaselineAnnotationTabRow(taskId);
        result.checkError();
        return result.getData();
    }

    public Response commonDownload(String fileEnum, String taskId, String sampleName, String resName) {
        return scrnaseqFileServiceClient.commonDownload(fileEnum, taskId, StrUtil.trimToEmpty(sampleName), StrUtil.trimToEmpty(resName));
    }

    public Response downloadLogs() {
        return scrnaseqFileServiceClient.downloadLogs();
    }

    public List<ScrnaseqTaskVO> findcompleteGenomicsTask(CurrentUser currentUser) {

        CommonResult<List<GenomicsTaskPO>> result = scrnaseqGenomicsServiceClient.findcompleteGenomicsTask(currentUser.getId());
        result.checkError();
        List<GenomicsTaskPO> data = result.getData();

        return data.stream().map(task -> {
            ScrnaseqTaskVO vo = new ScrnaseqTaskVO();
            vo.setGenomicsTask(task);
            return vo;
        }).collect(Collectors.toList());
    }

    public List<ScrnaseqTaskVO> findcompleteBaselineTask(CurrentUser currentUser) {
        CommonResult<List<BaselineTaskDTO>> result = scrnaseqBaselineServiceClient.findcompleteBaselineTask(currentUser.getId());
        result.checkError();
        List<BaselineTaskDTO> data = result.getData();

        return data.stream().map(task -> {
            ScrnaseqTaskVO vo = new ScrnaseqTaskVO();
            vo.setBaselineTask(task.getBaselineTask());
            vo.setGenomicsTask(task.getGenomicsTask());
            return vo;
        }).collect(Collectors.toList());
    }

    public List<TaskListTreeVO> findAllTaskWithTree(CurrentUser user, ScrnaseqTaskSearchVO searchVO) {
        TaskQueryVO vo = ScrnaseqMapper.INSTANCE.convertToTaskQuery(searchVO);
        vo.setUserId(user.getId());
        CommonResult<List<TaskListTreeVO>> result = scrnaseqServiceClient.findAllTaskWithTree(vo);
        result.checkError();
        return result.getData();
    }

    public List<TaskListTreeVO> findAllCompleteTaskWithTree(CurrentUser user, ScrnaseqTaskSearchVO searchVO) {
        TaskQueryVO vo = ScrnaseqMapper.INSTANCE.convertToTaskQuery(searchVO);
        vo.setUserId(user.getId());
        CommonResult<List<TaskListTreeVO>> result = scrnaseqServiceClient.findAllCompleteTaskWithTree(vo);
        result.checkError();
        return result.getData();
    }

    public String getVersion() {
        return scrnaseqServiceClient.getVersion();
    }
}
