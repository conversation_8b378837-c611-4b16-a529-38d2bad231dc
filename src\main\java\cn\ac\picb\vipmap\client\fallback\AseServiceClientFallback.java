package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.ase.po.AseTaskPO;
import cn.ac.picb.ase.vo.AseTaskInput;
import cn.ac.picb.ase.vo.AseTaskParamVO;
import cn.ac.picb.ase.vo.AseTaskQueryVO;
import cn.ac.picb.ase.vo.AseTaskVO;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.vipmap.client.AseServiceClient;
import feign.Response;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:19
 */
@Component
public class AseServiceClientFallback implements AseServiceClient {
    private final static String SERVER_NAME = "rnaseq-ase-service";

    @Override
    public CommonResult<AseTaskPO> saveTask(AseTaskParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadTemplateExcel() {
        return null;
    }

    @Override
    public CommonResult<List<AseTaskInput>> uploadTemplate(String username, MultipartFile file) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PageResult<AseTaskPO>> findTaskPage(AseTaskQueryVO query) {
        return serverError(SERVER_NAME);

    }

    @Override
    public CommonResult<AseTaskPO> deleteById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<AseTaskVO> findDetailById(String id) {
        return serverError(SERVER_NAME);

    }

    @Override
    public Response downloadResult(String taskId, String displayName) {
        return null;
    }
}
