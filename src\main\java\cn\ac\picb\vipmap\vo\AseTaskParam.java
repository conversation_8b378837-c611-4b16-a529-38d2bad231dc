package cn.ac.picb.vipmap.vo;

import cn.ac.picb.ase.vo.AseTaskInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:04
 */
@Data
public class AseTaskParam {
    @NotBlank
    private String taskName;

    @NotNull
    private List<AseTaskInput> inputs;

    @NotBlank
    private String qcMethod;

    @NotBlank
    private String species;

    @NotBlank
    private String specVersion;

    @NotBlank
    private String mappingMethod;
    @NotBlank
    private String aseMethod;

    @NotNull
    private Integer readLength;

    @NotBlank
    private String libType;

    @NotNull
    private String cstat;
}
