package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.vipmap.vo.WgbsChartFilter;
import cn.ac.picb.vipmap.vo.WgbsTaskParam;
import cn.ac.picb.vipmap.vo.WgbsTaskSearchVO;
import cn.ac.picb.wgbs.vo.WgbsChartFilterVO;
import cn.ac.picb.wgbs.vo.WgbsTaskParamVO;
import cn.ac.picb.wgbs.vo.WgbsTaskQueryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface WgbsMapper {

    WgbsMapper INSTANCE = Mappers.getMapper(WgbsMapper.class);

    WgbsTaskQueryVO convert(WgbsTaskSearchVO search);

    WgbsTaskParamVO convertToVO(WgbsTaskParam param);

    WgbsChartFilterVO filterToVo(WgbsChartFilter filter);
}
