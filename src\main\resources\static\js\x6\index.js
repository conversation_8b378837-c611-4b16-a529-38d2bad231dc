!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).X6={})}(this,(function(t){"use strict";function e(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n}function n(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o}"object"==typeof window&&window.NodeList&&!NodeList.prototype.forEach&&(NodeList.prototype.forEach=Array.prototype.forEach),[Element.prototype,Document.prototype,DocumentFragment.prototype].forEach((t=>{Object.prototype.hasOwnProperty.call(t,"append")||Object.defineProperty(t,"append",{configurable:!0,enumerable:!0,writable:!0,value(...t){const e=document.createDocumentFragment();t.forEach((t=>{const n=t instanceof Node;e.appendChild(n?t:document.createTextNode(String(t)))})),this.appendChild(e)}})}));class r{get disposed(){return!0===this._disposed}dispose(){this._disposed=!0}}!function(t){t.dispose=function(){return(t,e,n)=>{const r=n.value,i=t.__proto__;n.value=function(){this.disposed||(r.call(this),i.dispose.call(this))}}}}(r||(r={}));class i{constructor(){this.isDisposed=!1,this.items=new Set}get disposed(){return this.isDisposed}dispose(){this.isDisposed||(this.isDisposed=!0,this.items.forEach((t=>{t.dispose()})),this.items.clear())}contains(t){return this.items.has(t)}add(t){this.items.add(t)}remove(t){this.items.delete(t)}clear(){this.items.clear()}}!function(t){t.from=function(e){const n=new t;return e.forEach((t=>{n.add(t)})),n}}(i||(i={}));var s="object"==typeof global&&global&&global.Object===Object&&global,o="object"==typeof self&&self&&self.Object===Object&&self,a=s||o||Function("return this")(),l=a.Symbol,c=Object.prototype,h=c.hasOwnProperty,u=c.toString,g=l?l.toStringTag:void 0;var d=Object.prototype.toString;var f="[object Null]",p="[object Undefined]",m=l?l.toStringTag:void 0;function y(t){return null==t?void 0===t?p:f:m&&m in Object(t)?function(t){var e=h.call(t,g),n=t[g];try{t[g]=void 0;var r=!0}catch(t){}var i=u.call(t);return r&&(e?t[g]=n:delete t[g]),i}(t):function(t){return d.call(t)}(t)}function v(t){return null!=t&&"object"==typeof t}var b="[object Symbol]";function x(t){return"symbol"==typeof t||v(t)&&y(t)==b}var w=NaN;function A(t){return"number"==typeof t?t:x(t)?w:+t}function P(t,e){for(var n=-1,r=null==t?0:t.length,i=Array(r);++n<r;)i[n]=e(t[n],n,t);return i}var C=Array.isArray,M=1/0,E=l?l.prototype:void 0,S=E?E.toString:void 0;function O(t){if("string"==typeof t)return t;if(C(t))return P(t,O)+"";if(x(t))return S?S.call(t):"";var e=t+"";return"0"==e&&1/t==-M?"-0":e}function T(t,e){return function(n,r){var i;if(void 0===n&&void 0===r)return e;if(void 0!==n&&(i=n),void 0!==r){if(void 0===i)return r;"string"==typeof n||"string"==typeof r?(n=O(n),r=O(r)):(n=A(n),r=A(r)),i=t(n,r)}return i}}var k=T((function(t,e){return t+e}),0),N=/\s/;function j(t){for(var e=t.length;e--&&N.test(t.charAt(e)););return e}var _=/^\s+/;function L(t){return t?t.slice(0,j(t)+1).replace(_,""):t}function B(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}var I=NaN,D=/^[-+]0x[0-9a-f]+$/i,R=/^0b[01]+$/i,V=/^0o[0-7]+$/i,z=parseInt;function $(t){if("number"==typeof t)return t;if(x(t))return I;if(B(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=B(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=L(t);var n=R.test(t);return n||V.test(t)?z(t.slice(2),n?2:8):D.test(t)?I:+t}var F=1/0,G=17976931348623157e292;function U(t){return t?(t=$(t))===F||t===-F?(t<0?-1:1)*G:t==t?t:0:0===t?t:0}function q(t){var e=U(t),n=e%1;return e==e?n?e-n:e:0}var W="Expected a function";function H(t){return t}var J="[object AsyncFunction]",X="[object Function]",Y="[object GeneratorFunction]",Z="[object Proxy]";function K(t){if(!B(t))return!1;var e=y(t);return e==X||e==Y||e==J||e==Z}var Q,tt=a["__core-js_shared__"],et=(Q=/[^.]+$/.exec(tt&&tt.keys&&tt.keys.IE_PROTO||""))?"Symbol(src)_1."+Q:"";var nt=Function.prototype.toString;function rt(t){if(null!=t){try{return nt.call(t)}catch(t){}try{return t+""}catch(t){}}return""}var it=/^\[object .+?Constructor\]$/,st=Function.prototype,ot=Object.prototype,at=st.toString,lt=ot.hasOwnProperty,ct=RegExp("^"+at.call(lt).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function ht(t){return!(!B(t)||function(t){return!!et&&et in t}(t))&&(K(t)?ct:it).test(rt(t))}function ut(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return ht(n)?n:void 0}var gt=ut(a,"WeakMap"),dt=gt&&new gt,ft=dt?function(t,e){return dt.set(t,e),t}:H,pt=Object.create,mt=function(){function t(){}return function(e){if(!B(e))return{};if(pt)return pt(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}();function yt(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var n=mt(t.prototype),r=t.apply(n,e);return B(r)?r:n}}var vt=1;function bt(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}var xt=Math.max;function wt(t,e,n,r){for(var i=-1,s=t.length,o=n.length,a=-1,l=e.length,c=xt(s-o,0),h=Array(l+c),u=!r;++a<l;)h[a]=e[a];for(;++i<o;)(u||i<s)&&(h[n[i]]=t[i]);for(;c--;)h[a++]=t[i++];return h}var At=Math.max;function Pt(t,e,n,r){for(var i=-1,s=t.length,o=-1,a=n.length,l=-1,c=e.length,h=At(s-a,0),u=Array(h+c),g=!r;++i<h;)u[i]=t[i];for(var d=i;++l<c;)u[d+l]=e[l];for(;++o<a;)(g||i<s)&&(u[d+n[o]]=t[i++]);return u}function Ct(){}var Mt=**********;function Et(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=Mt,this.__views__=[]}function St(){}Et.prototype=mt(Ct.prototype),Et.prototype.constructor=Et;var Ot=dt?function(t){return dt.get(t)}:St,Tt={},kt=Object.prototype.hasOwnProperty;function Nt(t){for(var e=t.name+"",n=Tt[e],r=kt.call(Tt,e)?n.length:0;r--;){var i=n[r],s=i.func;if(null==s||s==t)return i.name}return e}function jt(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=void 0}function _t(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}function Lt(t){if(t instanceof Et)return t.clone();var e=new jt(t.__wrapped__,t.__chain__);return e.__actions__=_t(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e}jt.prototype=mt(Ct.prototype),jt.prototype.constructor=jt;var Bt=Object.prototype.hasOwnProperty;function It(t){if(v(t)&&!C(t)&&!(t instanceof Et)){if(t instanceof jt)return t;if(Bt.call(t,"__wrapped__"))return Lt(t)}return new jt(t)}function Dt(t){var e=Nt(t),n=It[e];if("function"!=typeof n||!(e in Et.prototype))return!1;if(t===n)return!0;var r=Ot(n);return!!r&&t===r[0]}It.prototype=Ct.prototype,It.prototype.constructor=It;var Rt=800,Vt=16,zt=Date.now;function $t(t){var e=0,n=0;return function(){var r=zt(),i=Vt-(r-n);if(n=r,i>0){if(++e>=Rt)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}var Ft=$t(ft),Gt=/\{\n\/\* \[wrapped with (.+)\] \*/,Ut=/,? & /;var qt=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/;function Wt(t){return function(){return t}}var Ht=function(){try{var t=ut(Object,"defineProperty");return t({},"",{}),t}catch(t){}}(),Jt=Ht?function(t,e){return Ht(t,"toString",{configurable:!0,enumerable:!1,value:Wt(e),writable:!0})}:H,Xt=$t(Jt);function Yt(t,e){for(var n=-1,r=null==t?0:t.length;++n<r&&!1!==e(t[n],n,t););return t}function Zt(t,e,n,r){for(var i=t.length,s=n+(r?1:-1);r?s--:++s<i;)if(e(t[s],s,t))return s;return-1}function Kt(t){return t!=t}function Qt(t,e,n){return e==e?function(t,e,n){for(var r=n-1,i=t.length;++r<i;)if(t[r]===e)return r;return-1}(t,e,n):Zt(t,Kt,n)}function te(t,e){return!!(null==t?0:t.length)&&Qt(t,e,0)>-1}var ee=[["ary",128],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",32],["partialRight",64],["rearg",256]];function ne(t,e,n){var r=e+"";return Xt(t,function(t,e){var n=e.length;if(!n)return t;var r=n-1;return e[r]=(n>1?"& ":"")+e[r],e=e.join(n>2?", ":" "),t.replace(qt,"{\n/* [wrapped with "+e+"] */\n")}(r,function(t,e){return Yt(ee,(function(n){var r="_."+n[0];e&n[1]&&!te(t,r)&&t.push(r)})),t.sort()}(function(t){var e=t.match(Gt);return e?e[1].split(Ut):[]}(r),n)))}var re=1,ie=2,se=4,oe=8,ae=32,le=64;function ce(t,e,n,r,i,s,o,a,l,c){var h=e&oe;e|=h?ae:le,(e&=~(h?le:ae))&se||(e&=~(re|ie));var u=[t,e,i,h?s:void 0,h?o:void 0,h?void 0:s,h?void 0:o,a,l,c],g=n.apply(void 0,u);return Dt(t)&&Ft(g,u),g.placeholder=r,ne(g,t,e)}function he(t){return t.placeholder}var ue=9007199254740991,ge=/^(?:0|[1-9]\d*)$/;function de(t,e){var n=typeof t;return!!(e=null==e?ue:e)&&("number"==n||"symbol"!=n&&ge.test(t))&&t>-1&&t%1==0&&t<e}var fe=Math.min;var pe="__lodash_placeholder__";function me(t,e){for(var n=-1,r=t.length,i=0,s=[];++n<r;){var o=t[n];o!==e&&o!==pe||(t[n]=pe,s[i++]=n)}return s}var ye=1,ve=2,be=8,xe=16,we=128,Ae=512;function Pe(t,e,n,r,i,s,o,l,c,h){var u=e&we,g=e&ye,d=e&ve,f=e&(be|xe),p=e&Ae,m=d?void 0:yt(t);return function y(){for(var v=arguments.length,b=Array(v),x=v;x--;)b[x]=arguments[x];if(f)var w=he(y),A=function(t,e){for(var n=t.length,r=0;n--;)t[n]===e&&++r;return r}(b,w);if(r&&(b=wt(b,r,i,f)),s&&(b=Pt(b,s,o,f)),v-=A,f&&v<h){var P=me(b,w);return ce(t,e,Pe,y.placeholder,n,b,P,l,c,h-v)}var C=g?n:this,M=d?C[t]:t;return v=b.length,l?b=function(t,e){for(var n=t.length,r=fe(e.length,n),i=_t(t);r--;){var s=e[r];t[r]=de(s,n)?i[s]:void 0}return t}(b,l):p&&v>1&&b.reverse(),u&&c<v&&(b.length=c),this&&this!==a&&this instanceof y&&(M=m||yt(M)),M.apply(C,b)}}var Ce=1;var Me="__lodash_placeholder__",Ee=1,Se=2,Oe=4,Te=8,ke=128,Ne=256,je=Math.min;var _e="Expected a function",Le=1,Be=2,Ie=8,De=16,Re=32,Ve=64,ze=Math.max;function $e(t,e,n,r,i,s,o,l){var c=e&Be;if(!c&&"function"!=typeof t)throw new TypeError(_e);var h=r?r.length:0;if(h||(e&=~(Re|Ve),r=i=void 0),o=void 0===o?o:ze(q(o),0),l=void 0===l?l:q(l),h-=i?i.length:0,e&Ve){var u=r,g=i;r=i=void 0}var d=c?void 0:Ot(t),f=[t,e,n,r,i,u,g,s,o,l];if(d&&function(t,e){var n=t[1],r=e[1],i=n|r,s=i<(Ee|Se|ke),o=r==ke&&n==Te||r==ke&&n==Ne&&t[7].length<=e[8]||r==(ke|Ne)&&e[7].length<=e[8]&&n==Te;if(!s&&!o)return t;r&Ee&&(t[2]=e[2],i|=n&Ee?0:Oe);var a=e[3];if(a){var l=t[3];t[3]=l?wt(l,a,e[4]):a,t[4]=l?me(t[3],Me):e[4]}(a=e[5])&&(l=t[5],t[5]=l?Pt(l,a,e[6]):a,t[6]=l?me(t[5],Me):e[6]),(a=e[7])&&(t[7]=a),r&ke&&(t[8]=null==t[8]?e[8]:je(t[8],e[8])),null==t[9]&&(t[9]=e[9]),t[0]=e[0],t[1]=i}(f,d),t=f[0],e=f[1],n=f[2],r=f[3],i=f[4],!(l=f[9]=void 0===f[9]?c?0:t.length:ze(f[9]-h,0))&&e&(Ie|De)&&(e&=~(Ie|De)),e&&e!=Le)p=e==Ie||e==De?function(t,e,n){var r=yt(t);return function i(){for(var s=arguments.length,o=Array(s),l=s,c=he(i);l--;)o[l]=arguments[l];var h=s<3&&o[0]!==c&&o[s-1]!==c?[]:me(o,c);return(s-=h.length)<n?ce(t,e,Pe,i.placeholder,void 0,o,h,void 0,void 0,n-s):bt(this&&this!==a&&this instanceof i?r:t,this,o)}}(t,e,l):e!=Re&&e!=(Le|Re)||i.length?Pe.apply(void 0,f):function(t,e,n,r){var i=e&Ce,s=yt(t);return function e(){for(var o=-1,l=arguments.length,c=-1,h=r.length,u=Array(h+l),g=this&&this!==a&&this instanceof e?s:t;++c<h;)u[c]=r[c];for(;l--;)u[c++]=arguments[++o];return bt(g,i?n:this,u)}}(t,e,n,r);else var p=function(t,e,n){var r=e&vt,i=yt(t);return function e(){return(this&&this!==a&&this instanceof e?i:t).apply(r?n:this,arguments)}}(t,e,n);return ne((d?ft:Ft)(p,f),t,e)}var Fe=128;function Ge(t,e,n){return e=n?void 0:e,e=t&&null==e?t.length:e,$e(t,Fe,void 0,void 0,void 0,void 0,e)}function Ue(t,e,n){"__proto__"==e&&Ht?Ht(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}function qe(t,e){return t===e||t!=t&&e!=e}var We=Object.prototype.hasOwnProperty;function He(t,e,n){var r=t[e];We.call(t,e)&&qe(r,n)&&(void 0!==n||e in t)||Ue(t,e,n)}function Je(t,e,n,r){var i=!n;n||(n={});for(var s=-1,o=e.length;++s<o;){var a=e[s],l=r?r(n[a],t[a],a,n,t):void 0;void 0===l&&(l=t[a]),i?Ue(n,a,l):He(n,a,l)}return n}var Xe=Math.max;function Ye(t,e,n){return e=Xe(void 0===e?t.length-1:e,0),function(){for(var r=arguments,i=-1,s=Xe(r.length-e,0),o=Array(s);++i<s;)o[i]=r[e+i];i=-1;for(var a=Array(e+1);++i<e;)a[i]=r[i];return a[e]=n(o),bt(t,this,a)}}function Ze(t,e){return Xt(Ye(t,e,H),t+"")}var Ke=9007199254740991;function Qe(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=Ke}function tn(t){return null!=t&&Qe(t.length)&&!K(t)}function en(t,e,n){if(!B(n))return!1;var r=typeof e;return!!("number"==r?tn(n)&&de(e,n.length):"string"==r&&e in n)&&qe(n[e],t)}function nn(t){return Ze((function(e,n){var r=-1,i=n.length,s=i>1?n[i-1]:void 0,o=i>2?n[2]:void 0;for(s=t.length>3&&"function"==typeof s?(i--,s):void 0,o&&en(n[0],n[1],o)&&(s=i<3?void 0:s,i=1),e=Object(e);++r<i;){var a=n[r];a&&t(e,a,r,s)}return e}))}var rn=Object.prototype;function sn(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||rn)}function on(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}var an="[object Arguments]";function ln(t){return v(t)&&y(t)==an}var cn=Object.prototype,hn=cn.hasOwnProperty,un=cn.propertyIsEnumerable,gn=ln(function(){return arguments}())?ln:function(t){return v(t)&&hn.call(t,"callee")&&!un.call(t,"callee")};function dn(){return!1}var fn="object"==typeof t&&t&&!t.nodeType&&t,pn=fn&&"object"==typeof module&&module&&!module.nodeType&&module,mn=pn&&pn.exports===fn?a.Buffer:void 0,yn=(mn?mn.isBuffer:void 0)||dn,vn={};function bn(t){return function(e){return t(e)}}vn["[object Float32Array]"]=vn["[object Float64Array]"]=vn["[object Int8Array]"]=vn["[object Int16Array]"]=vn["[object Int32Array]"]=vn["[object Uint8Array]"]=vn["[object Uint8ClampedArray]"]=vn["[object Uint16Array]"]=vn["[object Uint32Array]"]=!0,vn["[object Arguments]"]=vn["[object Array]"]=vn["[object ArrayBuffer]"]=vn["[object Boolean]"]=vn["[object DataView]"]=vn["[object Date]"]=vn["[object Error]"]=vn["[object Function]"]=vn["[object Map]"]=vn["[object Number]"]=vn["[object Object]"]=vn["[object RegExp]"]=vn["[object Set]"]=vn["[object String]"]=vn["[object WeakMap]"]=!1;var xn="object"==typeof t&&t&&!t.nodeType&&t,wn=xn&&"object"==typeof module&&module&&!module.nodeType&&module,An=wn&&wn.exports===xn&&s.process,Pn=function(){try{var t=wn&&wn.require&&wn.require("util").types;return t||An&&An.binding&&An.binding("util")}catch(t){}}(),Cn=Pn&&Pn.isTypedArray,Mn=Cn?bn(Cn):function(t){return v(t)&&Qe(t.length)&&!!vn[y(t)]},En=Object.prototype.hasOwnProperty;function Sn(t,e){var n=C(t),r=!n&&gn(t),i=!n&&!r&&yn(t),s=!n&&!r&&!i&&Mn(t),o=n||r||i||s,a=o?on(t.length,String):[],l=a.length;for(var c in t)!e&&!En.call(t,c)||o&&("length"==c||i&&("offset"==c||"parent"==c)||s&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||de(c,l))||a.push(c);return a}function On(t,e){return function(n){return t(e(n))}}var Tn=On(Object.keys,Object),kn=Object.prototype.hasOwnProperty;function Nn(t){if(!sn(t))return Tn(t);var e=[];for(var n in Object(t))kn.call(t,n)&&"constructor"!=n&&e.push(n);return e}function jn(t){return tn(t)?Sn(t):Nn(t)}var _n=Object.prototype.hasOwnProperty,Ln=nn((function(t,e){if(sn(e)||tn(e))Je(e,jn(e),t);else for(var n in e)_n.call(e,n)&&He(t,n,e[n])}));var Bn=Object.prototype.hasOwnProperty;function In(t){if(!B(t))return function(t){var e=[];if(null!=t)for(var n in Object(t))e.push(n);return e}(t);var e=sn(t),n=[];for(var r in t)("constructor"!=r||!e&&Bn.call(t,r))&&n.push(r);return n}function Dn(t){return tn(t)?Sn(t,!0):In(t)}var Rn=nn((function(t,e){Je(e,Dn(e),t)})),Vn=nn((function(t,e,n,r){Je(e,Dn(e),t,r)})),zn=nn((function(t,e,n,r){Je(e,jn(e),t,r)})),$n=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Fn=/^\w*$/;function Gn(t,e){if(C(t))return!1;var n=typeof t;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!x(t))||(Fn.test(t)||!$n.test(t)||null!=e&&t in Object(e))}var Un=ut(Object,"create");var qn="__lodash_hash_undefined__",Wn=Object.prototype.hasOwnProperty;var Hn=Object.prototype.hasOwnProperty;var Jn="__lodash_hash_undefined__";function Xn(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Yn(t,e){for(var n=t.length;n--;)if(qe(t[n][0],e))return n;return-1}Xn.prototype.clear=function(){this.__data__=Un?Un(null):{},this.size=0},Xn.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Xn.prototype.get=function(t){var e=this.__data__;if(Un){var n=e[t];return n===qn?void 0:n}return Wn.call(e,t)?e[t]:void 0},Xn.prototype.has=function(t){var e=this.__data__;return Un?void 0!==e[t]:Hn.call(e,t)},Xn.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=Un&&void 0===e?Jn:e,this};var Zn=Array.prototype.splice;function Kn(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}Kn.prototype.clear=function(){this.__data__=[],this.size=0},Kn.prototype.delete=function(t){var e=this.__data__,n=Yn(e,t);return!(n<0)&&(n==e.length-1?e.pop():Zn.call(e,n,1),--this.size,!0)},Kn.prototype.get=function(t){var e=this.__data__,n=Yn(e,t);return n<0?void 0:e[n][1]},Kn.prototype.has=function(t){return Yn(this.__data__,t)>-1},Kn.prototype.set=function(t,e){var n=this.__data__,r=Yn(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this};var Qn=ut(a,"Map");function tr(t,e){var n,r,i=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof e?"string":"hash"]:i.map}function er(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}er.prototype.clear=function(){this.size=0,this.__data__={hash:new Xn,map:new(Qn||Kn),string:new Xn}},er.prototype.delete=function(t){var e=tr(this,t).delete(t);return this.size-=e?1:0,e},er.prototype.get=function(t){return tr(this,t).get(t)},er.prototype.has=function(t){return tr(this,t).has(t)},er.prototype.set=function(t,e){var n=tr(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this};var nr="Expected a function";function rr(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError(nr);var n=function(){var r=arguments,i=e?e.apply(this,r):r[0],s=n.cache;if(s.has(i))return s.get(i);var o=t.apply(this,r);return n.cache=s.set(i,o)||s,o};return n.cache=new(rr.Cache||er),n}rr.Cache=er;var ir=500;var sr=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,or=/\\(\\)?/g,ar=function(t){var e=rr(t,(function(t){return n.size===ir&&n.clear(),t})),n=e.cache;return e}((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(sr,(function(t,n,r,i){e.push(r?i.replace(or,"$1"):n||t)})),e}));function lr(t){return null==t?"":O(t)}function cr(t,e){return C(t)?t:Gn(t,e)?[t]:ar(lr(t))}var hr=1/0;function ur(t){if("string"==typeof t||x(t))return t;var e=t+"";return"0"==e&&1/t==-hr?"-0":e}function gr(t,e){for(var n=0,r=(e=cr(e,t)).length;null!=t&&n<r;)t=t[ur(e[n++])];return n&&n==r?t:void 0}function dr(t,e,n){var r=null==t?void 0:gr(t,e);return void 0===r?n:r}function fr(t,e){for(var n=-1,r=e.length,i=Array(r),s=null==t;++n<r;)i[n]=s?void 0:dr(t,e[n]);return i}function pr(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}var mr=l?l.isConcatSpreadable:void 0;function yr(t){return C(t)||gn(t)||!!(mr&&t&&t[mr])}function vr(t,e,n,r,i){var s=-1,o=t.length;for(n||(n=yr),i||(i=[]);++s<o;){var a=t[s];e>0&&n(a)?e>1?vr(a,e-1,n,r,i):pr(i,a):r||(i[i.length]=a)}return i}function br(t){return(null==t?0:t.length)?vr(t,1):[]}function xr(t){return Xt(Ye(t,void 0,br),t+"")}var wr=xr(fr),Ar=On(Object.getPrototypeOf,Object),Pr="[object Object]",Cr=Function.prototype,Mr=Object.prototype,Er=Cr.toString,Sr=Mr.hasOwnProperty,Or=Er.call(Object);function Tr(t){if(!v(t)||y(t)!=Pr)return!1;var e=Ar(t);if(null===e)return!0;var n=Sr.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&Er.call(n)==Or}var kr="[object DOMException]",Nr="[object Error]";function jr(t){if(!v(t))return!1;var e=y(t);return e==Nr||e==kr||"string"==typeof t.message&&"string"==typeof t.name&&!Tr(t)}var _r=Ze((function(t,e){try{return bt(t,void 0,e)}catch(t){return jr(t)?t:new Error(t)}})),Lr="Expected a function";function Br(t,e){var n;if("function"!=typeof e)throw new TypeError(Lr);return t=q(t),function(){return--t>0&&(n=e.apply(this,arguments)),t<=1&&(e=void 0),n}}var Ir=Ze((function(t,e,n){var r=1;if(n.length){var i=me(n,he(Ir));r|=32}return $e(t,r,e,n,i)}));Ir.placeholder={};var Dr=xr((function(t,e){return Yt(e,(function(e){e=ur(e),Ue(t,e,Ir(t[e],t))})),t})),Rr=Ze((function(t,e,n){var r=3;if(n.length){var i=me(n,he(Rr));r|=32}return $e(e,r,t,n,i)}));function Vr(t,e,n){var r=-1,i=t.length;e<0&&(e=-e>i?0:i+e),(n=n>i?i:n)<0&&(n+=i),i=e>n?0:n-e>>>0,e>>>=0;for(var s=Array(i);++r<i;)s[r]=t[r+e];return s}function zr(t,e,n){var r=t.length;return n=void 0===n?r:n,!e&&n>=r?t:Vr(t,e,n)}Rr.placeholder={};var $r=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");function Fr(t){return $r.test(t)}var Gr="\\ud800-\\udfff",Ur="["+Gr+"]",qr="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",Wr="\\ud83c[\\udffb-\\udfff]",Hr="[^"+Gr+"]",Jr="(?:\\ud83c[\\udde6-\\uddff]){2}",Xr="[\\ud800-\\udbff][\\udc00-\\udfff]",Yr="(?:"+qr+"|"+Wr+")"+"?",Zr="[\\ufe0e\\ufe0f]?",Kr=Zr+Yr+("(?:\\u200d(?:"+[Hr,Jr,Xr].join("|")+")"+Zr+Yr+")*"),Qr="(?:"+[Hr+qr+"?",qr,Jr,Xr,Ur].join("|")+")",ti=RegExp(Wr+"(?="+Wr+")|"+Qr+Kr,"g");function ei(t){return Fr(t)?function(t){return t.match(ti)||[]}(t):function(t){return t.split("")}(t)}function ni(t){return function(e){var n=Fr(e=lr(e))?ei(e):void 0,r=n?n[0]:e.charAt(0),i=n?zr(n,1).join(""):e.slice(1);return r[t]()+i}}var ri=ni("toUpperCase");function ii(t){return ri(lr(t).toLowerCase())}function si(t,e,n,r){var i=-1,s=null==t?0:t.length;for(r&&s&&(n=t[++i]);++i<s;)n=e(n,t[i],i,t);return n}function oi(t){return function(e){return null==t?void 0:t[e]}}var ai=oi({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),li=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ci=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");function hi(t){return(t=lr(t))&&t.replace(li,ai).replace(ci,"")}var ui=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;var gi=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;var di="\\ud800-\\udfff",fi="\\u2700-\\u27bf",pi="a-z\\xdf-\\xf6\\xf8-\\xff",mi="A-Z\\xc0-\\xd6\\xd8-\\xde",yi="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vi="["+yi+"]",bi="\\d+",xi="["+fi+"]",wi="["+pi+"]",Ai="[^"+di+yi+bi+fi+pi+mi+"]",Pi="(?:\\ud83c[\\udde6-\\uddff]){2}",Ci="[\\ud800-\\udbff][\\udc00-\\udfff]",Mi="["+mi+"]",Ei="(?:"+wi+"|"+Ai+")",Si="(?:"+Mi+"|"+Ai+")",Oi="(?:['’](?:d|ll|m|re|s|t|ve))?",Ti="(?:['’](?:D|LL|M|RE|S|T|VE))?",ki="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\\ud83c[\\udffb-\\udfff])?",Ni="[\\ufe0e\\ufe0f]?",ji=Ni+ki+("(?:\\u200d(?:"+["[^"+di+"]",Pi,Ci].join("|")+")"+Ni+ki+")*"),_i="(?:"+[xi,Pi,Ci].join("|")+")"+ji,Li=RegExp([Mi+"?"+wi+"+"+Oi+"(?="+[vi,Mi,"$"].join("|")+")",Si+"+"+Ti+"(?="+[vi,Mi+Ei,"$"].join("|")+")",Mi+"?"+Ei+"+"+Oi,Mi+"+"+Ti,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",bi,_i].join("|"),"g");function Bi(t,e,n){return t=lr(t),void 0===(e=n?void 0:e)?function(t){return gi.test(t)}(t)?function(t){return t.match(Li)||[]}(t):function(t){return t.match(ui)||[]}(t):t.match(e)||[]}var Ii=RegExp("['’]","g");function Di(t){return function(e){return si(Bi(hi(e).replace(Ii,"")),t,"")}}var Ri=Di((function(t,e,n){return e=e.toLowerCase(),t+(n?ii(e):e)}));var Vi=a.isFinite,zi=Math.min;function $i(t){var e=Math[t];return function(t,n){if(t=$(t),(n=null==n?0:zi(q(n),292))&&Vi(t)){var r=(lr(t)+"e").split("e");return+((r=(lr(e(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return e(t)}}var Fi=$i("ceil");function Gi(t){var e=It(t);return e.__chain__=!0,e}var Ui=Math.ceil,qi=Math.max;function Wi(t,e,n){return t==t&&(void 0!==n&&(t=t<=n?t:n),void 0!==e&&(t=t>=e?t:e)),t}function Hi(t,e,n){return void 0===n&&(n=e,e=void 0),void 0!==n&&(n=(n=$(n))==n?n:0),void 0!==e&&(e=(e=$(e))==e?e:0),Wi($(t),e,n)}var Ji=200;function Xi(t){var e=this.__data__=new Kn(t);this.size=e.size}function Yi(t,e){return t&&Je(e,jn(e),t)}Xi.prototype.clear=function(){this.__data__=new Kn,this.size=0},Xi.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},Xi.prototype.get=function(t){return this.__data__.get(t)},Xi.prototype.has=function(t){return this.__data__.has(t)},Xi.prototype.set=function(t,e){var n=this.__data__;if(n instanceof Kn){var r=n.__data__;if(!Qn||r.length<Ji-1)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new er(r)}return n.set(t,e),this.size=n.size,this};var Zi="object"==typeof t&&t&&!t.nodeType&&t,Ki=Zi&&"object"==typeof module&&module&&!module.nodeType&&module,Qi=Ki&&Ki.exports===Zi?a.Buffer:void 0,ts=Qi?Qi.allocUnsafe:void 0;function es(t,e){if(e)return t.slice();var n=t.length,r=ts?ts(n):new t.constructor(n);return t.copy(r),r}function ns(t,e){for(var n=-1,r=null==t?0:t.length,i=0,s=[];++n<r;){var o=t[n];e(o,n,t)&&(s[i++]=o)}return s}function rs(){return[]}var is=Object.prototype.propertyIsEnumerable,ss=Object.getOwnPropertySymbols,os=ss?function(t){return null==t?[]:(t=Object(t),ns(ss(t),(function(e){return is.call(t,e)})))}:rs;var as=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)pr(e,os(t)),t=Ar(t);return e}:rs;function ls(t,e,n){var r=e(t);return C(t)?r:pr(r,n(t))}function cs(t){return ls(t,jn,os)}function hs(t){return ls(t,Dn,as)}var us=ut(a,"DataView"),gs=ut(a,"Promise"),ds=ut(a,"Set"),fs="[object Map]",ps="[object Promise]",ms="[object Set]",ys="[object WeakMap]",vs="[object DataView]",bs=rt(us),xs=rt(Qn),ws=rt(gs),As=rt(ds),Ps=rt(gt),Cs=y;(us&&Cs(new us(new ArrayBuffer(1)))!=vs||Qn&&Cs(new Qn)!=fs||gs&&Cs(gs.resolve())!=ps||ds&&Cs(new ds)!=ms||gt&&Cs(new gt)!=ys)&&(Cs=function(t){var e=y(t),n="[object Object]"==e?t.constructor:void 0,r=n?rt(n):"";if(r)switch(r){case bs:return vs;case xs:return fs;case ws:return ps;case As:return ms;case Ps:return ys}return e});var Ms=Cs,Es=Object.prototype.hasOwnProperty;var Ss=a.Uint8Array;function Os(t){var e=new t.constructor(t.byteLength);return new Ss(e).set(new Ss(t)),e}var Ts=/\w*$/;var ks=l?l.prototype:void 0,Ns=ks?ks.valueOf:void 0;function js(t,e){var n=e?Os(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}var _s="[object Boolean]",Ls="[object Date]",Bs="[object Map]",Is="[object Number]",Ds="[object RegExp]",Rs="[object Set]",Vs="[object String]",zs="[object Symbol]",$s="[object ArrayBuffer]",Fs="[object DataView]",Gs="[object Float32Array]",Us="[object Float64Array]",qs="[object Int8Array]",Ws="[object Int16Array]",Hs="[object Int32Array]",Js="[object Uint8Array]",Xs="[object Uint8ClampedArray]",Ys="[object Uint16Array]",Zs="[object Uint32Array]";function Ks(t,e,n){var r,i=t.constructor;switch(e){case $s:return Os(t);case _s:case Ls:return new i(+t);case Fs:return function(t,e){var n=e?Os(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}(t,n);case Gs:case Us:case qs:case Ws:case Hs:case Js:case Xs:case Ys:case Zs:return js(t,n);case Bs:return new i;case Is:case Vs:return new i(t);case Ds:return function(t){var e=new t.constructor(t.source,Ts.exec(t));return e.lastIndex=t.lastIndex,e}(t);case Rs:return new i;case zs:return r=t,Ns?Object(Ns.call(r)):{}}}function Qs(t){return"function"!=typeof t.constructor||sn(t)?{}:mt(Ar(t))}var to="[object Map]";var eo=Pn&&Pn.isMap,no=eo?bn(eo):function(t){return v(t)&&Ms(t)==to},ro="[object Set]";var io=Pn&&Pn.isSet,so=io?bn(io):function(t){return v(t)&&Ms(t)==ro},oo=1,ao=2,lo=4,co="[object Arguments]",ho="[object Function]",uo="[object GeneratorFunction]",go="[object Object]",fo={};function po(t,e,n,r,i,s){var o,a=e&oo,l=e&ao,c=e&lo;if(n&&(o=i?n(t,r,i,s):n(t)),void 0!==o)return o;if(!B(t))return t;var h=C(t);if(h){if(o=function(t){var e=t.length,n=new t.constructor(e);return e&&"string"==typeof t[0]&&Es.call(t,"index")&&(n.index=t.index,n.input=t.input),n}(t),!a)return _t(t,o)}else{var u=Ms(t),g=u==ho||u==uo;if(yn(t))return es(t,a);if(u==go||u==co||g&&!i){if(o=l||g?{}:Qs(t),!a)return l?function(t,e){return Je(t,as(t),e)}(t,function(t,e){return t&&Je(e,Dn(e),t)}(o,t)):function(t,e){return Je(t,os(t),e)}(t,Yi(o,t))}else{if(!fo[u])return i?t:{};o=Ks(t,u,a)}}s||(s=new Xi);var d=s.get(t);if(d)return d;s.set(t,o),so(t)?t.forEach((function(r){o.add(po(r,e,n,r,t,s))})):no(t)&&t.forEach((function(r,i){o.set(i,po(r,e,n,i,t,s))}));var f=h?void 0:(c?l?hs:cs:l?Dn:jn)(t);return Yt(f||t,(function(r,i){f&&(r=t[i=r]),He(o,i,po(r,e,n,i,t,s))})),o}fo[co]=fo["[object Array]"]=fo["[object ArrayBuffer]"]=fo["[object DataView]"]=fo["[object Boolean]"]=fo["[object Date]"]=fo["[object Float32Array]"]=fo["[object Float64Array]"]=fo["[object Int8Array]"]=fo["[object Int16Array]"]=fo["[object Int32Array]"]=fo["[object Map]"]=fo["[object Number]"]=fo[go]=fo["[object RegExp]"]=fo["[object Set]"]=fo["[object String]"]=fo["[object Symbol]"]=fo["[object Uint8Array]"]=fo["[object Uint8ClampedArray]"]=fo["[object Uint16Array]"]=fo["[object Uint32Array]"]=!0,fo["[object Error]"]=fo[ho]=fo["[object WeakMap]"]=!1;var mo=4;function yo(t){return po(t,mo)}var vo=1,bo=4;function xo(t){return po(t,vo|bo)}var wo=1,Ao=4;var Po=4;var Co="__lodash_hash_undefined__";function Mo(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new er;++e<n;)this.add(t[e])}function Eo(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}function So(t,e){return t.has(e)}Mo.prototype.add=Mo.prototype.push=function(t){return this.__data__.set(t,Co),this},Mo.prototype.has=function(t){return this.__data__.has(t)};var Oo=1,To=2;function ko(t,e,n,r,i,s){var o=n&Oo,a=t.length,l=e.length;if(a!=l&&!(o&&l>a))return!1;var c=s.get(t),h=s.get(e);if(c&&h)return c==e&&h==t;var u=-1,g=!0,d=n&To?new Mo:void 0;for(s.set(t,e),s.set(e,t);++u<a;){var f=t[u],p=e[u];if(r)var m=o?r(p,f,u,e,t,s):r(f,p,u,t,e,s);if(void 0!==m){if(m)continue;g=!1;break}if(d){if(!Eo(e,(function(t,e){if(!So(d,e)&&(f===t||i(f,t,n,r,s)))return d.push(e)}))){g=!1;break}}else if(f!==p&&!i(f,p,n,r,s)){g=!1;break}}return s.delete(t),s.delete(e),g}function No(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function jo(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var _o=1,Lo=2,Bo="[object Boolean]",Io="[object Date]",Do="[object Error]",Ro="[object Map]",Vo="[object Number]",zo="[object RegExp]",$o="[object Set]",Fo="[object String]",Go="[object Symbol]",Uo="[object ArrayBuffer]",qo="[object DataView]",Wo=l?l.prototype:void 0,Ho=Wo?Wo.valueOf:void 0;var Jo=1,Xo=Object.prototype.hasOwnProperty;var Yo=1,Zo="[object Arguments]",Ko="[object Array]",Qo="[object Object]",ta=Object.prototype.hasOwnProperty;function ea(t,e,n,r,i,s){var o=C(t),a=C(e),l=o?Ko:Ms(t),c=a?Ko:Ms(e),h=(l=l==Zo?Qo:l)==Qo,u=(c=c==Zo?Qo:c)==Qo,g=l==c;if(g&&yn(t)){if(!yn(e))return!1;o=!0,h=!1}if(g&&!h)return s||(s=new Xi),o||Mn(t)?ko(t,e,n,r,i,s):function(t,e,n,r,i,s,o){switch(n){case qo:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case Uo:return!(t.byteLength!=e.byteLength||!s(new Ss(t),new Ss(e)));case Bo:case Io:case Vo:return qe(+t,+e);case Do:return t.name==e.name&&t.message==e.message;case zo:case Fo:return t==e+"";case Ro:var a=No;case $o:var l=r&_o;if(a||(a=jo),t.size!=e.size&&!l)return!1;var c=o.get(t);if(c)return c==e;r|=Lo,o.set(t,e);var h=ko(a(t),a(e),r,i,s,o);return o.delete(t),h;case Go:if(Ho)return Ho.call(t)==Ho.call(e)}return!1}(t,e,l,n,r,i,s);if(!(n&Yo)){var d=h&&ta.call(t,"__wrapped__"),f=u&&ta.call(e,"__wrapped__");if(d||f){var p=d?t.value():t,m=f?e.value():e;return s||(s=new Xi),i(p,m,n,r,s)}}return!!g&&(s||(s=new Xi),function(t,e,n,r,i,s){var o=n&Jo,a=cs(t),l=a.length;if(l!=cs(e).length&&!o)return!1;for(var c=l;c--;){var h=a[c];if(!(o?h in e:Xo.call(e,h)))return!1}var u=s.get(t),g=s.get(e);if(u&&g)return u==e&&g==t;var d=!0;s.set(t,e),s.set(e,t);for(var f=o;++c<l;){var p=t[h=a[c]],m=e[h];if(r)var y=o?r(m,p,h,e,t,s):r(p,m,h,t,e,s);if(!(void 0===y?p===m||i(p,m,n,r,s):y)){d=!1;break}f||(f="constructor"==h)}if(d&&!f){var v=t.constructor,b=e.constructor;v==b||!("constructor"in t)||!("constructor"in e)||"function"==typeof v&&v instanceof v&&"function"==typeof b&&b instanceof b||(d=!1)}return s.delete(t),s.delete(e),d}(t,e,n,r,i,s))}function na(t,e,n,r,i){return t===e||(null==t||null==e||!v(t)&&!v(e)?t!=t&&e!=e:ea(t,e,n,r,na,i))}var ra=1,ia=2;function sa(t,e,n,r){var i=n.length,s=i,o=!r;if(null==t)return!s;for(t=Object(t);i--;){var a=n[i];if(o&&a[2]?a[1]!==t[a[0]]:!(a[0]in t))return!1}for(;++i<s;){var l=(a=n[i])[0],c=t[l],h=a[1];if(o&&a[2]){if(void 0===c&&!(l in t))return!1}else{var u=new Xi;if(r)var g=r(c,h,l,t,e,u);if(!(void 0===g?na(h,c,ra|ia,r,u):g))return!1}}return!0}function oa(t){return t==t&&!B(t)}function aa(t){for(var e=jn(t),n=e.length;n--;){var r=e[n],i=t[r];e[n]=[r,i,oa(i)]}return e}function la(t,e){return function(n){return null!=n&&(n[t]===e&&(void 0!==e||t in Object(n)))}}function ca(t){var e=aa(t);return 1==e.length&&e[0][2]?la(e[0][0],e[0][1]):function(n){return n===t||sa(n,t,e)}}function ha(t,e){return null!=t&&e in Object(t)}function ua(t,e,n){for(var r=-1,i=(e=cr(e,t)).length,s=!1;++r<i;){var o=ur(e[r]);if(!(s=null!=t&&n(t,o)))break;t=t[o]}return s||++r!=i?s:!!(i=null==t?0:t.length)&&Qe(i)&&de(o,i)&&(C(t)||gn(t))}function ga(t,e){return null!=t&&ua(t,e,ha)}var da=1,fa=2;function pa(t,e){return Gn(t)&&oa(e)?la(ur(t),e):function(n){var r=dr(n,t);return void 0===r&&r===e?ga(n,t):na(e,r,da|fa)}}function ma(t){return function(e){return null==e?void 0:e[t]}}function ya(t){return Gn(t)?ma(ur(t)):function(t){return function(e){return gr(e,t)}}(t)}function va(t){return"function"==typeof t?t:null==t?H:"object"==typeof t?C(t)?pa(t[0],t[1]):ca(t):ya(t)}var ba="Expected a function";function xa(t,e,n){var r=n.length;if(null==t)return!r;for(t=Object(t);r--;){var i=n[r],s=e[i],o=t[i];if(void 0===o&&!(i in t)||!s(o))return!1}return!0}var wa=1;function Aa(t,e,n,r){for(var i=-1,s=null==t?0:t.length;++i<s;){var o=t[i];e(r,o,n(o),t)}return r}function Pa(t){return function(e,n,r){for(var i=-1,s=Object(e),o=r(e),a=o.length;a--;){var l=o[t?a:++i];if(!1===n(s[l],l,s))break}return e}}var Ca=Pa();function Ma(t,e){return t&&Ca(t,e,jn)}function Ea(t,e){return function(n,r){if(null==n)return n;if(!tn(n))return t(n,r);for(var i=n.length,s=e?i:-1,o=Object(n);(e?s--:++s<i)&&!1!==r(o[s],s,o););return n}}var Sa=Ea(Ma);function Oa(t,e,n,r){return Sa(t,(function(t,i,s){e(r,t,n(t),s)})),r}function Ta(t,e){return function(n,r){var i=C(n)?Aa:Oa,s=e?e():{};return i(n,t,va(r),s)}}var ka=Object.prototype.hasOwnProperty,Na=Ta((function(t,e,n){ka.call(t,n)?++t[n]:Ue(t,n,1)}));var ja=8;function _a(t,e,n){var r=$e(t,ja,void 0,void 0,void 0,void 0,void 0,e=n?void 0:e);return r.placeholder=_a.placeholder,r}_a.placeholder={};var La=16;function Ba(t,e,n){var r=$e(t,La,void 0,void 0,void 0,void 0,void 0,e=n?void 0:e);return r.placeholder=Ba.placeholder,r}Ba.placeholder={};var Ia=function(){return a.Date.now()},Da="Expected a function",Ra=Math.max,Va=Math.min;function za(t,e,n){var r,i,s,o,a,l,c=0,h=!1,u=!1,g=!0;if("function"!=typeof t)throw new TypeError(Da);function d(e){var n=r,s=i;return r=i=void 0,c=e,o=t.apply(s,n)}function f(t){var n=t-l;return void 0===l||n>=e||n<0||u&&t-c>=s}function p(){var t=Ia();if(f(t))return m(t);a=setTimeout(p,function(t){var n=e-(t-l);return u?Va(n,s-(t-c)):n}(t))}function m(t){return a=void 0,g&&r?d(t):(r=i=void 0,o)}function y(){var t=Ia(),n=f(t);if(r=arguments,i=this,l=t,n){if(void 0===a)return function(t){return c=t,a=setTimeout(p,e),h?d(t):o}(l);if(u)return clearTimeout(a),a=setTimeout(p,e),d(l)}return void 0===a&&(a=setTimeout(p,e)),o}return e=$(e)||0,B(n)&&(h=!!n.leading,s=(u="maxWait"in n)?Ra($(n.maxWait)||0,e):s,g="trailing"in n?!!n.trailing:g),y.cancel=function(){void 0!==a&&clearTimeout(a),c=0,r=l=i=a=void 0},y.flush=function(){return void 0===a?o:m(Ia())},y}var $a=Object.prototype,Fa=$a.hasOwnProperty,Ga=Ze((function(t,e){t=Object(t);var n=-1,r=e.length,i=r>2?e[2]:void 0;for(i&&en(e[0],e[1],i)&&(r=1);++n<r;)for(var s=e[n],o=Dn(s),a=-1,l=o.length;++a<l;){var c=o[a],h=t[c];(void 0===h||qe(h,$a[c])&&!Fa.call(t,c))&&(t[c]=s[c])}return t}));function Ua(t,e,n){(void 0!==n&&!qe(t[e],n)||void 0===n&&!(e in t))&&Ue(t,e,n)}function qa(t){return v(t)&&tn(t)}function Wa(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}function Ha(t){return Je(t,Dn(t))}function Ja(t,e,n,r,i){t!==e&&Ca(e,(function(s,o){if(i||(i=new Xi),B(s))!function(t,e,n,r,i,s,o){var a=Wa(t,n),l=Wa(e,n),c=o.get(l);if(c)Ua(t,n,c);else{var h=s?s(a,l,n+"",t,e,o):void 0,u=void 0===h;if(u){var g=C(l),d=!g&&yn(l),f=!g&&!d&&Mn(l);h=l,g||d||f?C(a)?h=a:qa(a)?h=_t(a):d?(u=!1,h=es(l,!0)):f?(u=!1,h=js(l,!0)):h=[]:Tr(l)||gn(l)?(h=a,gn(a)?h=Ha(a):B(a)&&!K(a)||(h=Qs(l))):u=!1}u&&(o.set(l,h),i(h,l,r,s,o),o.delete(l)),Ua(t,n,h)}}(t,e,o,n,Ja,r,i);else{var a=r?r(Wa(t,o),s,o+"",t,e,i):void 0;void 0===a&&(a=s),Ua(t,o,a)}}),Dn)}function Xa(t,e,n,r,i,s){return B(t)&&B(e)&&(s.set(e,t),Ja(t,e,void 0,Xa,s),s.delete(e)),t}var Ya=nn((function(t,e,n,r){Ja(t,e,n,r)})),Za=Ze((function(t){return t.push(void 0,Xa),bt(Ya,void 0,t)})),Ka="Expected a function";function Qa(t,e,n){if("function"!=typeof t)throw new TypeError(Ka);return setTimeout((function(){t.apply(void 0,n)}),e)}var tl=Ze((function(t,e){return Qa(t,1,e)})),el=Ze((function(t,e,n){return Qa(t,$(e)||0,n)}));function nl(t,e,n){for(var r=-1,i=null==t?0:t.length;++r<i;)if(n(e,t[r]))return!0;return!1}var rl=200;function il(t,e,n,r){var i=-1,s=te,o=!0,a=t.length,l=[],c=e.length;if(!a)return l;n&&(e=P(e,bn(n))),r?(s=nl,o=!1):e.length>=rl&&(s=So,o=!1,e=new Mo(e));t:for(;++i<a;){var h=t[i],u=null==n?h:n(h);if(h=r||0!==h?h:0,o&&u==u){for(var g=c;g--;)if(e[g]===u)continue t;l.push(h)}else s(e,u,r)||l.push(h)}return l}var sl=Ze((function(t,e){return qa(t)?il(t,vr(e,1,qa,!0)):[]}));function ol(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}var al=Ze((function(t,e){var n=ol(e);return qa(n)&&(n=void 0),qa(t)?il(t,vr(e,1,qa,!0),va(n)):[]})),ll=Ze((function(t,e){var n=ol(e);return qa(n)&&(n=void 0),qa(t)?il(t,vr(e,1,qa,!0),void 0,n):[]})),cl=T((function(t,e){return t/e}),1);function hl(t,e,n,r){for(var i=t.length,s=r?i:-1;(r?s--:++s<i)&&e(t[s],s,t););return n?Vr(t,r?0:s,r?s+1:i):Vr(t,r?s+1:0,r?i:s)}function ul(t){return"function"==typeof t?t:H}function gl(t,e){return(C(t)?Yt:Sa)(t,ul(e))}function dl(t,e){for(var n=null==t?0:t.length;n--&&!1!==e(t[n],n,t););return t}var fl=Pa(!0);function pl(t,e){return t&&fl(t,e,jn)}var ml=Ea(pl,!0);function yl(t,e){return(C(t)?dl:ml)(t,ul(e))}var vl="[object Map]",bl="[object Set]";function xl(t){return function(e){var n=Ms(e);return n==vl?No(e):n==bl?function(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=[t,t]})),n}(e):function(t,e){return P(e,(function(e){return[e,t[e]]}))}(e,t(e))}}var wl=xl(jn),Al=xl(Dn),Pl=oi({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),Cl=/[&<>"']/g,Ml=RegExp(Cl.source);function El(t){return(t=lr(t))&&Ml.test(t)?t.replace(Cl,Pl):t}var Sl=/[\\^$.*+?()[\]{}|]/g,Ol=RegExp(Sl.source);function Tl(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(!e(t[n],n,t))return!1;return!0}function kl(t,e){var n=!0;return Sa(t,(function(t,r,i){return n=!!e(t,r,i)})),n}var Nl=**********;function jl(t){return t?Wi(q(t),0,Nl):0}function _l(t,e){var n=[];return Sa(t,(function(t,r,i){e(t,r,i)&&n.push(t)})),n}function Ll(t){return function(e,n,r){var i=Object(e);if(!tn(e)){var s=va(n);e=jn(e),n=function(t){return s(i[t],t,i)}}var o=t(e,n,r);return o>-1?i[s?e[o]:o]:void 0}}var Bl=Math.max;function Il(t,e,n){var r=null==t?0:t.length;if(!r)return-1;var i=null==n?0:q(n);return i<0&&(i=Bl(r+i,0)),Zt(t,va(e),i)}var Dl=Ll(Il);function Rl(t,e,n){var r;return n(t,(function(t,n,i){if(e(t,n,i))return r=n,!1})),r}var Vl=Math.max,zl=Math.min;function $l(t,e,n){var r=null==t?0:t.length;if(!r)return-1;var i=r-1;return void 0!==n&&(i=q(n),i=n<0?Vl(r+i,0):zl(i,r-1)),Zt(t,va(e),i,!0)}var Fl=Ll($l);function Gl(t){return t&&t.length?t[0]:void 0}function Ul(t,e){var n=-1,r=tn(t)?Array(t.length):[];return Sa(t,(function(t,i,s){r[++n]=e(t,i,s)})),r}function ql(t,e){return(C(t)?P:Ul)(t,va(e))}var Wl=1/0;var Hl=1/0;var Jl=512;var Xl=$i("floor"),Yl="Expected a function",Zl=8,Kl=32,Ql=128,tc=256;function ec(t){return xr((function(e){var n=e.length,r=n,i=jt.prototype.thru;for(t&&e.reverse();r--;){var s=e[r];if("function"!=typeof s)throw new TypeError(Yl);if(i&&!o&&"wrapper"==Nt(s))var o=new jt([],!0)}for(r=o?r:n;++r<n;){var a=Nt(s=e[r]),l="wrapper"==a?Ot(s):void 0;o=l&&Dt(l[0])&&l[1]==(Ql|Zl|Kl|tc)&&!l[4].length&&1==l[9]?o[Nt(l[0])].apply(o,l[3]):1==s.length&&Dt(s)?o[a]():o.thru(s)}return function(){var t=arguments,r=t[0];if(o&&1==t.length&&C(r))return o.plant(r).value();for(var i=0,s=n?e[i].apply(this,t):r;++i<n;)s=e[i].call(this,s);return s}}))}var nc=ec(),rc=ec(!0);function ic(t,e){return ns(e,(function(e){return K(t[e])}))}var sc=Object.prototype.hasOwnProperty,oc=Ta((function(t,e,n){sc.call(t,n)?t[n].push(e):Ue(t,n,[e])}));function ac(t,e){return t>e}function lc(t){return function(e,n){return"string"==typeof e&&"string"==typeof n||(e=$(e),n=$(n)),t(e,n)}}var cc=lc(ac),hc=lc((function(t,e){return t>=e})),uc=Object.prototype.hasOwnProperty;function gc(t,e){return null!=t&&uc.call(t,e)}function dc(t,e){return null!=t&&ua(t,e,gc)}var fc=Math.max,pc=Math.min;var mc="[object String]";function yc(t){return"string"==typeof t||!C(t)&&v(t)&&y(t)==mc}function vc(t,e){return P(e,(function(e){return t[e]}))}function bc(t){return null==t?[]:vc(t,jn(t))}var xc=Math.max;var wc=Math.max;var Ac=Math.min;function Pc(t,e,n){for(var r=n?nl:te,i=t[0].length,s=t.length,o=s,a=Array(s),l=1/0,c=[];o--;){var h=t[o];o&&e&&(h=P(h,bn(e))),l=Ac(h.length,l),a[o]=!n&&(e||i>=120&&h.length>=120)?new Mo(o&&h):void 0}h=t[0];var u=-1,g=a[0];t:for(;++u<i&&c.length<l;){var d=h[u],f=e?e(d):d;if(d=n||0!==d?d:0,!(g?So(g,f):r(c,f,n))){for(o=s;--o;){var p=a[o];if(!(p?So(p,f):r(t[o],f,n)))continue t}g&&g.push(f),c.push(d)}}return c}function Cc(t){return qa(t)?t:[]}var Mc=Ze((function(t){var e=P(t,Cc);return e.length&&e[0]===t[0]?Pc(e):[]})),Ec=Ze((function(t){var e=ol(t),n=P(t,Cc);return e===ol(n)?e=void 0:n.pop(),n.length&&n[0]===t[0]?Pc(n,va(e)):[]})),Sc=Ze((function(t){var e=ol(t),n=P(t,Cc);return(e="function"==typeof e?e:void 0)&&n.pop(),n.length&&n[0]===t[0]?Pc(n,void 0,e):[]}));function Oc(t,e){return function(n,r){return function(t,e,n,r){return Ma(t,(function(t,i,s){e(r,n(t),i,s)})),r}(n,t,e(r),{})}}var Tc=Object.prototype.toString,kc=Oc((function(t,e,n){null!=e&&"function"!=typeof e.toString&&(e=Tc.call(e)),t[e]=n}),Wt(H)),Nc=Object.prototype,jc=Nc.hasOwnProperty,_c=Nc.toString,Lc=Oc((function(t,e,n){null!=e&&"function"!=typeof e.toString&&(e=_c.call(e)),jc.call(t,e)?t[e].push(n):t[e]=[n]}),va);function Bc(t,e){return e.length<2?t:gr(t,Vr(e,0,-1))}function Ic(t,e,n){var r=null==(t=Bc(t,e=cr(e,t)))?t:t[ur(ol(e))];return null==r?void 0:bt(r,t,n)}var Dc=Ze(Ic),Rc=Ze((function(t,e,n){var r=-1,i="function"==typeof e,s=tn(t)?Array(t.length):[];return Sa(t,(function(t){s[++r]=i?bt(e,t,n):Ic(t,e,n)})),s})),Vc="[object ArrayBuffer]";var zc=Pn&&Pn.isArrayBuffer,$c=zc?bn(zc):function(t){return v(t)&&y(t)==Vc},Fc="[object Boolean]";var Gc="[object Date]";var Uc=Pn&&Pn.isDate,qc=Uc?bn(Uc):function(t){return v(t)&&y(t)==Gc};var Wc="[object Map]",Hc="[object Set]",Jc=Object.prototype.hasOwnProperty;function Xc(t){if(null==t)return!0;if(tn(t)&&(C(t)||"string"==typeof t||"function"==typeof t.splice||yn(t)||Mn(t)||gn(t)))return!t.length;var e=Ms(t);if(e==Wc||e==Hc)return!t.size;if(sn(t))return!Nn(t).length;for(var n in t)if(Jc.call(t,n))return!1;return!0}function Yc(t,e){return na(t,e)}var Zc=a.isFinite;function Kc(t){return"number"==typeof t&&t==q(t)}var Qc="[object Number]";function th(t){return"number"==typeof t||v(t)&&y(t)==Qc}var eh=tt?K:dn,nh="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.";var rh="[object RegExp]";var ih=Pn&&Pn.isRegExp,sh=ih?bn(ih):function(t){return v(t)&&y(t)==rh},oh=9007199254740991;var ah="[object WeakMap]";var lh="[object WeakSet]";var ch=1;var hh=Array.prototype.join;var uh=Di((function(t,e,n){return t+(n?"-":"")+e.toLowerCase()})),gh=Ta((function(t,e,n){Ue(t,n,e)}));var dh=Math.max,fh=Math.min;var ph=Di((function(t,e,n){return t+(n?" ":"")+e.toLowerCase()})),mh=ni("toLowerCase");function yh(t,e){return t<e}var vh=lc(yh),bh=lc((function(t,e){return t<=e}));var xh=1;var wh=1;function Ah(t,e,n){for(var r=-1,i=t.length;++r<i;){var s=t[r],o=e(s);if(null!=o&&(void 0===a?o==o&&!x(o):n(o,a)))var a=o,l=s}return l}function Ph(t){return t&&t.length?Ah(t,H,ac):void 0}function Ch(t,e){for(var n,r=-1,i=t.length;++r<i;){var s=e(t[r]);void 0!==s&&(n=void 0===n?s:n+s)}return n}var Mh=NaN;function Eh(t,e){var n=null==t?0:t.length;return n?Ch(t,e)/n:Mh}var Sh=nn((function(t,e,n){Ja(t,e,n)})),Oh=Ze((function(t,e){return function(n){return Ic(n,t,e)}})),Th=Ze((function(t,e){return function(n){return Ic(t,n,e)}}));function kh(t,e,n){var r=jn(e),i=ic(e,r),s=!(B(n)&&"chain"in n&&!n.chain),o=K(t);return Yt(i,(function(n){var r=e[n];t[n]=r,o&&(t.prototype[n]=function(){var e=this.__chain__;if(s||e){var n=t(this.__wrapped__);return(n.__actions__=_t(this.__actions__)).push({func:r,args:arguments,thisArg:t}),n.__chain__=e,n}return r.apply(t,pr([this.value()],arguments))})})),t}var Nh=T((function(t,e){return t*e}),1),jh="Expected a function";function _h(t){if("function"!=typeof t)throw new TypeError(jh);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}var Lh="[object Map]",Bh="[object Set]",Ih=l?l.iterator:void 0;function Dh(t){if(!t)return[];if(tn(t))return yc(t)?ei(t):_t(t);if(Ih&&t[Ih])return function(t){for(var e,n=[];!(e=t.next()).done;)n.push(e.value);return n}(t[Ih]());var e=Ms(t);return(e==Lh?No:e==Bh?jo:bc)(t)}function Rh(t,e){var n=t.length;if(n)return de(e+=e<0?n:0,n)?t[e]:void 0}function Vh(t,e){return null==(t=Bc(t,e=cr(e,t)))||delete t[ur(ol(e))]}function zh(t){return Tr(t)?void 0:t}var $h=xr((function(t,e){var n={};if(null==t)return n;var r=!1;e=P(e,(function(e){return e=cr(e,t),r||(r=e.length>1),e})),Je(t,hs(t),n),r&&(n=po(n,7,zh));for(var i=e.length;i--;)Vh(n,e[i]);return n}));function Fh(t,e,n,r){if(!B(t))return t;for(var i=-1,s=(e=cr(e,t)).length,o=s-1,a=t;null!=a&&++i<s;){var l=ur(e[i]),c=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return t;if(i!=o){var h=a[l];void 0===(c=r?r(h,l,a):void 0)&&(c=B(h)?h:de(e[i+1])?[]:{})}He(a,l,c),a=a[l]}return t}function Gh(t,e,n){for(var r=-1,i=e.length,s={};++r<i;){var o=e[r],a=gr(t,o);n(a,o)&&Fh(s,cr(o,t),a)}return s}function Uh(t,e){if(null==t)return{};var n=P(hs(t),(function(t){return[t]}));return e=va(e),Gh(t,n,(function(t,n){return e(t,n[0])}))}function qh(t,e){if(t!==e){var n=void 0!==t,r=null===t,i=t==t,s=x(t),o=void 0!==e,a=null===e,l=e==e,c=x(e);if(!a&&!c&&!s&&t>e||s&&o&&l&&!a&&!c||r&&o&&l||!n&&l||!i)return 1;if(!r&&!s&&!c&&t<e||c&&n&&i&&!r&&!s||a&&n&&i||!o&&i||!l)return-1}return 0}function Wh(t,e,n){e=e.length?P(e,(function(t){return C(t)?function(e){return gr(e,1===t.length?t[0]:t)}:t})):[H];var r=-1;e=P(e,bn(va));var i=Ul(t,(function(t,n,i){var s=P(e,(function(e){return e(t)}));return{criteria:s,index:++r,value:t}}));return function(t,e){var n=t.length;for(t.sort(e);n--;)t[n]=t[n].value;return t}(i,(function(t,e){return function(t,e,n){for(var r=-1,i=t.criteria,s=e.criteria,o=i.length,a=n.length;++r<o;){var l=qh(i[r],s[r]);if(l)return r>=a?l:l*("desc"==n[r]?-1:1)}return t.index-e.index}(t,e,n)}))}function Hh(t){return xr((function(e){return e=P(e,bn(va)),Ze((function(n){var r=this;return t(e,(function(t){return bt(t,r,n)}))}))}))}var Jh=Hh(P),Xh=Ze,Yh=Math.min,Zh=Xh((function(t,e){var n=(e=1==e.length&&C(e[0])?P(e[0],bn(va)):P(vr(e,1),bn(va))).length;return Ze((function(r){for(var i=-1,s=Yh(r.length,n);++i<s;)r[i]=e[i].call(this,r[i]);return bt(t,this,r)}))})),Kh=Hh(Tl),Qh=Hh(Eo),tu=9007199254740991,eu=Math.floor;function nu(t,e){var n="";if(!t||e<1||e>tu)return n;do{e%2&&(n+=t),(e=eu(e/2))&&(t+=t)}while(e);return n}var ru=ma("length"),iu="\\ud800-\\udfff",su="["+iu+"]",ou="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",au="\\ud83c[\\udffb-\\udfff]",lu="[^"+iu+"]",cu="(?:\\ud83c[\\udde6-\\uddff]){2}",hu="[\\ud800-\\udbff][\\udc00-\\udfff]",uu="(?:"+ou+"|"+au+")"+"?",gu="[\\ufe0e\\ufe0f]?",du=gu+uu+("(?:\\u200d(?:"+[lu,cu,hu].join("|")+")"+gu+uu+")*"),fu="(?:"+[lu+ou+"?",ou,cu,hu,su].join("|")+")",pu=RegExp(au+"(?="+au+")|"+fu+du,"g");function mu(t){return Fr(t)?function(t){for(var e=pu.lastIndex=0;pu.test(t);)++e;return e}(t):ru(t)}var yu=Math.ceil;function vu(t,e){var n=(e=void 0===e?" ":O(e)).length;if(n<2)return n?nu(e,t):e;var r=nu(e,yu(t/mu(e)));return Fr(e)?zr(ei(r),0,t).join(""):r.slice(0,t)}var bu=Math.ceil,xu=Math.floor;var wu=/^\s+/,Au=a.parseInt;var Pu=Ze((function(t,e){return $e(t,32,void 0,e,me(e,he(Pu)))}));Pu.placeholder={};var Cu=Ze((function(t,e){return $e(t,64,void 0,e,me(e,he(Cu)))}));Cu.placeholder={};var Mu=Ta((function(t,e,n){t[n?0:1].push(e)}),(function(){return[[],[]]}));var Eu=xr((function(t,e){return null==t?{}:function(t,e){return Gh(t,e,(function(e,n){return ga(t,n)}))}(t,e)}));function Su(t,e,n,r){for(var i=n-1,s=t.length;++i<s;)if(r(t[i],e))return i;return-1}var Ou=Array.prototype.splice;function Tu(t,e,n,r){var i=r?Su:Qt,s=-1,o=e.length,a=t;for(t===e&&(e=_t(e)),n&&(a=P(t,bn(n)));++s<o;)for(var l=0,c=e[s],h=n?n(c):c;(l=i(a,h,l,r))>-1;)a!==t&&Ou.call(a,l,1),Ou.call(t,l,1);return t}function ku(t,e){return t&&t.length&&e&&e.length?Tu(t,e):t}var Nu=Ze(ku);var ju=Array.prototype.splice;function _u(t,e){for(var n=t?e.length:0,r=n-1;n--;){var i=e[n];if(n==r||i!==s){var s=i;de(i)?ju.call(t,i,1):Vh(t,i)}}return t}var Lu=xr((function(t,e){var n=null==t?0:t.length,r=fr(t,e);return _u(t,P(e,(function(t){return de(t,n)?+t:t})).sort(qh)),r})),Bu=Math.floor,Iu=Math.random;function Du(t,e){return t+Bu(Iu()*(e-t+1))}var Ru=parseFloat,Vu=Math.min,zu=Math.random;var $u=Math.ceil,Fu=Math.max;function Gu(t){return function(e,n,r){return r&&"number"!=typeof r&&en(e,n,r)&&(n=r=void 0),e=U(e),void 0===n?(n=e,e=0):n=U(n),function(t,e,n,r){for(var i=-1,s=Fu($u((e-t)/(n||1)),0),o=Array(s);s--;)o[r?s:++i]=t,t+=n;return o}(e,n,r=void 0===r?e<n?1:-1:U(r),t)}}var Uu=Gu(),qu=Gu(!0),Wu=xr((function(t,e){return $e(t,256,void 0,void 0,void 0,e)}));function Hu(t,e,n,r,i){return i(t,(function(t,i,s){n=r?(r=!1,t):e(n,t,i,s)})),n}function Ju(t,e,n,r){var i=null==t?0:t.length;for(r&&i&&(n=t[--i]);i--;)n=e(n,t[i],i,t);return n}var Xu="Expected a function";var Yu=Array.prototype.reverse;function Zu(t){return null==t?t:Yu.call(t)}var Ku=$i("round");function Qu(t){var e=t.length;return e?t[Du(0,e-1)]:void 0}function tg(t){return Qu(bc(t))}function eg(t,e){var n=-1,r=t.length,i=r-1;for(e=void 0===e?r:e;++n<e;){var s=Du(n,i),o=t[s];t[s]=t[n],t[n]=o}return t.length=e,t}function ng(t,e){return eg(_t(t),Wi(e,0,t.length))}function rg(t,e){var n=bc(t);return eg(n,Wi(e,0,n.length))}function ig(t){return eg(_t(t))}function sg(t){return eg(bc(t))}var og="[object Map]",ag="[object Set]";var lg=Di((function(t,e,n){return t+(n?"_":"")+e.toLowerCase()}));function cg(t,e){var n;return Sa(t,(function(t,r,i){return!(n=e(t,r,i))})),!!n}var hg=Ze((function(t,e){if(null==t)return[];var n=e.length;return n>1&&en(t,e[0],e[1])?e=[]:n>2&&en(e[0],e[1],e[2])&&(e=[e[0]]),Wh(t,vr(e,1),[])})),ug=4294967294,gg=Math.floor,dg=Math.min;function fg(t,e,n,r){var i=0,s=null==t?0:t.length;if(0===s)return 0;for(var o=(e=n(e))!=e,a=null===e,l=x(e),c=void 0===e;i<s;){var h=gg((i+s)/2),u=n(t[h]),g=void 0!==u,d=null===u,f=u==u,p=x(u);if(o)var m=r||f;else m=c?f&&(r||g):a?f&&g&&(r||!d):l?f&&g&&!d&&(r||!p):!d&&!p&&(r?u<=e:u<e);m?i=h+1:s=h}return dg(s,ug)}var pg=2147483647;function mg(t,e,n){var r=0,i=null==t?r:t.length;if("number"==typeof e&&e==e&&i<=pg){for(;r<i;){var s=r+i>>>1,o=t[s];null!==o&&!x(o)&&(n?o<=e:o<e)?r=s+1:i=s}return i}return fg(t,e,H,n)}function yg(t,e){return mg(t,e)}function vg(t,e,n){return fg(t,e,va(n))}function bg(t,e){for(var n=-1,r=t.length,i=0,s=[];++n<r;){var o=t[n],a=e?e(o):o;if(!n||!qe(a,l)){var l=a;s[i++]=0===o?0:o}}return s}var xg=**********;var wg="Expected a function",Ag=Math.max;var Pg=Di((function(t,e,n){return t+(n?" ":"")+ri(e)}));var Cg=T((function(t,e){return t-e}),0);var Mg=Object.prototype,Eg=Mg.hasOwnProperty;function Sg(t,e,n,r){return void 0===t||qe(t,Mg[n])&&!Eg.call(r,n)?e:t}var Og={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"};function Tg(t){return"\\"+Og[t]}var kg=/<%=([\s\S]+?)%>/g,Ng={escape:/<%-([\s\S]+?)%>/g,evaluate:/<%([\s\S]+?)%>/g,interpolate:kg,variable:"",imports:{_:{escape:El}}},jg="Invalid `variable` option passed into `_.template`",_g=/\b__p \+= '';/g,Lg=/\b(__p \+=) '' \+/g,Bg=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Ig=/[()=,{}\[\]\/\s]/,Dg=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Rg=/($^)/,Vg=/['\n\r\u2028\u2029\\]/g,zg=Object.prototype.hasOwnProperty;var $g="Expected a function";function Fg(t,e,n){var r=!0,i=!0;if("function"!=typeof t)throw new TypeError($g);return B(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),za(t,e,{leading:r,maxWait:e,trailing:i})}function Gg(t,e){return e(t)}var Ug=9007199254740991,qg=**********,Wg=Math.min;function Hg(t,e){var n=t;return n instanceof Et&&(n=n.value()),si(e,(function(t,e){return e.func.apply(e.thisArg,pr([t],e.args))}),n)}function Jg(){return Hg(this.__wrapped__,this.__actions__)}var Xg=9007199254740991;function Yg(t,e){for(var n=t.length;n--&&Qt(e,t[n],0)>-1;);return n}function Zg(t,e){for(var n=-1,r=t.length;++n<r&&Qt(e,t[n],0)>-1;);return n}var Kg=/^\s+/;var Qg=30,td="...",ed=/\w*$/;var nd=oi({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),rd=/&(?:amp|lt|gt|quot|#39);/g,id=RegExp(rd.source);var sd=ds&&1/jo(new ds([,-0]))[1]==1/0?function(t){return new ds(t)}:St,od=200;function ad(t,e,n){var r=-1,i=te,s=t.length,o=!0,a=[],l=a;if(n)o=!1,i=nl;else if(s>=od){var c=e?null:sd(t);if(c)return jo(c);o=!1,i=So,l=new Mo}else l=e?[]:a;t:for(;++r<s;){var h=t[r],u=e?e(h):h;if(h=n||0!==h?h:0,o&&u==u){for(var g=l.length;g--;)if(l[g]===u)continue t;e&&l.push(u),a.push(h)}else i(l,u,n)||(l!==a&&l.push(u),a.push(h))}return a}var ld=Ze((function(t){return ad(vr(t,1,qa,!0))})),cd=Ze((function(t){var e=ol(t);return qa(e)&&(e=void 0),ad(vr(t,1,qa,!0),va(e))})),hd=Ze((function(t){var e=ol(t);return e="function"==typeof e?e:void 0,ad(vr(t,1,qa,!0),void 0,e)}));function ud(t){return t&&t.length?ad(t):[]}var gd=0;function dd(t){var e=++gd;return lr(t)+e}var fd=Math.max;function pd(t){if(!t||!t.length)return[];var e=0;return t=ns(t,(function(t){if(qa(t))return e=fd(t.length,e),!0})),on(e,(function(e){return P(t,ma(e))}))}function md(t,e){if(!t||!t.length)return[];var n=pd(t);return null==e?n:P(n,(function(t){return bt(e,void 0,t)}))}function yd(t,e,n,r){return Fh(t,e,n(gr(t,e)),r)}var vd=Di((function(t,e,n){return t+(n?" ":"")+e.toUpperCase()}));var bd=Ze((function(t,e){return qa(t)?il(t,e):[]}));var xd=xr((function(t){var e=t.length,n=e?t[0]:0,r=this.__wrapped__,i=function(e){return fr(e,t)};return!(e>1||this.__actions__.length)&&r instanceof Et&&de(n)?((r=r.slice(n,+n+(e?1:0))).__actions__.push({func:Gg,args:[i],thisArg:void 0}),new jt(r,this.__chain__).thru((function(t){return e&&!t.length&&t.push(void 0),t}))):this.thru(i)}));function wd(t,e,n){var r=t.length;if(r<2)return r?ad(t[0]):[];for(var i=-1,s=Array(r);++i<r;)for(var o=t[i],a=-1;++a<r;)a!=i&&(s[i]=il(s[i]||o,t[a],e,n));return ad(vr(s,1),e,n)}var Ad=Ze((function(t){return wd(ns(t,qa))})),Pd=Ze((function(t){var e=ol(t);return qa(e)&&(e=void 0),wd(ns(t,qa),va(e))})),Cd=Ze((function(t){var e=ol(t);return e="function"==typeof e?e:void 0,wd(ns(t,qa),void 0,e)})),Md=Ze(pd);function Ed(t,e,n){for(var r=-1,i=t.length,s=e.length,o={};++r<i;){var a=r<s?e[r]:void 0;n(o,t[r],a)}return o}var Sd=Ze((function(t){var e=t.length,n=e>1?t[e-1]:void 0;return n="function"==typeof n?(t.pop(),n):void 0,md(t,n)})),Od={chunk:function(t,e,n){e=(n?en(t,e,n):void 0===e)?1:qi(q(e),0);var r=null==t?0:t.length;if(!r||e<1)return[];for(var i=0,s=0,o=Array(Ui(r/e));i<r;)o[s++]=Vr(t,i,i+=e);return o},compact:function(t){for(var e=-1,n=null==t?0:t.length,r=0,i=[];++e<n;){var s=t[e];s&&(i[r++]=s)}return i},concat:function(){var t=arguments.length;if(!t)return[];for(var e=Array(t-1),n=arguments[0],r=t;r--;)e[r-1]=arguments[r];return pr(C(n)?_t(n):[n],vr(e,1))},difference:sl,differenceBy:al,differenceWith:ll,drop:function(t,e,n){var r=null==t?0:t.length;return r?Vr(t,(e=n||void 0===e?1:q(e))<0?0:e,r):[]},dropRight:function(t,e,n){var r=null==t?0:t.length;return r?Vr(t,0,(e=r-(e=n||void 0===e?1:q(e)))<0?0:e):[]},dropRightWhile:function(t,e){return t&&t.length?hl(t,va(e),!0,!0):[]},dropWhile:function(t,e){return t&&t.length?hl(t,va(e),!0):[]},fill:function(t,e,n,r){var i=null==t?0:t.length;return i?(n&&"number"!=typeof n&&en(t,e,n)&&(n=0,r=i),function(t,e,n,r){var i=t.length;for((n=q(n))<0&&(n=-n>i?0:i+n),(r=void 0===r||r>i?i:q(r))<0&&(r+=i),r=n>r?0:jl(r);n<r;)t[n++]=e;return t}(t,e,n,r)):[]},findIndex:Il,findLastIndex:$l,first:Gl,flatten:br,flattenDeep:function(t){return(null==t?0:t.length)?vr(t,Hl):[]},flattenDepth:function(t,e){return(null==t?0:t.length)?vr(t,e=void 0===e?1:q(e)):[]},fromPairs:function(t){for(var e=-1,n=null==t?0:t.length,r={};++e<n;){var i=t[e];r[i[0]]=i[1]}return r},head:Gl,indexOf:function(t,e,n){var r=null==t?0:t.length;if(!r)return-1;var i=null==n?0:q(n);return i<0&&(i=wc(r+i,0)),Qt(t,e,i)},initial:function(t){return(null==t?0:t.length)?Vr(t,0,-1):[]},intersection:Mc,intersectionBy:Ec,intersectionWith:Sc,join:function(t,e){return null==t?"":hh.call(t,e)},last:ol,lastIndexOf:function(t,e,n){var r=null==t?0:t.length;if(!r)return-1;var i=r;return void 0!==n&&(i=(i=q(n))<0?dh(r+i,0):fh(i,r-1)),e==e?function(t,e,n){for(var r=n+1;r--;)if(t[r]===e)return r;return r}(t,e,i):Zt(t,Kt,i,!0)},nth:function(t,e){return t&&t.length?Rh(t,q(e)):void 0},pull:Nu,pullAll:ku,pullAllBy:function(t,e,n){return t&&t.length&&e&&e.length?Tu(t,e,va(n)):t},pullAllWith:function(t,e,n){return t&&t.length&&e&&e.length?Tu(t,e,void 0,n):t},pullAt:Lu,remove:function(t,e){var n=[];if(!t||!t.length)return n;var r=-1,i=[],s=t.length;for(e=va(e);++r<s;){var o=t[r];e(o,r,t)&&(n.push(o),i.push(r))}return _u(t,i),n},reverse:Zu,slice:function(t,e,n){var r=null==t?0:t.length;return r?(n&&"number"!=typeof n&&en(t,e,n)?(e=0,n=r):(e=null==e?0:q(e),n=void 0===n?r:q(n)),Vr(t,e,n)):[]},sortedIndex:yg,sortedIndexBy:vg,sortedIndexOf:function(t,e){var n=null==t?0:t.length;if(n){var r=mg(t,e);if(r<n&&qe(t[r],e))return r}return-1},sortedLastIndex:function(t,e){return mg(t,e,!0)},sortedLastIndexBy:function(t,e,n){return fg(t,e,va(n),!0)},sortedLastIndexOf:function(t,e){if(null==t?0:t.length){var n=mg(t,e,!0)-1;if(qe(t[n],e))return n}return-1},sortedUniq:function(t){return t&&t.length?bg(t):[]},sortedUniqBy:function(t,e){return t&&t.length?bg(t,va(e)):[]},tail:function(t){var e=null==t?0:t.length;return e?Vr(t,1,e):[]},take:function(t,e,n){return t&&t.length?Vr(t,0,(e=n||void 0===e?1:q(e))<0?0:e):[]},takeRight:function(t,e,n){var r=null==t?0:t.length;return r?Vr(t,(e=r-(e=n||void 0===e?1:q(e)))<0?0:e,r):[]},takeRightWhile:function(t,e){return t&&t.length?hl(t,va(e),!1,!0):[]},takeWhile:function(t,e){return t&&t.length?hl(t,va(e)):[]},union:ld,unionBy:cd,unionWith:hd,uniq:ud,uniqBy:function(t,e){return t&&t.length?ad(t,va(e)):[]},uniqWith:function(t,e){return e="function"==typeof e?e:void 0,t&&t.length?ad(t,void 0,e):[]},unzip:pd,unzipWith:md,without:bd,xor:Ad,xorBy:Pd,xorWith:Cd,zip:Md,zipObject:function(t,e){return Ed(t||[],e||[],He)},zipObjectDeep:function(t,e){return Ed(t||[],e||[],Fh)},zipWith:Sd},Td={countBy:Na,each:gl,eachRight:yl,every:function(t,e,n){var r=C(t)?Tl:kl;return n&&en(t,e,n)&&(e=void 0),r(t,va(e))},filter:function(t,e){return(C(t)?ns:_l)(t,va(e))},find:Dl,findLast:Fl,flatMap:function(t,e){return vr(ql(t,e),1)},flatMapDeep:function(t,e){return vr(ql(t,e),Wl)},flatMapDepth:function(t,e,n){return n=void 0===n?1:q(n),vr(ql(t,e),n)},forEach:gl,forEachRight:yl,groupBy:oc,includes:function(t,e,n,r){t=tn(t)?t:bc(t),n=n&&!r?q(n):0;var i=t.length;return n<0&&(n=xc(i+n,0)),yc(t)?n<=i&&t.indexOf(e,n)>-1:!!i&&Qt(t,e,n)>-1},invokeMap:Rc,keyBy:gh,map:ql,orderBy:function(t,e,n,r){return null==t?[]:(C(e)||(e=null==e?[]:[e]),C(n=r?void 0:n)||(n=null==n?[]:[n]),Wh(t,e,n))},partition:Mu,reduce:function(t,e,n){var r=C(t)?si:Hu,i=arguments.length<3;return r(t,va(e),n,i,Sa)},reduceRight:function(t,e,n){var r=C(t)?Ju:Hu,i=arguments.length<3;return r(t,va(e),n,i,ml)},reject:function(t,e){return(C(t)?ns:_l)(t,_h(va(e)))},sample:function(t){return(C(t)?Qu:tg)(t)},sampleSize:function(t,e,n){return e=(n?en(t,e,n):void 0===e)?1:q(e),(C(t)?ng:rg)(t,e)},shuffle:function(t){return(C(t)?ig:sg)(t)},size:function(t){if(null==t)return 0;if(tn(t))return yc(t)?mu(t):t.length;var e=Ms(t);return e==og||e==ag?t.size:Nn(t).length},some:function(t,e,n){var r=C(t)?Eo:cg;return n&&en(t,e,n)&&(e=void 0),r(t,va(e))},sortBy:hg},kd={now:Ia},Nd={after:function(t,e){if("function"!=typeof e)throw new TypeError(W);return t=q(t),function(){if(--t<1)return e.apply(this,arguments)}},ary:Ge,before:Br,bind:Ir,bindKey:Rr,curry:_a,curryRight:Ba,debounce:za,defer:tl,delay:el,flip:function(t){return $e(t,Jl)},memoize:rr,negate:_h,once:function(t){return Br(2,t)},overArgs:Zh,partial:Pu,partialRight:Cu,rearg:Wu,rest:function(t,e){if("function"!=typeof t)throw new TypeError(Xu);return Ze(t,e=void 0===e?e:q(e))},spread:function(t,e){if("function"!=typeof t)throw new TypeError(wg);return e=null==e?0:Ag(q(e),0),Ze((function(n){var r=n[e],i=zr(n,0,e);return r&&pr(i,r),bt(t,this,i)}))},throttle:Fg,unary:function(t){return Ge(t,1)},wrap:function(t,e){return Pu(ul(e),t)}},jd={castArray:function(){if(!arguments.length)return[];var t=arguments[0];return C(t)?t:[t]},clone:yo,cloneDeep:xo,cloneDeepWith:function(t,e){return po(t,wo|Ao,e="function"==typeof e?e:void 0)},cloneWith:function(t,e){return po(t,Po,e="function"==typeof e?e:void 0)},conformsTo:function(t,e){return null==e||xa(t,e,jn(e))},eq:qe,gt:cc,gte:hc,isArguments:gn,isArray:C,isArrayBuffer:$c,isArrayLike:tn,isArrayLikeObject:qa,isBoolean:function(t){return!0===t||!1===t||v(t)&&y(t)==Fc},isBuffer:yn,isDate:qc,isElement:function(t){return v(t)&&1===t.nodeType&&!Tr(t)},isEmpty:Xc,isEqual:Yc,isEqualWith:function(t,e,n){var r=(n="function"==typeof n?n:void 0)?n(t,e):void 0;return void 0===r?na(t,e,void 0,n):!!r},isError:jr,isFinite:function(t){return"number"==typeof t&&Zc(t)},isFunction:K,isInteger:Kc,isLength:Qe,isMap:no,isMatch:function(t,e){return t===e||sa(t,e,aa(e))},isMatchWith:function(t,e,n){return n="function"==typeof n?n:void 0,sa(t,e,aa(e),n)},isNaN:function(t){return th(t)&&t!=+t},isNative:function(t){if(eh(t))throw new Error(nh);return ht(t)},isNil:function(t){return null==t},isNull:function(t){return null===t},isNumber:th,isObject:B,isObjectLike:v,isPlainObject:Tr,isRegExp:sh,isSafeInteger:function(t){return Kc(t)&&t>=-oh&&t<=oh},isSet:so,isString:yc,isSymbol:x,isTypedArray:Mn,isUndefined:function(t){return void 0===t},isWeakMap:function(t){return v(t)&&Ms(t)==ah},isWeakSet:function(t){return v(t)&&y(t)==lh},lt:vh,lte:bh,toArray:Dh,toFinite:U,toInteger:q,toLength:jl,toNumber:$,toPlainObject:Ha,toSafeInteger:function(t){return t?Wi(q(t),-Xg,Xg):0===t?t:0},toString:lr},_d={add:k,ceil:Fi,divide:cl,floor:Xl,max:Ph,maxBy:function(t,e){return t&&t.length?Ah(t,va(e),ac):void 0},mean:function(t){return Eh(t,H)},meanBy:function(t,e){return Eh(t,va(e))},min:function(t){return t&&t.length?Ah(t,H,yh):void 0},minBy:function(t,e){return t&&t.length?Ah(t,va(e),yh):void 0},multiply:Nh,round:Ku,subtract:Cg,sum:function(t){return t&&t.length?Ch(t,H):0},sumBy:function(t,e){return t&&t.length?Ch(t,va(e)):0}},Ld=Hi,Bd=function(t,e,n){return e=U(e),void 0===n?(n=e,e=0):n=U(n),function(t,e,n){return t>=pc(e,n)&&t<fc(e,n)}(t=$(t),e,n)},Id=function(t,e,n){if(n&&"boolean"!=typeof n&&en(t,e,n)&&(e=n=void 0),void 0===n&&("boolean"==typeof e?(n=e,e=void 0):"boolean"==typeof t&&(n=t,t=void 0)),void 0===t&&void 0===e?(t=0,e=1):(t=U(t),void 0===e?(e=t,t=0):e=U(e)),t>e){var r=t;t=e,e=r}if(n||t%1||e%1){var i=zu();return Vu(t+i*(e-t+Ru("1e-"+((i+"").length-1))),e)}return Du(t,e)},Dd={assign:Ln,assignIn:Rn,assignInWith:Vn,assignWith:zn,at:wr,create:function(t,e){var n=mt(t);return null==e?n:Yi(n,e)},defaults:Ga,defaultsDeep:Za,entries:wl,entriesIn:Al,extend:Rn,extendWith:Vn,findKey:function(t,e){return Rl(t,va(e),Ma)},findLastKey:function(t,e){return Rl(t,va(e),pl)},forIn:function(t,e){return null==t?t:Ca(t,ul(e),Dn)},forInRight:function(t,e){return null==t?t:fl(t,ul(e),Dn)},forOwn:function(t,e){return t&&Ma(t,ul(e))},forOwnRight:function(t,e){return t&&pl(t,ul(e))},functions:function(t){return null==t?[]:ic(t,jn(t))},functionsIn:function(t){return null==t?[]:ic(t,Dn(t))},get:dr,has:dc,hasIn:ga,invert:kc,invertBy:Lc,invoke:Dc,keys:jn,keysIn:Dn,mapKeys:function(t,e){var n={};return e=va(e),Ma(t,(function(t,r,i){Ue(n,e(t,r,i),t)})),n},mapValues:function(t,e){var n={};return e=va(e),Ma(t,(function(t,r,i){Ue(n,r,e(t,r,i))})),n},merge:Sh,mergeWith:Ya,omit:$h,omitBy:function(t,e){return Uh(t,_h(va(e)))},pick:Eu,pickBy:Uh,result:function(t,e,n){var r=-1,i=(e=cr(e,t)).length;for(i||(i=1,t=void 0);++r<i;){var s=null==t?void 0:t[ur(e[r])];void 0===s&&(r=i,s=n),t=K(s)?s.call(t):s}return t},set:function(t,e,n){return null==t?t:Fh(t,e,n)},setWith:function(t,e,n,r){return r="function"==typeof r?r:void 0,null==t?t:Fh(t,e,n,r)},toPairs:wl,toPairsIn:Al,transform:function(t,e,n){var r=C(t),i=r||yn(t)||Mn(t);if(e=va(e),null==n){var s=t&&t.constructor;n=i?r?new s:[]:B(t)&&K(s)?mt(Ar(t)):{}}return(i?Yt:Ma)(t,(function(t,r,i){return e(n,t,r,i)})),n},unset:function(t,e){return null==t||Vh(t,e)},update:function(t,e,n){return null==t?t:yd(t,e,ul(n))},updateWith:function(t,e,n,r){return r="function"==typeof r?r:void 0,null==t?t:yd(t,e,ul(n),r)},values:bc,valuesIn:function(t){return null==t?[]:vc(t,Dn(t))}},Rd={at:xd,chain:Gi,commit:function(){return new jt(this.value(),this.__chain__)},lodash:It,next:function(){void 0===this.__values__&&(this.__values__=Dh(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?void 0:this.__values__[this.__index__++]}},plant:function(t){for(var e,n=this;n instanceof Ct;){var r=Lt(n);r.__index__=0,r.__values__=void 0,e?i.__wrapped__=r:e=r;var i=r;n=n.__wrapped__}return i.__wrapped__=t,e},reverse:function(){var t=this.__wrapped__;if(t instanceof Et){var e=t;return this.__actions__.length&&(e=new Et(this)),(e=e.reverse()).__actions__.push({func:Gg,args:[Zu],thisArg:void 0}),new jt(e,this.__chain__)}return this.thru(Zu)},tap:function(t,e){return e(t),t},thru:Gg,toIterator:function(){return this},toJSON:Jg,value:Jg,valueOf:Jg,wrapperChain:function(){return Gi(this)}},Vd={camelCase:Ri,capitalize:ii,deburr:hi,endsWith:function(t,e,n){t=lr(t),e=O(e);var r=t.length,i=n=void 0===n?r:Wi(q(n),0,r);return(n-=e.length)>=0&&t.slice(n,i)==e},escape:El,escapeRegExp:function(t){return(t=lr(t))&&Ol.test(t)?t.replace(Sl,"\\$&"):t},kebabCase:uh,lowerCase:ph,lowerFirst:mh,pad:function(t,e,n){t=lr(t);var r=(e=q(e))?mu(t):0;if(!e||r>=e)return t;var i=(e-r)/2;return vu(xu(i),n)+t+vu(bu(i),n)},padEnd:function(t,e,n){t=lr(t);var r=(e=q(e))?mu(t):0;return e&&r<e?t+vu(e-r,n):t},padStart:function(t,e,n){t=lr(t);var r=(e=q(e))?mu(t):0;return e&&r<e?vu(e-r,n)+t:t},parseInt:function(t,e,n){return n||null==e?e=0:e&&(e=+e),Au(lr(t).replace(wu,""),e||0)},repeat:function(t,e,n){return e=(n?en(t,e,n):void 0===e)?1:q(e),nu(lr(t),e)},replace:function(){var t=arguments,e=lr(t[0]);return t.length<3?e:e.replace(t[1],t[2])},snakeCase:lg,split:function(t,e,n){return n&&"number"!=typeof n&&en(t,e,n)&&(e=n=void 0),(n=void 0===n?xg:n>>>0)?(t=lr(t))&&("string"==typeof e||null!=e&&!sh(e))&&!(e=O(e))&&Fr(t)?zr(ei(t),0,n):t.split(e,n):[]},startCase:Pg,startsWith:function(t,e,n){return t=lr(t),n=null==n?0:Wi(q(n),0,t.length),e=O(e),t.slice(n,n+e.length)==e},template:function(t,e,n){var r=Ng.imports._.templateSettings||Ng;n&&en(t,e,n)&&(e=void 0),t=lr(t),e=Vn({},e,r,Sg);var i,s,o=Vn({},e.imports,r.imports,Sg),a=jn(o),l=vc(o,a),c=0,h=e.interpolate||Rg,u="__p += '",g=RegExp((e.escape||Rg).source+"|"+h.source+"|"+(h===kg?Dg:Rg).source+"|"+(e.evaluate||Rg).source+"|$","g"),d=zg.call(e,"sourceURL")?"//# sourceURL="+(e.sourceURL+"").replace(/\s/g," ")+"\n":"";t.replace(g,(function(e,n,r,o,a,l){return r||(r=o),u+=t.slice(c,l).replace(Vg,Tg),n&&(i=!0,u+="' +\n__e("+n+") +\n'"),a&&(s=!0,u+="';\n"+a+";\n__p += '"),r&&(u+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),c=l+e.length,e})),u+="';\n";var f=zg.call(e,"variable")&&e.variable;if(f){if(Ig.test(f))throw new Error(jg)}else u="with (obj) {\n"+u+"\n}\n";u=(s?u.replace(_g,""):u).replace(Lg,"$1").replace(Bg,"$1;"),u="function("+(f||"obj")+") {\n"+(f?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(s?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+u+"return __p\n}";var p=_r((function(){return Function(a,d+"return "+u).apply(void 0,l)}));if(p.source=u,jr(p))throw p;return p},templateSettings:Ng,toLower:function(t){return lr(t).toLowerCase()},toUpper:function(t){return lr(t).toUpperCase()},trim:function(t,e,n){if((t=lr(t))&&(n||void 0===e))return L(t);if(!t||!(e=O(e)))return t;var r=ei(t),i=ei(e);return zr(r,Zg(r,i),Yg(r,i)+1).join("")},trimEnd:function(t,e,n){if((t=lr(t))&&(n||void 0===e))return t.slice(0,j(t)+1);if(!t||!(e=O(e)))return t;var r=ei(t);return zr(r,0,Yg(r,ei(e))+1).join("")},trimStart:function(t,e,n){if((t=lr(t))&&(n||void 0===e))return t.replace(Kg,"");if(!t||!(e=O(e)))return t;var r=ei(t);return zr(r,Zg(r,ei(e))).join("")},truncate:function(t,e){var n=Qg,r=td;if(B(e)){var i="separator"in e?e.separator:i;n="length"in e?q(e.length):n,r="omission"in e?O(e.omission):r}var s=(t=lr(t)).length;if(Fr(t)){var o=ei(t);s=o.length}if(n>=s)return t;var a=n-mu(r);if(a<1)return r;var l=o?zr(o,0,a).join(""):t.slice(0,a);if(void 0===i)return l+r;if(o&&(a+=l.length-a),sh(i)){if(t.slice(a).search(i)){var c,h=l;for(i.global||(i=RegExp(i.source,lr(ed.exec(i))+"g")),i.lastIndex=0;c=i.exec(h);)var u=c.index;l=l.slice(0,void 0===u?a:u)}}else if(t.indexOf(O(i),a)!=a){var g=l.lastIndexOf(i);g>-1&&(l=l.slice(0,g))}return l+r},unescape:function(t){return(t=lr(t))&&id.test(t)?t.replace(rd,nd):t},upperCase:vd,upperFirst:ri,words:Bi},zd={attempt:_r,bindAll:Dr,cond:function(t){var e=null==t?0:t.length,n=va;return t=e?P(t,(function(t){if("function"!=typeof t[1])throw new TypeError(ba);return[n(t[0]),t[1]]})):[],Ze((function(n){for(var r=-1;++r<e;){var i=t[r];if(bt(i[0],this,n))return bt(i[1],this,n)}}))},conforms:function(t){return function(t){var e=jn(t);return function(n){return xa(n,t,e)}}(po(t,wa))},constant:Wt,defaultTo:function(t,e){return null==t||t!=t?e:t},flow:nc,flowRight:rc,identity:H,iteratee:function(t){return va("function"==typeof t?t:po(t,ch))},matches:function(t){return ca(po(t,xh))},matchesProperty:function(t,e){return pa(t,po(e,wh))},method:Oh,methodOf:Th,mixin:kh,noop:St,nthArg:function(t){return t=q(t),Ze((function(e){return Rh(e,t)}))},over:Jh,overEvery:Kh,overSome:Qh,property:ya,propertyOf:function(t){return function(e){return null==t?void 0:gr(t,e)}},range:Uu,rangeRight:qu,stubArray:rs,stubFalse:dn,stubObject:function(){return{}},stubString:function(){return""},stubTrue:function(){return!0},times:function(t,e){if((t=q(t))<1||t>Ug)return[];var n=qg,r=Wg(t,qg);e=ul(e),t-=qg;for(var i=on(r,e);++n<t;)e(n);return i},toPath:function(t){return C(t)?P(t,ur):x(t)?[t]:_t(ar(lr(t)))},uniqueId:dd};var $d=Math.max,Fd=Math.min;var Gd=1,Ud=2,qd=Math.min;
/**
     * @license
     * Lodash (Custom Build) <https://lodash.com/>
     * Build: `lodash modularize exports="es" -o ./`
     * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
     * Released under MIT license <https://lodash.com/license>
     * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
     * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
     */
var Wd,Hd=**********,Jd=Array.prototype,Xd=Object.prototype.hasOwnProperty,Yd=l?l.iterator:void 0,Zd=Math.max,Kd=Math.min,Qd=function(t){return function(e,n,r){if(null==r){var i=B(n),s=i&&jn(n),o=s&&s.length&&ic(n,s);(o?o.length:i)||(r=n,n=e,e=this)}return t(e,n,r)}}(kh);function tf(t,e,n){if(n)switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2]);case 4:return t.call(e,n[0],n[1],n[2],n[3]);case 5:return t.call(e,n[0],n[1],n[2],n[3],n[4]);case 6:return t.call(e,n[0],n[1],n[2],n[3],n[4],n[5]);default:return t.apply(e,n)}return t.call(e)}function ef(t,e,...n){return tf(t,e,n)}function nf(t){return"object"==typeof t&&t.then&&"function"==typeof t.then}function rf(t){return null!=t&&(t instanceof Promise||nf(t))}function sf(...t){const e=[];t.forEach((t=>{Array.isArray(t)?e.push(...t):e.push(t)}));if(e.some((t=>rf(t)))){const t=e.map((t=>rf(t)?t:Promise.resolve(!1!==t)));return Promise.all(t).then((t=>t.reduce(((t,e)=>!1!==e&&t),!0)))}return e.every((t=>!1!==t))}It.after=Nd.after,It.ary=Nd.ary,It.assign=Dd.assign,It.assignIn=Dd.assignIn,It.assignInWith=Dd.assignInWith,It.assignWith=Dd.assignWith,It.at=Dd.at,It.before=Nd.before,It.bind=Nd.bind,It.bindAll=zd.bindAll,It.bindKey=Nd.bindKey,It.castArray=jd.castArray,It.chain=Rd.chain,It.chunk=Od.chunk,It.compact=Od.compact,It.concat=Od.concat,It.cond=zd.cond,It.conforms=zd.conforms,It.constant=zd.constant,It.countBy=Td.countBy,It.create=Dd.create,It.curry=Nd.curry,It.curryRight=Nd.curryRight,It.debounce=Nd.debounce,It.defaults=Dd.defaults,It.defaultsDeep=Dd.defaultsDeep,It.defer=Nd.defer,It.delay=Nd.delay,It.difference=Od.difference,It.differenceBy=Od.differenceBy,It.differenceWith=Od.differenceWith,It.drop=Od.drop,It.dropRight=Od.dropRight,It.dropRightWhile=Od.dropRightWhile,It.dropWhile=Od.dropWhile,It.fill=Od.fill,It.filter=Td.filter,It.flatMap=Td.flatMap,It.flatMapDeep=Td.flatMapDeep,It.flatMapDepth=Td.flatMapDepth,It.flatten=Od.flatten,It.flattenDeep=Od.flattenDeep,It.flattenDepth=Od.flattenDepth,It.flip=Nd.flip,It.flow=zd.flow,It.flowRight=zd.flowRight,It.fromPairs=Od.fromPairs,It.functions=Dd.functions,It.functionsIn=Dd.functionsIn,It.groupBy=Td.groupBy,It.initial=Od.initial,It.intersection=Od.intersection,It.intersectionBy=Od.intersectionBy,It.intersectionWith=Od.intersectionWith,It.invert=Dd.invert,It.invertBy=Dd.invertBy,It.invokeMap=Td.invokeMap,It.iteratee=zd.iteratee,It.keyBy=Td.keyBy,It.keys=jn,It.keysIn=Dd.keysIn,It.map=Td.map,It.mapKeys=Dd.mapKeys,It.mapValues=Dd.mapValues,It.matches=zd.matches,It.matchesProperty=zd.matchesProperty,It.memoize=Nd.memoize,It.merge=Dd.merge,It.mergeWith=Dd.mergeWith,It.method=zd.method,It.methodOf=zd.methodOf,It.mixin=Qd,It.negate=_h,It.nthArg=zd.nthArg,It.omit=Dd.omit,It.omitBy=Dd.omitBy,It.once=Nd.once,It.orderBy=Td.orderBy,It.over=zd.over,It.overArgs=Nd.overArgs,It.overEvery=zd.overEvery,It.overSome=zd.overSome,It.partial=Nd.partial,It.partialRight=Nd.partialRight,It.partition=Td.partition,It.pick=Dd.pick,It.pickBy=Dd.pickBy,It.property=zd.property,It.propertyOf=zd.propertyOf,It.pull=Od.pull,It.pullAll=Od.pullAll,It.pullAllBy=Od.pullAllBy,It.pullAllWith=Od.pullAllWith,It.pullAt=Od.pullAt,It.range=zd.range,It.rangeRight=zd.rangeRight,It.rearg=Nd.rearg,It.reject=Td.reject,It.remove=Od.remove,It.rest=Nd.rest,It.reverse=Od.reverse,It.sampleSize=Td.sampleSize,It.set=Dd.set,It.setWith=Dd.setWith,It.shuffle=Td.shuffle,It.slice=Od.slice,It.sortBy=Td.sortBy,It.sortedUniq=Od.sortedUniq,It.sortedUniqBy=Od.sortedUniqBy,It.split=Vd.split,It.spread=Nd.spread,It.tail=Od.tail,It.take=Od.take,It.takeRight=Od.takeRight,It.takeRightWhile=Od.takeRightWhile,It.takeWhile=Od.takeWhile,It.tap=Rd.tap,It.throttle=Nd.throttle,It.thru=Gg,It.toArray=jd.toArray,It.toPairs=Dd.toPairs,It.toPairsIn=Dd.toPairsIn,It.toPath=zd.toPath,It.toPlainObject=jd.toPlainObject,It.transform=Dd.transform,It.unary=Nd.unary,It.union=Od.union,It.unionBy=Od.unionBy,It.unionWith=Od.unionWith,It.uniq=Od.uniq,It.uniqBy=Od.uniqBy,It.uniqWith=Od.uniqWith,It.unset=Dd.unset,It.unzip=Od.unzip,It.unzipWith=Od.unzipWith,It.update=Dd.update,It.updateWith=Dd.updateWith,It.values=Dd.values,It.valuesIn=Dd.valuesIn,It.without=Od.without,It.words=Vd.words,It.wrap=Nd.wrap,It.xor=Od.xor,It.xorBy=Od.xorBy,It.xorWith=Od.xorWith,It.zip=Od.zip,It.zipObject=Od.zipObject,It.zipObjectDeep=Od.zipObjectDeep,It.zipWith=Od.zipWith,It.entries=Dd.toPairs,It.entriesIn=Dd.toPairsIn,It.extend=Dd.assignIn,It.extendWith=Dd.assignInWith,Qd(It,It),It.add=_d.add,It.attempt=zd.attempt,It.camelCase=Vd.camelCase,It.capitalize=Vd.capitalize,It.ceil=_d.ceil,It.clamp=Ld,It.clone=jd.clone,It.cloneDeep=jd.cloneDeep,It.cloneDeepWith=jd.cloneDeepWith,It.cloneWith=jd.cloneWith,It.conformsTo=jd.conformsTo,It.deburr=Vd.deburr,It.defaultTo=zd.defaultTo,It.divide=_d.divide,It.endsWith=Vd.endsWith,It.eq=jd.eq,It.escape=Vd.escape,It.escapeRegExp=Vd.escapeRegExp,It.every=Td.every,It.find=Td.find,It.findIndex=Od.findIndex,It.findKey=Dd.findKey,It.findLast=Td.findLast,It.findLastIndex=Od.findLastIndex,It.findLastKey=Dd.findLastKey,It.floor=_d.floor,It.forEach=Td.forEach,It.forEachRight=Td.forEachRight,It.forIn=Dd.forIn,It.forInRight=Dd.forInRight,It.forOwn=Dd.forOwn,It.forOwnRight=Dd.forOwnRight,It.get=Dd.get,It.gt=jd.gt,It.gte=jd.gte,It.has=Dd.has,It.hasIn=Dd.hasIn,It.head=Od.head,It.identity=H,It.includes=Td.includes,It.indexOf=Od.indexOf,It.inRange=Bd,It.invoke=Dd.invoke,It.isArguments=jd.isArguments,It.isArray=C,It.isArrayBuffer=jd.isArrayBuffer,It.isArrayLike=jd.isArrayLike,It.isArrayLikeObject=jd.isArrayLikeObject,It.isBoolean=jd.isBoolean,It.isBuffer=jd.isBuffer,It.isDate=jd.isDate,It.isElement=jd.isElement,It.isEmpty=jd.isEmpty,It.isEqual=jd.isEqual,It.isEqualWith=jd.isEqualWith,It.isError=jd.isError,It.isFinite=jd.isFinite,It.isFunction=jd.isFunction,It.isInteger=jd.isInteger,It.isLength=jd.isLength,It.isMap=jd.isMap,It.isMatch=jd.isMatch,It.isMatchWith=jd.isMatchWith,It.isNaN=jd.isNaN,It.isNative=jd.isNative,It.isNil=jd.isNil,It.isNull=jd.isNull,It.isNumber=jd.isNumber,It.isObject=B,It.isObjectLike=jd.isObjectLike,It.isPlainObject=jd.isPlainObject,It.isRegExp=jd.isRegExp,It.isSafeInteger=jd.isSafeInteger,It.isSet=jd.isSet,It.isString=jd.isString,It.isSymbol=jd.isSymbol,It.isTypedArray=jd.isTypedArray,It.isUndefined=jd.isUndefined,It.isWeakMap=jd.isWeakMap,It.isWeakSet=jd.isWeakSet,It.join=Od.join,It.kebabCase=Vd.kebabCase,It.last=ol,It.lastIndexOf=Od.lastIndexOf,It.lowerCase=Vd.lowerCase,It.lowerFirst=Vd.lowerFirst,It.lt=jd.lt,It.lte=jd.lte,It.max=_d.max,It.maxBy=_d.maxBy,It.mean=_d.mean,It.meanBy=_d.meanBy,It.min=_d.min,It.minBy=_d.minBy,It.stubArray=zd.stubArray,It.stubFalse=zd.stubFalse,It.stubObject=zd.stubObject,It.stubString=zd.stubString,It.stubTrue=zd.stubTrue,It.multiply=_d.multiply,It.nth=Od.nth,It.noop=zd.noop,It.now=kd.now,It.pad=Vd.pad,It.padEnd=Vd.padEnd,It.padStart=Vd.padStart,It.parseInt=Vd.parseInt,It.random=Id,It.reduce=Td.reduce,It.reduceRight=Td.reduceRight,It.repeat=Vd.repeat,It.replace=Vd.replace,It.result=Dd.result,It.round=_d.round,It.sample=Td.sample,It.size=Td.size,It.snakeCase=Vd.snakeCase,It.some=Td.some,It.sortedIndex=Od.sortedIndex,It.sortedIndexBy=Od.sortedIndexBy,It.sortedIndexOf=Od.sortedIndexOf,It.sortedLastIndex=Od.sortedLastIndex,It.sortedLastIndexBy=Od.sortedLastIndexBy,It.sortedLastIndexOf=Od.sortedLastIndexOf,It.startCase=Vd.startCase,It.startsWith=Vd.startsWith,It.subtract=_d.subtract,It.sum=_d.sum,It.sumBy=_d.sumBy,It.template=Vd.template,It.times=zd.times,It.toFinite=jd.toFinite,It.toInteger=q,It.toLength=jd.toLength,It.toLower=Vd.toLower,It.toNumber=jd.toNumber,It.toSafeInteger=jd.toSafeInteger,It.toString=jd.toString,It.toUpper=Vd.toUpper,It.trim=Vd.trim,It.trimEnd=Vd.trimEnd,It.trimStart=Vd.trimStart,It.truncate=Vd.truncate,It.unescape=Vd.unescape,It.uniqueId=zd.uniqueId,It.upperCase=Vd.upperCase,It.upperFirst=Vd.upperFirst,It.each=Td.forEach,It.eachRight=Td.forEachRight,It.first=Od.head,Qd(It,(Wd={},Ma(It,(function(t,e){Xd.call(It.prototype,e)||(Wd[e]=t)})),Wd),{chain:!1}),It.VERSION="4.17.21",(It.templateSettings=Vd.templateSettings).imports._=It,Yt(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(t){It[t].placeholder=It})),Yt(["drop","take"],(function(t,e){Et.prototype[t]=function(n){n=void 0===n?1:Zd(q(n),0);var r=this.__filtered__&&!e?new Et(this):this.clone();return r.__filtered__?r.__takeCount__=Kd(n,r.__takeCount__):r.__views__.push({size:Kd(n,Hd),type:t+(r.__dir__<0?"Right":"")}),r},Et.prototype[t+"Right"]=function(e){return this.reverse()[t](e).reverse()}})),Yt(["filter","map","takeWhile"],(function(t,e){var n=e+1,r=1==n||3==n;Et.prototype[t]=function(t){var e=this.clone();return e.__iteratees__.push({iteratee:va(t),type:n}),e.__filtered__=e.__filtered__||r,e}})),Yt(["head","last"],(function(t,e){var n="take"+(e?"Right":"");Et.prototype[t]=function(){return this[n](1).value()[0]}})),Yt(["initial","tail"],(function(t,e){var n="drop"+(e?"":"Right");Et.prototype[t]=function(){return this.__filtered__?new Et(this):this[n](1)}})),Et.prototype.compact=function(){return this.filter(H)},Et.prototype.find=function(t){return this.filter(t).head()},Et.prototype.findLast=function(t){return this.reverse().find(t)},Et.prototype.invokeMap=Ze((function(t,e){return"function"==typeof t?new Et(this):this.map((function(n){return Ic(n,t,e)}))})),Et.prototype.reject=function(t){return this.filter(_h(va(t)))},Et.prototype.slice=function(t,e){t=q(t);var n=this;return n.__filtered__&&(t>0||e<0)?new Et(n):(t<0?n=n.takeRight(-t):t&&(n=n.drop(t)),void 0!==e&&(n=(e=q(e))<0?n.dropRight(-e):n.take(e-t)),n)},Et.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},Et.prototype.toArray=function(){return this.take(Hd)},Ma(Et.prototype,(function(t,e){var n=/^(?:filter|find|map|reject)|While$/.test(e),r=/^(?:head|last)$/.test(e),i=It[r?"take"+("last"==e?"Right":""):e],s=r||/^find/.test(e);i&&(It.prototype[e]=function(){var e=this.__wrapped__,o=r?[1]:arguments,a=e instanceof Et,l=o[0],c=a||C(e),h=function(t){var e=i.apply(It,pr([t],o));return r&&u?e[0]:e};c&&n&&"function"==typeof l&&1!=l.length&&(a=c=!1);var u=this.__chain__,g=!!this.__actions__.length,d=s&&!u,f=a&&!g;if(!s&&c){e=f?e:new Et(this);var p=t.apply(e,o);return p.__actions__.push({func:Gg,args:[h],thisArg:void 0}),new jt(p,u)}return d&&f?t.apply(this,o):(p=this.thru(h),d?r?p.value()[0]:p.value():p)})})),Yt(["pop","push","shift","sort","splice","unshift"],(function(t){var e=Jd[t],n=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",r=/^(?:pop|shift)$/.test(t);It.prototype[t]=function(){var t=arguments;if(r&&!this.__chain__){var i=this.value();return e.apply(C(i)?i:[],t)}return this[n]((function(n){return e.apply(C(n)?n:[],t)}))}})),Ma(Et.prototype,(function(t,e){var n=It[e];if(n){var r=n.name+"";Xd.call(Tt,r)||(Tt[r]=[]),Tt[r].push({name:e,func:n})}})),Tt[Pe(void 0,2).name]=[{name:"wrapper",func:void 0}],Et.prototype.clone=function(){var t=new Et(this.__wrapped__);return t.__actions__=_t(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=_t(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=_t(this.__views__),t},Et.prototype.reverse=function(){if(this.__filtered__){var t=new Et(this);t.__dir__=-1,t.__filtered__=!0}else(t=this.clone()).__dir__*=-1;return t},Et.prototype.value=function(){var t=this.__wrapped__.value(),e=this.__dir__,n=C(t),r=e<0,i=n?t.length:0,s=function(t,e,n){for(var r=-1,i=n.length;++r<i;){var s=n[r],o=s.size;switch(s.type){case"drop":t+=o;break;case"dropRight":e-=o;break;case"take":e=Fd(e,t+o);break;case"takeRight":t=$d(t,e-o)}}return{start:t,end:e}}(0,i,this.__views__),o=s.start,a=s.end,l=a-o,c=r?a:o-1,h=this.__iteratees__,u=h.length,g=0,d=qd(l,this.__takeCount__);if(!n||!r&&i==l&&d==l)return Hg(t,this.__actions__);var f=[];t:for(;l--&&g<d;){for(var p=-1,m=t[c+=e];++p<u;){var y=h[p],v=y.iteratee,b=y.type,x=v(m);if(b==Ud)m=x;else if(!x){if(b==Gd)continue t;break t}}f[g++]=m}return f},It.prototype.at=Rd.at,It.prototype.chain=Rd.wrapperChain,It.prototype.commit=Rd.commit,It.prototype.next=Rd.next,It.prototype.plant=Rd.plant,It.prototype.reverse=Rd.reverse,It.prototype.toJSON=It.prototype.valueOf=It.prototype.value=Rd.value,It.prototype.first=It.prototype.head,Yd&&(It.prototype[Yd]=Rd.toIterator);var of=Object.freeze({__proto__:null,apply:tf,call:ef,debounce:za,isAsync:rf,isAsyncLike:nf,throttle:Fg,toAsyncBoolean:sf,toDeferredBoolean:function(...t){const e=sf(t);return"boolean"==typeof e?Promise.resolve(e):e}});function af(t,e){const n=[];for(let r=0;r<t.length;r+=2){const i=tf(t[r],t[r+1],Array.isArray(e)?e:[e]);n.push(i)}return sf(n)}class lf{constructor(){this.listeners={}}on(t,e,n){if(null==e)return this;this.listeners[t]||(this.listeners[t]=[]);return this.listeners[t].push(e,n),this}once(t,e,n){const r=(...i)=>(this.off(t,r),af([e,n],i));return this.on(t,r,this)}off(t,e,n){if(!(t||e||n))return this.listeners={},this;const r=this.listeners;return(t?[t]:Object.keys(r)).forEach((t=>{const i=r[t];if(i)if(e||n)for(let t=i.length-2;t>=0;t-=2)e&&i[t]!==e||n&&i[t+1]!==n||i.splice(t,2);else delete r[t]})),this}trigger(t,...e){let n=!0;if("*"!==t){const r=this.listeners[t];null!=r&&(n=af([...r],e))}const r=this.listeners["*"];return null!=r?sf([n,af([...r],[t,...e])]):n}emit(t,...e){return this.trigger(t,...e)}}function cf(t,...e){e.forEach((e=>{Object.getOwnPropertyNames(e.prototype).forEach((n=>{"constructor"!==n&&Object.defineProperty(t.prototype,n,Object.getOwnPropertyDescriptor(e.prototype,n))}))}))}const hf=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])};function uf(t,e){function n(){this.constructor=t}hf(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}const gf=/^\s*class\s+/.test(`${class{}}`)||/^\s*class\s*\{/.test(`${class{}}`);function df(t,e){let n;return gf?n=class extends e{}:(n=function(){return e.apply(this,arguments)},uf(n,e)),Object.defineProperty(n,"name",{value:t}),n}function ff(t,e){return null!=t?t:e}function pf(t){return"__proto__"===t}function mf(t,e,n="/"){let r;const i=Array.isArray(e)?e:e.split(n);if(i.length)for(r=t;i.length;){const t=i.shift();if(Object(r)!==r||!t||!(t in r))return;r=r[t]}return r}function yf(t,e,n,r="/"){const i=Array.isArray(e)?e:e.split(r),s=i.pop();if(s&&!pf(s)){let e=t;i.forEach((t=>{pf(t)||(null==e[t]&&(e[t]={}),e=e[t])})),e[s]=n}return t}function vf(t,e,n="/"){const r=Array.isArray(e)?e.slice():e.split(n),i=r.pop();if(i)if(r.length>0){const e=mf(t,r);e&&delete e[i]}else delete t[i];return t}var bf=Object.freeze({__proto__:null,applyMixins:cf,clone:yo,cloneDeep:xo,createClass:df,defaults:Ga,defaultsDeep:Za,ensure:ff,flatten:function t(e,n="/",r){const i={};Object.keys(e).forEach((s=>{const o=e[s];let a="object"==typeof o||Array.isArray(o);if(a&&r&&r(o)&&(a=!1),a){const e=t(o,n,r);Object.keys(e).forEach((t=>{i[s+n+t]=e[t]}))}else i[s]=o}));for(const t in e)Object.prototype.hasOwnProperty.call(e,t);return i},getBoolean:function(t,e,n){const r=null!=t?t[e]:null;return null==r?n:!!r},getByPath:mf,getNumber:function(t,e,n){let r=null!=t?t[e]:null;return null==r?n:(r=+r,Number.isNaN(r)||!Number.isFinite(r)?n:r)},getValue:function(t,e,n){const r=null!=t?t[e]:null;return void 0!==n?ff(r,n):r},has:dc,inherit:uf,isEmpty:Xc,isEqual:Yc,isMaliciousProp:pf,isObject:B,isPlainObject:Tr,merge:Sh,pick:Eu,setByPath:yf,unsetByPath:vf});class xf extends lf{}!function(t){t.dispose=r.dispose}(xf||(xf={})),cf(xf,r);var wf=Object.freeze({__proto__:null,difference:sl,groupBy:oc,max:Ph,sortBy:hg,sortedIndex:yg,sortedIndexBy:vg,union:ld,uniq:ud});const Af=t=>{const e=Object.create(null);return n=>e[n]||(e[n]=t(n))},Pf=Af((t=>t.replace(/\B([A-Z])/g,"-$1").toLowerCase())),Cf=Af((t=>Pg(Ri(t)).replace(/ /g,""))),Mf=Af((t=>vd(t).replace(/ /g,"_"))),Ef=Af((t=>ph(t).replace(/ /g,"."))),Sf=Af((t=>ph(t).replace(/ /g,"/"))),Of=Af((t=>ri(ph(t)))),Tf=Af((t=>Pg(Ri(t))));function kf(t){let e=2166136261,n=!1,r=t;for(let t=0,i=r.length;t<i;t+=1){let i=r.charCodeAt(t);i>127&&!n&&(r=unescape(encodeURIComponent(r)),i=r.charCodeAt(t),n=!0),e^=i,e+=(e<<1)+(e<<4)+(e<<7)+(e<<8)+(e<<24)}return e>>>0}function Nf(){let t="";const e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";for(let n=0,r=e.length;n<r;n+=1){const r=e[n],i=16*Math.random()|0;t+=("x"===r?i:"y"===r?3&i|8:r).toString(16)}return t}function jf(t,e,n){const r=Math.min(2,Math.floor(.34*t.length));let i,s=Math.floor(.4*t.length)+1,o=!1;const a=t.toLowerCase();for(const l of e){const e=n(l);if(void 0!==e&&Math.abs(e.length-a.length)<=r){const n=e.toLowerCase();if(n===a){if(e===t)continue;return l}if(o)continue;if(e.length<3)continue;const r=_f(a,n,s-1);if(void 0===r)continue;r<3?(o=!0,i=l):(s=r,i=l)}}return i}function _f(t,e,n){let r=new Array(e.length+1),i=new Array(e.length+1);const s=n+1;for(let t=0;t<=e.length;t+=1)r[t]=t;for(let o=1;o<=t.length;o+=1){const a=t.charCodeAt(o-1),l=o>n?o-n:1,c=e.length>n+o?n+o:e.length;i[0]=o;let h=o;for(let t=1;t<l;t+=1)i[t]=s;for(let t=l;t<=c;t+=1){const n=a===e.charCodeAt(t-1)?r[t-1]:Math.min(r[t]+1,i[t-1]+1,r[t-1]+2);i[t]=n,h=Math.min(h,n)}for(let t=c+1;t<=e.length;t+=1)i[t]=s;if(h>n)return;const u=r;r=i,i=u}const o=r[e.length];return o>n?void 0:o}var Lf=Object.freeze({__proto__:null,camelCase:Ri,constantCase:Mf,dotCase:Ef,getSpellingSuggestion:jf,hashcode:kf,kebabCase:Pf,lowerFirst:mh,pascalCase:Cf,pathCase:Sf,sentenceCase:Of,titleCase:Tf,uniqueId:dd,upperFirst:ri,uuid:Nf});function Bf(t){return"string"==typeof t&&"%"===t.slice(-1)}function If(t,e){if(null==t)return 0;let n;if("string"==typeof t){if(n=parseFloat(t),Bf(t)&&(n/=100,Number.isFinite(n)))return n*e}else n=t;return Number.isFinite(n)?n>0&&n<1?n*e:n:0}function Df(t){if("object"==typeof t){let e=0,n=0,r=0,i=0;return null!=t.vertical&&Number.isFinite(t.vertical)&&(n=i=t.vertical),null!=t.horizontal&&Number.isFinite(t.horizontal)&&(r=e=t.horizontal),null!=t.left&&Number.isFinite(t.left)&&(e=t.left),null!=t.top&&Number.isFinite(t.top)&&(n=t.top),null!=t.right&&Number.isFinite(t.right)&&(r=t.right),null!=t.bottom&&Number.isFinite(t.bottom)&&(i=t.bottom),{top:n,right:r,bottom:i,left:e}}let e=0;return null!=t&&Number.isFinite(t)&&(e=t),{top:e,right:e,bottom:e,left:e}}var Rf=Object.freeze({__proto__:null,clamp:Hi,isNumber:th,isPercentage:Bf,mod:function(t,e){return(t%e+e)%e},normalizePercentage:If,normalizeSides:Df,parseCssNumeric:function(t,e){const n=parseFloat(t);if(Number.isNaN(n))return null;let r;if(null==e)r="[A-Za-z]*";else if(Array.isArray(e)){if(0===e.length)return null;r=e.join("|")}else"string"==typeof e&&(r=e);const i=function(e){const n=new RegExp(`(?:\\d+(?:\\.\\d+)*)(${e})$`).exec(t);return n?n[1]:null}(r);return null===i?null:{unit:i,value:n}},random:function(t,e){if(null==e)e=null==t?1:t,t=0;else if(e<t){const n=t;t=e,e=n}return Math.floor(Math.random()*(e-t+1)+t)}});let Vf=!1,zf=!1,$f=!1,Ff=!1,Gf=!1,Uf=!1,qf=!1,Wf=!1,Hf=!1,Jf=!1,Xf=!1,Yf=!1,Zf=!1,Kf=!1,Qf=!1,tp=!1;if("object"==typeof navigator){const t=navigator.userAgent;Vf=t.indexOf("Macintosh")>=0,zf=!!t.match(/(iPad|iPhone|iPod)/g),$f=t.indexOf("Windows")>=0,Ff=t.indexOf("MSIE")>=0,Gf=!!t.match(/Trident\/7\./),Uf=!!t.match(/Edge\//),qf=t.indexOf("Mozilla/")>=0&&t.indexOf("MSIE")<0&&t.indexOf("Edge/")<0,Hf=t.indexOf("Chrome/")>=0&&t.indexOf("Edge/")<0,Jf=t.indexOf("Opera/")>=0||t.indexOf("OPR/")>=0,Xf=t.indexOf("Firefox/")>=0,Yf=t.indexOf("AppleWebKit/")>=0&&t.indexOf("Chrome/")<0&&t.indexOf("Edge/")<0,"object"==typeof document&&(tp=!document.createElementNS||"[object SVGForeignObjectElement]"!=`${document.createElementNS("http://www.w3.org/2000/svg","foreignObject")}`||t.indexOf("Opera/")>=0)}if("object"==typeof window&&(Wf=null!=window.chrome&&null!=window.chrome.app&&null!=window.chrome.app.runtime,Kf=null!=window.PointerEvent&&!Vf),"object"==typeof document){Zf="ontouchstart"in document.documentElement;try{const t=Object.defineProperty({},"passive",{get(){Qf=!0}}),e=document.createElement("div");e.addEventListener&&e.addEventListener("click",(()=>{}),t)}catch(t){}}var ep;t.Platform=void 0,(ep=t.Platform||(t.Platform={})).IS_MAC=Vf,ep.IS_IOS=zf,ep.IS_WINDOWS=$f,ep.IS_IE=Ff,ep.IS_IE11=Gf,ep.IS_EDGE=Uf,ep.IS_NETSCAPE=qf,ep.IS_CHROME_APP=Wf,ep.IS_CHROME=Hf,ep.IS_OPERA=Jf,ep.IS_FIREFOX=Xf,ep.IS_SAFARI=Yf,ep.SUPPORT_TOUCH=Zf,ep.SUPPORT_POINTER=Kf,ep.SUPPORT_PASSIVE=Qf,ep.NO_FOREIGNOBJECT=tp,ep.SUPPORT_FOREIGNOBJECT=!ep.NO_FOREIGNOBJECT,function(t){function e(){const t=window.module;return null!=t&&null!=t.hot&&null!=t.hot.status?t.hot.status():"unkonwn"}t.getHMRStatus=e,t.isApplyingHMR=function(){return"apply"===e()};const n={select:"input",change:"input",submit:"form",reset:"form",error:"img",load:"img",abort:"img"};t.isEventSupported=function(t){const e=document.createElement(n[t]||"div"),r=`on${t}`;let i=r in e;return i||(e.setAttribute(r,"return;"),i="function"==typeof e[r]),i}}(t.Platform||(t.Platform={}));const np=/[\t\r\n\f]/g,rp=/\S+/g,ip=t=>` ${t} `;function sp(t){return t&&t.getAttribute&&t.getAttribute("class")||""}function op(t,e){if(null==t||null==e)return!1;const n=ip(sp(t)),r=ip(e);return 1===t.nodeType&&n.replace(np," ").includes(r)}function ap(t,e){if(null!=t&&null!=e){if("function"==typeof e)return ap(t,e(sp(t)));if("string"==typeof e&&1===t.nodeType){const n=e.match(rp)||[],r=ip(sp(t)).replace(np," ");let i=n.reduce(((t,e)=>t.indexOf(ip(e))<0?`${t}${e} `:t),r);i=i.trim(),r!==i&&t.setAttribute("class",i)}}}function lp(t,e){if(null!=t){if("function"==typeof e)return lp(t,e(sp(t)));if((!e||"string"==typeof e)&&1===t.nodeType){const n=(e||"").match(rp)||[],r=ip(sp(t)).replace(np," ");let i=n.reduce(((t,e)=>{const n=ip(e);return t.indexOf(n)>-1?t.replace(n," "):t}),r);i=e?i.trim():"",r!==i&&t.setAttribute("class",i)}}}function cp(t,e,n){if(null!=t&&null!=e)if(null==n||"string"!=typeof e){if("function"==typeof e)return cp(t,e(sp(t),n),n);if("string"==typeof e){(e.match(rp)||[]).forEach((e=>{op(t,e)?lp(t,e):ap(t,e)}))}}else n?ap(t,e):lp(t,e)}let hp=0;function up(){return hp+=1,`v${hp}`}function gp(t){return null!=t.id&&""!==t.id||(t.id=up()),t.id}function dp(t){return null!=t&&("function"==typeof t.getScreenCTM&&t instanceof SVGElement)}const fp={svg:"http://www.w3.org/2000/svg",xmlns:"http://www.w3.org/2000/xmlns/",xml:"http://www.w3.org/XML/1998/namespace",xlink:"http://www.w3.org/1999/xlink",xhtml:"http://www.w3.org/1999/xhtml"},pp="1.1";function mp(t,e=document){return e.createElement(t)}function yp(t,e=fp.xhtml,n=document){return n.createElementNS(e,t)}function vp(t,e=document){return yp(t,fp.svg,e)}function bp(t){if(t){const e=`<svg xmlns="${fp.svg}" xmlns:xlink="${fp.xlink}" version="${pp}">${t}</svg>`,{documentElement:n}=xp(e,{async:!1});return n}const e=document.createElementNS(fp.svg,"svg");return e.setAttributeNS(fp.xmlns,"xmlns:xlink",fp.xlink),e.setAttribute("version",pp),e}function xp(t,e={}){let n;try{const r=new DOMParser;if(null!=e.async){r.async=e.async}n=r.parseFromString(t,e.mimeType||"text/xml")}catch(t){n=void 0}if(!n||n.getElementsByTagName("parsererror").length)throw new Error(`Invalid XML: ${t}`);return n}function wp(t,e=!0){const n=t.nodeName;return e?n.toLowerCase():n.toUpperCase()}function Ap(t){let e=0,n=t.previousSibling;for(;n;)1===n.nodeType&&(e+=1),n=n.previousSibling;return e}function Pp(t,e){return t.querySelectorAll(e)}function Cp(t,e){return t.querySelector(e)}function Mp(t,e,n){const r=t.ownerSVGElement;let i=t.parentNode;for(;i&&i!==n&&i!==r;){if(op(i,e))return i;i=i.parentNode}return null}function Ep(t,e){const n=e&&e.parentNode;return t===n||!!(n&&1===n.nodeType&&16&t.compareDocumentPosition(n))}function Sp(t){if(t){(Array.isArray(t)?t:[t]).forEach((t=>{t.parentNode&&t.parentNode.removeChild(t)}))}}function Op(t){for(;t.firstChild;)t.removeChild(t.firstChild)}function Tp(t,e){(Array.isArray(e)?e:[e]).forEach((e=>{null!=e&&t.appendChild(e)}))}function kp(t,e){const n=t.firstChild;return n?Np(n,e):Tp(t,e)}function Np(t,e){const n=t.parentNode;if(n){(Array.isArray(e)?e:[e]).forEach((e=>{null!=e&&n.insertBefore(e,t)}))}}function jp(t,e){null!=e&&e.appendChild(t)}function _p(t){return!!t&&1===t.nodeType}function Lp(t){try{return t instanceof HTMLElement}catch(e){return"object"==typeof t&&1===t.nodeType&&"object"==typeof t.style&&"object"==typeof t.ownerDocument}}const Bp=["viewBox","attributeName","attributeType","repeatCount","textLength","lengthAdjust"];function Ip(t,e){return t.getAttribute(e)}function Dp(t,e){const n=$p(e);n.ns?t.hasAttributeNS(n.ns,n.local)&&t.removeAttributeNS(n.ns,n.local):t.hasAttribute(e)&&t.removeAttribute(e)}function Rp(t,e,n){if(null==n)return Dp(t,e);const r=$p(e);r.ns&&"string"==typeof n?t.setAttributeNS(r.ns,e,n):"id"===e?t.id=`${n}`:t.setAttribute(e,`${n}`)}function Vp(t,e){Object.keys(e).forEach((n=>{Rp(t,n,e[n])}))}function zp(t,e,n){if(null==e){const e=t.attributes,n={};for(let t=0;t<e.length;t+=1)n[e[t].name]=e[t].value;return n}if("string"==typeof e&&void 0===n)return t.getAttribute(e);"object"==typeof e?Vp(t,e):Rp(t,e,n)}function $p(t){if(-1!==t.indexOf(":")){const e=t.split(":");return{ns:fp[e[0]],local:e[1]}}return{ns:null,local:t}}function Fp(t){const e={};return Object.keys(t).forEach((n=>{const r=Bp.includes(n)?n:Pf(n);e[r]=t[n]})),e}function Gp(t){const e={};return t.split(";").forEach((t=>{const n=t.trim();if(n){const t=n.split("=");t.length&&(e[t[0].trim()]=t[1]?t[1].trim():"")}})),e}function Up(t,e){return Object.keys(e).forEach((n=>{if("class"===n)t[n]=t[n]?`${t[n]} ${e[n]}`:e[n];else if("style"===n){const r="object"==typeof t[n],i="object"==typeof e[n];let s,o;r&&i?(s=t[n],o=e[n]):r?(s=t[n],o=Gp(e[n])):i?(s=Gp(t[n]),o=e[n]):(s=Gp(t[n]),o=Gp(e[n])),t[n]=Up(s,o)}else t[n]=e[n]})),t}function qp(t,e,n={}){const r=n.offset||0,i=[],s=[];let o,a,l=null;for(let c=0;c<t.length;c+=1){o=s[c]=t[c];for(let i=0,a=e.length;i<a;i+=1){const a=e[i],l=a.start+r,h=a.end+r;c>=l&&c<h&&("string"==typeof o?o=s[c]={t:t[c],attrs:a.attrs}:o.attrs=Up(Up({},o.attrs),a.attrs),n.includeAnnotationIndices&&(null==o.annotations&&(o.annotations=[]),o.annotations.push(i)))}a=s[c-1],a?B(o)&&B(a)?JSON.stringify(o.attrs)===JSON.stringify(a.attrs)?l.t+=o.t:(i.push(l),l=o):B(o)||B(a)?(i.push(l),l=o):l=(l||"")+o:l=o}return null!=l&&i.push(l),i}function Wp(t){return t.replace(/ /g," ")}var Hp=Object.freeze({__proto__:null,annotate:qp,findAnnotationsAtIndex:function(t,e){return t?t.filter((t=>t.start<e&&e<=t.end)):[]},findAnnotationsBetweenIndexes:function(t,e,n){return t?t.filter((t=>e>=t.start&&e<t.end||n>t.start&&n<=t.end||t.start>=e&&t.end<n)):[]},sanitize:Wp,shiftAnnotations:function(t,e,n){return t&&t.forEach((t=>{t.start<e&&t.end>=e?t.end+=n:t.start>=e&&(t.start+=n,t.end+=n)})),t}});let Jp;t.DataUri=void 0,function(t){function e(t){const e="data:";return t.substr(0,e.length)===e}function n(t){let e=t.replace(/\s/g,"");e=decodeURIComponent(e);const n=e.indexOf(","),r=e.slice(0,n),i=r.split(":")[1].split(";")[0],s=e.slice(n+1);let o;o=r.indexOf("base64")>=0?atob(s):unescape(encodeURIComponent(s));const a=new Uint8Array(o.length);for(let t=0;t<o.length;t+=1)a[t]=o.charCodeAt(t);return new Blob([a],{type:i})}function r(t,e){const n=window.navigator.msSaveBlob;if(n)n(t,e);else{const n=window.URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download=e,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(n)}}function i(t){const e=parseFloat(t);return Number.isNaN(e)?null:e}t.isDataUrl=e,t.imageToDataUri=function(t,n){if(!t||e(t))return void setTimeout((()=>n(null,t)));const r=()=>{n(new Error(`Failed to load image: ${t}`))},i=window.FileReader?t=>{if(200===t.status){const e=new FileReader;e.onload=t=>{const e=t.target.result;n(null,e)},e.onerror=r,e.readAsDataURL(t.response)}else r()}:e=>{if(200===e.status){let r=t.split(".").pop()||"png";"svg"===r&&(r="svg+xml");const i=`data:image/${r};base64,`,s=new Uint8Array(e.response),o=i+btoa((t=>{const e=[];for(let n=0;n<t.length;n+=32768)e.push(String.fromCharCode.apply(null,t.subarray(n,n+32768)));return e.join("")})(s));n(null,o)}else r()},s=new XMLHttpRequest;s.responseType=window.FileReader?"blob":"arraybuffer",s.open("GET",t,!0),s.addEventListener("error",r),s.addEventListener("load",(()=>i(s))),s.send()},t.dataUriToBlob=n,t.downloadBlob=r,t.downloadDataUri=function(t,e){r(n(t),e)},t.svgToDataUrl=function(t,e={}){let n=null;const r=e=>(null==n&&(n=function(t){const e=t.match(/<svg[^>]*viewBox\s*=\s*(["']?)(.+?)\1[^>]*>/i);return e&&e[2]?e[2].replace(/\s+/," ").split(" "):null}(t)),null!=n?i(n[e]):null),s=e=>{const n=t.match(e);return n&&n[2]?i(n[2]):null};let o=e.width;if(null==o&&(o=s(/<svg[^>]*width\s*=\s*(["']?)(.+?)\1[^>]*>/i)),null==o&&(o=r(2)),null==o)throw new Error("Can not parse width from svg string");let a=e.height;if(null==a&&(a=s(/<svg[^>]*height\s*=\s*(["']?)(.+?)\1[^>]*>/i)),null==a&&(a=r(3)),null==a)throw new Error("Can not parse height from svg string");return`data:image/svg+xml,${encodeURIComponent(t).replace(/'/g,"%27").replace(/"/g,"%22")}`}}(t.DataUri||(t.DataUri={}));const Xp={px:t=>t,mm:t=>Jp*t,cm:t=>Jp*t*10,in:t=>Jp*t*25.4,pt:t=>Jp*(25.4*t/72),pc:t=>Jp*(25.4*t/6)};t.Unit=void 0,function(t){function e(t,e,n){const r=document.createElement("div"),i=r.style;i.display="inline-block",i.position="absolute",i.left="-15000px",i.top="-15000px",i.width=t+(n||"px"),i.height=e+(n||"px"),document.body.appendChild(r);const s=r.getBoundingClientRect(),o={width:s.width||0,height:s.height||0};return document.body.removeChild(r),o}t.measure=e,t.toPx=function(t,n){null==Jp&&(Jp=e("1","1","mm").width);const r=n?Xp[n]:null;return r?r(t):t}}(t.Unit||(t.Unit={}));const Yp=/-(.)/g;const Zp={},Kp=["webkit","ms","moz","o"],Qp=document?document.createElement("div").style:{};function tm(t){const e=t.replace(Yp,((t,e)=>e.toUpperCase()));if(null==Zp[e]){const t=e.charAt(0).toUpperCase()+e.slice(1);Zp[e]=e in Qp?e:function(t){for(let e=0;e<Kp.length;e+=1){const n=Kp[e]+t;if(n in Qp)return n}return null}(t)}return Zp[e]}function em(t,e){const n=t.ownerDocument&&t.ownerDocument.defaultView&&t.ownerDocument.defaultView.opener?t.ownerDocument.defaultView.getComputedStyle(t,null):window.getComputedStyle(t,null);return n&&e?n.getPropertyValue(e)||n[e]:n}const nm=function(){const t=document;return t.selection?function(){t.selection.empty()}:window.getSelection?function(){const t=window.getSelection();t&&(t.empty?t.empty():t.removeAllRanges&&t.removeAllRanges())}:function(){}}(),rm={animationIterationCount:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0};function im(t){return/^--/.test(t)}function sm(t,e,n){const r=window.getComputedStyle(t,null);return n?r.getPropertyValue(e)||void 0:r[e]||t.style[e]}function om(t,e){return parseInt(sm(t,e),10)||0}function am(t,e,n){if("string"!=typeof e)for(const n in e)am(t,n,e[n]);else{const r=im(e);if(r||(e=tm(e)),void 0===n)return sm(t,e,r);r||(n=function(t,e){return rm[t]||"number"!=typeof e?e:`${e}px`}(e,n));const i=t.style;r?i.setProperty(e,n):i[e]=n}}const lm=new WeakMap;function cm(t,e){const n=Ri(e),r=lm.get(t);if(r)return r[n]}function hm(t,e,n){const r=Ri(e),i=lm.get(t);i?i[r]=n:lm.set(t,{[r]:n})}function um(t,e,n){if(!e){const e={};return Object.keys(lm).forEach((n=>{e[n]=cm(t,n)})),e}if("string"==typeof e)return void 0===n?cm(t,e):void hm(t,e,n);for(const n in e)um(t,n,e[n])}const gm={class:"className",contenteditable:"contentEditable",for:"htmlFor",readonly:"readOnly",maxlength:"maxLength",tabindex:"tabIndex",colspan:"colSpan",rowspan:"rowSpan",usemap:"useMap"};class dm{get[Symbol.toStringTag](){return dm.toStringTag}get type(){return this.node.nodeName}get id(){return this.node.id}set id(t){this.node.id=t}constructor(t,e,n){if(!t)throw new TypeError("Invalid element to create vector");let r;if(dm.isVector(t))r=t.node;else if("string"==typeof t)if("svg"===t.toLowerCase())r=bp();else if("<"===t[0]){const e=bp(t);r=document.importNode(e.firstChild,!0)}else r=document.createElementNS(fp.svg,t);else r=t;this.node=r,e&&this.setAttributes(e),n&&this.append(n)}transform(t,e){return null==t?Zm(this.node):(Zm(this.node,t,e),this)}translate(t,e=0,n={}){return null==t?Km(this.node):(Km(this.node,t,e,n),this)}rotate(t,e,n,r={}){return null==t?Qm(this.node):(Qm(this.node,t,e,n,r),this)}scale(t,e){return null==t?ty(this.node):(ty(this.node,t,e),this)}getTransformToElement(t){const e=dm.toNode(t);return ey(this.node,e)}removeAttribute(t){return Dp(this.node,t),this}getAttribute(t){return Ip(this.node,t)}setAttribute(t,e){return Rp(this.node,t,e),this}setAttributes(t){return Vp(this.node,t),this}attr(t,e){return null==t?zp(this.node):"string"==typeof t&&void 0===e?zp(this.node,t):("object"==typeof t?zp(this.node,t):zp(this.node,t,e),this)}svg(){return this.node instanceof SVGSVGElement?this:dm.create(this.node.ownerSVGElement)}defs(){const t=this.svg()||this,e=t.node.getElementsByTagName("defs")[0];return e?dm.create(e):dm.create("defs").appendTo(t)}text(t,e={}){return vm(this.node,t,e),this}tagName(){return wp(this.node)}clone(){return dm.create(this.node.cloneNode(!0))}remove(){return Sp(this.node),this}empty(){return Op(this.node),this}append(t){return Tp(this.node,dm.toNodes(t)),this}appendTo(t){return jp(this.node,dm.isVector(t)?t.node:t),this}prepend(t){return kp(this.node,dm.toNodes(t)),this}before(t){return Np(this.node,dm.toNodes(t)),this}replace(t){return this.node.parentNode&&this.node.parentNode.replaceChild(dm.toNode(t),this.node),dm.create(t)}first(){return this.node.firstChild?dm.create(this.node.firstChild):null}last(){return this.node.lastChild?dm.create(this.node.lastChild):null}get(t){const e=this.node.childNodes[t];return e?dm.create(e):null}indexOf(t){return Array.prototype.slice.call(this.node.childNodes).indexOf(dm.toNode(t))}find(t){const e=[],n=Pp(this.node,t);if(n)for(let t=0,r=n.length;t<r;t+=1)e.push(dm.create(n[t]));return e}findOne(t){const e=Cp(this.node,t);return e?dm.create(e):null}findParentByClass(t,e){const n=Mp(this.node,t,e);return n?dm.create(n):null}matches(t){const e=this.node;this.node.matches;const n=e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector||null;return n&&n.call(e,t)}contains(t){return Ep(this.node,dm.isVector(t)?t.node:t)}wrap(t){const e=dm.create(t),n=this.node.parentNode;return null!=n&&n.insertBefore(e.node,this.node),e.append(this)}parent(t){let e=this;if(null==e.node.parentNode)return null;if(e=dm.create(e.node.parentNode),null==t)return e;do{if("string"==typeof t?e.matches(t):e instanceof t)return e}while(e=dm.create(e.node.parentNode));return e}children(){const t=this.node.childNodes,e=[];for(let n=0;n<t.length;n+=1){1===t[n].nodeType&&e.push(dm.create(t[n]))}return e}eachChild(t,e){const n=this.children();for(let r=0,i=n.length;r<i;r+=1)t.call(n[r],n[r],r,n),e&&n[r].eachChild(t,e);return this}index(){return Ap(this.node)}hasClass(t){return op(this.node,t)}addClass(t){return ap(this.node,t),this}removeClass(t){return lp(this.node,t),this}toggleClass(t,e){return cp(this.node,t,e),this}toLocalPoint(t,e){return ry(this.node,t,e)}sample(t=1){return this.node instanceof SVGPathElement?Cm(this.node,t):[]}toPath(){return dm.create(Lm(this.node))}toPathData(){return Bm(this.node)}}!function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&r.node instanceof SVGElement&&"function"==typeof r.sample&&"function"==typeof r.toPath}function n(e,n,r){return new t(e,n,r)}function r(t){return e(t)?t.node:t}t.toStringTag=`X6.${t.name}`,t.isVector=e,t.create=n,t.createVectors=function(t){if("<"===t[0]){const e=bp(t),r=[];for(let t=0,i=e.childNodes.length;t<i;t+=1){const i=e.childNodes[t];r.push(n(document.importNode(i,!0)))}return r}return[n(t)]},t.toNode=r,t.toNodes=function(t){return Array.isArray(t)?t.map((t=>r(t))):[r(t)]}}(dm||(dm={}));const fm=document.createElement("canvas").getContext("2d");function pm(t,e,n){const r=n.eol,i=n.baseSize,s=n.lineHeight;let o,a=0;const l={},c=e.length-1;for(let s=0;s<=c;s+=1){let l=e[s],h=null;if("object"==typeof l){const t=l.attrs,e=dm.create("tspan",t);o=e.node;let u=l.t;r&&s===c&&(u+=r),o.textContent=u;const g=t.class;g&&e.addClass(g),n.includeAnnotationIndices&&e.attr("annotations",l.annotations.join(",")),h=parseFloat(t["font-size"]),void 0===h&&(h=i),h&&h>a&&(a=h)}else r&&s===c&&(l+=r),o=document.createTextNode(l||" "),i&&i>a&&(a=i);t.appendChild(o)}return a&&(l.maxFontSize=a),s?l.lineHeight=s:a&&(l.lineHeight=1.2*a),l}const mm=/em$/;function ym(t,e){const n=parseFloat(t);return mm.test(t)?n*e:n}function vm(t,e,n={}){e=Wp(e);const r=n.eol;let i=n.textPath;const s=n.textVerticalAnchor,o="middle"===s||"bottom"===s||"top"===s;let a=n.x;void 0===a&&(a=t.getAttribute("x")||0);const l=n.includeAnnotationIndices;let c=n.annotations;c&&!Array.isArray(c)&&(c=[c]);const h=n.lineHeight,u="auto"===h,g=u?"1.5em":h||"1em";let d=!0;const f=t.childNodes;if(1===f.length){const t=f[0];t&&"TITLE"===t.tagName.toUpperCase()&&(d=!1)}d&&Op(t),zp(t,{"xml:space":"preserve",display:e||n.displayEmpty?null:"none"});const p=zp(t,"font-size");let m,y,v=parseFloat(p);v||(v=16,!o&&!c||p||zp(t,"font-size",`${v}`)),i?("string"==typeof i&&(i={d:i}),m=function(t,e){const n=dm.create(e),r=dm.create("textPath"),i=t.d;if(i&&void 0===t["xlink:href"]){const t=dm.create("path").attr("d",i).appendTo(n.defs());r.attr("xlink:href",`#${t.id}`)}return"object"==typeof t&&r.attr(t),r.node}(i,t)):m=document.createDocumentFragment();let b,x=0;const w=e.split("\n"),A=[],P=w.length-1;for(let t=0;t<=P;t+=1){y=g;let e="v-line";const n=vp("tspan");let s,o=w[t];if(o)if(c){s=pm(n,qp(o,c,{offset:-x,includeAnnotationIndices:l}),{eol:t!==P&&r,baseSize:v,lineHeight:u?null:g,includeAnnotationIndices:l});const e=s.lineHeight;e&&u&&0!==t&&(y=e),0===t&&(b=.8*s.maxFontSize)}else r&&t!==P&&(o+=r),n.textContent=o;else{n.textContent="-",e+=" v-empty-line";const t=n.style;t.fillOpacity=0,t.strokeOpacity=0,c&&(s={})}s&&A.push(s),t>0&&n.setAttribute("dy",y),(t>0||i)&&n.setAttribute("x",a),n.className.baseVal=e,m.appendChild(n),x+=o.length+1}if(o)if(c)y=function(t,e,n,r){if(!Array.isArray(e))return 0;const i=e.length;if(!i)return 0;let s=e[0];const o=ym(s.maxFontSize,n)||n;let a=0;const l=ym(r,n);for(let t=1;t<i;t+=1)s=e[t],a+=ym(s.lineHeight,n)||l;const c=ym(s.maxFontSize,n)||n;let h;switch(t){case"middle":h=o/2-.15*c-a/2;break;case"bottom":h=-.25*c-a;break;default:h=.8*o}return h}(s,A,v,g);else if("top"===s)y="0.8em";else{let t;switch(P>0?(t=parseFloat(g)||1,t*=P,mm.test(g)||(t/=v)):t=0,s){case"middle":y=.3-t/2+"em";break;case"bottom":y=-t-.3+"em"}}else 0===s?y="0em":s?y=s:(y=0,null==t.getAttribute("y")&&t.setAttribute("y",`${b||"0.8em"}`));m.firstChild.setAttribute("dy",y),t.appendChild(m)}function bm(t,e={}){if(!t)return{width:0};const n=[],r=e["font-size"]?`${parseFloat(e["font-size"])}px`:"14px";return n.push(e["font-style"]||"normal"),n.push(e["font-variant"]||"normal"),n.push(e["font-weight"]||400),n.push(r),n.push(e["font-family"]||"sans-serif"),fm.font=n.join(" "),fm.measureText(t)}function xm(t,e,n,r={}){if(e>=n)return[t,""];const i=t.length,s={};let o=Math.round(e/n*i-1);for(o<0&&(o=0);o>=0&&o<i;){const n=t.slice(0,o),i=s[n]||bm(n,r).width,a=t.slice(0,o+1),l=s[a]||bm(a,r).width;if(s[n]=i,s[a]=l,i>e)o-=1;else{if(!(l<=e))break;o+=1}}return[t.slice(0,o),t.slice(o)]}function wm(t,e,n={},r={}){const i=e.width,s=e.height,o=r.eol||"\n",{width:a}=bm(t,n);if(a<i)return t;const l=[],c=n.fontSize||14,h=n.lineHeight?parseFloat(n.lineHeight):Math.ceil(1.4*c),u=Math.floor(s/h);let g=t,d=a,f=r.ellipsis,p=0;f&&("string"!=typeof f&&(f="…"),p=bm(f,n).width);for(let t=0;t<u;t+=1){if(!(d>i)){l.push(g);break}if(t===u-1){const[t]=xm(g,i-p,d,n);l.push(f?`${t}${f}`:t)}else{const[t,e]=xm(g,i,d,n);l.push(t),g=e,d=bm(g,n).width}}return l.join(o)}const Am=.551784;function Pm(t,e,n=NaN){const r=t.getAttribute(e);if(null==r)return n;const i=parseFloat(r);return Number.isNaN(i)?n:i}function Cm(t,e=1){const n=t.getTotalLength(),r=[];let i,s=0;for(;s<n;)i=t.getPointAtLength(s),r.push({distance:s,x:i.x,y:i.y}),s+=e;return r}function Mm(t){return["M",Pm(t,"x1"),Pm(t,"y1"),"L",Pm(t,"x2"),Pm(t,"y2")].join(" ")}function Em(t){const e=Tm(t);return 0===e.length?null:`${Om(e)} Z`}function Sm(t){const e=Tm(t);return 0===e.length?null:Om(e)}function Om(t){return`M ${t.map((t=>`${t.x} ${t.y}`)).join(" L")}`}function Tm(t){const e=[],n=t.points;if(n)for(let t=0,r=n.numberOfItems;t<r;t+=1)e.push(n.getItem(t));return e}function km(t){const e=Pm(t,"cx",0),n=Pm(t,"cy",0),r=Pm(t,"r"),i=r*Am;return["M",e,n-r,"C",e+i,n-r,e+r,n-i,e+r,n,"C",e+r,n+i,e+i,n+r,e,n+r,"C",e-i,n+r,e-r,n+i,e-r,n,"C",e-r,n-i,e-i,n-r,e,n-r,"Z"].join(" ")}function Nm(t){const e=Pm(t,"cx",0),n=Pm(t,"cy",0),r=Pm(t,"rx"),i=Pm(t,"ry")||r,s=r*Am,o=i*Am;return["M",e,n-i,"C",e+s,n-i,e+r,n-o,e+r,n,"C",e+r,n+o,e+s,n+i,e,n+i,"C",e-s,n+i,e-r,n+o,e-r,n,"C",e-r,n-o,e-s,n-i,e,n-i,"Z"].join(" ")}function jm(t){return _m({x:Pm(t,"x",0),y:Pm(t,"y",0),width:Pm(t,"width",0),height:Pm(t,"height",0),rx:Pm(t,"rx",0),ry:Pm(t,"ry",0)})}function _m(t){let e;const n=t.x,r=t.y,i=t.width,s=t.height,o=Math.min(t.rx||t["top-rx"]||0,i/2),a=Math.min(t.rx||t["bottom-rx"]||0,i/2),l=Math.min(t.ry||t["top-ry"]||0,s/2),c=Math.min(t.ry||t["bottom-ry"]||0,s/2);return e=o||a||l||c?["M",n,r+l,"v",s-l-c,"a",a,c,0,0,0,a,c,"h",i-2*a,"a",a,c,0,0,0,a,-c,"v",-(s-c-l),"a",o,l,0,0,0,-o,-l,"h",-(i-2*o),"a",o,l,0,0,0,-o,l,"Z"]:["M",n,r,"H",n+i,"V",r+s,"H",n,"V",r,"Z"],e.join(" ")}function Lm(t){const e=vp("path");zp(e,zp(t));const n=Bm(t);return n&&e.setAttribute("d",n),e}function Bm(t){const e=t.tagName.toLowerCase();switch(e){case"path":return t.getAttribute("d");case"line":return Mm(t);case"polygon":return Em(t);case"polyline":return Sm(t);case"ellipse":return Nm(t);case"circle":return km(t);case"rect":return jm(t)}throw new Error(`"${e}" cannot be converted to svg path element.`)}const Im=vp("svg"),Dm=/(\w+)\(([^,)]+),?([^)]+)?\)/gi,Rm=/[ ,]+/,Vm=/^(\w+)\((.*)\)/;function zm(t,e){const n=Im.createSVGPoint();return n.x=t,n.y=e,n}function $m(t){const e=Im.createSVGMatrix();if(null!=t){const n=t,r=e;for(const t in n)r[t]=n[t]}return e}function Fm(t){return null!=t?(t instanceof DOMMatrix||(t=$m(t)),Im.createSVGTransformFromMatrix(t)):Im.createSVGTransform()}function Gm(t){let e=$m();const n=null!=t&&t.match(Dm);if(!n)return e;for(let t=0,r=n.length;t<r;t+=1){const r=n[t].match(Vm);if(r){let t,n,i,s,o,a=$m();const l=r[2].split(Rm);switch(r[1].toLowerCase()){case"scale":t=parseFloat(l[0]),n=void 0===l[1]?t:parseFloat(l[1]),a=a.scaleNonUniform(t,n);break;case"translate":i=parseFloat(l[0]),s=parseFloat(l[1]),a=a.translate(i,s);break;case"rotate":o=parseFloat(l[0]),i=parseFloat(l[1])||0,s=parseFloat(l[2])||0,a=0!==i||0!==s?a.translate(i,s).rotate(o).translate(-i,-s):a.rotate(o);break;case"skewx":o=parseFloat(l[0]),a=a.skewX(o);break;case"skewy":o=parseFloat(l[0]),a=a.skewY(o);break;case"matrix":a.a=parseFloat(l[0]),a.b=parseFloat(l[1]),a.c=parseFloat(l[2]),a.d=parseFloat(l[3]),a.e=parseFloat(l[4]),a.f=parseFloat(l[5]);break;default:continue}e=e.multiply(a)}}return e}function Um(t){const e=t||{};return`matrix(${null!=e.a?e.a:1},${null!=e.b?e.b:0},${null!=e.c?e.c:0},${null!=e.d?e.d:1},${null!=e.e?e.e:0},${null!=e.f?e.f:0})`}function qm(t){let e,n,r;if(t){const i=Rm;if(t.trim().indexOf("matrix")>=0){const i=Hm(Gm(t));e=[i.translateX,i.translateY],n=[i.rotation],r=[i.scaleX,i.scaleY];const s=[];0===e[0]&&0===e[1]||s.push(`translate(${e.join(",")})`),1===r[0]&&1===r[1]||s.push(`scale(${r.join(",")})`),0!==n[0]&&s.push(`rotate(${n[0]})`),t=s.join(" ")}else{const s=t.match(/translate\((.*?)\)/);s&&(e=s[1].split(i));const o=t.match(/rotate\((.*?)\)/);o&&(n=o[1].split(i));const a=t.match(/scale\((.*?)\)/);a&&(r=a[1].split(i))}}const i=r&&r[0]?parseFloat(r[0]):1;return{raw:t||"",translation:{tx:e&&e[0]?parseInt(e[0],10):0,ty:e&&e[1]?parseInt(e[1],10):0},rotation:{angle:n&&n[0]?parseInt(n[0],10):0,cx:n&&n[1]?parseInt(n[1],10):void 0,cy:n&&n[2]?parseInt(n[2],10):void 0},scale:{sx:i,sy:r&&r[1]?parseFloat(r[1]):i}}}function Wm(t,e){return{x:e.x*t.a+e.y*t.c+0,y:e.x*t.b+e.y*t.d+0}}function Hm(t){const e=Wm(t,{x:0,y:1}),n=Wm(t,{x:1,y:0}),r=180/Math.PI*Math.atan2(e.y,e.x)-90;return{skewX:r,skewY:180/Math.PI*Math.atan2(n.y,n.x),translateX:t.e,translateY:t.f,scaleX:Math.sqrt(t.a*t.a+t.b*t.b),scaleY:Math.sqrt(t.c*t.c+t.d*t.d),rotation:r}}function Jm(t){let e,n,r,i;return t?(e=null==t.a?1:t.a,i=null==t.d?1:t.d,n=t.b,r=t.c):e=i=1,{sx:n?Math.sqrt(e*e+n*n):e,sy:r?Math.sqrt(r*r+i*i):i}}function Xm(t){let e={x:0,y:1};t&&(e=Wm(t,e));const n=180*Math.atan2(e.y,e.x)/Math.PI%360-90;return{angle:n%360+(n<0?360:0)}}function Ym(t){return{tx:t&&t.e||0,ty:t&&t.f||0}}function Zm(t,e,n={}){if(null==e)return Gm(zp(t,"transform"));if(n.absolute)return void t.setAttribute("transform",Um(e));const r=t.transform,i=Fm(e);r.baseVal.appendItem(i)}function Km(t,e,n=0,r={}){let i=zp(t,"transform");const s=qm(i);if(null==e)return s.translation;i=s.raw,i=i.replace(/translate\([^)]*\)/g,"").trim();const o=`translate(${r.absolute?e:s.translation.tx+e},${r.absolute?n:s.translation.ty+n})`;t.setAttribute("transform",`${o} ${i}`.trim())}function Qm(t,e,n,r,i={}){let s=zp(t,"transform");const o=qm(s);if(null==e)return o.rotation;s=o.raw,s=s.replace(/rotate\([^)]*\)/g,"").trim(),e%=360;const a=`rotate(${i.absolute?e:o.rotation.angle+e}${null!=n&&null!=r?`,${n},${r}`:""})`;t.setAttribute("transform",`${s} ${a}`.trim())}function ty(t,e,n){let r=zp(t,"transform");const i=qm(r);if(null==e)return i.scale;n=null==n?e:n,r=i.raw,r=r.replace(/scale\([^)]*\)/g,"").trim();const s=`scale(${e},${n})`;t.setAttribute("transform",`${r} ${s}`.trim())}function ey(t,e){if(dp(e)&&dp(t)){const n=e.getScreenCTM(),r=t.getScreenCTM();if(n&&r)return n.inverse().multiply(r)}return $m()}function ny(t,e){let n=$m();if(dp(e)&&dp(t)){let r=t;const i=[];for(;r&&r!==e;){const t=Gm(r.getAttribute("transform")||null);i.push(t),r=r.parentNode}i.reverse().forEach((t=>{n=n.multiply(t)}))}return n}function ry(t,e,n){const r=t instanceof SVGSVGElement?t:t.ownerSVGElement,i=r.createSVGPoint();i.x=e,i.y=n;try{const e=r.getScreenCTM(),n=i.matrixTransform(e.inverse()),s=ey(t,r).inverse();return n.matrixTransform(s)}catch(t){return i}}var iy,sy,oy;!function(t){const e={};t.get=function(t){return e[t]||{}},t.register=function(t,n){e[t]=n},t.unregister=function(t){delete e[t]}}(iy||(iy={})),function(t){const e=new WeakMap;t.ensure=function(t){return e.has(t)||e.set(t,{events:Object.create(null)}),e.get(t)},t.get=function(t){return e.get(t)},t.remove=function(t){return e.delete(t)}}(sy||(sy={})),function(t){t.returnTrue=()=>!0,t.returnFalse=()=>!1,t.stopPropagationCallback=function(t){t.stopPropagation()},t.addEventListener=function(t,e,n){null!=t.addEventListener&&t.addEventListener(e,n)},t.removeEventListener=function(t,e,n){null!=t.removeEventListener&&t.removeEventListener(e,n)}}(oy||(oy={})),function(t){const e=/[^\x20\t\r\n\f]+/g,n=/^([^.]*)(?:\.(.+)|)/;t.splitType=function(t){return(t||"").match(e)||[""]},t.normalizeType=function(t){const e=n.exec(t)||[];return{originType:e[1]?e[1].trim():e[1],namespaces:e[2]?e[2].split(".").map((t=>t.trim())).sort():[]}},t.isValidTarget=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType},t.isValidSelector=function(t,e){if(e){const n=t;return null!=n.querySelector&&null!=n.querySelector(e)}return!0}}(oy||(oy={})),function(t){let e=0;const n=new WeakMap;t.ensureHandlerId=function(t){return n.has(t)||(n.set(t,e),e+=1),n.get(t)},t.getHandlerId=function(t){return n.get(t)},t.removeHandlerId=function(t){return n.delete(t)},t.setHandlerId=function(t,e){return n.set(t,e)}}(oy||(oy={})),function(t){t.getHandlerQueue=function(t,e){const n=[],r=sy.get(t),i=r&&r.events&&r.events[e.type],s=i&&i.handlers||[],o=i?i.delegateCount:0;if(o>0&&!("click"===e.type&&"number"==typeof e.button&&e.button>=1))for(let r=e.target;r!==t;r=r.parentNode||t)if(1===r.nodeType&&("click"!==e.type||!0!==r.disabled)){const e=[],i={};for(let n=0;n<o;n+=1){const o=s[n],a=o.selector;if(null!=a&&null==i[a]){const e=[];t.querySelectorAll(a).forEach((t=>{e.push(t)})),i[a]=e.includes(r)}i[a]&&e.push(o)}e.length&&n.push({elem:r,handlers:e})}return o<s.length&&n.push({elem:t,handlers:s.slice(o)}),n}}(oy||(oy={})),function(t){t.isWindow=function(t){return null!=t&&t===t.window}}(oy||(oy={})),function(t){t.contains=function(t,e){const n=9===t.nodeType?t.documentElement:t,r=e&&e.parentNode;return t===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):t.compareDocumentPosition&&16&t.compareDocumentPosition(r)))}}(oy||(oy={}));class ay{constructor(t,e){this.isDefaultPrevented=oy.returnFalse,this.isPropagationStopped=oy.returnFalse,this.isImmediatePropagationStopped=oy.returnFalse,this.isSimulated=!1,this.preventDefault=()=>{const t=this.originalEvent;this.isDefaultPrevented=oy.returnTrue,t&&!this.isSimulated&&t.preventDefault()},this.stopPropagation=()=>{const t=this.originalEvent;this.isPropagationStopped=oy.returnTrue,t&&!this.isSimulated&&t.stopPropagation()},this.stopImmediatePropagation=()=>{const t=this.originalEvent;this.isImmediatePropagationStopped=oy.returnTrue,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()},"string"==typeof t?this.type=t:t.type&&(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented?oy.returnTrue:oy.returnFalse,this.target=t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget,this.timeStamp=t.timeStamp),e&&Object.assign(this,e),this.timeStamp||(this.timeStamp=Date.now())}}!function(t){t.create=function(e){return e instanceof t?e:new t(e)}}(ay||(ay={})),function(t){t.addProperty=function(e,n){Object.defineProperty(t.prototype,e,{enumerable:!0,configurable:!0,get:"function"==typeof n?function(){if(this.originalEvent)return n(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})}}(ay||(ay={})),function(t){const e={bubbles:!0,cancelable:!0,eventPhase:!0,detail:!0,view:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pageX:!0,pageY:!0,screenX:!0,screenY:!0,toElement:!0,pointerId:!0,pointerType:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,touches:!0,changedTouches:!0,targetTouches:!0,which:!0,altKey:!0,ctrlKey:!0,metaKey:!0,shiftKey:!0};Object.keys(e).forEach((n=>t.addProperty(n,e[n])))}(ay||(ay={})),iy.register("load",{noBubble:!0}),iy.register("beforeunload",{postDispatch(t,e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}),iy.register("mouseenter",{delegateType:"mouseover",bindType:"mouseover",handle(t,e){let n;const r=e.relatedTarget,i=e.handleObj;return r&&(r===t||oy.contains(t,r))||(e.type=i.originType,n=i.handler.call(t,e),e.type="mouseover"),n}}),iy.register("mouseleave",{delegateType:"mouseout",bindType:"mouseout",handle(t,e){let n;const r=e.relatedTarget,i=e.handleObj;return r&&(r===t||oy.contains(t,r))||(e.type=i.originType,n=i.handler.call(t,e),e.type="mouseout"),n}});var ly,cy,hy,uy=window&&window.__rest||function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};!function(t){let e;function n(t,e,...n){const r=ay.create(e);r.delegateTarget=t;const i=iy.get(r.type);if(i.preDispatch&&!1===i.preDispatch(t,r))return;const s=oy.getHandlerQueue(t,r);for(let t=0,e=s.length;t<e&&!r.isPropagationStopped();t+=1){const e=s[t];r.currentTarget=e.elem;for(let t=0,i=e.handlers.length;t<i&&!r.isImmediatePropagationStopped();t+=1){const i=e.handlers[t];if(null==r.rnamespace||i.namespace&&r.rnamespace.test(i.namespace)){r.handleObj=i,r.data=i.data;const t=iy.get(i.originType).handle,s=t?t(e.elem,r,...n):i.handler.call(e.elem,r,...n);void 0!==s&&(r.result=s,!1===s&&(r.preventDefault(),r.stopPropagation()))}}}return i.postDispatch&&i.postDispatch(t,r),r.result}t.on=function(t,r,i,s,o){if(!oy.isValidTarget(t))return;let a;if("function"!=typeof i){const{handler:t,selector:e}=i,n=uy(i,["handler","selector"]);i=t,o=e,a=n}const l=sy.ensure(t);let c=l.handler;null==c&&(c=l.handler=function(r,...i){return e!==r.type?n(t,r,...i):void 0});const h=oy.ensureHandlerId(i);oy.splitType(r).forEach((e=>{const{originType:n,namespaces:r}=oy.normalizeType(e);if(!n)return;let u=n,g=iy.get(u);u=(o?g.delegateType:g.bindType)||u,g=iy.get(u);const d=Object.assign({type:u,originType:n,data:s,selector:o,guid:h,handler:i,namespace:r.join(".")},a),f=l.events;let p=f[u];p||(p=f[u]={handlers:[],delegateCount:0},g.setup&&!1!==g.setup(t,s,r,c)||oy.addEventListener(t,u,c)),g.add&&(oy.removeHandlerId(d.handler),g.add(t,d),oy.setHandlerId(d.handler,h)),o?(p.handlers.splice(p.delegateCount,0,d),p.delegateCount+=1):p.handlers.push(d)}))},t.off=function t(e,n,r,i,s){const o=sy.get(e);if(!o)return;const a=o.events;a&&(oy.splitType(n).forEach((n=>{const{originType:l,namespaces:c}=oy.normalizeType(n);if(!l)return void Object.keys(a).forEach((s=>{t(e,s+n,r,i,!0)}));let h=l;const u=iy.get(h);h=(i?u.delegateType:u.bindType)||h;const g=a[h]||{},d=c.length>0?new RegExp(`(^|\\.)${c.join("\\.(?:.*\\.|)")}(\\.|$)`):null,f=g.handlers.length;for(let t=g.handlers.length-1;t>=0;t-=1){const n=g.handlers[t];!s&&l!==n.originType||r&&oy.getHandlerId(r)!==n.guid||!(null==d||n.namespace&&d.test(n.namespace))||!(null==i||i===n.selector||"**"===i&&n.selector)||(g.handlers.splice(t,1),n.selector&&(g.delegateCount-=1),u.remove&&u.remove(e,n))}f&&0===g.handlers.length&&(u.teardown&&!1!==u.teardown(e,c,o.handler)||oy.removeEventListener(e,h,o.handler),delete a[h])})),0===Object.keys(a).length&&sy.remove(e))},t.dispatch=n,t.trigger=function(t,n,r,i){let s=t,o="string"==typeof t?t:t.type,a="string"==typeof t||null==s.namespace?[]:s.namespace.split(".");const l=r;if(3===l.nodeType||8===l.nodeType)return;o.indexOf(".")>-1&&(a=o.split("."),o=a.shift(),a.sort());const c=o.indexOf(":")<0&&`on${o}`;s=t instanceof ay?t:new ay(o,"object"==typeof t?t:null),s.namespace=a.join("."),s.rnamespace=s.namespace?new RegExp(`(^|\\.)${a.join("\\.(?:.*\\.|)")}(\\.|$)`):null,s.result=void 0,s.target||(s.target=l);const h=[s];Array.isArray(n)?h.push(...n):h.push(n);const u=iy.get(o);if(!i&&u.trigger&&!1===u.trigger(l,s,n))return;let g;const d=[l];if(!i&&!u.noBubble&&!oy.isWindow(l)){g=u.delegateType||o;let t=l,e=l.parentNode;for(;null!=e;)d.push(e),t=e,e=e.parentNode;if(t===(l.ownerDocument||document)){const e=t.defaultView||t.parentWindow||window;d.push(e)}}let f=l;for(let t=0,e=d.length;t<e&&!s.isPropagationStopped();t+=1){const e=d[t];f=e,s.type=t>1?g:u.bindType||o;const n=sy.get(e);n&&n.events[s.type]&&n.handler&&n.handler.call(e,...h);const r=c&&e[c]||null;r&&oy.isValidTarget(e)&&(s.result=r.call(e,...h),!1===s.result&&s.preventDefault())}if(s.type=o,!i&&!s.isDefaultPrevented()){const t=u.preventDefault;if((null==t||!1===t(d.pop(),s,n))&&oy.isValidTarget(l)&&c&&"function"==typeof l[o]&&!oy.isWindow(l)){const t=l[c];t&&(l[c]=null),e=o,s.isPropagationStopped()&&f.addEventListener(o,oy.stopPropagationCallback),l[o](),s.isPropagationStopped()&&f.removeEventListener(o,oy.stopPropagationCallback),e=void 0,t&&(l[c]=t)}}return s.result}}(ly||(ly={})),function(t){t.on=function(t,e,n,r,i){return hy.on(t,e,n,r,i),t},t.once=function(t,e,n,r,i){return hy.on(t,e,n,r,i,!0),t},t.off=function(t,e,n,r){return hy.off(t,e,n,r),t},t.trigger=function(t,e,n,r){return ly.trigger(e,n,t,r),t}}(cy||(cy={})),function(t){t.on=function e(n,r,i,s,o,a){if("object"==typeof r)return"string"!=typeof i&&(s=s||i,i=void 0),void Object.keys(r).forEach((t=>e(n,t,i,s,r[t],a)));if(null==s&&null==o?(o=i,s=i=void 0):null==o&&("string"==typeof i?(o=s,s=void 0):(o=s,s=i,i=void 0)),!1===o)o=oy.returnFalse;else if(!o)return;if(a){const e=o;o=function(r,...i){return t.off(n,r),e.call(this,r,...i)},oy.setHandlerId(o,oy.ensureHandlerId(e))}ly.on(n,r,o,s,i)},t.off=function t(e,n,r,i){const s=n;if(s&&null!=s.preventDefault&&null!=s.handleObj){const e=s.handleObj;t(s.delegateTarget,e.namespace?`${e.originType}.${e.namespace}`:e.originType,e.selector,e.handler)}else if("object"!=typeof n)!1!==r&&"function"!=typeof r||(i=r,r=void 0),!1===i&&(i=oy.returnFalse),ly.off(e,n,i,r);else{const i=n;Object.keys(i).forEach((n=>t(e,n,r,i[n])))}}}(hy||(hy={}));class gy{constructor(e,n,r){this.animationFrameId=0,this.deltaX=0,this.deltaY=0,this.eventName=t.Platform.isEventSupported("wheel")?"wheel":"mousewheel",this.target=e,this.onWheelCallback=n,this.onWheelGuard=r,this.onWheel=this.onWheel.bind(this),this.didWheel=this.didWheel.bind(this)}enable(){this.target.addEventListener(this.eventName,this.onWheel,{passive:!1})}disable(){this.target.removeEventListener(this.eventName,this.onWheel)}onWheel(t){if(null!=this.onWheelGuard&&!this.onWheelGuard(t))return;let e;this.deltaX+=t.deltaX,this.deltaY+=t.deltaY,t.preventDefault(),0===this.deltaX&&0===this.deltaY||(t.stopPropagation(),e=!0),!0===e&&0===this.animationFrameId&&(this.animationFrameId=requestAnimationFrame((()=>{this.didWheel(t)})))}didWheel(t){this.animationFrameId=0,this.onWheelCallback(t,this.deltaX,this.deltaY),this.deltaX=0,this.deltaY=0}}function dy(t){const e=t.getBoundingClientRect(),n=t.ownerDocument.defaultView;return{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}}var fy=Object.freeze({__proto__:null,CASE_SENSITIVE_ATTR:Bp,get Event(){return cy},get EventHook(){return iy},get EventObject(){return ay},KAPPA:Am,MouseWheelHandle:gy,addClass:ap,after:function(t,e){const n=t.parentNode;if(n){(Array.isArray(e)?e:[e]).forEach((e=>{null!=e&&n.insertBefore(e,t.nextSibling)}))}},append:Tp,appendTo:jp,attr:zp,before:Np,breakText:wm,children:function(t,e){const n=[];let r=t.firstChild;for(;r;r=r.nextSibling)1===r.nodeType&&(e&&!op(r,e)||n.push(r));return n},circleToPathData:km,clearSelection:nm,computeStyle:sm,computeStyleInt:om,contains:Ep,createElement:mp,createElementNS:yp,createSVGMatrix:$m,createSVGPoint:zm,createSVGTransform:Fm,createSlicePathData:function(t,e,n,r){const i=2*Math.PI-1e-6,s=t,o=e;let a=n,l=r;if(l<a){const t=a;a=l,l=t}const c=l-a,h=c<Math.PI?"0":"1",u=Math.cos(a),g=Math.sin(a),d=Math.cos(l),f=Math.sin(l);return c>=i?s?`M0,${o}A${o},${o} 0 1,1 0,${-o}A${o},${o} 0 1,1 0,${o}M0,${s}A${s},${s} 0 1,0 0,${-s}A${s},${s} 0 1,0 0,${s}Z`:`M0,${o}A${o},${o} 0 1,1 0,${-o}A${o},${o} 0 1,1 0,${o}Z`:s?`M${o*u},${o*g}A${o},${o} 0 ${h},1 ${o*d},${o*f}L${s*d},${s*f}A${s},${s} 0 ${h},0 ${s*u},${s*g}Z`:`M${o*u},${o*g}A${o},${o} 0 ${h},1 ${o*d},${o*f}L0,0Z`},createSvgDocument:bp,createSvgElement:vp,css:am,data:um,decomposeMatrix:Hm,ellipseToPathData:Nm,empty:Op,ensureId:gp,find:Pp,findOne:Cp,findParentByClass:Mp,getAttribute:Ip,getClass:sp,getComputedStyle:em,getData:cm,getPointsFromSvgElement:Tm,getTransformToElement:ey,getTransformToParentElement:ny,getVendorPrefixedName:tm,hasClass:op,hasScrollbars:function(t){const e=em(t);return null!=e&&("scroll"===e.overflow||"auto"===e.overflow)},height:function(t){return t.getBoundingClientRect().height},index:Ap,isCSSVariable:im,isElement:_p,isHTMLElement:Lp,isSVGGraphicsElement:dp,kebablizeAttrs:Fp,lineToPathData:Mm,matrixToRotation:Xm,matrixToScale:Jm,matrixToTransformString:Um,matrixToTranslation:Ym,measureText:bm,mergeAttrs:Up,ns:fp,offset:dy,parseTransformString:qm,parseXML:xp,polygonToPathData:Em,polylineToPathData:Sm,position:function(t){const e="fixed"===sm(t,"position");let n;if(e){const e=t.getBoundingClientRect();n={left:e.left,top:e.top}}else n=dy(t);if(!e){const e=t.ownerDocument;let r=t.offsetParent||e.documentElement;for(;(r===e.body||r===e.documentElement)&&"static"===sm(r,"position");)r=r.parentNode;if(r!==t&&_p(r)){const t=dy(r);n.top-=t.top+om(r,"borderTopWidth"),n.left-=t.left+om(r,"borderLeftWidth")}}return{top:n.top-om(t,"marginTop"),left:n.left-om(t,"marginLeft")}},prepend:kp,prop:function t(e,n,r){if(n){if("string"==typeof n)return n=gm[n]||n,arguments.length<3?e[n]:void(e[n]=r);for(const r in n)t(e,r,n[r])}},qualifyAttr:$p,rectToPathData:_m,rectangleToPathData:jm,remove:Sp,removeAttribute:Dp,removeClass:lp,rotate:Qm,sample:Cm,scale:ty,setAttribute:Rp,setAttributes:Vp,setData:hm,setPrefixedStyle:function(t,e,n){const r=tm(e);null!=r&&(t[r]=n),t[e]=n},splitTextByLength:xm,styleToObject:Gp,svgVersion:pp,tagName:wp,text:vm,toLocalPoint:ry,toPath:Lm,toPathData:Bm,toggleClass:cp,transform:Zm,transformStringToMatrix:Gm,translate:Km,uniqueId:up,width:function(t){return t.getBoundingClientRect().width}});function py(t,e=60){let n=null;return(...r)=>{n&&clearTimeout(n),n=window.setTimeout((()=>{t.apply(this,r)}),e)}}const my="undefined"!=typeof ResizeObserver?function(t){let e=null,n=[];const r=py((()=>{n.forEach((e=>{e(t)}))})),i=()=>{e&&(e.disconnect(),n=[],e=null)};return{element:t,bind:i=>{e||(e=(()=>{const e=new ResizeObserver(r);return e.observe(t),r(),e})()),-1===n.indexOf(i)&&n.push(i)},destroy:i,unbind:t=>{const r=n.indexOf(t);-1!==r&&n.splice(r,1),0===n.length&&e&&i()}}}:function(t){let e=null,n=[];const r=py((()=>{n.forEach((e=>e(t)))})),i=()=>{e&&e.parentNode&&(e.contentDocument&&e.contentDocument.defaultView.removeEventListener("resize",r),e.parentNode.removeChild(e),e=null,n=[])};return{element:t,bind:i=>{e||(e=(()=>{"static"===getComputedStyle(t).position&&(t.style.position="relative");const e=document.createElement("object");return e.onload=()=>{e.contentDocument.defaultView.addEventListener("resize",r),r()},e.style.display="block",e.style.position="absolute",e.style.top="0",e.style.left="0",e.style.height="100%",e.style.width="100%",e.style.overflow="hidden",e.style.pointerEvents="none",e.style.zIndex="-1",e.style.opacity="0",e.setAttribute("tabindex","-1"),e.type="text/html",t.appendChild(e),e.data="about:blank",e})()),-1===n.indexOf(i)&&n.push(i)},destroy:i,unbind:t=>{const r=n.indexOf(t);-1!==r&&n.splice(r,1),0===n.length&&e&&i()}}};t.SizeSensor=void 0,function(t){const e=new WeakMap;function n(t){let n=e.get(t);return n||(n=my(t),e.set(t,n),n)}t.bind=(t,e)=>{const r=n(t);return r.bind(e),()=>r.unbind(e)},t.clear=t=>{!function(t){t.destroy(),e.delete(t.element)}(n(t))}}(t.SizeSensor||(t.SizeSensor={}));class yy{constructor(t={}){this.comparator=t.comparator||yy.defaultComparator,this.index={},this.data=t.data||[],this.heapify()}isEmpty(){return 0===this.data.length}insert(t,e,n){const r={priority:t,value:e},i=this.data.length;return n&&(r.id=n,this.index[n]=i),this.data.push(r),this.bubbleUp(i),this}peek(){return this.data[0]?this.data[0].value:null}peekPriority(){return this.data[0]?this.data[0].priority:null}updatePriority(t,e){const n=this.index[t];if(void 0===n)throw new Error(`Node with id '${t}' was not found in the heap.`);const r=this.data,i=r[n].priority,s=this.comparator(e,i);s<0?(r[n].priority=e,this.bubbleUp(n)):s>0&&(r[n].priority=e,this.bubbleDown(n))}remove(){const t=this.data,e=t[0],n=t.pop();return e.id&&delete this.index[e.id],t.length>0&&(t[0]=n,n.id&&(this.index[n.id]=0),this.bubbleDown(0)),e?e.value:null}heapify(){for(let t=0;t<this.data.length;t+=1)this.bubbleUp(t)}bubbleUp(t){const e=this.data;let n,r,i=t;for(;i>0&&(r=i-1>>>1,this.comparator(e[i].priority,e[r].priority)<0);){n=e[r],e[r]=e[i];let t=e[i].id;null!=t&&(this.index[t]=r),e[i]=n,t=e[i].id,null!=t&&(this.index[t]=i),i=r}}bubbleDown(t){const e=this.data,n=e.length-1;let r=t;for(;;){const t=1+(r<<1),i=t+1;let s=r;if(t<=n&&this.comparator(e[t].priority,e[s].priority)<0&&(s=t),i<=n&&this.comparator(e[i].priority,e[s].priority)<0&&(s=i),s===r)break;{const t=e[s];e[s]=e[r];let n=e[r].id;null!=n&&(this.index[n]=s),e[r]=t,n=e[r].id,null!=n&&(this.index[n]=r),r=s}}}}var vy,by;!function(t){t.defaultComparator=(t,e)=>t-e}(yy||(yy={})),t.Dijkstra=void 0,(t.Dijkstra||(t.Dijkstra={})).run=function(t,e,n=((t,e)=>1)){const r={},i={},s={},o=new yy;for(r[e]=0,Object.keys(t).forEach((t=>{t!==e&&(r[t]=1/0),o.insert(r[t],t,t)}));!o.isEmpty();){const e=o.remove();s[e]=!0;const a=t[e]||[];for(let t=0;t<a.length;t+=1){const l=a[t];if(!s[l]){const t=r[e]+n(e,l);t<r[l]&&(r[l]=t,i[l]=e,o.updatePriority(l,t))}}}return i};class xy{constructor(t,e,n,r){return null==t?this.set(255,255,255,1):"number"==typeof t?this.set(t,e,n,r):"string"==typeof t?xy.fromString(t)||this:Array.isArray(t)?this.set(t):void this.set(t.r,t.g,t.b,null==t.a?1:t.a)}blend(t,e,n){this.set(t.r+(e.r-t.r)*n,t.g+(e.g-t.g)*n,t.b+(e.b-t.b)*n,t.a+(e.a-t.a)*n)}lighten(t){const e=xy.lighten(this.toArray(),t);this.r=e[0],this.g=e[1],this.b=e[2],this.a=e[3]}darken(t){this.lighten(-t)}set(t,e,n,r){const i=Array.isArray(t)?t[0]:t,s=Array.isArray(t)?t[1]:e,o=Array.isArray(t)?t[2]:n,a=Array.isArray(t)?t[3]:r;return this.r=Math.round(Hi(i,0,255)),this.g=Math.round(Hi(s,0,255)),this.b=Math.round(Hi(o,0,255)),this.a=null==a?1:Hi(a,0,1),this}toHex(){return`#${["r","g","b"].map((t=>{const e=this[t].toString(16);return e.length<2?`0${e}`:e})).join("")}`}toRGBA(){return this.toArray()}toHSLA(){return xy.rgba2hsla(this.r,this.g,this.b,this.a)}toCSS(t){const e=`${this.r},${this.g},${this.b},`;return t?`rgb(${e})`:`rgba(${e},${this.a})`}toGrey(){return xy.makeGrey(Math.round((this.r+this.g+this.b)/3),this.a)}toArray(){return[this.r,this.g,this.b,this.a]}toString(){return this.toCSS()}}!function(t){function e(e){return new t([...a(e),1])}function n(e){const n=e.toLowerCase().match(/^rgba?\(([\s.,0-9]+)\)/);if(n){const e=n[1].split(/\s*,\s*/).map((t=>parseInt(t,10)));return new t(e)}return null}function r(t,e,n){n<0&&++n,n>1&&--n;const r=6*n;return r<1?t+(e-t)*r:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function i(e){const n=e.toLowerCase().match(/^hsla?\(([\s.,0-9]+)\)/);if(n){const e=n[2].split(/\s*,\s*/),r=(parseFloat(e[0])%360+360)%360/360,i=parseFloat(e[1])/100,o=parseFloat(e[2])/100,a=null==e[3]?1:parseInt(e[3],10);return new t(s(r,i,o,a))}return null}function s(t,e,n,i){const s=Array.isArray(t)?t[0]:t,o=Array.isArray(t)?t[1]:e,a=Array.isArray(t)?t[2]:n,l=Array.isArray(t)?t[3]:i,c=a<=.5?a*(o+1):a+o-a*o,h=2*a-c;return[256*r(h,c,s+1/3),256*r(h,c,s),256*r(h,c,s-1/3),null==l?1:l]}function o(e){return new t(Math.round(256*Math.random()),Math.round(256*Math.random()),Math.round(256*Math.random()),e?void 0:parseFloat(Math.random().toFixed(2)))}function a(t){const e=0===t.indexOf("#")?t:`#${t}`;let n=Number(`0x${e.substr(1)}`);if(4!==e.length&&7!==e.length||Number.isNaN(n))throw new Error("Invalid hex color.");const r=4===e.length?4:8,i=(1<<r)-1,s=["b","g","r"].map((()=>{const t=n&i;return n>>=r,4===r?17*t:t}));return[s[2],s[1],s[0]]}function l(t,e,n){const r=t=>t.length<2?`0${t}`:t;return`${r(t.toString(16))}${r(e.toString(16))}${r(n.toString(16))}`}function c(t,e){if("string"==typeof t){const n="#"===t[0],r=parseInt(n?t.substr(1):t,16),i=Hi((r>>16)+e,0,255),s=Hi((r>>8&255)+e,0,255);return`${n?"#":""}${(Hi((255&r)+e,0,255)|s<<8|i<<16).toString(16)}`}const n=a(c(l(t[0],t[1],t[2]),e));return[n[0],n[1],n[2],t[3]]}t.fromArray=function(e){return new t(e)},t.fromHex=e,t.fromRGBA=n,t.fromHSLA=i,t.fromString=function(r){if(r.startsWith("#"))return e(r);if(r.startsWith("rgb"))return n(r);const s=t.named[r];return s?e(s):i(r)},t.makeGrey=function(e,n){return t.fromArray([e,e,e,n])},t.rgba2hsla=function(t,e,n,r){const i=Array.isArray(t)?t[0]:t,s=Array.isArray(t)?t[1]:e,o=Array.isArray(t)?t[2]:n,a=Array.isArray(t)?t[3]:r,l=Math.max(i,s,o),c=Math.min(i,s,o),h=(l+c)/2;let u=0,g=0;if(c!==l){const t=l-c;switch(g=h>.5?t/(2-l-c):t/(l+c),l){case i:u=(s-o)/t+(s<o?6:0);break;case s:u=(o-i)/t+2;break;case o:u=(i-s)/t+4}u/=6}return[u,g,h,null==a?1:a]},t.hsla2rgba=s,t.random=o,t.randomHex=function(){let t="#";for(let e=0;e<6;e+=1)t+="0123456789ABCDEF"[Math.floor(16*Math.random())];return t},t.randomRGBA=function(t){return o(t).toString()},t.invert=function(t,e){if("string"==typeof t){const n="#"===t[0],[r,i,s]=a(t);return e?.299*r+.587*i+.114*s>186?"#000000":"#ffffff":`${n?"#":""}${l(255-r,255-i,255-s)}`}const n=t[0],r=t[1],i=t[2],s=t[3];return e?.299*n+.587*r+.114*i>186?[0,0,0,s]:[255,255,255,s]:[255-n,255-r,255-i,s]},t.lighten=function(t,e){return c(t,e)},t.darken=function(t,e){return c(t,-e)}}(xy||(xy={})),function(t){t.named={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",burntsienna:"#ea7e5d",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"}}(xy||(xy={}));class wy{constructor(){this.clear()}clear(){this.map=new WeakMap,this.arr=[]}has(t){return this.map.has(t)}get(t){return this.map.get(t)}set(t,e){this.map.set(t,e),this.arr.push(t)}delete(t){const e=this.arr.indexOf(t);e>=0&&this.arr.splice(e,1);const n=this.map.get(t);return this.map.delete(t),n}each(t){this.arr.forEach((e=>{const n=this.map.get(e);t(n,e)}))}dispose(){this.clear()}}t.ModifierKey=void 0,function(t){function e(t){const e=[],n=[];return Array.isArray(t)?e.push(...t):t.split("|").forEach((t=>{-1===t.indexOf("&")?e.push(t):n.push(...t.split("&"))})),{or:e,and:n}}t.parse=e,t.equals=function(t,n){if(null!=t&&null!=n){const r=e(t),i=e(n),s=r.or.sort(),o=i.or.sort(),a=r.and.sort(),l=i.and.sort(),c=(t,e)=>t.length===e.length&&(0===t.length||t.every(((t,n)=>t===e[n])));return c(s,o)&&c(a,l)}return null==t&&null==n},t.isMatch=function(t,n,r){if(null==n||Array.isArray(n)&&0===n.length)return!r||!0!==t.altKey&&!0!==t.ctrlKey&&!0!==t.metaKey&&!0!==t.shiftKey;const{or:i,and:s}=e(n),o=e=>{const n=`${e.toLowerCase()}Key`;return!0===t[n]};return i.some((t=>o(t)))&&s.every((t=>o(t)))}}(t.ModifierKey||(t.ModifierKey={})),t.Timing=void 0,(vy=t.Timing||(t.Timing={})).linear=t=>t,vy.quad=t=>t*t,vy.cubic=t=>t*t*t,vy.inout=t=>{if(t<=0)return 0;if(t>=1)return 1;const e=t*t,n=e*t;return 4*(t<.5?n:3*(t-e)+n-.75)},vy.exponential=t=>Math.pow(2,10*(t-1)),vy.bounce=t=>{for(let e=0,n=1;;e+=n,n/=2)if(t>=(7-4*e)/11){const r=(11-6*e-11*t)/4;return-r*r+n*n}},function(t){t.decorators={reverse:t=>e=>1-t(1-e),reflect:t=>e=>.5*(e<.5?t(2*e):2-t(2-2*e)),clamp:(t,e=0,n=1)=>r=>{const i=t(r);return i<e?e:i>n?n:i},back:(t=1.70158)=>e=>e*e*((t+1)*e-t),elastic:(t=1.5)=>e=>Math.pow(2,10*(e-1))*Math.cos(20*Math.PI*t/3*e)}}(t.Timing||(t.Timing={})),function(t){function e(t){const e=t/1;if(e<1/2.75)return 7.5625*e*e;if(e<2/2.75){const t=e-1.5/2.75;return 7.5625*t*t+.75}if(e<2.5/2.75){const t=e-2.25/2.75;return 7.5625*t*t+.9375}{const t=e-2.625/2.75;return 7.5625*t*t+.984375}}function n(t){return 1-e(1-t)}t.easeInSine=function(t){return-1*Math.cos(t*(Math.PI/2))+1},t.easeOutSine=function(t){return Math.sin(t*(Math.PI/2))},t.easeInOutSine=function(t){return-.5*(Math.cos(Math.PI*t)-1)},t.easeInQuad=function(t){return t*t},t.easeOutQuad=function(t){return t*(2-t)},t.easeInOutQuad=function(t){return t<.5?2*t*t:(4-2*t)*t-1},t.easeInCubic=function(t){return t*t*t},t.easeOutCubic=function(t){const e=t-1;return e*e*e+1},t.easeInOutCubic=function(t){return t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1},t.easeInQuart=function(t){return t*t*t*t},t.easeOutQuart=function(t){const e=t-1;return 1-e*e*e*e},t.easeInOutQuart=function(t){const e=t-1;return t<.5?8*t*t*t*t:1-8*e*e*e*e},t.easeInQuint=function(t){return t*t*t*t*t},t.easeOutQuint=function(t){const e=t-1;return 1+e*e*e*e*e},t.easeInOutQuint=function(t){const e=t-1;return t<.5?16*t*t*t*t*t:1+16*e*e*e*e*e},t.easeInExpo=function(t){return 0===t?0:Math.pow(2,10*(t-1))},t.easeOutExpo=function(t){return 1===t?1:1-Math.pow(2,-10*t)},t.easeInOutExpo=function(t){if(0===t||1===t)return t;const e=2*t,n=e-1;return e<1?.5*Math.pow(2,10*n):.5*(2-Math.pow(2,-10*n))},t.easeInCirc=function(t){const e=t/1;return-1*(Math.sqrt(1-e*t)-1)},t.easeOutCirc=function(t){const e=t-1;return Math.sqrt(1-e*e)},t.easeInOutCirc=function(t){const e=2*t,n=e-2;return e<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-n*n)+1)},t.easeInBack=function(t,e=1.70158){return t*t*((e+1)*t-e)},t.easeOutBack=function(t,e=1.70158){const n=t/1-1;return n*n*((e+1)*n+e)+1},t.easeInOutBack=function(t,e=1.70158){const n=2*t,r=n-2,i=1.525*e;return n<1?.5*n*n*((i+1)*n-i):.5*(r*r*((i+1)*r+i)+2)},t.easeInElastic=function(t,e=.7){if(0===t||1===t)return t;const n=t/1-1,r=1-e,i=r/(2*Math.PI)*Math.asin(1);return-Math.pow(2,10*n)*Math.sin((n-i)*(2*Math.PI)/r)},t.easeOutElastic=function(t,e=.7){const n=1-e,r=2*t;if(0===t||1===t)return t;const i=n/(2*Math.PI)*Math.asin(1);return Math.pow(2,-10*r)*Math.sin((r-i)*(2*Math.PI)/n)+1},t.easeInOutElastic=function(t,e=.65){const n=1-e;if(0===t||1===t)return t;const r=2*t,i=r-1,s=n/(2*Math.PI)*Math.asin(1);return r<1?Math.pow(2,10*i)*Math.sin((i-s)*(2*Math.PI)/n)*-.5:Math.pow(2,-10*i)*Math.sin((i-s)*(2*Math.PI)/n)*.5+1},t.easeOutBounce=e,t.easeInBounce=n,t.easeInOutBounce=function(t){return t<.5?.5*n(2*t):.5*e(2*t-1)+.5}}(t.Timing||(t.Timing={})),t.Interp=void 0,(by=t.Interp||(t.Interp={})).number=(t,e)=>{const n=e-t;return e=>t+n*e},by.object=(t,e)=>{const n=Object.keys(t);return r=>{const i={};for(let s=n.length-1;-1!==s;s-=1){const o=n[s];i[o]=t[o]+(e[o]-t[o])*r}return i}},by.unit=(t,e)=>{const n=/(-?[0-9]*.[0-9]*)(px|em|cm|mm|in|pt|pc|%)/,r=n.exec(t),i=n.exec(e),s=i?i[1]:"",o=r?+r[1]:0,a=i?+i[1]:0,l=s.indexOf("."),c=l>0?s[1].length-l-1:0,h=a-o,u=r?r[2]:"";return t=>(o+h*t).toFixed(c)+u},by.color=(t,e)=>{const n=parseInt(t.slice(1),16),r=parseInt(e.slice(1),16),i=255&n,s=(255&r)-i,o=65280&n,a=(65280&r)-o,l=16711680&n,c=(16711680&r)-l;return t=>`#${(1<<24|i+s*t&255|o+a*t&65280|l+c*t&16711680).toString(16).slice(1)}`};const Ay=[];function Py(e,n){const r=Ay.find((t=>t.name===e));if(!(r&&(r.loadTimes+=1,r.loadTimes>1)||t.Platform.isApplyingHMR())){const t=document.createElement("style");t.setAttribute("type","text/css"),t.textContent=n;const r=document.querySelector("head");r&&r.insertBefore(t,r.firstChild),Ay.push({name:e,loadTimes:1,styleElement:t})}}function Cy(t){const e=Ay.findIndex((e=>e.name===t));if(e>-1){const t=Ay[e];if(t.loadTimes-=1,t.loadTimes>0)return;let n=t.styleElement;n&&n.parentNode&&n.parentNode.removeChild(n),n=null,Ay.splice(e,1)}}var My,Ey,Sy=Object.freeze({__proto__:null,clean:Cy,ensure:Py});t.Angle=void 0,(My=t.Angle||(t.Angle={})).toDeg=function(t){return 180*t/Math.PI%360},My.toRad=function(t,e=!1){return(e?t:t%360)*Math.PI/180},My.normalize=function(t){return t%360+(t<0?360:0)},t.GeometryUtil=void 0,(Ey=t.GeometryUtil||(t.GeometryUtil={})).round=function(t,e=0){return Number.isInteger(t)?t:+t.toFixed(e)},Ey.random=function(t,e){let n,r;if(null==e?(r=null==t?1:t,n=0):(r=e,n=null==t?0:t),r<n){const t=n;n=r,r=t}return Math.floor(Math.random()*(r-n+1)+n)},Ey.clamp=function(t,e,n){return Number.isNaN(t)?NaN:Number.isNaN(e)||Number.isNaN(n)?0:e<n?t<e?e:t>n?n:t:t<n?n:t>e?e:t},Ey.snapToGrid=function(t,e){return e*Math.round(t/e)},Ey.containsPoint=function(t,e){return null!=e&&null!=t&&e.x>=t.x&&e.x<=t.x+t.width&&e.y>=t.y&&e.y<=t.y+t.height},Ey.squaredLength=function(t,e){const n=t.x-e.x,r=t.y-e.y;return n*n+r*r};class Oy{valueOf(){return this.toJSON()}toString(){return JSON.stringify(this.toJSON())}}class Ty extends Oy{constructor(t,e){super(),this.x=null==t?0:t,this.y=null==e?0:e}round(e=0){return this.x=t.GeometryUtil.round(this.x,e),this.y=t.GeometryUtil.round(this.y,e),this}add(t,e){const n=Ty.create(t,e);return this.x+=n.x,this.y+=n.y,this}update(t,e){const n=Ty.create(t,e);return this.x=n.x,this.y=n.y,this}translate(t,e){const n=Ty.create(t,e);return this.x+=n.x,this.y+=n.y,this}rotate(t,e){const n=Ty.rotate(this,t,e);return this.x=n.x,this.y=n.y,this}scale(t,e,n=new Ty){const r=Ty.create(n);return this.x=r.x+t*(this.x-r.x),this.y=r.y+e*(this.y-r.y),this}closest(t){if(1===t.length)return Ty.create(t[0]);let e=null,n=1/0;return t.forEach((t=>{const r=this.squaredDistance(t);r<n&&(e=t,n=r)})),e?Ty.create(e):null}distance(t){return Math.sqrt(this.squaredDistance(t))}squaredDistance(t){const e=Ty.create(t),n=this.x-e.x,r=this.y-e.y;return n*n+r*r}manhattanDistance(t){const e=Ty.create(t);return Math.abs(e.x-this.x)+Math.abs(e.y-this.y)}magnitude(){return Math.sqrt(this.x*this.x+this.y*this.y)||.01}theta(t=new Ty){const e=Ty.create(t),n=-(e.y-this.y),r=e.x-this.x;let i=Math.atan2(n,r);return i<0&&(i=2*Math.PI+i),180*i/Math.PI}angleBetween(t,e){if(this.equals(t)||this.equals(e))return NaN;let n=this.theta(e)-this.theta(t);return n<0&&(n+=360),n}vectorAngle(t){return new Ty(0,0).angleBetween(this,t)}toPolar(t){return this.update(Ty.toPolar(this,t)),this}changeInAngle(t,e,n=new Ty){return this.clone().translate(-t,-e).theta(n)-this.theta(n)}adhereToRect(e){return t.GeometryUtil.containsPoint(e,this)||(this.x=Math.min(Math.max(this.x,e.x),e.x+e.width),this.y=Math.min(Math.max(this.y,e.y),e.y+e.height)),this}bearing(e){const n=Ty.create(e),r=t.Angle.toRad(this.y),i=t.Angle.toRad(n.y),s=this.x,o=n.x,a=t.Angle.toRad(o-s),l=Math.sin(a)*Math.cos(i),c=Math.cos(r)*Math.sin(i)-Math.sin(r)*Math.cos(i)*Math.cos(a);let h=t.Angle.toDeg(Math.atan2(l,c))-22.5;return h<0&&(h+=360),h=parseInt(h/45,10),["NE","E","SE","S","SW","W","NW","N"][h]}cross(t,e){if(null!=t&&null!=e){const n=Ty.create(t),r=Ty.create(e);return(r.x-this.x)*(n.y-this.y)-(r.y-this.y)*(n.x-this.x)}return NaN}dot(t){const e=Ty.create(t);return this.x*e.x+this.y*e.y}diff(t,e){if("number"==typeof t)return new Ty(this.x-t,this.y-e);const n=Ty.create(t);return new Ty(this.x-n.x,this.y-n.y)}lerp(t,e){const n=Ty.create(t);return new Ty((1-e)*this.x+e*n.x,(1-e)*this.y+e*n.y)}normalize(t=1){const e=t/this.magnitude();return this.scale(e,e)}move(e,n){const r=Ty.create(e),i=t.Angle.toRad(r.theta(this));return this.translate(Math.cos(i)*n,-Math.sin(i)*n)}reflection(t){return Ty.create(t).move(this,this.distance(t))}snapToGrid(e,n){return this.x=t.GeometryUtil.snapToGrid(this.x,e),this.y=t.GeometryUtil.snapToGrid(this.y,null==n?e:n),this}equals(t){const e=Ty.create(t);return null!=e&&e.x===this.x&&e.y===this.y}clone(){return Ty.clone(this)}toJSON(){return Ty.toJSON(this)}serialize(){return`${this.x} ${this.y}`}}!function(t){t.isPoint=function(e){return null!=e&&e instanceof t}}(Ty||(Ty={})),function(t){t.isPointLike=function(t){return null!=t&&"object"==typeof t&&"number"==typeof t.x&&"number"==typeof t.y},t.isPointData=function(t){return null!=t&&Array.isArray(t)&&2===t.length&&"number"==typeof t[0]&&"number"==typeof t[1]}}(Ty||(Ty={})),function(e){function n(t){return e.isPoint(t)?new e(t.x,t.y):Array.isArray(t)?new e(t[0],t[1]):new e(t.x,t.y)}function r(t,e){return t===e||null!=t&&null!=e&&(t.x===e.x&&t.y===e.y)}function i(t,r,i,s=new e){const o=n(t),a=n(s),l=o.x-a.x,c=o.y-a.y,h=c*r+l*i;return new e(l*r-c*i+a.x,h+a.y)}e.create=function(t,r){return null==t||"number"==typeof t?new e(t,r):n(t)},e.clone=n,e.toJSON=function(t){return e.isPoint(t)?{x:t.x,y:t.y}:Array.isArray(t)?{x:t[0],y:t[1]}:{x:t.x,y:t.y}},e.fromPolar=function(r,i,s=new e){let o=Math.abs(r*Math.cos(i)),a=Math.abs(r*Math.sin(i));const l=n(s),c=t.Angle.normalize(t.Angle.toDeg(i));return c<90?a=-a:c<180?(o=-o,a=-a):c<270&&(o=-o),new e(l.x+o,l.y+a)},e.toPolar=function(r,i=new e){const s=n(r),o=n(i),a=s.x-o.x,l=s.y-o.y;return new e(Math.sqrt(a*a+l*l),t.Angle.toRad(o.theta(s)))},e.equals=r,e.equalPoints=function(t,e){if(null==t&&null!=e||null!=t&&null==e||null!=t&&null!=e&&t.length!==e.length)return!1;if(null!=t&&null!=e)for(let n=0,i=t.length;n<i;n+=1)if(!r(t[n],e[n]))return!1;return!0},e.random=function(n,r,i,s){return new e(t.GeometryUtil.random(n,r),t.GeometryUtil.random(i,s))},e.rotate=function(e,n,r){const s=t.Angle.toRad(t.Angle.normalize(-n)),o=Math.sin(s);return i(e,Math.cos(s),o,r)},e.rotateEx=i}(Ty||(Ty={}));class ky extends Oy{get left(){return this.x}get top(){return this.y}get right(){return this.x+this.width}get bottom(){return this.y+this.height}get origin(){return new Ty(this.x,this.y)}get topLeft(){return new Ty(this.x,this.y)}get topCenter(){return new Ty(this.x+this.width/2,this.y)}get topRight(){return new Ty(this.x+this.width,this.y)}get center(){return new Ty(this.x+this.width/2,this.y+this.height/2)}get bottomLeft(){return new Ty(this.x,this.y+this.height)}get bottomCenter(){return new Ty(this.x+this.width/2,this.y+this.height)}get bottomRight(){return new Ty(this.x+this.width,this.y+this.height)}get corner(){return new Ty(this.x+this.width,this.y+this.height)}get rightMiddle(){return new Ty(this.x+this.width,this.y+this.height/2)}get leftMiddle(){return new Ty(this.x,this.y+this.height/2)}get topLine(){return new Ny(this.topLeft,this.topRight)}get rightLine(){return new Ny(this.topRight,this.bottomRight)}get bottomLine(){return new Ny(this.bottomLeft,this.bottomRight)}get leftLine(){return new Ny(this.topLeft,this.bottomLeft)}constructor(t,e,n,r){super(),this.x=null==t?0:t,this.y=null==e?0:e,this.width=null==n?0:n,this.height=null==r?0:r}getOrigin(){return this.origin}getTopLeft(){return this.topLeft}getTopCenter(){return this.topCenter}getTopRight(){return this.topRight}getCenter(){return this.center}getCenterX(){return this.x+this.width/2}getCenterY(){return this.y+this.height/2}getBottomLeft(){return this.bottomLeft}getBottomCenter(){return this.bottomCenter}getBottomRight(){return this.bottomRight}getCorner(){return this.corner}getRightMiddle(){return this.rightMiddle}getLeftMiddle(){return this.leftMiddle}getTopLine(){return this.topLine}getRightLine(){return this.rightLine}getBottomLine(){return this.bottomLine}getLeftLine(){return this.leftLine}bbox(e){if(!e)return this.clone();const n=t.Angle.toRad(e),r=Math.abs(Math.sin(n)),i=Math.abs(Math.cos(n)),s=this.width*i+this.height*r,o=this.width*r+this.height*i;return new ky(this.x+(this.width-s)/2,this.y+(this.height-o)/2,s,o)}round(e=0){return this.x=t.GeometryUtil.round(this.x,e),this.y=t.GeometryUtil.round(this.y,e),this.width=t.GeometryUtil.round(this.width,e),this.height=t.GeometryUtil.round(this.height,e),this}add(t,e,n,r){const i=ky.create(t,e,n,r),s=Math.min(this.x,i.x),o=Math.min(this.y,i.y),a=Math.max(this.x+this.width,i.x+i.width),l=Math.max(this.y+this.height,i.y+i.height);return this.x=s,this.y=o,this.width=a-s,this.height=l-o,this}update(t,e,n,r){const i=ky.create(t,e,n,r);return this.x=i.x,this.y=i.y,this.width=i.width,this.height=i.height,this}inflate(t,e){const n=t,r=null!=e?e:t;return this.x-=n,this.y-=r,this.width+=2*n,this.height+=2*r,this}snapToGrid(t,e){const n=this.origin.snapToGrid(t,e),r=this.corner.snapToGrid(t,e);return this.x=n.x,this.y=n.y,this.width=r.x-n.x,this.height=r.y-n.y,this}translate(t,e){const n=Ty.create(t,e);return this.x+=n.x,this.y+=n.y,this}scale(t,e,n=new Ty){const r=this.origin.scale(t,e,n);return this.x=r.x,this.y=r.y,this.width*=t,this.height*=e,this}rotate(e,n=this.getCenter()){if(0!==e){const r=t.Angle.toRad(e),i=Math.cos(r),s=Math.sin(r);let o=this.getOrigin(),a=this.getTopRight(),l=this.getBottomRight(),c=this.getBottomLeft();o=Ty.rotateEx(o,i,s,n),a=Ty.rotateEx(a,i,s,n),l=Ty.rotateEx(l,i,s,n),c=Ty.rotateEx(c,i,s,n);const h=new ky(o.x,o.y,0,0);h.add(a.x,a.y,0,0),h.add(l.x,l.y,0,0),h.add(c.x,c.y,0,0),this.update(h)}return this}rotate90(){const t=(this.width-this.height)/2;this.x+=t,this.y-=t;const e=this.width;return this.width=this.height,this.height=e,this}moveAndExpand(t){const e=ky.clone(t);return this.x+=e.x||0,this.y+=e.y||0,this.width+=e.width||0,this.height+=e.height||0,this}getMaxScaleToFit(t,e=this.center){const n=ky.clone(t),r=e.x,i=e.y;let s=1/0,o=1/0,a=1/0,l=1/0,c=1/0,h=1/0,u=1/0,g=1/0;const d=n.topLeft;d.x<r&&(s=(this.x-r)/(d.x-r)),d.y<i&&(c=(this.y-i)/(d.y-i));const f=n.bottomRight;f.x>r&&(o=(this.x+this.width-r)/(f.x-r)),f.y>i&&(h=(this.y+this.height-i)/(f.y-i));const p=n.topRight;p.x>r&&(a=(this.x+this.width-r)/(p.x-r)),p.y<i&&(u=(this.y-i)/(p.y-i));const m=n.bottomLeft;return m.x<r&&(l=(this.x-r)/(m.x-r)),m.y>i&&(g=(this.y+this.height-i)/(m.y-i)),{sx:Math.min(s,o,a,l),sy:Math.min(c,h,u,g)}}getMaxUniformScaleToFit(t,e=this.center){const n=this.getMaxScaleToFit(t,e);return Math.min(n.sx,n.sy)}containsPoint(e,n){return t.GeometryUtil.containsPoint(this,Ty.create(e,n))}containsRect(t,e,n,r){const i=ky.create(t,e,n,r),s=this.x,o=this.y,a=this.width,l=this.height,c=i.x,h=i.y,u=i.width,g=i.height;return 0!==a&&0!==l&&0!==u&&0!==g&&(c>=s&&h>=o&&c+u<=s+a&&h+g<=o+l)}intersectsWithLine(t){const e=[this.topLine,this.rightLine,this.bottomLine,this.leftLine],n=[],r=[];return e.forEach((e=>{const i=t.intersectsWithLine(e);null!==i&&r.indexOf(i.toString())<0&&(n.push(i),r.push(i.toString()))})),n.length>0?n:null}intersectsWithLineFromCenterToPoint(t,e){const n=Ty.clone(t),r=this.center;let i=null;null!=e&&0!==e&&n.rotate(e,r);const s=[this.topLine,this.rightLine,this.bottomLine,this.leftLine],o=new Ny(r,n);for(let t=s.length-1;t>=0;t-=1){const e=s[t].intersectsWithLine(o);if(null!==e){i=e;break}}return i&&null!=e&&0!==e&&i.rotate(-e,r),i}intersectsWithRect(t,e,n,r){const i=ky.create(t,e,n,r);if(!this.isIntersectWithRect(i))return null;const s=this.origin,o=this.corner,a=i.origin,l=i.corner,c=Math.max(s.x,a.x),h=Math.max(s.y,a.y);return new ky(c,h,Math.min(o.x,l.x)-c,Math.min(o.y,l.y)-h)}isIntersectWithRect(t,e,n,r){const i=ky.create(t,e,n,r),s=this.origin,o=this.corner,a=i.origin,l=i.corner;return!(l.x<=s.x||l.y<=s.y||a.x>=o.x||a.y>=o.y)}normalize(){let t=this.x,e=this.y,n=this.width,r=this.height;return this.width<0&&(t=this.x+this.width,n=-this.width),this.height<0&&(e=this.y+this.height,r=-this.height),this.x=t,this.y=e,this.width=n,this.height=r,this}union(t){const e=ky.clone(t),n=this.origin,r=this.corner,i=e.origin,s=e.corner,o=Math.min(n.x,i.x),a=Math.min(n.y,i.y),l=Math.max(r.x,s.x),c=Math.max(r.y,s.y);return new ky(o,a,l-o,c-a)}getNearestSideToPoint(t){const e=Ty.clone(t),n=e.x-this.x,r=this.x+this.width-e.x,i=e.y-this.y;let s=n,o="left";return r<s&&(s=r,o="right"),i<s&&(s=i,o="top"),this.y+this.height-e.y<s&&(o="bottom"),o}getNearestPointToPoint(t){const e=Ty.clone(t);if(this.containsPoint(e)){const t=this.getNearestSideToPoint(e);if("left"===t)return new Ty(this.x,e.y);if("top"===t)return new Ty(e.x,this.y);if("right"===t)return new Ty(this.x+this.width,e.y);if("bottom"===t)return new Ty(e.x,this.y+this.height)}return e.adhereToRect(this)}equals(t){return null!=t&&t.x===this.x&&t.y===this.y&&t.width===this.width&&t.height===this.height}clone(){return new ky(this.x,this.y,this.width,this.height)}toJSON(){return{x:this.x,y:this.y,width:this.width,height:this.height}}serialize(){return`${this.x} ${this.y} ${this.width} ${this.height}`}}!function(t){t.isRectangle=function(e){return null!=e&&e instanceof t}}(ky||(ky={})),function(t){t.isRectangleLike=function(t){return null!=t&&"object"==typeof t&&"number"==typeof t.x&&"number"==typeof t.y&&"number"==typeof t.width&&"number"==typeof t.height}}(ky||(ky={})),function(t){function e(e){return t.isRectangle(e)?e.clone():Array.isArray(e)?new t(e[0],e[1],e[2],e[3]):new t(e.x,e.y,e.width,e.height)}t.create=function(n,r,i,s){return null==n||"number"==typeof n?new t(n,r,i,s):e(n)},t.clone=e,t.fromEllipse=function(e){return new t(e.x-e.a,e.y-e.b,2*e.a,2*e.b)},t.fromSize=function(e){return new t(0,0,e.width,e.height)},t.fromPositionAndSize=function(e,n){return new t(e.x,e.y,n.width,n.height)}}(ky||(ky={}));class Ny extends Oy{get center(){return new Ty((this.start.x+this.end.x)/2,(this.start.y+this.end.y)/2)}constructor(t,e,n,r){super(),"number"==typeof t&&"number"==typeof e?(this.start=new Ty(t,e),this.end=new Ty(n,r)):(this.start=Ty.create(t),this.end=Ty.create(e))}getCenter(){return this.center}round(t=0){return this.start.round(t),this.end.round(t),this}translate(t,e){return"number"==typeof t?(this.start.translate(t,e),this.end.translate(t,e)):(this.start.translate(t),this.end.translate(t)),this}rotate(t,e){return this.start.rotate(t,e),this.end.rotate(t,e),this}scale(t,e,n){return this.start.scale(t,e,n),this.end.scale(t,e,n),this}length(){return Math.sqrt(this.squaredLength())}squaredLength(){const t=this.start.x-this.end.x,e=this.start.y-this.end.y;return t*t+e*e}setLength(t){const e=this.length();if(!e)return this;const n=t/e;return this.scale(n,n,this.start)}parallel(t){const e=this.clone();if(!e.isDifferentiable())return e;const{start:n,end:r}=e,i=n.clone().rotate(270,r),s=r.clone().rotate(90,n);return n.move(s,t),r.move(i,t),e}vector(){return new Ty(this.end.x-this.start.x,this.end.y-this.start.y)}angle(){const t=new Ty(this.start.x+1,this.start.y);return this.start.angleBetween(this.end,t)}bbox(){const t=Math.min(this.start.x,this.end.x),e=Math.min(this.start.y,this.end.y),n=Math.max(this.start.x,this.end.x),r=Math.max(this.start.y,this.end.y);return new ky(t,e,n-t,r-e)}bearing(){return this.start.bearing(this.end)}closestPoint(t){return this.pointAt(this.closestPointNormalizedLength(t))}closestPointLength(t){return this.closestPointNormalizedLength(t)*this.length()}closestPointTangent(t){return this.tangentAt(this.closestPointNormalizedLength(t))}closestPointNormalizedLength(t){const e=this.vector().dot(new Ny(this.start,t).vector()),n=Math.min(1,Math.max(0,e/this.squaredLength()));return Number.isNaN(n)?0:n}pointAt(t){const e=this.start,n=this.end;return t<=0?e.clone():t>=1?n.clone():e.lerp(n,t)}pointAtLength(t){const e=this.start,n=this.end;let r=!0;t<0&&(r=!1,t=-t);const i=this.length();if(t>=i)return r?n.clone():e.clone();const s=(r?t:i-t)/i;return this.pointAt(s)}divideAt(t){const e=this.pointAt(t);return[new Ny(this.start,e),new Ny(e,this.end)]}divideAtLength(t){const e=this.pointAtLength(t);return[new Ny(this.start,e),new Ny(e,this.end)]}containsPoint(t){const e=this.start,n=this.end;if(0!==e.cross(t,n))return!1;const r=this.length();return!(new Ny(e,t).length()>r)&&!(new Ny(t,n).length()>r)}intersect(t,e){const n=t.intersectsWithLine(this,e);return n?Array.isArray(n)?n:[n]:null}intersectsWithLine(t){const e=new Ty(this.end.x-this.start.x,this.end.y-this.start.y),n=new Ty(t.end.x-t.start.x,t.end.y-t.start.y),r=e.x*n.y-e.y*n.x,i=new Ty(t.start.x-this.start.x,t.start.y-this.start.y),s=i.x*n.y-i.y*n.x,o=i.x*e.y-i.y*e.x;if(0===r||s*r<0||o*r<0)return null;if(r>0){if(s>r||o>r)return null}else if(s<r||o<r)return null;return new Ty(this.start.x+s*e.x/r,this.start.y+s*e.y/r)}isDifferentiable(){return!this.start.equals(this.end)}pointOffset(t){const e=Ty.clone(t),n=this.start,r=this.end;return((r.x-n.x)*(e.y-n.y)-(r.y-n.y)*(e.x-n.x))/this.length()}pointSquaredDistance(t,e){const n=Ty.create(t,e);return this.closestPoint(n).squaredDistance(n)}pointDistance(t,e){const n=Ty.create(t,e);return this.closestPoint(n).distance(n)}tangentAt(t){if(!this.isDifferentiable())return null;const e=this.start,n=this.end,r=this.pointAt(t),i=new Ny(e,n);return i.translate(r.x-e.x,r.y-e.y),i}tangentAtLength(t){if(!this.isDifferentiable())return null;const e=this.start,n=this.end,r=this.pointAtLength(t),i=new Ny(e,n);return i.translate(r.x-e.x,r.y-e.y),i}relativeCcw(t,e){const n=Ty.create(t,e);let r=n.x-this.start.x,i=n.y-this.start.y;const s=this.end.x-this.start.x,o=this.end.y-this.start.y;let a=r*o-i*s;return 0===a&&(a=r*s+i*o,a>0&&(r-=s,i-=o,a=r*s+i*o,a<0&&(a=0))),a<0?-1:a>0?1:0}equals(t){return null!=t&&this.start.x===t.start.x&&this.start.y===t.start.y&&this.end.x===t.end.x&&this.end.y===t.end.y}clone(){return new Ny(this.start,this.end)}toJSON(){return{start:this.start.toJSON(),end:this.end.toJSON()}}serialize(){return[this.start.serialize(),this.end.serialize()].join(" ")}}!function(t){t.isLine=function(e){return null!=e&&e instanceof t}}(Ny||(Ny={}));let jy=class t extends Oy{get center(){return new Ty(this.x,this.y)}constructor(t,e,n,r){super(),this.x=null==t?0:t,this.y=null==e?0:e,this.a=null==n?0:n,this.b=null==r?0:r}bbox(){return ky.fromEllipse(this)}getCenter(){return this.center}inflate(t,e){const n=t,r=null!=e?e:t;return this.a+=2*n,this.b+=2*r,this}normalizedDistance(t,e){const n=Ty.create(t,e),r=n.x-this.x,i=n.y-this.y,s=this.a,o=this.b;return r*r/(s*s)+i*i/(o*o)}containsPoint(t,e){return this.normalizedDistance(t,e)<=1}intersectsWithLine(t){const e=[],n=this.a,r=this.b,i=t.start,s=t.end,o=t.vector(),a=i.diff(new Ty(this.x,this.y)),l=new Ty(o.x/(n*n),o.y/(r*r)),c=new Ty(a.x/(n*n),a.y/(r*r)),h=o.dot(l),u=o.dot(c),g=u*u-h*(a.dot(c)-1);if(g<0)return null;if(g>0){const t=Math.sqrt(g),n=(-u-t)/h,r=(-u+t)/h;if((n<0||n>1)&&(r<0||r>1))return null;n>=0&&n<=1&&e.push(i.lerp(s,n)),r>=0&&r<=1&&e.push(i.lerp(s,r))}else{const t=-u/h;if(!(t>=0&&t<=1))return null;e.push(i.lerp(s,t))}return e}intersectsWithLineFromCenterToPoint(t,e=0){const n=Ty.clone(t);e&&n.rotate(e,this.getCenter());const r=n.x-this.x,i=n.y-this.y;let s;if(0===r)return s=this.bbox().getNearestPointToPoint(n),e?s.rotate(-e,this.getCenter()):s;const o=i/r,a=o*o,l=this.a*this.a,c=this.b*this.b;let h=Math.sqrt(1/(1/l+a/c));h=r<0?-h:h;const u=o*h;return s=new Ty(this.x+h,this.y+u),e?s.rotate(-e,this.getCenter()):s}tangentTheta(t){const e=Ty.clone(t),n=e.x,r=e.y,i=this.a,s=this.b,o=this.bbox().center,a=o.x,l=o.y,c=n>o.x+i/2,h=n<o.x-i/2;let u,g;return c||h?(g=n>o.x?r-30:r+30,u=i*i/(n-a)-i*i*(r-l)*(g-l)/(s*s*(n-a))+a):(u=r>o.y?n+30:n-30,g=s*s/(r-l)-s*s*(n-a)*(u-a)/(i*i*(r-l))+l),new Ty(u,g).theta(e)}scale(t,e){return this.a*=t,this.b*=e,this}rotate(e,n){const r=ky.fromEllipse(this);r.rotate(e,n);const i=t.fromRect(r);return this.a=i.a,this.b=i.b,this.x=i.x,this.y=i.y,this}translate(t,e){const n=Ty.create(t,e);return this.x+=n.x,this.y+=n.y,this}equals(t){return null!=t&&t.x===this.x&&t.y===this.y&&t.a===this.a&&t.b===this.b}clone(){return new t(this.x,this.y,this.a,this.b)}toJSON(){return{x:this.x,y:this.y,a:this.a,b:this.b}}serialize(){return`${this.x} ${this.y} ${this.a} ${this.b}`}};!function(t){t.isEllipse=function(e){return null!=e&&e instanceof t}}(jy||(jy={})),function(t){function e(e){return t.isEllipse(e)?e.clone():Array.isArray(e)?new t(e[0],e[1],e[2],e[3]):new t(e.x,e.y,e.a,e.b)}t.create=function(n,r,i,s){return null==n||"number"==typeof n?new t(n,r,i,s):e(n)},t.parse=e,t.fromRect=function(e){const n=e.center;return new t(n.x,n.y,e.width/2,e.height/2)}}(jy||(jy={}));const _y=new RegExp("^[\\s\\dLMCZz,.]*$");function Ly(t){return"string"==typeof t&&_y.test(t)}function By(t,e){return(t%e+e)%e}function Iy(t,e={}){const n=[];return t&&t.length&&t.forEach((t=>{Array.isArray(t)?n.push({x:t[0],y:t[1]}):n.push({x:t.x,y:t.y})})),function(t,e,n,r,i){const s=[],o=t[t.length-1],a=null!=e&&e>0,l=e||0;if(r&&a){const e=(t=t.slice())[0],n=new Ty(o.x+(e.x-o.x)/2,o.y+(e.y-o.y)/2);t.splice(0,0,n)}let c=t[0],h=1;for(n?s.push("M",c.x,c.y):s.push("L",c.x,c.y);h<(r?t.length:t.length-1);){let e=t[By(h,t.length)],n=c.x-e.x,r=c.y-e.y;if(a&&(0!==n||0!==r)&&(null==i||i.indexOf(h-1)<0)){let i=Math.sqrt(n*n+r*r);const o=n*Math.min(l,i/2)/i,a=r*Math.min(l,i/2)/i,c=e.x+o,u=e.y+a;s.push("L",c,u);let g=t[By(h+1,t.length)];for(;h<t.length-2&&0===Math.round(g.x-e.x)&&0===Math.round(g.y-e.y);)g=t[By(h+2,t.length)],h+=1;n=g.x-e.x,r=g.y-e.y,i=Math.max(1,Math.sqrt(n*n+r*r));const d=n*Math.min(l,i/2)/i,f=r*Math.min(l,i/2)/i,p=e.x+d,m=e.y+f;s.push("Q",e.x,e.y,p,m),e=new Ty(p,m)}else s.push("L",e.x,e.y);c=e,h+=1}return r?s.push("Z"):s.push("L",o.x,o.y),s.map((t=>"string"==typeof t?t:+t.toFixed(3))).join(" ")}(n,e.round,null==e.initialMove||e.initialMove,e.close,e.exclude)}function Dy(t,e,n,r,i=0,s=0,o=0,a,l){if(0===n||0===r)return[];a-=t,l-=e,n=Math.abs(n),r=Math.abs(r);const c=-a/2,h=-l/2,u=Math.cos(i*Math.PI/180),g=Math.sin(i*Math.PI/180),d=u*c+g*h,f=-1*g*c+u*h,p=d*d,m=f*f,y=n*n,v=r*r,b=p/y+m/v;let x;if(b>1)n=Math.sqrt(b)*n,r=Math.sqrt(b)*r,x=0;else{let t=1;s===o&&(t=-1),x=t*Math.sqrt((y*v-y*m-v*p)/(y*m+v*p))}const w=x*n*f/r,A=-1*x*r*d/n,P=u*w-g*A+a/2,C=g*w+u*A+l/2;let M=Math.atan2((f-A)/r,(d-w)/n)-Math.atan2(0,1),E=M>=0?M:2*Math.PI+M;M=Math.atan2((-f-A)/r,(-d-w)/n)-Math.atan2((f-A)/r,(d-w)/n);let S=M>=0?M:2*Math.PI+M;0===o&&S>0?S-=2*Math.PI:0!==o&&S<0&&(S+=2*Math.PI);const O=2*S/Math.PI,T=Math.ceil(O<0?-1*O:O),k=S/T,N=8/3*Math.sin(k/4)*Math.sin(k/4)/Math.sin(k/2),j=u*n,_=u*r,L=g*n,B=g*r;let I=Math.cos(E),D=Math.sin(E),R=-N*(j*D+B*I),V=-N*(L*D-_*I),z=0,$=0;const F=[];for(let n=0;n<T;n+=1){E+=k,I=Math.cos(E),D=Math.sin(E),z=j*I-B*D+P,$=L*I+_*D+C;const r=-N*(j*D+B*I),i=-N*(L*D-_*I),s=6*n;F[s]=Number(R+t),F[s+1]=Number(V+e),F[s+2]=Number(z-r+t),F[s+3]=Number($-i+e),F[s+4]=Number(z+t),F[s+5]=Number($+e),R=z+r,V=$+i}return F.map((t=>+t.toFixed(2)))}function Ry(t,e,n,r,i=0,s=0,o=0,a,l){const c=[],h=Dy(t,e,n,r,i,s,o,a,l);if(null!=h)for(let t=0,e=h.length;t<e;t+=6)c.push("C",h[t],h[t+1],h[t+2],h[t+3],h[t+4],h[t+5]);return c.join(" ")}let Vy=class t extends Oy{get start(){return this.points[0]||null}get end(){return this.points[this.points.length-1]||null}constructor(e){if(super(),null!=e){if("string"==typeof e)return t.parse(e);this.points=e.map((t=>Ty.create(t)))}else this.points=[]}scale(t,e,n=new Ty){return this.points.forEach((r=>r.scale(t,e,n))),this}rotate(t,e){return this.points.forEach((n=>n.rotate(t,e))),this}translate(t,e){const n=Ty.create(t,e);return this.points.forEach((t=>t.translate(n.x,n.y))),this}round(t=0){return this.points.forEach((e=>e.round(t))),this}bbox(){if(0===this.points.length)return new ky;let t=1/0,e=-1/0,n=1/0,r=-1/0;const i=this.points;for(let s=0,o=i.length;s<o;s+=1){const o=i[s],a=o.x,l=o.y;a<t&&(t=a),a>e&&(e=a),l<n&&(n=l),l>r&&(r=l)}return new ky(t,n,e-t,r-n)}closestPoint(t){const e=this.closestPointLength(t);return this.pointAtLength(e)}closestPointLength(t){const e=this.points,n=e.length;if(0===n||1===n)return 0;let r=0,i=0,s=1/0;for(let o=0,a=n-1;o<a;o+=1){const n=new Ny(e[o],e[o+1]),a=n.length(),l=n.closestPointNormalizedLength(t),c=n.pointAt(l).squaredDistance(t);c<s&&(s=c,i=r+l*a),r+=a}return i}closestPointNormalizedLength(t){const e=this.length();if(0===e)return 0;return this.closestPointLength(t)/e}closestPointTangent(t){const e=this.closestPointLength(t);return this.tangentAtLength(e)}containsPoint(t){if(0===this.points.length)return!1;const e=Ty.clone(t),n=e.x,r=e.y,i=this.points,s=i.length;let o=s-1,a=0;for(let l=0;l<s;l+=1){const s=i[o],c=i[l];if(e.equals(s))return!0;const h=new Ny(s,c);if(h.containsPoint(t))return!0;if(r<=s.y&&r>c.y||r>s.y&&r<=c.y){const e=s.x-n>c.x-n?s.x-n:c.x-n;if(e>=0){const i=new Ty(n+e,r),s=new Ny(t,i);h.intersectsWithLine(s)&&(a+=1)}}o=l}return a%2==1}intersectsWithLine(t){const e=[];for(let n=0,r=this.points.length-1;n<r;n+=1){const r=this.points[n],i=this.points[n+1],s=t.intersectsWithLine(new Ny(r,i));s&&e.push(s)}return e.length>0?e:null}isDifferentiable(){for(let t=0,e=this.points.length-1;t<e;t+=1){const e=this.points[t],n=this.points[t+1];if(new Ny(e,n).isDifferentiable())return!0}return!1}length(){let t=0;for(let e=0,n=this.points.length-1;e<n;e+=1){const n=this.points[e],r=this.points[e+1];t+=n.distance(r)}return t}pointAt(t){const e=this.points,n=e.length;if(0===n)return null;if(1===n)return e[0].clone();if(t<=0)return e[0].clone();if(t>=1)return e[n-1].clone();const r=this.length()*t;return this.pointAtLength(r)}pointAtLength(t){const e=this.points,n=e.length;if(0===n)return null;if(1===n)return e[0].clone();let r=!0;t<0&&(r=!1,t=-t);let i=0;for(let s=0,o=n-1;s<o;s+=1){const n=r?s:o-1-s,a=e[n],l=e[n+1],c=new Ny(a,l),h=a.distance(l);if(t<=i+h)return c.pointAtLength((r?1:-1)*(t-i));i+=h}return(r?e[n-1]:e[0]).clone()}tangentAt(t){const e=this.points.length;if(0===e||1===e)return null;t<0&&(t=0),t>1&&(t=1);const n=this.length()*t;return this.tangentAtLength(n)}tangentAtLength(t){const e=this.points,n=e.length;if(0===n||1===n)return null;let r,i=!0;t<0&&(i=!1,t=-t);let s=0;for(let o=0,a=n-1;o<a;o+=1){const n=i?o:a-1-o,l=e[n],c=e[n+1],h=new Ny(l,c),u=l.distance(c);if(h.isDifferentiable()){if(t<=s+u)return h.tangentAtLength((i?1:-1)*(t-s));r=h}s+=u}if(r){const t=i?1:0;return r.tangentAt(t)}return null}simplify(t={}){const e=this.points;if(e.length<3)return this;const n=t.threshold||0;let r=0;for(;e[r+2];){const t=r+1,i=r+2,s=e[r],o=e[t],a=e[i];new Ny(s,a).closestPoint(o).distance(o)<=n?e.splice(t,1):r+=1}return this}toHull(){const e=this.points,n=e.length;if(0===n)return new t;let r=e[0];for(let t=1;t<n;t+=1)(e[t].y<r.y||e[t].y===r.y&&e[t].x>r.x)&&(r=e[t]);const i=[];for(let t=0;t<n;t+=1){let n=r.theta(e[t]);0===n&&(n=360),i.push([e[t],t,n])}if(i.sort(((t,e)=>{let n=t[2]-e[2];return 0===n&&(n=e[1]-t[1]),n})),i.length>2){const t=i[i.length-1];i.unshift(t)}const s={},o=[],a=t=>`${t[0].toString()}@${t[1]}`;for(;0!==i.length;){const t=i.pop(),e=t[0];if(s[a(t)])continue;let n=!1;for(;!n;)if(o.length<2)o.push(t),n=!0;else{const r=o.pop(),l=r[0],c=o.pop(),h=c[0],u=h.cross(l,e);if(u<0)o.push(c),o.push(r),o.push(t),n=!0;else if(0===u){const t=1e-10,n=l.angleBetween(h,e);Math.abs(n-180)<t||l.equals(e)||h.equals(l)?(s[a(r)]=l,o.push(c)):Math.abs((n+1)%360-1)<t&&(o.push(c),i.push(r))}else s[a(r)]=l,o.push(c)}}let l;o.length>2&&o.pop();let c=-1;for(let t=0,e=o.length;t<e;t+=1){const e=o[t][1];(void 0===l||e<l)&&(l=e,c=t)}let h=[];if(c>0){const t=o.slice(c),e=o.slice(0,c);h=t.concat(e)}else h=o;const u=[];for(let t=0,e=h.length;t<e;t+=1)u.push(h[t][0]);return new t(u)}equals(t){return null!=t&&(t.points.length===this.points.length&&t.points.every(((t,e)=>t.equals(this.points[e]))))}clone(){return new t(this.points.map((t=>t.clone())))}toJSON(){return this.points.map((t=>t.toJSON()))}serialize(){return this.points.map((t=>`${t.serialize()}`)).join(" ")}};!function(t){t.isPolyline=function(e){return null!=e&&e instanceof t}}(Vy||(Vy={})),function(t){t.parse=function(e){const n=e.trim();if(""===n)return new t;const r=[],i=n.split(/\s*,\s*|\s+/);for(let t=0,e=i.length;t<e;t+=2)r.push({x:+i[t],y:+i[t+1]});return new t(r)}}(Vy||(Vy={}));class zy extends Oy{constructor(t,e,n,r){super(),this.PRECISION=3,this.start=Ty.create(t),this.controlPoint1=Ty.create(e),this.controlPoint2=Ty.create(n),this.end=Ty.create(r)}bbox(){const t=this.start,e=this.controlPoint1,n=this.controlPoint2,r=this.end,i=t.x,s=t.y,o=e.x,a=e.y,l=n.x,c=n.y,h=r.x,u=r.y,g=[],d=[[],[]];let f,p,m,y,v,b,x,w,A,P,C;for(let t=0;t<2;t+=1)if(0===t?(p=6*i-12*o+6*l,f=-3*i+9*o-9*l+3*h,m=3*o-3*i):(p=6*s-12*a+6*c,f=-3*s+9*a-9*c+3*u,m=3*a-3*s),Math.abs(f)<1e-12){if(Math.abs(p)<1e-12)continue;y=-m/p,y>0&&y<1&&g.push(y)}else x=p*p-4*m*f,w=Math.sqrt(x),x<0||(v=(-p+w)/(2*f),v>0&&v<1&&g.push(v),b=(-p-w)/(2*f),b>0&&b<1&&g.push(b));let M=g.length;const E=M;for(;M;)M-=1,y=g[M],C=1-y,A=C*C*C*i+3*C*C*y*o+3*C*y*y*l+y*y*y*h,d[0][M]=A,P=C*C*C*s+3*C*C*y*a+3*C*y*y*c+y*y*y*u,d[1][M]=P;g[E]=0,g[E+1]=1,d[0][E]=i,d[1][E]=s,d[0][E+1]=h,d[1][E+1]=u,g.length=E+2,d[0].length=E+2,d[1].length=E+2;const S=Math.min.apply(null,d[0]),O=Math.min.apply(null,d[1]),T=Math.max.apply(null,d[0]),k=Math.max.apply(null,d[1]);return new ky(S,O,T-S,k-O)}closestPoint(t,e={}){return this.pointAtT(this.closestPointT(t,e))}closestPointLength(t,e={}){const n=this.getOptions(e);return this.lengthAtT(this.closestPointT(t,n),n)}closestPointNormalizedLength(t,e={}){const n=this.getOptions(e),r=this.closestPointLength(t,n);if(!r)return 0;const i=this.length(n);return 0===i?0:r/i}closestPointT(t,e={}){const n=this.getPrecision(e),r=this.getDivisions(e),i=Math.pow(10,-n);let s=null,o=0,a=0,l=0,c=0,h=0,u=null;const g=r.length;let d=g>0?1/g:0;for(r.forEach(((e,n)=>{const r=e.start.distance(t),i=e.end.distance(t),g=r+i;(null==u||g<u)&&(s=e,o=n*d,a=(n+1)*d,l=r,c=i,u=g,h=e.endpointDistance())}));;){const e=l?Math.abs(l-c)/l:0,n=null!=c?Math.abs(l-c)/c:0;if(e<i||n<i||(!l||l<h*i||(!c||c<h*i)))return l<=c?o:a;const r=s.divide(.5);d/=2;const u=r[0].start.distance(t),g=r[0].end.distance(t),f=u+g,p=r[1].start.distance(t),m=r[1].end.distance(t);f<=p+m?(s=r[0],a-=d,l=u,c=g):(s=r[1],o+=d,l=p,c=m)}}closestPointTangent(t,e={}){return this.tangentAtT(this.closestPointT(t,e))}containsPoint(t,e={}){return this.toPolyline(e).containsPoint(t)}divideAt(t,e={}){if(t<=0)return this.divideAtT(0);if(t>=1)return this.divideAtT(1);const n=this.tAt(t,e);return this.divideAtT(n)}divideAtLength(t,e={}){const n=this.tAtLength(t,e);return this.divideAtT(n)}divide(t){return this.divideAtT(t)}divideAtT(t){const e=this.start,n=this.controlPoint1,r=this.controlPoint2,i=this.end;if(t<=0)return[new zy(e,e,e,e),new zy(e,n,r,i)];if(t>=1)return[new zy(e,n,r,i),new zy(i,i,i,i)];const s=this.getSkeletonPoints(t),o=s.startControlPoint1,a=s.startControlPoint2,l=s.divider,c=s.dividerControlPoint1,h=s.dividerControlPoint2;return[new zy(e,o,a,l),new zy(l,c,h,i)]}endpointDistance(){return this.start.distance(this.end)}getSkeletonPoints(t){const e=this.start,n=this.controlPoint1,r=this.controlPoint2,i=this.end;if(t<=0)return{startControlPoint1:e.clone(),startControlPoint2:e.clone(),divider:e.clone(),dividerControlPoint1:n.clone(),dividerControlPoint2:r.clone()};if(t>=1)return{startControlPoint1:n.clone(),startControlPoint2:r.clone(),divider:i.clone(),dividerControlPoint1:i.clone(),dividerControlPoint2:i.clone()};const s=new Ny(e,n).pointAt(t),o=new Ny(n,r).pointAt(t),a=new Ny(r,i).pointAt(t),l=new Ny(s,o).pointAt(t),c=new Ny(o,a).pointAt(t);return{startControlPoint1:s,startControlPoint2:l,divider:new Ny(l,c).pointAt(t),dividerControlPoint1:c,dividerControlPoint2:a}}getSubdivisions(t={}){const e=this.getPrecision(t);let n=[new zy(this.start,this.controlPoint1,this.controlPoint2,this.end)];if(0===e)return n;let r=this.endpointDistance();const i=Math.pow(10,-e);let s=0;for(;;){s+=1;const t=[];n.forEach((e=>{const n=e.divide(.5);t.push(n[0],n[1])}));const e=t.reduce(((t,e)=>t+e.endpointDistance()),0);if(s>1&&(0!==e?(e-r)/e:0)<i)return t;n=t,r=e}}length(t={}){return this.getDivisions(t).reduce(((t,e)=>t+e.endpointDistance()),0)}lengthAtT(t,e={}){if(t<=0)return 0;const n=void 0===e.precision?this.PRECISION:e.precision;return this.divide(t)[0].length({precision:n})}pointAt(t,e={}){if(t<=0)return this.start.clone();if(t>=1)return this.end.clone();const n=this.tAt(t,e);return this.pointAtT(n)}pointAtLength(t,e={}){const n=this.tAtLength(t,e);return this.pointAtT(n)}pointAtT(t){return t<=0?this.start.clone():t>=1?this.end.clone():this.getSkeletonPoints(t).divider}isDifferentiable(){const t=this.start,e=this.controlPoint1,n=this.controlPoint2,r=this.end;return!(t.equals(e)&&e.equals(n)&&n.equals(r))}tangentAt(t,e={}){if(!this.isDifferentiable())return null;t<0?t=0:t>1&&(t=1);const n=this.tAt(t,e);return this.tangentAtT(n)}tangentAtLength(t,e={}){if(!this.isDifferentiable())return null;const n=this.tAtLength(t,e);return this.tangentAtT(n)}tangentAtT(t){if(!this.isDifferentiable())return null;t<0&&(t=0),t>1&&(t=1);const e=this.getSkeletonPoints(t),n=e.startControlPoint2,r=e.dividerControlPoint1,i=e.divider,s=new Ny(n,r);return s.translate(i.x-n.x,i.y-n.y),s}getPrecision(t={}){return null==t.precision?this.PRECISION:t.precision}getDivisions(t={}){if(null!=t.subdivisions)return t.subdivisions;const e=this.getPrecision(t);return this.getSubdivisions({precision:e})}getOptions(t={}){return{precision:this.getPrecision(t),subdivisions:this.getDivisions(t)}}tAt(t,e={}){if(t<=0)return 0;if(t>=1)return 1;const n=this.getOptions(e),r=this.length(n)*t;return this.tAtLength(r,n)}tAtLength(t,e={}){let n=!0;t<0&&(n=!1,t=-t);const r=this.getPrecision(e),i=this.getDivisions(e),s={precision:r,subdivisions:i};let o,a,l=null,c=0,h=0,u=0;const g=i.length;let d=g>0?1/g:0;for(let e=0;e<g;e+=1){const r=n?e:g-1-e,s=i[e],f=s.endpointDistance();if(t<=u+f){l=s,o=r*d,a=(r+1)*d,c=n?t-u:f+u-t,h=n?f+u-t:t-u;break}u+=f}if(null==l)return n?1:0;const f=this.length(s),p=Math.pow(10,-r);for(;;){let t,e,n;if(t=0!==f?c/f:0,t<p)return o;if(t=0!==f?h/f:0,t<p)return a;const r=l.divide(.5);d/=2;const i=r[0].endpointDistance(),s=r[1].endpointDistance();c<=i?(l=r[0],a-=d,e=c,n=i-e):(l=r[1],o+=d,e=c-i,n=s-e),c=e,h=n}}toPoints(t={}){const e=this.getDivisions(t),n=[e[0].start.clone()];return e.forEach((t=>n.push(t.end.clone()))),n}toPolyline(t={}){return new Vy(this.toPoints(t))}scale(t,e,n){return this.start.scale(t,e,n),this.controlPoint1.scale(t,e,n),this.controlPoint2.scale(t,e,n),this.end.scale(t,e,n),this}rotate(t,e){return this.start.rotate(t,e),this.controlPoint1.rotate(t,e),this.controlPoint2.rotate(t,e),this.end.rotate(t,e),this}translate(t,e){return"number"==typeof t?(this.start.translate(t,e),this.controlPoint1.translate(t,e),this.controlPoint2.translate(t,e),this.end.translate(t,e)):(this.start.translate(t),this.controlPoint1.translate(t),this.controlPoint2.translate(t),this.end.translate(t)),this}equals(t){return null!=t&&this.start.equals(t.start)&&this.controlPoint1.equals(t.controlPoint1)&&this.controlPoint2.equals(t.controlPoint2)&&this.end.equals(t.end)}clone(){return new zy(this.start,this.controlPoint1,this.controlPoint2,this.end)}toJSON(){return{start:this.start.toJSON(),controlPoint1:this.controlPoint1.toJSON(),controlPoint2:this.controlPoint2.toJSON(),end:this.end.toJSON()}}serialize(){return[this.start.serialize(),this.controlPoint1.serialize(),this.controlPoint2.serialize(),this.end.serialize()].join(" ")}}!function(t){t.isCurve=function(e){return null!=e&&e instanceof t}}(zy||(zy={})),function(t){function e(t){const e=t.length,n=[],r=[];let i=2;n[0]=t[0]/i;for(let s=1;s<e;s+=1)r[s]=1/i,i=(s<e-1?4:3.5)-r[s],n[s]=(t[s]-n[s-1])/i;for(let t=1;t<e;t+=1)n[e-t-1]-=r[e-t]*n[e-t];return n}t.throughPoints=function(n){if(null==n||Array.isArray(n)&&n.length<2)throw new Error("At least 2 points are required");const r=function(t){const n=t.map((t=>Ty.clone(t))),r=[],i=[],s=n.length-1;if(1===s)return r[0]=new Ty((2*n[0].x+n[1].x)/3,(2*n[0].y+n[1].y)/3),i[0]=new Ty(2*r[0].x-n[0].x,2*r[0].y-n[0].y),[r,i];const o=[];for(let t=1;t<s-1;t+=1)o[t]=4*n[t].x+2*n[t+1].x;o[0]=n[0].x+2*n[1].x,o[s-1]=(8*n[s-1].x+n[s].x)/2;const a=e(o);for(let t=1;t<s-1;t+=1)o[t]=4*n[t].y+2*n[t+1].y;o[0]=n[0].y+2*n[1].y,o[s-1]=(8*n[s-1].y+n[s].y)/2;const l=e(o);for(let t=0;t<s;t+=1)r.push(new Ty(a[t],l[t])),t<s-1?i.push(new Ty(2*n[t+1].x-a[t+1],2*n[t+1].y-l[t+1])):i.push(new Ty((n[s].x+a[s-1])/2,(n[s].y+l[s-1])/2));return[r,i]}(n),i=[];for(let e=0,s=r[0].length;e<s;e+=1){const s=new Ty(r[0][e].x,r[0][e].y),o=new Ty(r[1][e].x,r[1][e].y);i.push(new t(n[e],s,o,n[e+1]))}return i}}(zy||(zy={}));class $y extends Oy{constructor(){super(...arguments),this.isVisible=!0,this.isSegment=!0,this.isSubpathStart=!1}get end(){return this.endPoint}get start(){if(null==this.previousSegment)throw new Error("Missing previous segment. (This segment cannot be the first segment of a path, or segment has not yet been added to a path.)");return this.previousSegment.end}closestPointT(t,e){if(this.closestPointNormalizedLength)return this.closestPointNormalizedLength(t);throw new Error("Neither `closestPointT` nor `closestPointNormalizedLength` method is implemented.")}lengthAtT(t,e){if(t<=0)return 0;const n=this.length();return t>=1?n:n*t}divideAtT(t){if(this.divideAt)return this.divideAt(t);throw new Error("Neither `divideAtT` nor `divideAt` method is implemented.")}pointAtT(t){if(this.pointAt)return this.pointAt(t);throw new Error("Neither `pointAtT` nor `pointAt` method is implemented.")}tangentAtT(t){if(this.tangentAt)return this.tangentAt(t);throw new Error("Neither `tangentAtT` nor `tangentAt` method is implemented.")}}class Fy extends $y{constructor(t,e){super(),Ny.isLine(t)?this.endPoint=t.end.clone().round(2):this.endPoint=Ty.create(t,e).round(2)}get type(){return"L"}get line(){return new Ny(this.start,this.end)}bbox(){return this.line.bbox()}closestPoint(t){return this.line.closestPoint(t)}closestPointLength(t){return this.line.closestPointLength(t)}closestPointNormalizedLength(t){return this.line.closestPointNormalizedLength(t)}closestPointTangent(t){return this.line.closestPointTangent(t)}length(){return this.line.length()}divideAt(t){const e=this.line.divideAt(t);return[new Fy(e[0]),new Fy(e[1])]}divideAtLength(t){const e=this.line.divideAtLength(t);return[new Fy(e[0]),new Fy(e[1])]}getSubdivisions(){return[]}pointAt(t){return this.line.pointAt(t)}pointAtLength(t){return this.line.pointAtLength(t)}tangentAt(t){return this.line.tangentAt(t)}tangentAtLength(t){return this.line.tangentAtLength(t)}isDifferentiable(){return null!=this.previousSegment&&!this.start.equals(this.end)}clone(){return new Fy(this.end)}scale(t,e,n){return this.end.scale(t,e,n),this}rotate(t,e){return this.end.rotate(t,e),this}translate(t,e){return"number"==typeof t?this.end.translate(t,e):this.end.translate(t),this}equals(t){return this.type===t.type&&this.start.equals(t.start)&&this.end.equals(t.end)}toJSON(){return{type:this.type,start:this.start.toJSON(),end:this.end.toJSON()}}serialize(){const t=this.end;return`${this.type} ${t.x} ${t.y}`}}!function(t){t.create=function(...e){const n=e.length,r=e[0];if(Ny.isLine(r))return new t(r);if(Ty.isPointLike(r))return 1===n?new t(r):e.map((e=>new t(e)));if(2===n)return new t(+e[0],+e[1]);const i=[];for(let r=0;r<n;r+=2){const n=+e[r],s=+e[r+1];i.push(new t(n,s))}return i}}(Fy||(Fy={}));class Gy extends $y{get end(){if(!this.subpathStartSegment)throw new Error("Missing subpath start segment. (This segment needs a subpath start segment (e.g. MoveTo), or segment has not yet been added to a path.)");return this.subpathStartSegment.end}get type(){return"Z"}get line(){return new Ny(this.start,this.end)}bbox(){return this.line.bbox()}closestPoint(t){return this.line.closestPoint(t)}closestPointLength(t){return this.line.closestPointLength(t)}closestPointNormalizedLength(t){return this.line.closestPointNormalizedLength(t)}closestPointTangent(t){return this.line.closestPointTangent(t)}length(){return this.line.length()}divideAt(t){const e=this.line.divideAt(t);return[e[1].isDifferentiable()?new Fy(e[0]):this.clone(),new Fy(e[1])]}divideAtLength(t){const e=this.line.divideAtLength(t);return[e[1].isDifferentiable()?new Fy(e[0]):this.clone(),new Fy(e[1])]}getSubdivisions(){return[]}pointAt(t){return this.line.pointAt(t)}pointAtLength(t){return this.line.pointAtLength(t)}tangentAt(t){return this.line.tangentAt(t)}tangentAtLength(t){return this.line.tangentAtLength(t)}isDifferentiable(){return!(!this.previousSegment||!this.subpathStartSegment)&&!this.start.equals(this.end)}scale(){return this}rotate(){return this}translate(){return this}equals(t){return this.type===t.type&&this.start.equals(t.start)&&this.end.equals(t.end)}clone(){return new Gy}toJSON(){return{type:this.type,start:this.start.toJSON(),end:this.end.toJSON()}}serialize(){return this.type}}!function(t){t.create=function(){return new t}}(Gy||(Gy={}));class Uy extends $y{constructor(t,e){super(),this.isVisible=!1,this.isSubpathStart=!0,Ny.isLine(t)||zy.isCurve(t)?this.endPoint=t.end.clone().round(2):this.endPoint=Ty.create(t,e).round(2)}get start(){throw new Error("Illegal access. Moveto segments should not need a start property.")}get type(){return"M"}bbox(){return null}closestPoint(){return this.end.clone()}closestPointLength(){return 0}closestPointNormalizedLength(){return 0}closestPointT(){return 1}closestPointTangent(){return null}length(){return 0}lengthAtT(){return 0}divideAt(){return[this.clone(),this.clone()]}divideAtLength(){return[this.clone(),this.clone()]}getSubdivisions(){return[]}pointAt(){return this.end.clone()}pointAtLength(){return this.end.clone()}pointAtT(){return this.end.clone()}tangentAt(){return null}tangentAtLength(){return null}tangentAtT(){return null}isDifferentiable(){return!1}scale(t,e,n){return this.end.scale(t,e,n),this}rotate(t,e){return this.end.rotate(t,e),this}translate(t,e){return"number"==typeof t?this.end.translate(t,e):this.end.translate(t),this}clone(){return new Uy(this.end)}equals(t){return this.type===t.type&&this.end.equals(t.end)}toJSON(){return{type:this.type,end:this.end.toJSON()}}serialize(){const t=this.end;return`${this.type} ${t.x} ${t.y}`}}!function(t){t.create=function(...e){const n=e.length,r=e[0];if(Ny.isLine(r))return new t(r);if(zy.isCurve(r))return new t(r);if(Ty.isPointLike(r)){if(1===n)return new t(r);const i=[];for(let r=0;r<n;r+=1)0===r?i.push(new t(e[r])):i.push(new Fy(e[r]));return i}if(2===n)return new t(+e[0],+e[1]);const i=[];for(let r=0;r<n;r+=2){const n=+e[r],s=+e[r+1];0===r?i.push(new t(n,s)):i.push(new Fy(n,s))}return i}}(Uy||(Uy={}));class qy extends $y{constructor(t,e,n,r,i,s){super(),zy.isCurve(t)?(this.controlPoint1=t.controlPoint1.clone().round(2),this.controlPoint2=t.controlPoint2.clone().round(2),this.endPoint=t.end.clone().round(2)):"number"==typeof t?(this.controlPoint1=new Ty(t,e).round(2),this.controlPoint2=new Ty(n,r).round(2),this.endPoint=new Ty(i,s).round(2)):(this.controlPoint1=Ty.create(t).round(2),this.controlPoint2=Ty.create(e).round(2),this.endPoint=Ty.create(n).round(2))}get type(){return"C"}get curve(){return new zy(this.start,this.controlPoint1,this.controlPoint2,this.end)}bbox(){return this.curve.bbox()}closestPoint(t){return this.curve.closestPoint(t)}closestPointLength(t){return this.curve.closestPointLength(t)}closestPointNormalizedLength(t){return this.curve.closestPointNormalizedLength(t)}closestPointTangent(t){return this.curve.closestPointTangent(t)}length(){return this.curve.length()}divideAt(t,e={}){const n=this.curve.divideAt(t,e);return[new qy(n[0]),new qy(n[1])]}divideAtLength(t,e={}){const n=this.curve.divideAtLength(t,e);return[new qy(n[0]),new qy(n[1])]}divideAtT(t){const e=this.curve.divideAtT(t);return[new qy(e[0]),new qy(e[1])]}getSubdivisions(){return[]}pointAt(t){return this.curve.pointAt(t)}pointAtLength(t){return this.curve.pointAtLength(t)}tangentAt(t){return this.curve.tangentAt(t)}tangentAtLength(t){return this.curve.tangentAtLength(t)}isDifferentiable(){if(!this.previousSegment)return!1;const t=this.start,e=this.controlPoint1,n=this.controlPoint2,r=this.end;return!(t.equals(e)&&e.equals(n)&&n.equals(r))}scale(t,e,n){return this.controlPoint1.scale(t,e,n),this.controlPoint2.scale(t,e,n),this.end.scale(t,e,n),this}rotate(t,e){return this.controlPoint1.rotate(t,e),this.controlPoint2.rotate(t,e),this.end.rotate(t,e),this}translate(t,e){return"number"==typeof t?(this.controlPoint1.translate(t,e),this.controlPoint2.translate(t,e),this.end.translate(t,e)):(this.controlPoint1.translate(t),this.controlPoint2.translate(t),this.end.translate(t)),this}equals(t){return this.start.equals(t.start)&&this.end.equals(t.end)&&this.controlPoint1.equals(t.controlPoint1)&&this.controlPoint2.equals(t.controlPoint2)}clone(){return new qy(this.controlPoint1,this.controlPoint2,this.end)}toJSON(){return{type:this.type,start:this.start.toJSON(),controlPoint1:this.controlPoint1.toJSON(),controlPoint2:this.controlPoint2.toJSON(),end:this.end.toJSON()}}serialize(){const t=this.controlPoint1,e=this.controlPoint2,n=this.end;return[this.type,t.x,t.y,e.x,e.y,n.x,n.y].join(" ")}}function Wy(t,e,n){return{x:t*Math.cos(n)-e*Math.sin(n),y:t*Math.sin(n)+e*Math.cos(n)}}function Hy(t,e,n,r,i,s){const o=1/3,a=2/3;return[o*t+a*n,o*e+a*r,o*i+a*n,o*s+a*r,i,s]}function Jy(t,e,n,r,i,s,o,a,l,c){const h=120*Math.PI/180,u=Math.PI/180*(+i||0);let g,d,f,p,m,y=[];if(c)d=c[0],f=c[1],p=c[2],m=c[3];else{g=Wy(t,e,-u),t=g.x,e=g.y,g=Wy(a,l,-u);const i=(t-(a=g.x))/2,c=(e-(l=g.y))/2;let h=i*i/(n*n)+c*c/(r*r);h>1&&(h=Math.sqrt(h),n*=h,r*=h);const y=n*n,v=r*r,b=(s===o?-1:1)*Math.sqrt(Math.abs((y*v-y*c*c-v*i*i)/(y*c*c+v*i*i)));p=b*n*c/r+(t+a)/2,m=b*-r*i/n+(e+l)/2,d=Math.asin((e-m)/r),f=Math.asin((l-m)/r),d=t<p?Math.PI-d:d,f=a<p?Math.PI-f:f,d<0&&(d=2*Math.PI+d),f<0&&(f=2*Math.PI+f),o&&d>f&&(d-=2*Math.PI),!o&&f>d&&(f-=2*Math.PI)}let v=f-d;if(Math.abs(v)>h){const t=f,e=a,s=l;f=d+h*(o&&f>d?1:-1),y=Jy(a=p+n*Math.cos(f),l=m+r*Math.sin(f),n,r,i,0,o,e,s,[f,t,p,m])}v=f-d;const b=Math.cos(d),x=Math.sin(d),w=Math.cos(f),A=Math.sin(f),P=Math.tan(v/4),C=4/3*(n*P),M=4/3*(r*P),E=[t,e],S=[t+C*x,e-M*b],O=[a+C*A,l-M*w],T=[a,l];if(S[0]=2*E[0]-S[0],S[1]=2*E[1]-S[1],c)return[S,O,T].concat(y);{y=[S,O,T].concat(y).join().split(",");const t=[],e=y.length;for(let n=0;n<e;n+=1)t[n]=n%2?Wy(+y[n-1],+y[n],u).y:Wy(+y[n],+y[n+1],u).x;return t}}function Xy(t){const e=function(t){if(!t)return null;const e="\t\n\v\f\r   ᠎             　\u2028\u2029",n=new RegExp(`([a-z])[${e},]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?[${e}]*,?[${e}]*)+)`,"ig"),r=new RegExp(`(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[${e}]*,?[${e}]*`,"ig"),i={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},s=[];return t.replace(n,((t,e,n)=>{const o=[];let a=e.toLowerCase();n.replace(r,((t,e)=>(e&&o.push(+e),t))),"m"===a&&o.length>2&&(s.push([e,...o.splice(0,2)]),a="l",e="m"===e?"l":"L");const l=i[a];for(;o.length>=l&&(s.push([e,...o.splice(0,l)]),l););return t})),s}(t);if(!e||!e.length)return[["M",0,0]];let n=0,r=0,i=0,s=0;const o=[];for(let t=0,a=e.length;t<a;t+=1){const a=[];o.push(a);const l=e[t],c=l[0];if(c!==c.toUpperCase())switch(a[0]=c.toUpperCase(),a[0]){case"A":a[1]=l[1],a[2]=l[2],a[3]=l[3],a[4]=l[4],a[5]=l[5],a[6]=+l[6]+n,a[7]=+l[7]+r;break;case"V":a[1]=+l[1]+r;break;case"H":a[1]=+l[1]+n;break;case"M":i=+l[1]+n,s=+l[2]+r;for(let t=1,e=l.length;t<e;t+=1)a[t]=+l[t]+(t%2?n:r);break;default:for(let t=1,e=l.length;t<e;t+=1)a[t]=+l[t]+(t%2?n:r)}else for(let t=0,e=l.length;t<e;t+=1)a[t]=l[t];switch(a[0]){case"Z":n=+i,r=+s;break;case"H":n=a[1];break;case"V":r=a[1];break;case"M":i=a[a.length-2],s=a[a.length-1],n=a[a.length-2],r=a[a.length-1];break;default:n=a[a.length-2],r=a[a.length-1]}}return o}function Yy(t){const e=Xy(t),n={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null};function r(t,e,n){let r,i;if(!t)return["C",e.x,e.y,e.x,e.y,e.x,e.y];switch(t[0]in{T:1,Q:1}||(e.qx=null,e.qy=null),t[0]){case"M":e.X=t[1],e.Y=t[2];break;case"A":return 0===parseFloat(t[1])||0===parseFloat(t[2])?["L",t[6],t[7]]:["C"].concat(Jy.apply(0,[e.x,e.y].concat(t.slice(1))));case"S":return"C"===n||"S"===n?(r=2*e.x-e.bx,i=2*e.y-e.by):(r=e.x,i=e.y),["C",r,i].concat(t.slice(1));case"T":return"Q"===n||"T"===n?(e.qx=2*e.x-e.qx,e.qy=2*e.y-e.qy):(e.qx=e.x,e.qy=e.y),["C"].concat(Hy(e.x,e.y,e.qx,e.qy,t[1],t[2]));case"Q":return e.qx=t[1],e.qy=t[2],["C"].concat(Hy(e.x,e.y,t[1],t[2],t[3],t[4]));case"H":return["L"].concat(t[1],e.y);case"V":return["L"].concat(e.x,t[1])}return t}function i(t,n){if(t[n].length>7){t[n].shift();const r=t[n];for(;r.length;)s[n]="A",n+=1,t.splice(n,0,["C"].concat(r.splice(0,6)));t.splice(n,1),a=e.length}}const s=[];let o="",a=e.length;for(let t=0;t<a;t+=1){let a="";e[t]&&(a=e[t][0]),"C"!==a&&(s[t]=a,t>0&&(o=s[t-1])),e[t]=r(e[t],n,o),"A"!==s[t]&&"C"===a&&(s[t]="C"),i(e,t);const l=e[t],c=l.length;n.x=l[c-2],n.y=l[c-1],n.bx=parseFloat(l[c-4])||n.x,n.by=parseFloat(l[c-3])||n.y}return e[0][0]&&"M"===e[0][0]||e.unshift(["M",0,0]),e}function Zy(e){return Yy(e).map((e=>e.map((e=>"string"==typeof e?e:t.GeometryUtil.round(e,2))))).join(",").split(",").join(" ")}!function(t){t.create=function(...e){const n=e.length,r=e[0];if(zy.isCurve(r))return new t(r);if(Ty.isPointLike(r)){if(3===n)return new t(e[0],e[1],e[2]);const r=[];for(let i=0;i<n;i+=3)r.push(new t(e[i],e[i+1],e[i+2]));return r}if(6===n)return new t(e[0],e[1],e[2],e[3],e[4],e[5]);const i=[];for(let r=0;r<n;r+=6)i.push(new t(e[r],e[r+1],e[r+2],e[r+3],e[r+4],e[r+5]));return i}}(qy||(qy={}));let Ky=class e extends Oy{constructor(t){if(super(),this.PRECISION=3,this.segments=[],Array.isArray(t))if(Ny.isLine(t[0])||zy.isCurve(t[0])){let n=null;t.forEach(((t,r)=>{0===r&&this.appendSegment(e.createSegment("M",t.start)),null==n||n.end.equals(t.start)||this.appendSegment(e.createSegment("M",t.start)),Ny.isLine(t)?this.appendSegment(e.createSegment("L",t.end)):zy.isCurve(t)&&this.appendSegment(e.createSegment("C",t.controlPoint1,t.controlPoint2,t.end)),n=t}))}else{t.forEach((t=>{t.isSegment&&this.appendSegment(t)}))}else null!=t&&(Ny.isLine(t)?(this.appendSegment(e.createSegment("M",t.start)),this.appendSegment(e.createSegment("L",t.end))):zy.isCurve(t)?(this.appendSegment(e.createSegment("M",t.start)),this.appendSegment(e.createSegment("C",t.controlPoint1,t.controlPoint2,t.end))):Vy.isPolyline(t)?t.points&&t.points.length&&t.points.forEach(((t,n)=>{const r=0===n?e.createSegment("M",t):e.createSegment("L",t);this.appendSegment(r)})):t.isSegment&&this.appendSegment(t))}get start(){const t=this.segments,e=t.length;if(0===e)return null;for(let n=0;n<e;n+=1){const e=t[n];if(e.isVisible)return e.start}return t[e-1].end}get end(){const t=this.segments,e=t.length;if(0===e)return null;for(let n=e-1;n>=0;n-=1){const e=t[n];if(e.isVisible)return e.end}return t[e-1].end}moveTo(...t){return this.appendSegment(Uy.create.call(null,...t))}lineTo(...t){return this.appendSegment(Fy.create.call(null,...t))}curveTo(...t){return this.appendSegment(qy.create.call(null,...t))}arcTo(t,e,n,r,i,s,o){const a=this.end||new Ty,l="number"==typeof s?Dy(a.x,a.y,t,e,n,r,i,s,o):Dy(a.x,a.y,t,e,n,r,i,s.x,s.y);if(null!=l)for(let t=0,e=l.length;t<e;t+=6)this.curveTo(l[t],l[t+1],l[t+2],l[t+3],l[t+4],l[t+5]);return this}quadTo(t,n,r,i){const s=this.end||new Ty,o=["M",s.x,s.y];if("number"==typeof t)o.push("Q",t,n,r,i);else{const e=n;o.push("Q",t.x,t.y,e.x,e.y)}const a=e.parse(o.join(" "));return this.appendSegment(a.segments.slice(1)),this}close(){return this.appendSegment(Gy.create())}drawPoints(t,n={}){const r=Iy(t,n),i=e.parse(r);i&&i.segments&&this.appendSegment(i.segments)}bbox(){const t=this.segments,e=t.length;if(0===e)return null;let n;for(let r=0;r<e;r+=1){const e=t[r];if(e.isVisible){const t=e.bbox();null!=t&&(n=n?n.union(t):t)}}if(null!=n)return n;const r=t[e-1];return new ky(r.end.x,r.end.y,0,0)}appendSegment(t){const e=this.segments.length;let n,r=0!==e?this.segments[e-1]:null;if(Array.isArray(t))for(let e=0,i=t.length;e<i;e+=1){const i=t[e];n=this.prepareSegment(i,r,null),this.segments.push(n),r=n}else null!=t&&t.isSegment&&(n=this.prepareSegment(t,r,null),this.segments.push(n));return this}insertSegment(t,e){const n=this.segments.length;if(t<0&&(t=n+t+1),t>n||t<0)throw new Error("Index out of range.");let r,i=null,s=null;if(0!==n&&(t>=1?(i=this.segments[t-1],s=i.nextSegment):(i=null,s=this.segments[0])),Array.isArray(e))for(let n=0,o=e.length;n<o;n+=1){const o=e[n];r=this.prepareSegment(o,i,s),this.segments.splice(t+n,0,r),i=r}else r=this.prepareSegment(e,i,s),this.segments.splice(t,0,r);return this}removeSegment(t){const e=this.fixIndex(t),n=this.segments.splice(e,1)[0],r=n.previousSegment,i=n.nextSegment;return r&&(r.nextSegment=i),i&&(i.previousSegment=r),n.isSubpathStart&&i&&this.updateSubpathStartSegment(i),n}replaceSegment(t,e){const n=this.fixIndex(t);let r;const i=this.segments[n];let s=i.previousSegment;const o=i.nextSegment;let a=i.isSubpathStart;if(Array.isArray(e)){this.segments.splice(t,1);for(let n=0,i=e.length;n<i;n+=1){const i=e[n];r=this.prepareSegment(i,s,o),this.segments.splice(t+n,0,r),s=r,a&&r.isSubpathStart&&(a=!1)}}else r=this.prepareSegment(e,s,o),this.segments.splice(n,1,r),a&&r.isSubpathStart&&(a=!1);a&&o&&this.updateSubpathStartSegment(o)}getSegment(t){const e=this.fixIndex(t);return this.segments[e]}fixIndex(t){const e=this.segments.length;if(0===e)throw new Error("Path has no segments.");let n=t;for(;n<0;)n=e+n;if(n>=e||n<0)throw new Error("Index out of range.");return n}segmentAt(t,e={}){const n=this.segmentIndexAt(t,e);return n?this.getSegment(n):null}segmentAtLength(t,e={}){const n=this.segmentIndexAtLength(t,e);return n?this.getSegment(n):null}segmentIndexAt(e,n={}){if(0===this.segments.length)return null;const r=t.GeometryUtil.clamp(e,0,1),i=this.getOptions(n),s=this.length(i)*r;return this.segmentIndexAtLength(s,i)}segmentIndexAtLength(t,e={}){const n=this.segments.length;if(0===n)return null;let r=!0;t<0&&(r=!1,t=-t);const i=this.getPrecision(e),s=this.getSubdivisions(e);let o=0,a=null;for(let e=0;e<n;e+=1){const l=r?e:n-1-e,c=this.segments[l],h=s[l],u=c.length({precision:i,subdivisions:h});if(c.isVisible){if(t<=o+u)return l;a=l}o+=u}return a}getSegmentSubdivisions(t={}){const e=this.getPrecision(t),n=[];for(let t=0,r=this.segments.length;t<r;t+=1){const r=this.segments[t].getSubdivisions({precision:e});n.push(r)}return n}updateSubpathStartSegment(t){let e=t.previousSegment,n=t;for(;n&&!n.isSubpathStart;)n.subpathStartSegment=null!=e?e.subpathStartSegment:null,e=n,n=n.nextSegment}prepareSegment(t,e,n){t.previousSegment=e,t.nextSegment=n,null!=e&&(e.nextSegment=t),null!=n&&(n.previousSegment=t);let r=t;return t.isSubpathStart&&(t.subpathStartSegment=t,r=n),null!=r&&this.updateSubpathStartSegment(r),t}closestPoint(t,e={}){const n=this.closestPointT(t,e);return n?this.pointAtT(n):null}closestPointLength(t,e={}){const n=this.getOptions(e),r=this.closestPointT(t,n);return r?this.lengthAtT(r,n):0}closestPointNormalizedLength(t,e={}){const n=this.getOptions(e),r=this.closestPointLength(t,n);if(0===r)return 0;const i=this.length(n);return 0===i?0:r/i}closestPointT(e,n={}){if(0===this.segments.length)return null;const r=this.getPrecision(n),i=this.getSubdivisions(n);let s,o=1/0;for(let n=0,a=this.segments.length;n<a;n+=1){const a=this.segments[n],l=i[n];if(a.isVisible){const i=a.closestPointT(e,{precision:r,subdivisions:l}),c=a.pointAtT(i),h=t.GeometryUtil.squaredLength(c,e);h<o&&(s={segmentIndex:n,value:i},o=h)}}return s||{segmentIndex:this.segments.length-1,value:1}}closestPointTangent(e,n={}){if(0===this.segments.length)return null;const r=this.getPrecision(n),i=this.getSubdivisions(n);let s,o=1/0;for(let n=0,a=this.segments.length;n<a;n+=1){const a=this.segments[n],l=i[n];if(a.isDifferentiable()){const n=a.closestPointT(e,{precision:r,subdivisions:l}),i=a.pointAtT(n),c=t.GeometryUtil.squaredLength(i,e);c<o&&(s=a.tangentAtT(n),o=c)}}return s||null}containsPoint(t,e={}){const n=this.toPolylines(e);if(!n)return!1;let r=0;for(let e=0,i=n.length;e<i;e+=1){n[e].containsPoint(t)&&(r+=1)}return r%2==1}pointAt(t,e={}){if(0===this.segments.length)return null;if(t<=0)return this.start.clone();if(t>=1)return this.end.clone();const n=this.getOptions(e),r=this.length(n)*t;return this.pointAtLength(r,n)}pointAtLength(t,e={}){if(0===this.segments.length)return null;if(0===t)return this.start.clone();let n=!0;t<0&&(n=!1,t=-t);const r=this.getPrecision(e),i=this.getSubdivisions(e);let s,o=0;for(let e=0,a=this.segments.length;e<a;e+=1){const l=n?e:a-1-e,c=this.segments[l],h=i[l],u=c.length({precision:r,subdivisions:h});if(c.isVisible){if(t<=o+u)return c.pointAtLength((n?1:-1)*(t-o),{precision:r,subdivisions:h});s=c}o+=u}if(s)return n?s.end:s.start;return this.segments[this.segments.length-1].end.clone()}pointAtT(e){const n=this.segments,r=n.length;if(0===r)return null;const i=e.segmentIndex;if(i<0)return n[0].pointAtT(0);if(i>=r)return n[r-1].pointAtT(1);const s=t.GeometryUtil.clamp(e.value,0,1);return n[i].pointAtT(s)}divideAt(e,n={}){if(0===this.segments.length)return null;const r=t.GeometryUtil.clamp(e,0,1),i=this.getOptions(n),s=this.length(i)*r;return this.divideAtLength(s,i)}divideAtLength(t,n={}){if(0===this.segments.length)return null;let r=!0;t<0&&(r=!1,t=-t);const i=this.getPrecision(n),s=this.getSubdivisions(n);let o,a,l,c,h,u=0;for(let e=0,n=this.segments.length;e<n;e+=1){const h=r?e:n-1-e,g=this.getSegment(h),d={precision:i,subdivisions:s[h]},f=g.length(d);if(g.isDifferentiable()&&(l=g,c=h,t<=u+f)){a=h,o=g.divideAtLength((r?1:-1)*(t-u),d);break}u+=f}if(!l)return null;o||(a=c,h=r?1:0,o=l.divideAtT(h));const g=this.clone(),d=a;g.replaceSegment(d,o);const f=d;let p=d+1,m=d+2;o[0].isDifferentiable()||(g.removeSegment(f),p-=1,m-=1);const y=g.getSegment(p).start;g.insertSegment(p,e.createSegment("M",y)),m+=1,o[1].isDifferentiable()||(g.removeSegment(m-1),m-=1);const v=m-f-1;for(let t=m,n=g.segments.length;t<n;t+=1){const n=this.getSegment(t-v),r=g.getSegment(t);if("Z"===r.type&&!n.subpathStartSegment.end.equals(r.subpathStartSegment.end)){const r=e.createSegment("L",n.end);g.replaceSegment(t,r)}}return[new e(g.segments.slice(0,p)),new e(g.segments.slice(p))]}intersectsWithLine(t,e={}){const n=this.toPolylines(e);if(null==n)return null;let r=null;for(let e=0,i=n.length;e<i;e+=1){const i=n[e],s=t.intersect(i);s&&(null==r&&(r=[]),Array.isArray(s)?r.push(...s):r.push(s))}return r}isDifferentiable(){for(let t=0,e=this.segments.length;t<e;t+=1){if(this.segments[t].isDifferentiable())return!0}return!1}isValid(){const t=this.segments;return 0===t.length||"M"===t[0].type}length(t={}){if(0===this.segments.length)return 0;const e=this.getSubdivisions(t);let n=0;for(let t=0,r=this.segments.length;t<r;t+=1){const r=this.segments[t],i=e[t];n+=r.length({subdivisions:i})}return n}lengthAtT(e,n={}){const r=this.segments.length;if(0===r)return 0;let i=e.segmentIndex;if(i<0)return 0;let s=t.GeometryUtil.clamp(e.value,0,1);i>=r&&(i=r-1,s=1);const o=this.getPrecision(n),a=this.getSubdivisions(n);let l=0;for(let t=0;t<i;t+=1){const e=this.segments[t],n=a[t];l+=e.length({precision:o,subdivisions:n})}const c=this.segments[i],h=a[i];return l+=c.lengthAtT(s,{precision:o,subdivisions:h}),l}tangentAt(e,n={}){if(0===this.segments.length)return null;const r=t.GeometryUtil.clamp(e,0,1),i=this.getOptions(n),s=this.length(i)*r;return this.tangentAtLength(s,i)}tangentAtLength(t,e={}){if(0===this.segments.length)return null;let n=!0;t<0&&(n=!1,t=-t);const r=this.getPrecision(e),i=this.getSubdivisions(e);let s,o=0;for(let e=0,a=this.segments.length;e<a;e+=1){const l=n?e:a-1-e,c=this.segments[l],h=i[l],u=c.length({precision:r,subdivisions:h});if(c.isDifferentiable()){if(t<=o+u)return c.tangentAtLength((n?1:-1)*(t-o),{precision:r,subdivisions:h});s=c}o+=u}if(s){const t=n?1:0;return s.tangentAtT(t)}return null}tangentAtT(e){const n=this.segments.length;if(0===n)return null;const r=e.segmentIndex;if(r<0)return this.segments[0].tangentAtT(0);if(r>=n)return this.segments[n-1].tangentAtT(1);const i=t.GeometryUtil.clamp(e.value,0,1);return this.segments[r].tangentAtT(i)}getPrecision(t={}){return null==t.precision?this.PRECISION:t.precision}getSubdivisions(t={}){if(null==t.segmentSubdivisions){const e=this.getPrecision(t);return this.getSegmentSubdivisions({precision:e})}return t.segmentSubdivisions}getOptions(t={}){return{precision:this.getPrecision(t),segmentSubdivisions:this.getSubdivisions(t)}}toPoints(t={}){const e=this.segments,n=e.length;if(0===n)return null;const r=this.getSubdivisions(t),i=[];let s=[];for(let t=0;t<n;t+=1){const n=e[t];if(n.isVisible){const e=r[t];e.length>0?e.forEach((t=>s.push(t.start))):s.push(n.start)}else s.length>0&&(s.push(e[t-1].end),i.push(s),s=[])}return s.length>0&&(s.push(this.end),i.push(s)),i}toPolylines(t={}){const e=this.toPoints(t);return e?e.map((t=>new Vy(t))):null}scale(t,e,n){return this.segments.forEach((r=>r.scale(t,e,n))),this}rotate(t,e){return this.segments.forEach((n=>n.rotate(t,e))),this}translate(t,e){return"number"==typeof t?this.segments.forEach((n=>n.translate(t,e))):this.segments.forEach((e=>e.translate(t))),this}clone(){const t=new e;return this.segments.forEach((e=>t.appendSegment(e.clone()))),t}equals(t){if(null==t)return!1;const e=this.segments,n=t.segments,r=e.length;if(n.length!==r)return!1;for(let t=0;t<r;t+=1){const r=e[t],i=n[t];if(r.type!==i.type||!r.equals(i))return!1}return!0}toJSON(){return this.segments.map((t=>t.toJSON()))}serialize(){if(!this.isValid())throw new Error("Invalid path segments.");return this.segments.map((t=>t.serialize())).join(" ")}toString(){return this.serialize()}};!function(t){t.isPath=function(e){return null!=e&&e instanceof t}}(Ky||(Ky={})),function(t){function e(t,...e){if("M"===t)return Uy.create.call(null,...e);if("L"===t)return Fy.create.call(null,...e);if("C"===t)return qy.create.call(null,...e);if("z"===t||"Z"===t)return Gy.create();throw new Error(`Invalid path segment type "${t}"`)}t.parse=function(n){if(!n)return new t;const r=new t,i=t.normalize(n).match(/(?:[a-zA-Z] *)(?:(?:-?\d+(?:\.\d+)?(?:e[-+]?\d+)? *,? *)|(?:-?\.\d+ *,? *))+|(?:[a-zA-Z] *)(?! |\d|-|\.)/g);if(null!=i)for(let t=0,n=i.length;t<n;t+=1){const n=/(?:[a-zA-Z])|(?:(?:-?\d+(?:\.\d+)?(?:e[-+]?\d+)?))|(?:(?:-?\.\d+))/g,s=i[t].match(n);if(null!=s){const t=s[0],n=s.slice(1).map((t=>+t)),i=e.call(null,t,...n);r.appendSegment(i)}}return r},t.createSegment=e}(Ky||(Ky={})),function(t){t.normalize=Zy,t.isValid=Ly,t.drawArc=Ry,t.drawPoints=Iy,t.arcToCurves=Dy}(Ky||(Ky={}));class Qy{constructor(t){this.options=Object.assign({},t),this.data=this.options.data||{},this.register=this.register.bind(this),this.unregister=this.unregister.bind(this)}get names(){return Object.keys(this.data)}register(e,n,r=!1){if("object"==typeof e)return void Object.entries(e).forEach((([t,e])=>{this.register(t,e,n)}));!this.exist(e)||r||t.Platform.isApplyingHMR()||this.onDuplicated(e);const i=this.options.process,s=i?ef(i,this,e,n):n;return this.data[e]=s,s}unregister(t){const e=t?this.data[t]:null;return delete this.data[t],e}get(t){return t?this.data[t]:null}exist(t){return!!t&&null!=this.data[t]}onDuplicated(t){try{throw this.options.onConflict&&ef(this.options.onConflict,this,t),new Error(`${ri(this.options.type)} with name '${t}' already registered.`)}catch(t){throw t}}onNotFound(t,e){throw new Error(this.getSpellingSuggestion(t,e))}getSpellingSuggestion(t,e){const n=this.getSpellingSuggestionForName(t),r=e?`${e} ${mh(this.options.type)}`:this.options.type;return`${ri(r)} with name '${t}' does not exist.${n?` Did you mean '${n}'?`:""}`}getSpellingSuggestionForName(t){return jf(t,Object.keys(this.data),(t=>t))}}!function(t){t.create=function(e){return new t(e)}}(Qy||(Qy={}));const tv={color:"#aaaaaa",thickness:1,markup:"rect",update(t,e){const n=e.thickness*e.sx,r=e.thickness*e.sy;zp(t,{width:n,height:r,rx:n,ry:r,fill:e.color})}},ev={color:"#aaaaaa",thickness:1,markup:"rect",update(t,e){const n=e.sx<=1?e.thickness*e.sx:e.thickness;zp(t,{width:n,height:n,rx:n,ry:n,fill:e.color})}},nv={color:"rgba(224,224,224,1)",thickness:1,markup:"path",update(t,e){let n;const r=e.width,i=e.height,s=e.thickness;n=r-s>=0&&i-s>=0?["M",r,0,"H0 M0 0 V0",i].join(" "):"M 0 0 0 0",zp(t,{d:n,stroke:e.color,"stroke-width":e.thickness})}},rv=[{color:"rgba(224,224,224,1)",thickness:1,markup:"path",update(t,e){let n;const r=e.width,i=e.height,s=e.thickness;n=r-s>=0&&i-s>=0?["M",r,0,"H0 M0 0 V0",i].join(" "):"M 0 0 0 0",zp(t,{d:n,stroke:e.color,"stroke-width":e.thickness})}},{color:"rgba(224,224,224,0.2)",thickness:3,factor:4,markup:"path",update(t,e){let n;const r=e.factor||1,i=e.width*r,s=e.height*r,o=e.thickness;n=i-o>=0&&s-o>=0?["M",i,0,"H0 M0 0 V0",s].join(" "):"M 0 0 0 0",e.width=i,e.height=s,zp(t,{d:n,stroke:e.color,"stroke-width":e.thickness})}}];var iv=Object.freeze({__proto__:null,dot:tv,doubleMesh:rv,fixedDot:ev,mesh:nv});class sv{constructor(){this.patterns={},this.root=dm.create(bp(),{width:"100%",height:"100%"},[vp("defs")]).node}add(t,e){const n=this.root.childNodes[0];n&&n.appendChild(e),this.patterns[t]=e,dm.create("rect",{width:"100%",height:"100%",fill:`url(#${t})`}).appendTo(this.root)}get(t){return this.patterns[t]}has(t){return null!=this.patterns[t]}}!function(t){t.presets=iv,t.registry=Qy.create({type:"grid"}),t.registry.register(t.presets,!0)}(sv||(sv={}));const ov=function(t){const e=document.createElement("canvas"),n=t.width,r=t.height;e.width=2*n,e.height=r;const i=e.getContext("2d");return i.drawImage(t,0,0,n,r),i.translate(2*n,0),i.scale(-1,1),i.drawImage(t,0,0,n,r),e},av=function(t){const e=document.createElement("canvas"),n=t.width,r=t.height;e.width=n,e.height=2*r;const i=e.getContext("2d");return i.drawImage(t,0,0,n,r),i.translate(0,2*r),i.scale(1,-1),i.drawImage(t,0,0,n,r),e},lv=function(t){const e=document.createElement("canvas"),n=t.width,r=t.height;e.width=2*n,e.height=2*r;const i=e.getContext("2d");return i.drawImage(t,0,0,n,r),i.setTransform(-1,0,0,-1,e.width,e.height),i.drawImage(t,0,0,n,r),i.setTransform(-1,0,0,1,e.width,0),i.drawImage(t,0,0,n,r),i.setTransform(1,0,0,-1,0,e.height),i.drawImage(t,0,0,n,r),e};var cv,hv=Object.freeze({__proto__:null,flipX:ov,flipXY:lv,flipY:av,watermark:function(e,n){const r=e.width,i=e.height,s=document.createElement("canvas");s.width=3*r,s.height=3*i;const o=s.getContext("2d"),a=null!=n.angle?-n.angle:-20,l=t.Angle.toRad(a),c=s.width/4,h=s.height/4;for(let t=0;t<4;t+=1)for(let n=0;n<4;n+=1)(t+n)%2>0&&(o.setTransform(1,0,0,1,(2*t-1)*c,(2*n-1)*h),o.rotate(l),o.drawImage(e,-r/2,-i/2,r,i));return s}});function uv(t,e){return null!=t?t:e}function gv(t,e){return null!=t&&Number.isFinite(t)?t:e}!function(t){t.presets=Object.assign({},hv),t.presets["flip-x"]=ov,t.presets["flip-y"]=av,t.presets["flip-xy"]=lv,t.registry=Qy.create({type:"background pattern"}),t.registry.register(t.presets,!0)}(cv||(cv={}));var dv,fv=Object.freeze({__proto__:null,blur:function(t={}){const e=gv(t.x,2);return`\n    <filter>\n      <feGaussianBlur stdDeviation="${null!=t.y&&Number.isFinite(t.y)?[e,t.y]:e}"/>\n    </filter>\n  `.trim()},brightness:function(t={}){const e=gv(t.amount,1);return`\n    <filter>\n      <feComponentTransfer>\n        <feFuncR type="linear" slope="${e}"/>\n        <feFuncG type="linear" slope="${e}"/>\n        <feFuncB type="linear" slope="${e}"/>\n      </feComponentTransfer>\n    </filter>\n  `.trim()},contrast:function(t={}){const e=gv(t.amount,1),n=.5-e/2;return`\n    <filter>\n     <feComponentTransfer>\n        <feFuncR type="linear" slope="${e}" intercept="${n}"/>\n        <feFuncG type="linear" slope="${e}" intercept="${n}"/>\n        <feFuncB type="linear" slope="${e}" intercept="${n}"/>\n      </feComponentTransfer>\n    </filter>\n  `.trim()},dropShadow:function(t={}){const e=gv(t.dx,0),n=gv(t.dy,0),r=uv(t.color,"black"),i=gv(t.blur,4),s=gv(t.opacity,1);return"SVGFEDropShadowElement"in window?`<filter>\n         <feDropShadow stdDeviation="${i}" dx="${e}" dy="${n}" flood-color="${r}" flood-opacity="${s}" />\n       </filter>`.trim():`<filter>\n         <feGaussianBlur in="SourceAlpha" stdDeviation="${i}" />\n         <feOffset dx="${e}" dy="${n}" result="offsetblur" />\n         <feFlood flood-color="${r}" />\n         <feComposite in2="offsetblur" operator="in" />\n         <feComponentTransfer>\n           <feFuncA type="linear" slope="${s}" />\n         </feComponentTransfer>\n         <feMerge>\n           <feMergeNode/>\n           <feMergeNode in="SourceGraphic"/>\n         </feMerge>\n       </filter>`.trim()},grayScale:function(t={}){const e=gv(t.amount,1),n=.7152-.7152*(1-e);return`\n    <filter>\n      <feColorMatrix type="matrix" values="${.2126+.7874*(1-e)} ${n} ${.0722-.0722*(1-e)} 0 0 ${.2126-.2126*(1-e)} ${.7152+.2848*(1-e)} ${.0722-.0722*(1-e)} 0 0 ${.2126-.2126*(1-e)} ${n} ${.0722+.9278*(1-e)} 0 0 0 0 0 1 0"/>\n    </filter>\n  `.trim()},highlight:function(t={}){const e=uv(t.color,"red"),n=gv(t.blur,0),r=gv(t.width,1);return`\n      <filter>\n        <feFlood flood-color="${e}" flood-opacity="${gv(t.opacity,1)}" result="colored"/>\n        <feMorphology result="morphed" in="SourceGraphic" operator="dilate" radius="${r}"/>\n        <feComposite result="composed" in="colored" in2="morphed" operator="in"/>\n        <feGaussianBlur result="blured" in="composed" stdDeviation="${n}"/>\n        <feBlend in="SourceGraphic" in2="blured" mode="normal"/>\n      </filter>\n    `.trim()},hueRotate:function(t={}){return`\n      <filter>\n        <feColorMatrix type="hueRotate" values="${gv(t.angle,0)}"/>\n      </filter>\n    `.trim()},invert:function(t={}){const e=gv(t.amount,1),n=1-e;return`\n      <filter>\n        <feComponentTransfer>\n          <feFuncR type="table" tableValues="${e} ${n}"/>\n          <feFuncG type="table" tableValues="${e} ${n}"/>\n          <feFuncB type="table" tableValues="${e} ${n}"/>\n        </feComponentTransfer>\n      </filter>\n    `.trim()},outline:function(t={}){const e=uv(t.color,"blue"),n=gv(t.width,1),r=gv(t.margin,2);return`\n    <filter>\n      <feFlood flood-color="${e}" flood-opacity="${gv(t.opacity,1)}" result="colored"/>\n      <feMorphology in="SourceAlpha" result="morphedOuter" operator="dilate" radius="${r+n}" />\n      <feMorphology in="SourceAlpha" result="morphedInner" operator="dilate" radius="${r}" />\n      <feComposite result="morphedOuterColored" in="colored" in2="morphedOuter" operator="in"/>\n      <feComposite operator="xor" in="morphedOuterColored" in2="morphedInner" result="outline"/>\n      <feMerge>\n        <feMergeNode in="outline"/>\n        <feMergeNode in="SourceGraphic"/>\n      </feMerge>\n    </filter>\n  `.trim()},saturate:function(t={}){return`\n      <filter>\n        <feColorMatrix type="saturate" values="${1-gv(t.amount,1)}"/>\n      </filter>\n    `.trim()},sepia:function(t={}){const e=gv(t.amount,1);return`\n      <filter>\n        <feColorMatrix type="matrix" values="${.393+.607*(1-e)} ${.769-.769*(1-e)} ${.189-.189*(1-e)} 0 0 ${.349-.349*(1-e)} ${.686+.314*(1-e)} ${.168-.168*(1-e)} 0 0 ${.272-.272*(1-e)} ${.534-.534*(1-e)} ${.131+.869*(1-e)} 0 0 0 0 0 1 0"/>\n      </filter>\n    `.trim()}});!function(t){t.presets=fv,t.registry=Qy.create({type:"filter"}),t.registry.register(t.presets,!0)}(dv||(dv={}));const pv={xlinkHref:"xlink:href",xlinkShow:"xlink:show",xlinkRole:"xlink:role",xlinkType:"xlink:type",xlinkArcrole:"xlink:arcrole",xlinkTitle:"xlink:title",xlinkActuate:"xlink:actuate",xmlSpace:"xml:space",xmlBase:"xml:base",xmlLang:"xml:lang",preserveAspectRatio:"preserveAspectRatio",requiredExtension:"requiredExtension",requiredFeatures:"requiredFeatures",systemLanguage:"systemLanguage",externalResourcesRequired:"externalResourceRequired"},mv={position:Vv("x","width","origin")},yv={position:Vv("y","height","origin")},vv={position:Vv("x","width","corner")},bv={position:Vv("y","height","corner")},xv={set:zv("width","width")},wv={set:zv("height","height")},Av={set:zv("rx","width")},Pv={set:zv("ry","height")},Cv={set:(t=>{const e=zv("r","width"),n=zv("r","height");return function(t,r){const i=r.refBBox;return ef(i.height>i.width?e:n,this,t,r)}})()},Mv={set(t,{refBBox:e}){let n=parseFloat(t);const r=Bf(t);r&&(n/=100);const i=Math.sqrt(e.height*e.height+e.width*e.width);let s;return Number.isFinite(n)&&(s=r||n>=0&&n<=1?n*i:Math.max(n+i,0)),{r:s}}},Ev={set:zv("cx","width")},Sv={set:zv("cy","height")},Ov={set:Fv({resetOffset:!0})},Tv={set:Fv({resetOffset:!1})},kv={set:Gv({resetOffset:!0})},Nv={set:Gv({resetOffset:!1})},jv=Cv,_v=Ov,Lv=kv,Bv=mv,Iv=yv,Dv=xv,Rv=wv;function Vv(t,e,n){return(r,{refBBox:i})=>{if(null==r)return null;let s=parseFloat(r);const o=Bf(r);let a;if(o&&(s/=100),Number.isFinite(s)){const r=i[n];a=o||s>0&&s<1?r[t]+i[e]*s:r[t]+s}const l=new Ty;return l[t]=a||0,l}}function zv(t,e){return function(n,{refBBox:r}){let i=parseFloat(n);const s=Bf(n);s&&(i/=100);const o={};if(Number.isFinite(i)){const n=s||i>=0&&i<=1?i*r[e]:Math.max(i+r[e],0);o[t]=n}return o}}function $v(t,e){const n="x6-shape",r=e&&e.resetOffset;return function(e,{elem:i,refBBox:s}){let o=um(i,n);if(!o||o.value!==e){const r=t(e);o={value:e,shape:r,shapeBBox:r.bbox()},um(i,n,o)}const a=o.shape.clone(),l=o.shapeBBox.clone(),c=l.getOrigin(),h=s.getOrigin();l.x=h.x,l.y=h.y;const u=s.getMaxScaleToFit(l,h),g=0===l.width||0===s.width?1:u.sx,d=0===l.height||0===s.height?1:u.sy;return a.scale(g,d,c),r&&a.translate(-c.x,-c.y),a}}function Fv(t){const e=$v((function(t){return Ky.parse(t)}),t);return(t,n)=>({d:e(t,n).serialize()})}function Gv(t){const e=$v((t=>new Vy(t)),t);return(t,n)=>({points:e(t,n).serialize()})}const Uv={qualify:Tr,set:(t,{view:e})=>`url(#${e.graph.defineGradient(t)})`},qv={qualify:Tr,set(t,{view:e}){const n=e.cell,r=Object.assign({},t);if(n.isEdge()&&"linearGradient"===r.type){const t=e,i=t.sourcePoint,s=t.targetPoint;r.id=`gradient-${r.type}-${n.id}`,r.attrs=Object.assign(Object.assign({},r.attrs),{x1:i.x,y1:i.y,x2:s.x,y2:s.y,gradientUnits:"userSpaceOnUse"}),e.graph.defs.remove(r.id)}return`url(#${e.graph.defineGradient(r)})`}},Wv={qualify:(t,{attrs:e})=>null==e.textWrap||!Tr(e.textWrap),set(t,{view:e,elem:n,attrs:r}){const i="x6-text",s=um(n,i),o=t=>{try{return JSON.parse(t)}catch(e){return t}},a={x:r.x,eol:r.eol,annotations:o(r.annotations),textPath:o(r["text-path"]||r.textPath),textVerticalAnchor:r["text-vertical-anchor"]||r.textVerticalAnchor,displayEmpty:"true"===(r["display-empty"]||r.displayEmpty),lineHeight:r["line-height"]||r.lineHeight},l=r["font-size"]||r.fontSize,c=JSON.stringify([t,a]);if(l&&n.setAttribute("font-size",l),null==s||s!==c){const r=a.textPath;if(null!=r&&"object"==typeof r){const t=r.selector;if("string"==typeof t){const n=e.find(t)[0];n instanceof SVGPathElement&&(gp(n),a.textPath=Object.assign({"xlink:href":`#${n.id}`},r))}}vm(n,`${t}`,a),um(n,i,c)}}},Hv={qualify:Tr,set(t,{view:e,elem:n,attrs:r,refBBox:i}){const s=t,o=s.width||0;Bf(o)?i.width*=parseFloat(o)/100:o<=0?i.width+=o:i.width=o;const a=s.height||0;let l;Bf(a)?i.height*=parseFloat(a)/100:a<=0?i.height+=a:i.height=a;let c=s.text;null==c&&(c=r.text),l=null!=c?wm(`${c}`,i,{"font-weight":r["font-weight"]||r.fontWeight,"font-size":r["font-size"]||r.fontSize,"font-family":r["font-family"]||r.fontFamily,lineHeight:r.lineHeight},{ellipsis:s.ellipsis}):"",ef(Wv.set,this,l,{view:e,elem:n,attrs:r,refBBox:i,cell:e.cell})}},Jv=(t,{attrs:e})=>void 0!==e.text,Xv={qualify:Jv},Yv={qualify:Jv},Zv={qualify:Jv},Kv={qualify:Jv},Qv={qualify:Jv},tb={qualify:Jv},eb={qualify:(t,{elem:e})=>e instanceof SVGElement,set(t,{elem:e}){const n="x6-title",r=`${t}`,i=um(e,n);if(null==i||i!==r){um(e,n,r);const t=e.firstChild;if(t&&"TITLE"===t.tagName.toUpperCase()){t.textContent=r}else{const n=document.createElementNS(e.namespaceURI,"title");n.textContent=r,e.insertBefore(n,t)}}}},nb={offset:sb("x","width","right")},rb={offset:sb("y","height","bottom")},ib={offset:(t,{refBBox:e})=>t?{x:-e.x,y:-e.y}:{x:0,y:0}};function sb(t,e,n){return(r,{refBBox:i})=>{const s=new Ty;let o;return o="middle"===r?i[e]/2:r===n?i[e]:"number"==typeof r&&Number.isFinite(r)?r>-1&&r<1?-i[e]*r:-r:Bf(r)?i[e]*parseFloat(r)/100:0,s[t]=-(i[t]+o),s}}const ob={qualify:Tr,set(t,{elem:e}){am(e,t)}},ab={set(t,{elem:e}){e.innerHTML=`${t}`}},lb={qualify:Tr,set:(t,{view:e})=>`url(#${e.graph.defineFilter(t)})`},cb={set:t=>null!=t&&"object"==typeof t&&t.id?t.id:t};function hb(t,e,n){let r,i;"object"==typeof e?(r=e.x,i=e.y):(r=e,i=n);const s=Ky.parse(t),o=s.bbox();if(o){let t=-o.height/2-o.y,e=-o.width/2-o.x;"number"==typeof r&&(e-=r),"number"==typeof i&&(t-=i),s.translate(e,t)}return s.serialize()}function ub(t,e,n,r=3/4,i={}){const s=t.size||10,o=t.width||s,a=t.height||s,l=new Ky,c={};if(e)l.moveTo(o,0).lineTo(0,a/2).lineTo(o,a),c.fill="none";else{if(l.moveTo(0,a/2),l.lineTo(o,0),!n){const t=Hi(r,0,1);l.lineTo(o*t,a/2)}l.lineTo(o,a),l.close()}return Object.assign(Object.assign(Object.assign({},c),i),{tagName:"path",d:hb(l.serialize(),{x:null!=t.offset?t.offset:-o/2})})}const gb=t=>{var{r:n}=t,r=e(t,["r"]);const i=n||5;return Object.assign(Object.assign({cx:i},r),{tagName:"circle",r:i})};var db,fb=Object.freeze({__proto__:null,async:t=>{var{width:n,height:r,offset:i,open:s,flip:o}=t,a=e(t,["width","height","offset","open","flip"]);let l=r||6;const c=n||10,h=!0===s,u=!0===o,g=Object.assign(Object.assign({},a),{tagName:"path"});u&&(l=-l);const d=new Ky;return d.moveTo(0,l).lineTo(c,0),h?g.fill="none":(d.lineTo(c,l),d.close()),g.d=hb(d.serialize(),{x:i||-c/2,y:l/2}),g},block:t=>{var{size:n,width:r,height:i,offset:s,open:o}=t;return ub({size:n,width:r,height:i,offset:s},!0===o,!0,void 0,e(t,["size","width","height","offset","open"]))},circle:gb,circlePlus:t=>{var{r:n}=t,r=e(t,["r"]);const i=n||5,s=new Ky;return s.moveTo(i,0).lineTo(i,2*i),s.moveTo(0,i).lineTo(2*i,i),{children:[Object.assign(Object.assign({},gb({r:i})),{fill:"none"}),Object.assign(Object.assign({},r),{tagName:"path",d:hb(s.serialize(),-i)})]}},classic:t=>{var{size:n,width:r,height:i,offset:s,factor:o}=t;return ub({size:n,width:r,height:i,offset:s},!1,!1,o,e(t,["size","width","height","offset","factor"]))},cross:t=>{var{size:n,width:r,height:i,offset:s}=t,o=e(t,["size","width","height","offset"]);const a=n||10,l=r||a,c=i||a,h=new Ky;return h.moveTo(0,0).lineTo(l,c).moveTo(0,c).lineTo(l,0),Object.assign(Object.assign({},o),{tagName:"path",fill:"none",d:hb(h.serialize(),s||-l/2)})},diamond:t=>{var{size:n,width:r,height:i,offset:s}=t,o=e(t,["size","width","height","offset"]);const a=n||10,l=r||a,c=i||a,h=new Ky;return h.moveTo(0,c/2).lineTo(l/2,0).lineTo(l,c/2).lineTo(l/2,c).close(),Object.assign(Object.assign({},o),{tagName:"path",d:hb(h.serialize(),null==s?-l/2:s)})},ellipse:t=>{var{rx:n,ry:r}=t,i=e(t,["rx","ry"]);const s=n||5,o=r||5;return Object.assign(Object.assign({cx:s},i),{tagName:"ellipse",rx:s,ry:o})},path:t=>{var{d:n,offsetX:r,offsetY:i}=t,s=e(t,["d","offsetX","offsetY"]);return Object.assign(Object.assign({},s),{tagName:"path",d:hb(n,r,i)})}});function pb(t){return"string"==typeof t||Tr(t)}!function(t){t.presets=fb,t.registry=Qy.create({type:"marker"}),t.registry.register(t.presets,!0)}(db||(db={})),function(t){t.normalize=hb}(db||(db={}));const mb={qualify:pb,set:(t,{view:e,attrs:n})=>bb("marker-start",t,e,n)},yb={qualify:pb,set:(t,{view:e,attrs:n})=>bb("marker-end",t,e,n,{transform:"rotate(180)"})},vb={qualify:pb,set:(t,{view:e,attrs:n})=>bb("marker-mid",t,e,n)};function bb(t,n,r,i,s={}){const o="string"==typeof n?{name:n}:n,{name:a,args:l}=o,c=e(o,["name","args"]);let h=c;if(a&&"string"==typeof a){const t=db.registry.get(a);if(!t)return db.registry.onNotFound(a);h=t(Object.assign(Object.assign({},c),l))}const u=Object.assign(Object.assign(Object.assign({},function(t,e){const n={},r=t.stroke;"string"==typeof r&&(n.stroke=r,n.fill=r);let i=t.strokeOpacity;null==i&&(i=t["stroke-opacity"]);null==i&&(i=t.opacity);null!=i&&(n["stroke-opacity"]=i,n["fill-opacity"]=i);if("marker-mid"!==e){const r=parseFloat(t.strokeWidth||t["stroke-width"]);if(Number.isFinite(r)&&r>1){const t=Math.ceil(r/2);n.refX="marker-start"===e?t:-t}}return n}(i,t)),s),h);return{[t]:`url(#${r.graph.defineMarker(u)})`}}const xb=(t,{view:e})=>e.cell.isEdge(),wb={qualify:xb,set(t,e){var n,r,i,s;const o=e.view,a=t.reverse||!1,l=t.stubs||0;let c;if(Number.isFinite(l)&&0!==l)if(a){let t,e;const a=o.getConnectionLength()||0;l<0?(t=(a+l)/2,e=-l):(t=l,e=a-2*l);const h=o.getConnection();c=null===(s=null===(i=null===(r=null===(n=null==h?void 0:h.divideAtLength(t))||void 0===n?void 0:n[1])||void 0===r?void 0:r.divideAtLength(e))||void 0===i?void 0:i[0])||void 0===s?void 0:s.serialize()}else{let t;if(l<0){t=((o.getConnectionLength()||0)+l)/2}else t=l;const e=o.getConnection();if(e){const n=e.divideAtLength(t),r=e.divideAtLength(-t);n&&r&&(c=`${n[0].serialize()} ${r[1].serialize()}`)}}return{d:c||o.getConnectionPathData()}}},Ab={qualify:xb,set:Ob("getTangentAtLength",{rotate:!0})},Pb={qualify:xb,set:Ob("getTangentAtLength",{rotate:!1})},Cb={qualify:xb,set:Ob("getTangentAtRatio",{rotate:!0})},Mb={qualify:xb,set:Ob("getTangentAtRatio",{rotate:!1})},Eb=Ab,Sb=Cb;function Ob(t,e){const n={x:1,y:0};return(r,i)=>{let s,o;const a=i.view,l=a[t](Number(r));return l?(o=e.rotate?l.vector().vectorAngle(n):0,s=l.start):(s=a.path.start,o=0),0===o?{transform:`translate(${s.x},${s.y}')`}:{transform:`translate(${s.x},${s.y}') rotate(${o})`}}}var Tb,kb=Object.freeze({__proto__:null,annotations:Kv,atConnectionLength:Eb,atConnectionLengthIgnoreGradient:Pb,atConnectionLengthKeepGradient:Ab,atConnectionRatio:Sb,atConnectionRatioIgnoreGradient:Mb,atConnectionRatioKeepGradient:Cb,connection:wb,displayEmpty:tb,eol:Qv,fill:Uv,filter:lb,html:ab,lineHeight:Xv,port:cb,ref:{},refCx:Ev,refCy:Sv,refD:_v,refDKeepOffset:Tv,refDResetOffset:Ov,refDx:vv,refDy:bv,refHeight:wv,refHeight2:Rv,refPoints:Lv,refPointsKeepOffset:Nv,refPointsResetOffset:kv,refR:jv,refRCircumscribed:Mv,refRInscribed:Cv,refRx:Av,refRy:Pv,refWidth:xv,refWidth2:Dv,refX:mv,refX2:Bv,refY:yv,refY2:Iv,resetOffset:ib,sourceMarker:mb,stroke:qv,style:ob,targetMarker:yb,text:Wv,textPath:Zv,textVerticalAnchor:Yv,textWrap:Hv,title:eb,vertexMarker:vb,xAlign:nb,yAlign:rb});!function(t){t.isValidDefinition=function(t,e,n){if(null!=t){if("string"==typeof t)return!0;if("function"!=typeof t.qualify||ef(t.qualify,this,e,n))return!0}return!1}}(Tb||(Tb={})),function(t){t.presets=Object.assign(Object.assign({},pv),kb),t.registry=Qy.create({type:"attribute definition"}),t.registry.register(t.presets,!0)}(Tb||(Tb={}));const Nb={prefixCls:"x6",autoInsertCSS:!0,useCSSSelector:!0,prefix:t=>`${Nb.prefixCls}-${t}`},jb=Nb.prefix("highlighted"),_b={highlight(t,e,n){ap(e,n&&n.className||jb)},unhighlight(t,e,n){lp(e,n&&n.className||jb)}},Lb=Nb.prefix("highlight-opacity"),Bb={highlight(t,e){ap(e,Lb)},unhighlight(t,e){lp(e,Lb)}};t.Util=void 0,function(t){const e=vp("svg");function n(t,e){const n=zm(t.x,t.y).matrixTransform(e);return new Ty(n.x,n.y)}function r(t,n){const r=e.createSVGPoint();r.x=t.x,r.y=t.y;const i=r.matrixTransform(n);r.x=t.x+t.width,r.y=t.y;const s=r.matrixTransform(n);r.x=t.x+t.width,r.y=t.y+t.height;const o=r.matrixTransform(n);r.x=t.x,r.y=t.y+t.height;const a=r.matrixTransform(n),l=Math.min(i.x,s.x,o.x,a.x),c=Math.max(i.x,s.x,o.x,a.x),h=Math.min(i.y,s.y,o.y,a.y),u=Math.max(i.y,s.y,o.y,a.y);return new ky(l,h,c-l,u-h)}function i(t,e={}){let n;if(!t.ownerSVGElement||!dp(t)){if(Lp(t)){const{left:e,top:n,width:r,height:i}=s(t);return new ky(e,n,r,i)}return new ky(0,0,0,0)}let o=e.target;if(!e.recursive){try{n=t.getBBox()}catch(e){n={x:t.clientLeft,y:t.clientTop,width:t.clientWidth,height:t.clientHeight}}if(!o)return ky.create(n);return r(n,ey(t,o))}{const e=t.childNodes,r=e.length;if(0===r)return i(t,{target:o});o||(o=t);for(let t=0;t<r;t+=1){const r=e[t];let s;s=0===r.childNodes.length?i(r,{target:o}):i(r,{target:o,recursive:!0}),n=n?n.union(s):s}return n}}function s(t){let e=0,n=0,r=0,i=0;if(t){let s=t;for(;s;)e+=s.offsetLeft,n+=s.offsetTop,s=s.offsetParent,s&&(e+=parseInt(em(s,"borderLeft"),10),n+=parseInt(em(s,"borderTop"),10));r=t.offsetWidth,i=t.offsetHeight}return{left:e,top:n,width:r,height:i}}function o(t){const e=e=>{const n=t.getAttribute(e),r=n?parseFloat(n):0;return Number.isNaN(r)?0:r};switch(t instanceof SVGElement&&t.nodeName.toLowerCase()){case"rect":return new ky(e("x"),e("y"),e("width"),e("height"));case"circle":return new jy(e("cx"),e("cy"),e("r"),e("r"));case"ellipse":return new jy(e("cx"),e("cy"),e("rx"),e("ry"));case"polyline":{const e=Tm(t);return new Vy(e)}case"polygon":{const e=Tm(t);return e.length>1&&e.push(e[0]),new Vy(e)}case"path":{let e=t.getAttribute("d");return Ky.isValid(e)||(e=Ky.normalize(e)),Ky.parse(e)}case"line":return new Ny(e("x1"),e("y1"),e("x2"),e("y2"))}return i(t)}function a(t){if(null==t)return null;let e=t;do{let t=e.tagName;if("string"!=typeof t)return null;if(t=t.toUpperCase(),op(e,"x6-port"))e=e.nextElementSibling;else if("G"===t)e=e.firstElementChild;else{if("TITLE"!==t)break;e=e.nextElementSibling}}while(e);return e}t.normalizeMarker=hb,t.transformPoint=n,t.transformLine=function(t,e){return new Ny(n(t.start,e),n(t.end,e))},t.transformPolyline=function(t,e){let r=t instanceof Vy?t.points:t;return Array.isArray(r)||(r=[]),new Vy(r.map((t=>n(t,e))))},t.transformRectangle=r,t.bbox=function(t,e,n){let i;const s=t.ownerSVGElement;if(!s)return new ky(0,0,0,0);try{i=t.getBBox()}catch(e){i={x:t.clientLeft,y:t.clientTop,width:t.clientWidth,height:t.clientHeight}}return e?ky.create(i):r(i,ey(t,n||s))},t.getBBox=i,t.getBoundingOffsetRect=s,t.toGeometryShape=o,t.translateAndAutoOrient=function(t,e,n,r){const s=Ty.create(e),o=Ty.create(n);if(!r){r=t instanceof SVGSVGElement?t:t.ownerSVGElement}const a=ty(t);t.setAttribute("transform","");const l=i(t,{target:r}).scale(a.sx,a.sy),c=Fm();c.setTranslate(-l.x-l.width/2,-l.y-l.height/2);const h=Fm(),u=s.angleBetween(o,s.clone().translate(1,0));u&&h.setRotate(u,0,0);const g=Fm(),d=s.clone().move(o,l.width/2);g.setTranslate(2*s.x-d.x,2*s.y-d.y);const f=ey(t,r),p=Fm();p.setMatrix(g.matrix.multiply(h.matrix.multiply(c.matrix.multiply(f.scale(a.sx,a.sy))))),t.setAttribute("transform",Um(p.matrix))},t.findShapeNode=a,t.getBBoxV2=function(t){const e=a(t);if(!dp(e)){if(Lp(t)){const{left:e,top:n,width:r,height:i}=s(t);return new ky(e,n,r,i)}return new ky(0,0,0,0)}return o(e).bbox()||ky.create()}}(t.Util||(t.Util={}));const Ib={padding:3,rx:0,ry:0,attrs:{"stroke-width":3,stroke:"#FEB663"}},Db={highlight(e,n,r){const i=Rb.getHighlighterId(n,r);if(Rb.hasCache(i))return;r=Za({},r,Ib);const s=dm.create(n);let o,a;try{o=s.toPathData()}catch(e){a=t.Util.bbox(s.node,!0),o=_m(Object.assign(Object.assign({},r),a))}const l=vp("path");if(zp(l,Object.assign({d:o,"pointer-events":"none","vector-effect":"non-scaling-stroke",fill:"none"},r.attrs?Fp(r.attrs):null)),e.isEdgeElement(n))zp(l,"d",e.getConnectionPathData());else{let n=s.getTransformToElement(e.container);const i=r.padding;if(i){null==a&&(a=t.Util.bbox(s.node,!0));const e=a.x+a.width/2,r=a.y+a.height/2;a=t.Util.transformRectangle(a,n);const o=Math.max(a.width,1),l=Math.max(a.height,1),c=(o+i)/o,h=(l+i)/l,u=$m({a:c,b:0,c:0,d:h,e:e-c*e,f:r-h*r});n=n.multiply(u)}Zm(l,n)}ap(l,Nb.prefix("highlight-stroke"));const c=e.cell,h=()=>Rb.removeHighlighter(i);c.on("removed",h),c.model&&c.model.on("reseted",h),e.container.appendChild(l),Rb.setCache(i,l)},unhighlight(t,e,n){Rb.removeHighlighter(Rb.getHighlighterId(e,n))}};var Rb;!function(t){t.getHighlighterId=function(t,e){return gp(t),t.id+JSON.stringify(e)};const e={};t.setCache=function(t,n){e[t]=n},t.hasCache=function(t){return null!=e[t]},t.removeHighlighter=function(t){const n=e[t];n&&(Sp(n),delete e[t])}}(Rb||(Rb={}));var Vb,zb=Object.freeze({__proto__:null,className:_b,opacity:Bb,stroke:Db});function $b(t,e={}){return new Ty(If(e.x,t.width),If(e.y,t.height))}function Fb(t,e,n){return Object.assign({angle:e,position:t.toJSON()},n)}!function(t){t.check=function(t,e){if("function"!=typeof e.highlight)throw new Error(`Highlighter '${t}' is missing required \`highlight()\` method`);if("function"!=typeof e.unhighlight)throw new Error(`Highlighter '${t}' is missing required \`unhighlight()\` method`)}}(Vb||(Vb={})),function(t){t.presets=zb,t.registry=Qy.create({type:"highlighter"}),t.registry.register(t.presets,!0)}(Vb||(Vb={}));function Gb(t,e,n,r){const i=e.getCenter(),s=e.getTopCenter(),o=e.width/e.height,a=jy.fromRect(e),l=t.length;return t.map(((t,e)=>{const c=n+r(e,l),h=s.clone().rotate(-c,i).scale(o,1,i),u=t.compensateRotate?-a.tangentTheta(h):0;return(t.dx||t.dy)&&h.translate(t.dx||0,t.dy||0),t.dr&&h.move(i,t.dr),Fb(h.round(),u,t)}))}function Ub(t,n,r,i){const s=new Ny(n,r),o=t.length;return t.map(((t,n)=>{var{strict:r}=t,a=e(t,["strict"]);const l=r||i.strict?(n+1)/(o+1):(n+.5)/o,c=s.pointAt(l);return(a.dx||a.dy)&&c.translate(a.dx||0,a.dy||0),Fb(c.round(),0,a)}))}var qb,Wb=Object.freeze({__proto__:null,absolute:(t,e)=>t.map((({x:t,y:n,angle:r})=>Fb($b(e,{x:t,y:n}),r||0))),bottom:(t,e,n)=>Ub(t,e.getBottomLeft(),e.getBottomRight(),n),ellipse:(t,e,n)=>{const r=n.start||0,i=n.step||20;return Gb(t,e,r,((t,e)=>(t+.5-e/2)*i))},ellipseSpread:(t,e,n)=>{const r=n.start||0,i=n.step||360/t.length;return Gb(t,e,r,(t=>t*i))},left:(t,e,n)=>Ub(t,e.getTopLeft(),e.getBottomLeft(),n),line:(t,e,n)=>Ub(t,$b(e,n.start||e.getOrigin()),$b(e,n.end||e.getCorner()),n),right:(t,e,n)=>Ub(t,e.getTopRight(),e.getBottomRight(),n),top:(t,e,n)=>Ub(t,e.getTopLeft(),e.getTopRight(),n)});!function(t){t.presets=Wb,t.registry=Qy.create({type:"port layout"}),t.registry.register(t.presets,!0)}(qb||(qb={}));const Hb={position:{x:0,y:0},angle:0,attrs:{".":{y:"0","text-anchor":"start"}}};function Jb(t,e){const{x:n,y:r,angle:i,attrs:s}=e||{};return Za({},{angle:i,attrs:s,position:{x:n,y:r}},t,Hb)}function Xb(t,e,n,r){const i=null!=r.offset?r.offset:15,s=e.getCenter().theta(t),o=Zb(e);let a,l,c,h,u=0;return s<o[1]||s>o[2]?(a=".3em",l=i,c=0,h="start"):s<o[0]?(a="0",l=0,c=-i,n?(u=-90,h="start"):h="middle"):s<o[3]?(a=".3em",l=-i,c=0,h="end"):(a=".6em",l=0,c=i,n?(u=90,h="start"):h="middle"),Jb({position:{x:Math.round(l),y:Math.round(c)},angle:u,attrs:{".":{y:a,"text-anchor":h}}},r)}function Yb(t,e,n,r){const i=null!=r.offset?r.offset:15,s=e.getCenter().theta(t),o=Zb(e);let a,l,c,h,u=0;return s<o[1]||s>o[2]?(a=".3em",l=-i,c=0,h="end"):s<o[0]?(a=".6em",l=0,c=i,n?(u=90,h="start"):h="middle"):s<o[3]?(a=".3em",l=i,c=0,h="start"):(a="0em",l=0,c=-i,n?(u=-90,h="start"):h="middle"),Jb({position:{x:Math.round(l),y:Math.round(c)},angle:u,attrs:{".":{y:a,"text-anchor":h}}},r)}function Zb(t){const e=t.getCenter(),n=e.theta(t.getTopLeft()),r=e.theta(t.getBottomLeft()),i=e.theta(t.getBottomRight());return[n,e.theta(t.getTopRight()),i,r]}function Kb(t,e,n){const r=null!=n.offset?n.offset:20,i=new Ty(0,0),s=-t.theta(i);let o,a=".3em",l=s;return(s+90)%180==0?(o=e?"end":"middle",e||-270!==s||(a="0em")):s>-270&&s<-90?(o="start",l=s-180):o="end",Jb({position:t.clone().move(i,r).diff(t).round().round().toJSON(),angle:e?l:0,attrs:{".":{y:a,"text-anchor":o}}},n)}var Qb,tx,ex=Object.freeze({__proto__:null,bottom:(t,e,n)=>Jb({position:{x:0,y:15},attrs:{".":{y:".6em","text-anchor":"middle"}}},n),inside:(t,e,n)=>Yb(t,e,!1,n),insideOriented:(t,e,n)=>Yb(t,e,!0,n),left:(t,e,n)=>Jb({position:{x:-15,y:0},attrs:{".":{y:".3em","text-anchor":"end"}}},n),manual:(t,e,n)=>Jb({position:e.getTopLeft()},n),outside:(t,e,n)=>Xb(t,e,!1,n),outsideOriented:(t,e,n)=>Xb(t,e,!0,n),radial:(t,e,n)=>Kb(t.diff(e.getCenter()),!1,n),radialOriented:(t,e,n)=>Kb(t.diff(e.getCenter()),!0,n),right:(t,e,n)=>Jb({position:{x:15,y:0},attrs:{".":{y:".3em","text-anchor":"start"}}},n),top:(t,e,n)=>Jb({position:{x:0,y:-15},attrs:{".":{"text-anchor":"middle"}}},n)});!function(t){t.presets=ex,t.registry=Qy.create({type:"port label layout"}),t.registry.register(t.presets,!0)}(Qb||(Qb={}));class nx extends xf{get priority(){return 2}constructor(){super(),this.cid=tx.uniqueId(),nx.views[this.cid]=this}confirmUpdate(t,e){return 0}empty(t=this.container){return Op(t),this}unmount(t=this.container){return Sp(t),this}remove(t=this.container){return t===this.container&&(this.removeEventListeners(document),this.onRemove(),delete nx.views[this.cid]),this.unmount(t),this}onRemove(){}setClass(t,e=this.container){e.classList.value=Array.isArray(t)?t.join(" "):t}addClass(t,e=this.container){return ap(e,Array.isArray(t)?t.join(" "):t),this}removeClass(t,e=this.container){return lp(e,Array.isArray(t)?t.join(" "):t),this}setStyle(t,e=this.container){return am(e,t),this}setAttrs(t,e=this.container){return null!=t&&null!=e&&zp(e,t),this}findAttr(t,e=this.container){let n=e;for(;n&&1===n.nodeType;){const e=n.getAttribute(t);if(null!=e)return e;if(n===this.container)return null;n=n.parentNode}return null}find(t,e=this.container,n=this.selectors){return nx.find(t,e,n).elems}findOne(t,e=this.container,n=this.selectors){const r=this.find(t,e,n);return r.length>0?r[0]:null}findByAttr(t,e=this.container){let n=e;for(;n&&n.getAttribute;){const e=n.getAttribute(t);if((null!=e||n===this.container)&&"false"!==e)return n;n=n.parentNode}return null}getSelector(t,e){let n;if(t===this.container)return"string"==typeof e&&(n=`> ${e}`),n;if(t){const r=Ap(t)+1;n=`${t.tagName.toLowerCase()}:nth-child(${r})`,e&&(n+=` > ${e}`),n=this.getSelector(t.parentNode,n)}return n}prefixClassName(t){return Nb.prefix(t)}delegateEvents(t,e){if(null==t)return this;e||this.undelegateEvents();const n=/^(\S+)\s*(.*)$/;return Object.keys(t).forEach((e=>{const r=e.match(n);if(null==r)return;const i=this.getEventHandler(t[e]);"function"==typeof i&&this.delegateEvent(r[1],r[2],i)})),this}undelegateEvents(){return cy.off(this.container,this.getEventNamespace()),this}delegateDocumentEvents(t,e){return this.addEventListeners(document,t,e),this}undelegateDocumentEvents(){return this.removeEventListeners(document),this}delegateEvent(t,e,n){return cy.on(this.container,t+this.getEventNamespace(),e,n),this}undelegateEvent(t,e,n){const r=t+this.getEventNamespace();return null==e?cy.off(this.container,r):"string"==typeof e?cy.off(this.container,r,e,n):cy.off(this.container,r,e),this}addEventListeners(t,e,n){if(null==e)return this;const r=this.getEventNamespace();return Object.keys(e).forEach((i=>{const s=this.getEventHandler(e[i]);"function"==typeof s&&cy.on(t,i+r,n,s)})),this}removeEventListeners(t){return null!=t&&cy.off(t,this.getEventNamespace()),this}getEventNamespace(){return`.${Nb.prefixCls}-event-${this.cid}`}getEventHandler(t){let e;if("string"==typeof t){const n=this[t];"function"==typeof n&&(e=(...t)=>n.call(this,...t))}else e=(...e)=>t.call(this,...e);return e}getEventTarget(t,e={}){const{target:n,type:r,clientX:i=0,clientY:s=0}=t;return e.fromPoint||"touchmove"===r||"touchend"===r?document.elementFromPoint(i,s):n}stopPropagation(t){return this.setEventData(t,{propagationStopped:!0}),this}isPropagationStopped(t){return!0===this.getEventData(t).propagationStopped}getEventData(t){return this.eventData(t)}setEventData(t,e){return this.eventData(t,e)}eventData(t,e){if(null==t)throw new TypeError("Event object required");let n=t.data;const r=`__${this.cid}__`;return null==e?null==n?{}:n[r]||{}:(null==n&&(n=t.data={}),null==n[r]?n[r]=Object.assign({},e):n[r]=Object.assign(Object.assign({},n[r]),e),n[r])}normalizeEvent(t){return nx.normalizeEvent(t)}}!function(t){t.createElement=function(t,e){return e?vp(t||"g"):yp(t||"div")},t.find=function(t,e,n){if(!t||"."===t)return{elems:[e]};if(n){const e=n[t];if(e)return{elems:Array.isArray(e)?e:[e]}}if(Nb.useCSSSelector){const n=t.includes(">")?`:scope ${t}`:t;return{isCSSSelector:!0,elems:Array.prototype.slice.call(e.querySelectorAll(n))}}return{elems:[]}},t.normalizeEvent=function(t){let e=t;const n=t.originalEvent,r=n&&n.changedTouches&&n.changedTouches[0];if(r){for(const e in t)void 0===r[e]&&(r[e]=t[e]);e=r}return e}}(nx||(nx={})),function(t){t.views={},t.getView=function(e){return t.views[e]||null}}(nx||(nx={})),function(t){let e=0;t.uniqueId=function(){const t=`v${e}`;return e+=1,t}}(tx||(tx={}));class rx{constructor(t){this.view=t,this.clean()}clean(){this.elemCache&&this.elemCache.dispose(),this.elemCache=new wy,this.pathCache={}}get(t){return this.elemCache.has(t)||this.elemCache.set(t,{}),this.elemCache.get(t)}getData(t){const e=this.get(t);return e.data||(e.data={}),e.data}getMatrix(t){const e=this.get(t);if(null==e.matrix){const n=this.view.container;e.matrix=ny(t,n)}return $m(e.matrix)}getShape(e){const n=this.get(e);return null==n.shape&&(n.shape=t.Util.toGeometryShape(e)),n.shape.clone()}getBoundingRect(e){const n=this.get(e);return null==n.boundingRect&&(n.boundingRect=t.Util.getBBoxV2(e)),n.boundingRect.clone()}}t.Markup=void 0,function(t){function e(t){return null!=t&&"string"==typeof t}function n(t,e={ns:fp.svg}){const n=document.createDocumentFragment(),r={},i={},s=[{markup:Array.isArray(t)?t:[t],parent:n,ns:e.ns}];for(;s.length>0;){const t=s.pop();let e=t.ns||fp.svg;const n=t.markup,o=t.parent;n.forEach((t=>{const n=t.tagName;if(!n)throw new TypeError("Invalid tagName");t.ns&&(e=t.ns);const a=e?yp(n,e):mp(n),l=t.attrs;l&&zp(a,Fp(l));const c=t.style;c&&am(a,c);const h=t.className;null!=h&&a.setAttribute("class",Array.isArray(h)?h.join(" "):h),t.textContent&&(a.textContent=t.textContent);const u=t.selector;if(null!=u){if(i[u])throw new TypeError("Selector must be unique");i[u]=a}if(t.groupSelector){let e=t.groupSelector;Array.isArray(e)||(e=[e]),e.forEach((t=>{r[t]||(r[t]=[]),r[t].push(a)}))}o.appendChild(a);const g=t.children;Array.isArray(g)&&s.push({ns:e,markup:g,parent:a})}))}return Object.keys(r).forEach((t=>{if(i[t])throw new Error("Ambiguous group selector");i[t]=r[t]})),{fragment:n,selectors:i,groups:r}}function r(t){return t instanceof SVGElement?vp("g"):mp("div")}t.isJSONMarkup=function(t){return null!=t&&!e(t)},t.isStringMarkup=e,t.clone=function(t){return null==t||e(t)?t:xo(t)},t.sanitize=function(t){return`${t}`.trim().replace(/[\r|\n]/g," ").replace(/>\s+</g,"><")},t.parseJSONMarkup=n,t.renderMarkup=function(t){if(e(t)){const e=dm.createVectors(t),n=e.length;if(1===n)return{elem:e[0].node};if(n>1){const t=r(e[0].node);return e.forEach((e=>{t.appendChild(e.node)})),{elem:t}}return{}}const i=n(t),s=i.fragment;let o=null;return s.childNodes.length>1?(o=r(s.firstChild),o.appendChild(s)):o=s.firstChild,{elem:o,selectors:i.selectors}},t.parseLabelStringMarkup=function(t){const e=dm.createVectors(t),n=document.createDocumentFragment();for(let t=0,r=e.length;t<r;t+=1){const r=e[t].node;n.appendChild(r)}return{fragment:n,selectors:{}}}}(t.Markup||(t.Markup={})),(t.Markup||(t.Markup={})).getSelector=function t(e,n,r){if(null!=e){let i;const s=e.tagName.toLowerCase();if(e===n)return i="string"==typeof r?`> ${s} > ${r}`:`> ${s}`,i;const o=e.parentNode;return i=o&&o.childNodes.length>1?`${s}:nth-child(${Ap(e)+1})`:s,r&&(i+=` > ${r}`),t(e.parentNode,n,i)}return r},function(t){t.getPortContainerMarkup=function(){return"g"},t.getPortMarkup=function(){return{tagName:"circle",selector:"circle",attrs:{r:10,fill:"#FFFFFF",stroke:"#000000"}}},t.getPortLabelMarkup=function(){return{tagName:"text",selector:"text",attrs:{fill:"#000000"}}}}(t.Markup||(t.Markup={})),function(t){t.getEdgeMarkup=function(){return[{tagName:"path",selector:"wrap",groupSelector:"lines",attrs:{fill:"none",cursor:"pointer",stroke:"transparent",strokeLinecap:"round"}},{tagName:"path",selector:"line",groupSelector:"lines",attrs:{fill:"none",pointerEvents:"none"}}]}}(t.Markup||(t.Markup={})),function(t){t.getForeignObjectMarkup=function(t=!1){return{tagName:"foreignObject",selector:"fo",children:[{ns:fp.xhtml,tagName:"body",selector:"foBody",attrs:{xmlns:fp.xhtml},style:{width:"100%",height:"100%",background:"transparent"},children:t?[]:[{tagName:"div",selector:"foContent",style:{width:"100%",height:"100%"}}]}]}}}(t.Markup||(t.Markup={}));class ix{constructor(t){this.view=t}get cell(){return this.view.cell}getDefinition(t){return this.cell.getAttrDefinition(t)}processAttrs(t,e){let n,r,i,s;const o=[];return Object.keys(e).forEach((r=>{const i=e[r],s=this.getDefinition(r),a=ef(Tb.isValidDefinition,this.view,s,i,{elem:t,attrs:e,cell:this.cell,view:this.view});if(s&&a)"string"==typeof s?(null==n&&(n={}),n[s]=i):null!==i&&o.push({name:r,definition:s});else{null==n&&(n={});const t=Bp.includes(r)?r:Pf(r);n[t]=i}})),o.forEach((({name:t,definition:n})=>{const o=e[t];"function"==typeof n.set&&(null==r&&(r={}),r[t]=o);"function"==typeof n.offset&&(null==i&&(i={}),i[t]=o);"function"==typeof n.position&&(null==s&&(s={}),s[t]=o)})),{raw:e,normal:n,set:r,offset:i,position:s}}mergeProcessedAttrs(t,e){t.set=Object.assign(Object.assign({},t.set),e.set),t.position=Object.assign(Object.assign({},t.position),e.position),t.offset=Object.assign(Object.assign({},t.offset),e.offset);const n=t.normal&&t.normal.transform;null!=n&&e.normal&&(e.normal.transform=n),t.normal=e.normal}findAttrs(t,e,n,r){const i=[],s=new wy;return Object.keys(t).forEach((o=>{const a=t[o];if(!Tr(a))return;const{isCSSSelector:l,elems:c}=nx.find(o,e,r);n[o]=c;for(let t=0,e=c.length;t<e;t+=1){const n=c[t],h=r&&r[o]===n,u=s.get(n);if(u){u.array||(i.push(n),u.array=!0,u.attrs=[u.attrs],u.priority=[u.priority]);const t=u.attrs,r=u.priority;if(h)t.unshift(a),r.unshift(-1);else{const n=yg(r,l?-1:e);t.splice(n,0,a),r.splice(n,0,e)}}else s.set(n,{elem:n,attrs:a,priority:h?-1:e,array:!1})}})),i.forEach((t=>{const e=s.get(t),n=e.attrs;e.attrs=n.reduceRight(((t,e)=>Sh(t,e)),{})})),s}updateRelativeAttrs(e,n,r){const i=n.raw||{};let s=n.normal||{};const o=n.set,a=n.position,l=n.offset,c=()=>({elem:e,cell:this.cell,view:this.view,attrs:i,refBBox:r.clone()});if(null!=o&&Object.keys(o).forEach((t=>{const e=o[t],n=this.getDefinition(t);if(null!=n){const r=ef(n.set,this.view,e,c());"object"==typeof r?s=Object.assign(Object.assign({},s),r):null!=r&&(s[t]=r)}})),e instanceof HTMLElement)return void this.view.setAttrs(s,e);const h=s.transform,u=Gm(h?`${h}`:null),g=new Ty(u.e,u.f);h&&(delete s.transform,u.e=0,u.f=0);let d=!1;null!=a&&Object.keys(a).forEach((t=>{const e=a[t],n=this.getDefinition(t);if(null!=n){const t=ef(n.position,this.view,e,c());null!=t&&(d=!0,g.translate(Ty.create(t)))}})),this.view.setAttrs(s,e);let f=!1;if(null!=l){const n=this.view.getBoundingRectOfElement(e);if(n.width>0&&n.height>0){const r=t.Util.transformRectangle(n,u);Object.keys(l).forEach((t=>{const n=l[t],s=this.getDefinition(t);if(null!=s){const t=ef(s.offset,this.view,n,{elem:e,cell:this.cell,view:this.view,attrs:i,refBBox:r});null!=t&&(f=!0,g.translate(Ty.create(t)))}}))}}(null!=h||d||f)&&(g.round(1),u.e=g.x,u.f=g.y,e.setAttribute("transform",Um(u)))}update(e,n,r){const i={},s=this.findAttrs(r.attrs||n,e,i,r.selectors),o=r.attrs?this.findAttrs(n,e,i,r.selectors):s,a=[];s.each((t=>{const n=t.elem,s=t.attrs,l=this.processAttrs(n,s);if(null==l.set&&null==l.position&&null==l.offset)this.view.setAttrs(l.normal,n);else{const t=o.get(n),c=t?t.attrs:null,h=c&&null==s.ref?c.ref:s.ref;let u;if(h){if(u=(i[h]||this.view.find(h,e,r.selectors))[0],!u)throw new Error(`"${h}" reference does not exist.`)}else u=null;const g={node:n,refNode:u,attributes:c,processedAttributes:l},d=a.findIndex((t=>t.refNode===n));d>-1?a.splice(d,0,g):a.push(g)}}));const l=new wy;let c;a.forEach((n=>{const i=n.node,s=n.refNode;let o;const a=null!=s&&null!=r.rotatableNode&&Ep(r.rotatableNode,s);if(s&&(o=l.get(s)),!o){const n=a?r.rotatableNode:e;o=s?t.Util.getBBox(s,{target:n}):r.rootBBox,s&&l.set(s,o)}let h;r.attrs&&n.attributes?(h=this.processAttrs(i,n.attributes),this.mergeProcessedAttrs(h,n.processedAttributes)):h=n.processedAttributes;let u=o;a&&null!=r.rotatableNode&&!r.rotatableNode.contains(i)&&(c||(c=Gm(zp(r.rotatableNode,"transform"))),u=t.Util.transformRectangle(o,c)),this.updateRelativeAttrs(i,h,u)}))}}class sx{get cell(){return this.view.cell}constructor(t,e,n=[]){this.view=t;const r={},i={};let s=0;Object.keys(e).forEach((t=>{let n=e[t];Array.isArray(n)||(n=[n]),n.forEach((e=>{let n=r[e];n||(s+=1,n=r[e]=1<<s),i[t]|=n}))}));let o=n;if(Array.isArray(o)||(o=[o]),o.forEach((t=>{r[t]||(s+=1,r[t]=1<<s)})),s>25)throw new Error("Maximum number of flags exceeded.");this.flags=r,this.attrs=i,this.bootstrap=n}getFlag(t){const e=this.flags;return null==e?0:Array.isArray(t)?t.reduce(((t,n)=>t|e[n]),0):0|e[t]}hasAction(t,e){return t&this.getFlag(e)}removeAction(t,e){return t^t&this.getFlag(e)}getBootstrapFlag(){return this.getFlag(this.bootstrap)}getChangedFlag(){let t=0;return this.attrs?(Object.keys(this.attrs).forEach((e=>{this.cell.hasChanged(e)&&(t|=this.attrs[e])})),t):t}}class ox extends nx{static getDefaults(){return this.defaults}static config(t){this.defaults=this.getOptions(t)}static getOptions(t){const n=(t,e)=>null!=e?ud([...Array.isArray(t)?t:[t],...Array.isArray(e)?e:[e]]):Array.isArray(t)?[...t]:[t],r=xo(this.getDefaults()),{bootstrap:i,actions:s,events:o,documentEvents:a}=t,l=e(t,["bootstrap","actions","events","documentEvents"]);return i&&(r.bootstrap=n(r.bootstrap,i)),s&&Object.entries(s).forEach((([t,e])=>{const i=r.actions[t];e&&i?r.actions[t]=n(i,e):e&&(r.actions[t]=n(e))})),o&&(r.events=Object.assign(Object.assign({},r.events),o)),t.documentEvents&&(r.documentEvents=Object.assign(Object.assign({},r.documentEvents),a)),Sh(r,l)}get[Symbol.toStringTag](){return ox.toStringTag}constructor(t,e={}){super(),this.cell=t,this.options=this.ensureOptions(e),this.graph=this.options.graph,this.attr=new ix(this),this.flag=new sx(this,this.options.actions,this.options.bootstrap),this.cache=new rx(this),this.setContainer(this.ensureContainer()),this.setup(),this.init()}init(){}onRemove(){this.removeTools()}get priority(){return this.options.priority}get rootSelector(){return this.options.rootSelector}getConstructor(){return this.constructor}ensureOptions(t){return this.getConstructor().getOptions(t)}getContainerTagName(){return this.options.isSvgElement?"g":"div"}getContainerStyle(){}getContainerAttrs(){return{"data-cell-id":this.cell.id,"data-shape":this.cell.shape}}getContainerClassName(){return this.prefixClassName("cell")}ensureContainer(){return nx.createElement(this.getContainerTagName(),this.options.isSvgElement)}setContainer(t){if(this.container!==t){this.undelegateEvents(),this.container=t,null!=this.options.events&&this.delegateEvents(this.options.events);const e=this.getContainerAttrs();null!=e&&this.setAttrs(e,t);const n=this.getContainerStyle();null!=n&&this.setStyle(n,t);const r=this.getContainerClassName();null!=r&&this.addClass(r,t)}return this}isNodeView(){return!1}isEdgeView(){return!1}render(){return this}confirmUpdate(t,e={}){return 0}getBootstrapFlag(){return this.flag.getBootstrapFlag()}getFlag(t){return this.flag.getFlag(t)}hasAction(t,e){return this.flag.hasAction(t,e)}removeAction(t,e){return this.flag.removeAction(t,e)}handleAction(t,e,n,r){if(this.hasAction(t,e)){n();const i=[e];return r&&("string"==typeof r?i.push(r):i.push(...r)),this.removeAction(t,i)}return t}setup(){this.cell.on("changed",(({options:t})=>this.onAttrsChange(t)))}onAttrsChange(t){let e=this.flag.getChangedFlag();!t.updated&&e&&(t.dirty&&this.hasAction(e,"update")&&(e|=this.getFlag("render")),t.toolId&&(t.async=!1),null!=this.graph&&this.graph.renderer.requestViewUpdate(this,e,t))}parseJSONMarkup(e,n){const r=t.Markup.parseJSONMarkup(e),i=r.selectors,s=this.rootSelector;if(n&&s){if(i[s])throw new Error("Invalid root selector");i[s]=n}return r}can(t){let e=this.graph.options.interacting;if("function"==typeof e&&(e=ef(e,this.graph,this)),"object"==typeof e){let n=e[t];return"function"==typeof n&&(n=ef(n,this.graph,this)),!1!==n}return"boolean"==typeof e&&e}cleanCache(){return this.cache.clean(),this}getCache(t){return this.cache.get(t)}getDataOfElement(t){return this.cache.getData(t)}getMatrixOfElement(t){return this.cache.getMatrix(t)}getShapeOfElement(t){return this.cache.getShape(t)}getBoundingRectOfElement(t){return this.cache.getBoundingRect(t)}getBBoxOfElement(e){const n=this.getBoundingRectOfElement(e),r=this.getMatrixOfElement(e),i=this.getRootRotatedMatrix(),s=this.getRootTranslatedMatrix();return t.Util.transformRectangle(n,s.multiply(i).multiply(r))}getUnrotatedBBoxOfElement(e){const n=this.getBoundingRectOfElement(e),r=this.getMatrixOfElement(e),i=this.getRootTranslatedMatrix();return t.Util.transformRectangle(n,i.multiply(r))}getBBox(t={}){let e;if(t.useCellGeometry){const t=this.cell,n=t.isNode()?t.getAngle():0;e=t.getBBox().bbox(n)}else e=this.getBBoxOfElement(this.container);return this.graph.coord.localToGraphRect(e)}getRootTranslatedMatrix(){const t=this.cell,e=t.isNode()?t.getPosition():{x:0,y:0};return $m().translate(e.x,e.y)}getRootRotatedMatrix(){let t=$m();const e=this.cell,n=e.isNode()?e.getAngle():0;if(n){const r=e.getBBox(),i=r.width/2,s=r.height/2;t=t.translate(i,s).rotate(n).translate(-i,-s)}return t}findMagnet(t=this.container){return this.findByAttr("magnet",t)}updateAttrs(t,e,n={}){null==n.rootBBox&&(n.rootBBox=new ky),null==n.selectors&&(n.selectors=this.selectors),this.attr.update(t,e,n)}isEdgeElement(t){return this.cell.isEdge()&&(null==t||t===this.container)}prepareHighlight(t,e={}){const n=t||this.container;return e.partial=n===this.container,n}highlight(t,e={}){const n=this.prepareHighlight(t,e);return this.notify("cell:highlight",{magnet:n,options:e,view:this,cell:this.cell}),this.isEdgeView()?this.notify("edge:highlight",{magnet:n,options:e,view:this,edge:this.cell,cell:this.cell}):this.isNodeView()&&this.notify("node:highlight",{magnet:n,options:e,view:this,node:this.cell,cell:this.cell}),this}unhighlight(t,e={}){const n=this.prepareHighlight(t,e);return this.notify("cell:unhighlight",{magnet:n,options:e,view:this,cell:this.cell}),this.isNodeView()?this.notify("node:unhighlight",{magnet:n,options:e,view:this,node:this.cell,cell:this.cell}):this.isEdgeView()&&this.notify("edge:unhighlight",{magnet:n,options:e,view:this,edge:this.cell,cell:this.cell}),this}notifyUnhighlight(t,e){}getEdgeTerminal(t,e,n,r,i){const s=this.cell,o=this.findAttr("port",t),a=t.getAttribute("data-selector"),l={cell:s.id};return null!=a&&(l.magnet=a),null!=o?(l.port=o,s.isNode()&&(s.hasPort(o)||null!=a||(l.selector=this.getSelector(t)))):null==a&&this.container!==t&&(l.selector=this.getSelector(t)),l}getMagnetFromEdgeTerminal(t){const e=this.cell,n=this.container,r=t.port;let i,s=t.magnet;return null!=r&&e.isNode()&&e.hasPort(r)?i=this.findPortElem(r,s)||n:(s||(s=t.selector),s||null==r||(s=`[port="${r}"]`),i=this.findOne(s,n,this.selectors)),i}hasTools(t){const e=this.tools;return null!=e&&(null==t||e.name===t)}addTools(t){if(!this.can("toolsAddable"))return this;if(this.removeTools(),t){const e=ax.isToolsView(t)?t:new ax(t);this.tools=e,e.config({view:this}),e.mount()}return this}updateTools(t={}){return this.tools&&this.tools.update(t),this}removeTools(){return this.tools&&(this.tools.remove(),this.tools=null),this}hideTools(){return this.tools&&this.tools.hide(),this}showTools(){return this.tools&&this.tools.show(),this}renderTools(){const t=this.cell.getTools();return this.addTools(t),this}notify(t,e){return this.trigger(t,e),this.graph.trigger(t,e),this}getEventArgs(t,e,n){const r=this,i=r.cell;return null==e||null==n?{e:t,view:r,cell:i}:{e:t,x:e,y:n,view:r,cell:i}}onClick(t,e,n){this.notify("cell:click",this.getEventArgs(t,e,n))}onDblClick(t,e,n){this.notify("cell:dblclick",this.getEventArgs(t,e,n))}onContextMenu(t,e,n){this.notify("cell:contextmenu",this.getEventArgs(t,e,n))}onMouseDown(t,e,n){this.cell.model&&(this.cachedModelForMouseEvent=this.cell.model,this.cachedModelForMouseEvent.startBatch("mouse")),this.notify("cell:mousedown",this.getEventArgs(t,e,n))}onMouseUp(t,e,n){this.notify("cell:mouseup",this.getEventArgs(t,e,n)),this.cachedModelForMouseEvent&&(this.cachedModelForMouseEvent.stopBatch("mouse",{cell:this.cell}),this.cachedModelForMouseEvent=null)}onMouseMove(t,e,n){this.notify("cell:mousemove",this.getEventArgs(t,e,n))}onMouseOver(t){this.notify("cell:mouseover",this.getEventArgs(t))}onMouseOut(t){this.notify("cell:mouseout",this.getEventArgs(t))}onMouseEnter(t){this.notify("cell:mouseenter",this.getEventArgs(t))}onMouseLeave(t){this.notify("cell:mouseleave",this.getEventArgs(t))}onMouseWheel(t,e,n,r){this.notify("cell:mousewheel",Object.assign({delta:r},this.getEventArgs(t,e,n)))}onCustomEvent(t,e,n,r){this.notify("cell:customevent",Object.assign({name:e},this.getEventArgs(t,n,r))),this.notify(e,Object.assign({},this.getEventArgs(t,n,r)))}onMagnetMouseDown(t,e,n,r){}onMagnetDblClick(t,e,n,r){}onMagnetContextMenu(t,e,n,r){}onLabelMouseDown(t,e,n){}checkMouseleave(t){const e=this.getEventTarget(t,{fromPoint:!0}),n=this.graph.findViewByElem(e);n!==this&&(this.onMouseLeave(t),n&&n.onMouseEnter(t))}}ox.defaults={isSvgElement:!0,rootSelector:"root",priority:0,bootstrap:[],actions:{}},function(t){t.Flag=sx,t.Attr=ix}(ox||(ox={})),function(t){t.toStringTag=`X6.${t.name}`,t.isCellView=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"==typeof r.isNodeView&&"function"==typeof r.isEdgeView&&"function"==typeof r.confirmUpdate}}(ox||(ox={})),function(t){t.priority=function(t){return function(e){e.config({priority:t})}},t.bootstrap=function(t){return function(e){e.config({bootstrap:t})}}}(ox||(ox={})),function(t){t.registry=Qy.create({type:"view"})}(ox||(ox={}));class ax extends nx{get name(){return this.options.name}get graph(){return this.cellView.graph}get cell(){return this.cellView.cell}get[Symbol.toStringTag](){return ax.toStringTag}constructor(t={}){super(),this.svgContainer=this.createContainer(!0,t),this.htmlContainer=this.createContainer(!1,t),this.config(t)}createContainer(t,e){const n=t?nx.createElement("g",!0):nx.createElement("div",!1);return ap(n,this.prefixClassName("cell-tools")),e.className&&ap(n,e.className),n}config(t){if(this.options=Object.assign(Object.assign({},this.options),t),!ox.isCellView(t.view)||t.view===this.cellView)return this;this.cellView=t.view,this.cell.isEdge()?(ap(this.svgContainer,this.prefixClassName("edge-tools")),ap(this.htmlContainer,this.prefixClassName("edge-tools"))):this.cell.isNode()&&(ap(this.svgContainer,this.prefixClassName("node-tools")),ap(this.htmlContainer,this.prefixClassName("node-tools"))),this.svgContainer.setAttribute("data-cell-id",this.cell.id),this.htmlContainer.setAttribute("data-cell-id",this.cell.id),this.name&&(this.svgContainer.setAttribute("data-tools-name",this.name),this.htmlContainer.setAttribute("data-tools-name",this.name));const e=this.options.items;if(!Array.isArray(e))return this;this.tools=[];const n=[];e.forEach((t=>{if(ax.ToolItem.isToolItem(t))"vertices"===t.name?n.unshift(t):n.push(t);else{"vertices"===("object"==typeof t?t.name:t)?n.unshift(t):n.push(t)}}));for(let t=0;t<n.length;t+=1){const e=n[t];let r;if(ax.ToolItem.isToolItem(e))r=e;else{const t="object"==typeof e?e.name:e,n="object"==typeof e&&e.args||{};if(t)if(this.cell.isNode()){const e=Sx.registry.get(t);if(!e)return Sx.registry.onNotFound(t);r=new e(n)}else if(this.cell.isEdge()){const e=Ox.registry.get(t);if(!e)return Ox.registry.onNotFound(t);r=new e(n)}}if(r){r.config(this.cellView,this),r.render();(!1!==r.options.isSVGElement?this.svgContainer:this.htmlContainer).appendChild(r.container),this.tools.push(r)}}return this}update(t={}){const e=this.tools;return e&&e.forEach((e=>{t.toolId!==e.cid&&e.isVisible()&&e.update()})),this}focus(t){const e=this.tools;return e&&e.forEach((e=>{t===e?e.show():e.hide()})),this}blur(t){const e=this.tools;return e&&e.forEach((e=>{e===t||e.isVisible()||(e.show(),e.update())})),this}hide(){return this.focus(null)}show(){return this.blur(null)}remove(){const t=this.tools;return t&&(t.forEach((t=>t.remove())),this.tools=null),Sp(this.svgContainer),Sp(this.htmlContainer),super.remove()}mount(){const t=this.tools,e=this.cellView;if(e&&t){const n=t.some((t=>!1!==t.options.isSVGElement)),r=t.some((t=>!1===t.options.isSVGElement));if(n){(this.options.local?e.container:e.graph.view.decorator).appendChild(this.svgContainer)}r&&this.graph.container.appendChild(this.htmlContainer)}return this}}!function(t){t.toStringTag=`X6.${t.name}`,t.isToolsView=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&null!=r.graph&&null!=r.cell&&"function"==typeof r.config&&"function"==typeof r.update&&"function"==typeof r.focus&&"function"==typeof r.blur&&"function"==typeof r.show&&"function"==typeof r.hide}}(ax||(ax={})),function(e){class n extends nx{static getDefaults(){return this.defaults}static config(t){this.defaults=this.getOptions(t)}static getOptions(t){return Sh(xo(this.getDefaults()),t)}get graph(){return this.cellView.graph}get cell(){return this.cellView.cell}get name(){return this.options.name}get[Symbol.toStringTag](){return n.toStringTag}constructor(t={}){super(),this.visible=!0,this.options=this.getOptions(t),this.container=nx.createElement(this.options.tagName||"g",!1!==this.options.isSVGElement),ap(this.container,this.prefixClassName("cell-tool")),"string"==typeof this.options.className&&ap(this.container,this.options.className),this.init()}init(){}getOptions(t){return this.constructor.getOptions(t)}delegateEvents(){return this.options.events&&super.delegateEvents(this.options.events),this}config(t,e){return this.cellView=t,this.parent=e,this.stamp(this.container),this.cell.isEdge()?ap(this.container,this.prefixClassName("edge-tool")):this.cell.isNode()&&ap(this.container,this.prefixClassName("node-tool")),this.name&&this.container.setAttribute("data-tool-name",this.name),this.delegateEvents(),this}render(){this.empty();const e=this.options.markup;if(e){const n=t.Markup.parseJSONMarkup(e);this.container.appendChild(n.fragment),this.childNodes=n.selectors}return this.onRender(),this}onRender(){}update(){return this}stamp(t){t&&t.setAttribute("data-cell-id",this.cellView.cell.id)}show(){return this.container.style.display="",this.visible=!0,this}hide(){return this.container.style.display="none",this.visible=!1,this}isVisible(){return this.visible}focus(){const t=this.options.focusOpacity;return null!=t&&Number.isFinite(t)&&(this.container.style.opacity=`${t}`),this.parent.focus(this),this}blur(){return this.container.style.opacity="",this.parent.blur(this),this}guard(t){return null==this.graph||null==this.cellView||this.graph.view.guard(t,this.cellView)}}n.defaults={isSVGElement:!0,tagName:"g"},e.ToolItem=n,function(t){let e=0;t.define=function(t){const n=df((r=t.name)?Cf(r):(e+=1,`CustomTool${e}`),this);var r;return n.config(t),n}}(n=e.ToolItem||(e.ToolItem={})),function(t){t.toStringTag=`X6.${t.name}`,t.isToolItem=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&null!=r.graph&&null!=r.cell&&"function"==typeof r.config&&"function"==typeof r.update&&"function"==typeof r.focus&&"function"==typeof r.blur&&"function"==typeof r.show&&"function"==typeof r.hide&&"function"==typeof r.isVisible}}(n=e.ToolItem||(e.ToolItem={}))}(ax||(ax={}));function lx(t,e){return 0===e?"0%":`${Math.round(t/e*100)}%`}function cx(t){return(e,n,r,i)=>n.isEdgeElement(r)?function(t,e,n,r,i){const s=n.getConnection();if(!s)return e;const o=s.closestPointLength(i);if(t){const t=s.length();e.anchor={name:"ratio",args:{ratio:o/t}}}else e.anchor={name:"length",args:{length:o}};return e}(t,e,n,0,i):function(t,e,n,r,i){const s=n.cell,o=s.getAngle(),a=n.getUnrotatedBBoxOfElement(r),l=s.getBBox().getCenter(),c=Ty.create(i).rotate(o,l);let h=c.x-a.x,u=c.y-a.y;t&&(h=lx(h,a.width),u=lx(u,a.height));return e.anchor={name:"topLeft",args:{dx:h,dy:u,rotate:!0}},e}(t,e,n,r,i)}const hx=cx(!0),ux=cx(!1);var gx,dx=Object.freeze({__proto__:null,noop:t=>t,pinAbsolute:ux,pinRelative:hx});function fx(t,e,n,r){return ef(gx.presets.pinRelative,this.graph,{},e,n,t,this.cell,r,{}).anchor}function px(t,e){return e?t.cell.getBBox():t.cell.isEdge()?t.getConnection().bbox():t.getUnrotatedBBoxOfElement(t.container)}!function(t){t.presets=dx,t.registry=Qy.create({type:"connection strategy"}),t.registry.register(t.presets,!0)}(gx||(gx={}));class mx extends ax.ToolItem{onRender(){ap(this.container,this.prefixClassName("cell-tool-button")),this.update()}update(){return this.updatePosition(),this}updatePosition(){const t=this.cellView.cell.isEdge()?this.getEdgeMatrix():this.getNodeMatrix();Zm(this.container,t,{absolute:!0})}getNodeMatrix(){const t=this.cellView,e=this.options;let{x:n=0,y:r=0}=e;const{offset:i,useCellGeometry:s,rotate:o}=e;let a=px(t,s);const l=t.cell.getAngle();o||(a=a.bbox(l));let c=0,h=0;"number"==typeof i?(c=i,h=i):"object"==typeof i&&(c=i.x,h=i.y),n=If(n,a.width),r=If(r,a.height);let u=$m().translate(a.x+a.width/2,a.y+a.height/2);return o&&(u=u.rotate(l)),u=u.translate(n+c-a.width/2,r+h-a.height/2),u}getEdgeMatrix(){const t=this.cellView,e=this.options,{offset:n=0,distance:r=0,rotate:i}=e;let s,o,a;const l=If(r,1);s=l>=0&&l<=1?t.getTangentAtRatio(l):t.getTangentAtLength(l),s?(o=s.start,a=s.vector().vectorAngle(new Ty(1,0))||0):(o=t.getConnection().start,a=0);let c=$m().translate(o.x,o.y).rotate(a);return c="object"==typeof n?c.translate(n.x||0,n.y||0):c.translate(0,n),i||(c=c.rotate(-a)),c}onMouseDown(t){if(this.guard(t))return;t.stopPropagation(),t.preventDefault();const e=this.options.onClick;"function"==typeof e&&ef(e,this.cellView,{e:t,view:this.cellView,cell:this.cellView.cell,btn:this})}}!function(t){t.config({name:"button",useCellGeometry:!0,events:{mousedown:"onMouseDown",touchstart:"onMouseDown"}})}(mx||(mx={})),function(t){t.Remove=t.define({name:"button-remove",markup:[{tagName:"circle",selector:"button",attrs:{r:7,fill:"#FF1D00",cursor:"pointer"}},{tagName:"path",selector:"icon",attrs:{d:"M -3 -3 3 3 M -3 3 3 -3",fill:"none",stroke:"#FFFFFF","stroke-width":2,"pointer-events":"none"}}],distance:60,offset:0,useCellGeometry:!0,onClick({view:t,btn:e}){e.parent.remove(),t.cell.remove({ui:!0,toolId:e.cid})}})}(mx||(mx={}));class yx extends ax.ToolItem{onRender(){if(ap(this.container,this.prefixClassName("cell-tool-boundary")),this.options.attrs){const t=this.options.attrs,{class:n}=t,r=e(t,["class"]);zp(this.container,Fp(r)),n&&ap(this.container,n)}this.update()}update(){const t=this.cellView,e=this.options,{useCellGeometry:n,rotate:r}=e,i=Df(e.padding);let s=px(t,n).moveAndExpand({x:-i.left,y:-i.top,width:i.left+i.right,height:i.top+i.bottom});const o=t.cell;if(o.isNode()){const t=o.getAngle();if(t)if(r){const e=o.getBBox().getCenter();Qm(this.container,t,e.x,e.y,{absolute:!0})}else s=s.bbox(t)}return zp(this.container,s.toJSON()),this}}!function(t){t.config({name:"boundary",tagName:"rect",padding:10,useCellGeometry:!0,attrs:{fill:"none",stroke:"#333","stroke-width":.5,"stroke-dasharray":"5, 5","pointer-events":"none"}})}(yx||(yx={}));class vx extends ax.ToolItem{constructor(){super(...arguments),this.handles=[]}get vertices(){return this.cellView.cell.getVertices()}onRender(){return this.addClass(this.prefixClassName("edge-tool-vertices")),this.options.addable&&this.updatePath(),this.resetHandles(),this.renderHandles(),this}update(){return this.vertices.length===this.handles.length?this.updateHandles():(this.resetHandles(),this.renderHandles()),this.options.addable&&this.updatePath(),this}resetHandles(){const t=this.handles;this.handles=[],t&&t.forEach((t=>{this.stopHandleListening(t),t.remove()}))}renderHandles(){const t=this.vertices;for(let e=0,n=t.length;e<n;e+=1){const n=t[e],r=this.options.createHandle,i=this.options.processHandle,s=r({index:e,graph:this.graph,guard:t=>this.guard(t),attrs:this.options.attrs||{}});i&&i(s),s.updatePosition(n.x,n.y),this.stamp(s.container),this.container.appendChild(s.container),this.handles.push(s),this.startHandleListening(s)}}updateHandles(){const t=this.vertices;for(let e=0,n=t.length;e<n;e+=1){const n=t[e],r=this.handles[e];r&&r.updatePosition(n.x,n.y)}}updatePath(){const t=this.childNodes.connection;t&&t.setAttribute("d",this.cellView.getConnectionPathData())}startHandleListening(t){const e=this.cellView;e.can("vertexMovable")&&(t.on("change",this.onHandleChange,this),t.on("changing",this.onHandleChanging,this),t.on("changed",this.onHandleChanged,this)),e.can("vertexDeletable")&&t.on("remove",this.onHandleRemove,this)}stopHandleListening(t){const e=this.cellView;e.can("vertexMovable")&&(t.off("change",this.onHandleChange,this),t.off("changing",this.onHandleChanging,this),t.off("changed",this.onHandleChanged,this)),e.can("vertexDeletable")&&t.off("remove",this.onHandleRemove,this)}getNeighborPoints(t){const e=this.cellView,n=this.vertices,r=t>0?n[t-1]:e.sourceAnchor,i=t<n.length-1?n[t+1]:e.targetAnchor;return{prev:Ty.create(r),next:Ty.create(i)}}getMouseEventArgs(t){const e=this.normalizeEvent(t),{x:n,y:r}=this.graph.snapToGrid(e.clientX,e.clientY);return{e:e,x:n,y:r}}onHandleChange({e:t}){this.focus();const e=this.cellView;if(e.cell.startBatch("move-vertex",{ui:!0,toolId:this.cid}),!this.options.stopPropagation){const{e:n,x:r,y:i}=this.getMouseEventArgs(t);e.notifyMouseDown(n,r,i)}}onHandleChanging({handle:t,e:e}){const n=this.cellView,r=t.options.index,{e:i,x:s,y:o}=this.getMouseEventArgs(e),a={x:s,y:o};this.snapVertex(a,r),n.cell.setVertexAt(r,a,{ui:!0,toolId:this.cid}),t.updatePosition(a.x,a.y),this.options.stopPropagation||n.notifyMouseMove(i,s,o)}onHandleChanged({e:t}){const e=this.options,n=this.cellView;if(e.addable&&this.updatePath(),!e.removeRedundancies)return;n.removeRedundantLinearVertices({ui:!0,toolId:this.cid})&&this.render(),this.blur(),n.cell.stopBatch("move-vertex",{ui:!0,toolId:this.cid}),this.eventData(t).vertexAdded&&n.cell.stopBatch("add-vertex",{ui:!0,toolId:this.cid});const{e:r,x:i,y:s}=this.getMouseEventArgs(t);this.options.stopPropagation||n.notifyMouseUp(r,i,s),n.checkMouseleave(r),e.onChanged&&e.onChanged({edge:n.cell,edgeView:n})}snapVertex(t,e){const n=this.options.snapRadius||0;if(n>0){const r=this.getNeighborPoints(e),i=r.prev,s=r.next;Math.abs(t.x-i.x)<n?t.x=i.x:Math.abs(t.x-s.x)<n&&(t.x=s.x),Math.abs(t.y-i.y)<n?t.y=r.prev.y:Math.abs(t.y-s.y)<n&&(t.y=s.y)}}onHandleRemove({handle:t,e:e}){if(this.options.removable){const n=t.options.index,r=this.cellView;r.cell.removeVertexAt(n,{ui:!0}),this.options.addable&&this.updatePath(),r.checkMouseleave(this.normalizeEvent(e))}}onPathMouseDown(t){const e=this.cellView;if(this.guard(t)||!this.options.addable||!e.can("vertexAddable"))return;t.stopPropagation(),t.preventDefault();const n=this.normalizeEvent(t),r=this.graph.snapToGrid(n.clientX,n.clientY).toJSON();e.cell.startBatch("add-vertex",{ui:!0,toolId:this.cid});const i=e.getVertexIndex(r.x,r.y);this.snapVertex(r,i),e.cell.insertVertex(r,i,{ui:!0,toolId:this.cid}),this.render();const s=this.handles[i];this.eventData(n,{vertexAdded:!0}),s.onMouseDown(n)}onRemove(){this.resetHandles()}}!function(t){t.Handle=class extends nx{get graph(){return this.options.graph}constructor(t){super(),this.options=t,this.render(),this.delegateEvents({mousedown:"onMouseDown",touchstart:"onMouseDown",dblclick:"onDoubleClick"})}render(){this.container=nx.createElement("circle",!0);const e=this.options.attrs;if("function"==typeof e){const n=t.getDefaults();this.setAttrs(Object.assign(Object.assign({},n.attrs),e(this)))}else this.setAttrs(e);this.addClass(this.prefixClassName("edge-tool-vertex"))}updatePosition(t,e){this.setAttrs({cx:t,cy:e})}onMouseDown(t){this.options.guard(t)||(t.stopPropagation(),t.preventDefault(),this.graph.view.undelegateEvents(),this.delegateDocumentEvents({mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"},t.data),this.emit("change",{e:t,handle:this}))}onMouseMove(t){this.emit("changing",{e:t,handle:this})}onMouseUp(t){this.emit("changed",{e:t,handle:this}),this.undelegateDocumentEvents(),this.graph.view.delegateEvents()}onDoubleClick(t){this.emit("remove",{e:t,handle:this})}}}(vx||(vx={})),function(t){const e=Nb.prefix("edge-tool-vertex-path");t.config({name:"vertices",snapRadius:20,addable:!0,removable:!0,removeRedundancies:!0,stopPropagation:!0,attrs:{r:6,fill:"#333",stroke:"#fff",cursor:"move","stroke-width":2},createHandle:e=>new t.Handle(e),markup:[{tagName:"path",selector:"connection",className:e,attrs:{fill:"none",stroke:"transparent","stroke-width":10,cursor:"pointer"}}],events:{[`mousedown .${e}`]:"onPathMouseDown",[`touchstart .${e}`]:"onPathMouseDown"}})}(vx||(vx={}));class bx extends ax.ToolItem{constructor(){super(...arguments),this.handles=[]}get vertices(){return this.cellView.cell.getVertices()}update(){return this.render(),this}onRender(){ap(this.container,this.prefixClassName("edge-tool-segments")),this.resetHandles();const t=this.cellView,e=[...this.vertices];e.unshift(t.sourcePoint),e.push(t.targetPoint);for(let t=0,n=e.length;t<n-1;t+=1){const n=e[t],r=e[t+1],i=this.renderHandle(n,r,t);this.stamp(i.container),this.handles.push(i)}return this}renderHandle(t,e,n){const r=this.options.createHandle({index:n,graph:this.graph,guard:t=>this.guard(t),attrs:this.options.attrs||{}});return this.options.processHandle&&this.options.processHandle(r),this.updateHandle(r,t,e),this.container.appendChild(r.container),this.startHandleListening(r),r}startHandleListening(t){t.on("change",this.onHandleChange,this),t.on("changing",this.onHandleChanging,this),t.on("changed",this.onHandleChanged,this)}stopHandleListening(t){t.off("change",this.onHandleChange,this),t.off("changing",this.onHandleChanging,this),t.off("changed",this.onHandleChanged,this)}resetHandles(){const t=this.handles;this.handles=[],t&&t.forEach((t=>{this.stopHandleListening(t),t.remove()}))}shiftHandleIndexes(t){const e=this.handles;for(let n=0,r=e.length;n<r;n+=1)e[n].options.index+=t}resetAnchor(t,e){const n=this.cellView.cell,r={ui:!0,toolId:this.cid};e?n.prop([t,"anchor"],e,r):n.removeProp([t,"anchor"],r)}snapHandle(t,e,n){const r=t.options.axis,i=t.options.index,s=this.cellView.cell.getVertices(),o=s[i-2]||n.sourceAnchor,a=s[i+1]||n.targetAnchor,l=this.options.snapRadius;return Math.abs(e[r]-o[r])<l?e[r]=o[r]:Math.abs(e[r]-a[r])<l&&(e[r]=a[r]),e}onHandleChanging({handle:t,e:e}){const n=this.graph,r=this.options,i=this.cellView,s=r.anchor,o=t.options.axis,a=t.options.index-1,l=this.getEventData(e),c=this.normalizeEvent(e),h=n.snapToGrid(c.clientX,c.clientY),u=this.snapHandle(t,h.clone(),l),g=xo(this.vertices);let d=g[a],f=g[a+1];const p=i.sourceView,m=i.sourceBBox;let y=!1,v=!1;if(d?0===a?m.containsPoint(d)?(g.shift(),this.shiftHandleIndexes(-1),y=!0):(d[o]=u[o],v=!0):d[o]=u[o]:(d=i.sourceAnchor.toJSON(),d[o]=u[o],m.containsPoint(d)?y=!0:(g.unshift(d),this.shiftHandleIndexes(1),v=!0)),"function"==typeof s&&p){if(y){const t=l.sourceAnchor.clone();t[o]=u[o];const e=ef(s,i,t,p,i.sourceMagnet||p.container,"source",i,this);this.resetAnchor("source",e)}v&&this.resetAnchor("source",l.sourceAnchorDef)}const b=i.targetView,x=i.targetBBox;let w=!1,A=!1;if(f?a===g.length-2?x.containsPoint(f)?(g.pop(),w=!0):(f[o]=u[o],A=!0):f[o]=u[o]:(f=i.targetAnchor.toJSON(),f[o]=u[o],x.containsPoint(f)?w=!0:(g.push(f),A=!0)),"function"==typeof s&&b){if(w){const t=l.targetAnchor.clone();t[o]=u[o];const e=ef(s,i,t,b,i.targetMagnet||b.container,"target",i,this);this.resetAnchor("target",e)}A&&this.resetAnchor("target",l.targetAnchorDef)}Ty.equalPoints(g,this.vertices)||this.cellView.cell.setVertices(g,{ui:!0,toolId:this.cid}),this.updateHandle(t,d,f,0),r.stopPropagation||i.notifyMouseMove(c,h.x,h.y)}onHandleChange({handle:t,e:e}){const n=this.options,r=this.handles,i=this.cellView,s=t.options.index;if(Array.isArray(r)){for(let t=0,e=r.length;t<e;t+=1)t!==s&&r[t].hide();if(this.focus(),this.setEventData(e,{sourceAnchor:i.sourceAnchor.clone(),targetAnchor:i.targetAnchor.clone(),sourceAnchorDef:xo(this.cell.prop(["source","anchor"])),targetAnchorDef:xo(this.cell.prop(["target","anchor"]))}),this.cell.startBatch("move-segment",{ui:!0,toolId:this.cid}),!n.stopPropagation){const t=this.normalizeEvent(e),n=this.graph.snapToGrid(t.clientX,t.clientY);i.notifyMouseDown(t,n.x,n.y)}}}onHandleChanged({e:t}){const e=this.options,n=this.cellView;e.removeRedundancies&&n.removeRedundantLinearVertices({ui:!0,toolId:this.cid});const r=this.normalizeEvent(t),i=this.graph.snapToGrid(r.clientX,r.clientY);this.render(),this.blur(),this.cell.stopBatch("move-segment",{ui:!0,toolId:this.cid}),e.stopPropagation||n.notifyMouseUp(r,i.x,i.y),n.checkMouseleave(r),e.onChanged&&e.onChanged({edge:n.cell,edgeView:n})}updateHandle(t,e,n,r=0){const i=this.options.precision||0,s=Math.abs(e.x-n.x)<i,o=Math.abs(e.y-n.y)<i;if(s||o){const i=new Ny(e,n);if(i.length()<this.options.threshold)t.hide();else{const e=i.getCenter(),n=s?"x":"y";e[n]+=r||0;const o=i.vector().vectorAngle(new Ty(1,0));t.updatePosition(e.x,e.y,o,this.cellView),t.show(),t.options.axis=n}}else t.hide()}onRemove(){this.resetHandles()}}!function(t){t.Handle=class extends nx{constructor(t){super(),this.options=t,this.render(),this.delegateEvents({mousedown:"onMouseDown",touchstart:"onMouseDown"})}render(){this.container=nx.createElement("rect",!0);const e=this.options.attrs;if("function"==typeof e){const n=t.getDefaults();this.setAttrs(Object.assign(Object.assign({},n.attrs),e(this)))}else this.setAttrs(e);this.addClass(this.prefixClassName("edge-tool-segment"))}updatePosition(t,e,n,r){const i=r.getClosestPoint(new Ty(t,e))||new Ty(t,e);let s=$m().translate(i.x,i.y);if(i.equals({x:t,y:e}))s=s.rotate(n);else{let n=new Ny(t,e,i.x,i.y).vector().vectorAngle(new Ty(1,0));0!==n&&(n+=90),s=s.rotate(n)}this.setAttrs({transform:Um(s),cursor:n%180==0?"row-resize":"col-resize"})}onMouseDown(t){this.options.guard(t)||(this.trigger("change",{e:t,handle:this}),t.stopPropagation(),t.preventDefault(),this.options.graph.view.undelegateEvents(),this.delegateDocumentEvents({mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"},t.data))}onMouseMove(t){this.emit("changing",{e:t,handle:this})}onMouseUp(t){this.emit("changed",{e:t,handle:this}),this.undelegateDocumentEvents(),this.options.graph.view.delegateEvents()}show(){this.container.style.display=""}hide(){this.container.style.display="none"}}}(bx||(bx={})),function(t){t.config({name:"segments",precision:.5,threshold:40,snapRadius:10,stopPropagation:!0,removeRedundancies:!0,attrs:{width:20,height:8,x:-10,y:-4,rx:4,ry:4,fill:"#333",stroke:"#fff","stroke-width":2},createHandle:e=>new t.Handle(e),anchor:fx})}(bx||(bx={}));class xx extends ax.ToolItem{get type(){return this.options.type}onRender(){ap(this.container,this.prefixClassName(`edge-tool-${this.type}-anchor`)),this.toggleArea(!1),this.update()}update(){const t=this.type;return this.cellView.getTerminalView(t)?(this.updateAnchor(),this.updateArea(),this.container.style.display=""):this.container.style.display="none",this}updateAnchor(){const t=this.childNodes;if(!t)return;const e=t.anchor;if(!e)return;const n=this.type,r=this.cellView,i=this.options,s=r.getTerminalAnchor(n),o=r.cell.prop([n,"anchor"]);e.setAttribute("transform",`translate(${s.x}, ${s.y})`);const a=o?i.customAnchorAttrs:i.defaultAnchorAttrs;a&&Object.keys(a).forEach((t=>{e.setAttribute(t,a[t])}))}updateArea(){const t=this.childNodes;if(!t)return;const e=t.area;if(!e)return;const n=this.type,r=this.cellView,i=r.getTerminalView(n);if(i){const t=i.cell,s=r.getTerminalMagnet(n);let o,a,l,c=this.options.areaPadding||0;Number.isFinite(c)||(c=0),i.isEdgeElement(s)?(o=i.getBBox(),a=0,l=o.getCenter()):(o=i.getUnrotatedBBoxOfElement(s),a=t.getAngle(),l=o.getCenter(),a&&l.rotate(-a,t.getBBox().getCenter())),o.inflate(c),zp(e,{x:-o.width/2,y:-o.height/2,width:o.width,height:o.height,transform:`translate(${l.x}, ${l.y}) rotate(${a})`})}}toggleArea(t){if(this.childNodes){const e=this.childNodes.area;e&&(e.style.display=t?"":"none")}}onMouseDown(t){this.guard(t)||(t.stopPropagation(),t.preventDefault(),this.graph.view.undelegateEvents(),this.options.documentEvents&&this.delegateDocumentEvents(this.options.documentEvents),this.focus(),this.toggleArea(this.options.restrictArea),this.cell.startBatch("move-anchor",{ui:!0,toolId:this.cid}))}resetAnchor(t){const e=this.type,n=this.cell;t?n.prop([e,"anchor"],t,{rewrite:!0,ui:!0,toolId:this.cid}):n.removeProp([e,"anchor"],{ui:!0,toolId:this.cid})}onMouseMove(t){const e=this.type,n=this.cellView,r=n.getTerminalView(e);if(null==r)return;const i=this.normalizeEvent(t),s=r.cell,o=n.getTerminalMagnet(e);let a=this.graph.coord.clientToLocalPoint(i.clientX,i.clientY);const l=this.options.snap;if("function"==typeof l){const t=ef(l,n,a,r,o,e,n,this);a=Ty.create(t)}if(this.options.restrictArea)if(r.isEdgeElement(o)){const t=r.getClosestPoint(a);t&&(a=t)}else{const t=r.getUnrotatedBBoxOfElement(o),e=s.getAngle(),n=s.getBBox().getCenter(),i=a.clone().rotate(e,n);t.containsPoint(i)||(a=t.getNearestPointToPoint(i).rotate(-e,n))}let c;const h=this.options.anchor;"function"==typeof h&&(c=ef(h,n,a,r,o,e,n,this)),this.resetAnchor(c),this.update()}onMouseUp(t){this.graph.view.delegateEvents(),this.undelegateDocumentEvents(),this.blur(),this.toggleArea(!1);const e=this.cellView;this.options.removeRedundancies&&e.removeRedundantLinearVertices({ui:!0,toolId:this.cid}),this.cell.stopBatch("move-anchor",{ui:!0,toolId:this.cid})}onDblClick(){const t=this.options.resetAnchor;t&&this.resetAnchor(!0===t?void 0:t),this.update()}}!function(t){t.config({tagName:"g",markup:[{tagName:"circle",selector:"anchor",attrs:{cursor:"pointer"}},{tagName:"rect",selector:"area",attrs:{"pointer-events":"none",fill:"none",stroke:"#33334F","stroke-dasharray":"2,4",rx:5,ry:5}}],events:{mousedown:"onMouseDown",touchstart:"onMouseDown",dblclick:"onDblClick"},documentEvents:{mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"},customAnchorAttrs:{"stroke-width":4,stroke:"#33334F",fill:"#FFFFFF",r:5},defaultAnchorAttrs:{"stroke-width":2,stroke:"#FFFFFF",fill:"#33334F",r:6},areaPadding:6,snapRadius:10,resetAnchor:!0,restrictArea:!0,removeRedundancies:!0,anchor:fx,snap(t,e,n,r,i,s){const o=s.options.snapRadius||0,a="source"===r,l=a?0:-1,c=this.cell.getVertexAt(l)||this.getTerminalAnchor(a?"target":"source");return c&&(Math.abs(c.x-t.x)<o&&(t.x=c.x),Math.abs(c.y-t.y)<o&&(t.y=c.y)),t}})}(xx||(xx={}));const wx=xx.define({name:"source-anchor",type:"source"}),Ax=xx.define({name:"target-anchor",type:"target"});class Px extends ax.ToolItem{get type(){return this.options.type}get ratio(){return this.options.ratio}init(){if(this.options.attrs){const t=this.options.attrs,{class:n}=t,r=e(t,["class"]);this.setAttrs(r,this.container),n&&ap(this.container,n)}}onRender(){ap(this.container,this.prefixClassName(`edge-tool-${this.type}-arrowhead`)),this.update()}update(){const t=this.ratio,e=this.cellView,n=e.getTangentAtRatio(t),r=n?n.start:e.getPointAtRatio(t),i=n&&n.vector().vectorAngle(new Ty(1,0))||0;if(!r)return this;const s=$m().translate(r.x,r.y).rotate(i);return Zm(this.container,s,{absolute:!0}),this}onMouseDown(t){if(this.guard(t))return;t.stopPropagation(),t.preventDefault();const e=this.cellView;if(e.can("arrowheadMovable")){e.cell.startBatch("move-arrowhead",{ui:!0,toolId:this.cid});const n=this.graph.snapToGrid(t.clientX,t.clientY),r=e.prepareArrowheadDragging(this.type,{x:n.x,y:n.y,options:Object.assign(Object.assign({},this.options),{toolId:this.cid})});this.cellView.setEventData(t,r),this.delegateDocumentEvents(this.options.documentEvents,t.data),e.graph.view.undelegateEvents(),this.container.style.pointerEvents="none"}this.focus()}onMouseMove(t){const e=this.normalizeEvent(t),n=this.graph.snapToGrid(e.clientX,e.clientY);this.cellView.onMouseMove(e,n.x,n.y),this.update()}onMouseUp(t){this.undelegateDocumentEvents();const e=this.normalizeEvent(t),n=this.cellView,r=this.graph.snapToGrid(e.clientX,e.clientY);n.onMouseUp(e,r.x,r.y),this.graph.view.delegateEvents(),this.blur(),this.container.style.pointerEvents="",n.cell.stopBatch("move-arrowhead",{ui:!0,toolId:this.cid})}}!function(t){t.config({tagName:"path",isSVGElement:!0,events:{mousedown:"onMouseDown",touchstart:"onMouseDown"},documentEvents:{mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"}})}(Px||(Px={}));const Cx=Px.define({name:"source-arrowhead",type:"source",ratio:0,attrs:{d:"M 10 -8 -10 0 10 8 Z",fill:"#333",stroke:"#fff","stroke-width":2,cursor:"move"}}),Mx=Px.define({name:"target-arrowhead",type:"target",ratio:1,attrs:{d:"M -10 -8 10 0 -10 8 Z",fill:"#333",stroke:"#fff","stroke-width":2,cursor:"move"}});class Ex extends ax.ToolItem{constructor(){super(...arguments),this.labelIndex=-1,this.distance=.5,this.dblClick=this.onCellDblClick.bind(this)}onRender(){const t=this.cellView;t&&t.on("cell:dblclick",this.dblClick)}createElement(){const t=[this.prefixClassName((this.cell.isEdge()?"edge":"node")+"-tool-editor"),this.prefixClassName("cell-tool-editor")];this.editor=ax.createElement("div",!1),this.addClass(t,this.editor),this.editor.contentEditable="true",this.container.appendChild(this.editor)}removeElement(){this.undelegateDocumentEvents(),this.editor&&(this.container.removeChild(this.editor),this.editor=null)}updateEditor(){const{cell:t,editor:e}=this;if(!e)return;const{style:n}=e;t.isNode()?this.updateNodeEditorTransform():t.isEdge()&&this.updateEdgeEditorTransform();const{attrs:r}=this.options;n.fontSize=`${r.fontSize}px`,n.fontFamily=r.fontFamily,n.color=r.color,n.backgroundColor=r.backgroundColor;const i=this.getCellText()||"";return e.innerText=i,this.setCellText(""),this}updateNodeEditorTransform(){const{graph:t,cell:e,editor:n}=this;if(!n)return;let r=Ty.create(),i=20,s="",{x:o,y:a}=this.options;if(void 0!==o&&void 0!==a){const t=e.getBBox();o=If(o,t.width),a=If(a,t.height),r=t.topLeft.translate(o,a),i=t.width-2*o}else{const t=e.getBBox();r=t.center,i=t.width-4,s="translate(-50%, -50%)"}const l=t.scale(),{style:c}=n;r=t.localToGraph(r),c.left=`${r.x}px`,c.top=`${r.y}px`,c.transform=`scale(${l.sx}, ${l.sy}) ${s}`,c.minWidth=`${i}px`}updateEdgeEditorTransform(){if(!this.event)return;const{graph:e,editor:n}=this;if(!n)return;let r=Ty.create(),i=20;const{style:s}=n,o=this.event.target,a=o.parentElement;if(a&&op(a,this.prefixClassName("edge-label"))){const e=a.getAttribute("data-index")||"0";this.labelIndex=parseInt(e,10);const n=a.getAttribute("transform"),{translation:s}=qm(n);r=new Ty(s.tx,s.ty),i=t.Util.getBBox(o).width}else{if(!this.options.labelAddable)return this;r=e.clientToLocal(Ty.create(this.event.clientX,this.event.clientY));const t=this.cellView.path.closestPointLength(r);this.distance=t,this.labelIndex=-1}r=e.localToGraph(r);const l=e.scale();s.left=`${r.x}px`,s.top=`${r.y}px`,s.minWidth=`${i}px`,s.transform=`scale(${l.sx}, ${l.sy}) translate(-50%, -50%)`}onDocumentMouseDown(t){if(this.editor&&t.target!==this.editor){const t=this.editor.innerText.replace(/\n$/,"")||"";this.setCellText(""!==t?t:null),this.removeElement()}}onCellDblClick({e:t}){t.stopPropagation(),this.removeElement(),this.event=t,this.createElement(),this.updateEditor(),this.autoFocus(),this.delegateDocumentEvents(this.options.documentEvents)}onMouseDown(t){t.stopPropagation()}autoFocus(){setTimeout((()=>{this.editor&&(this.editor.focus(),this.selectText())}))}selectText(){if(window.getSelection&&this.editor){const t=document.createRange(),e=window.getSelection();t.selectNodeContents(this.editor),e.removeAllRanges(),e.addRange(t)}}getCellText(){const{getText:t}=this.options;if("function"==typeof t)return ef(t,this.cellView,{cell:this.cell,index:this.labelIndex});if("string"==typeof t){if(this.cell.isNode())return this.cell.attr(t);if(this.cell.isEdge()&&-1!==this.labelIndex)return this.cell.prop(`labels/${this.labelIndex}/attrs/${t}`)}}setCellText(t){const e=this.options.setText;if("function"!=typeof e){if("string"==typeof e){if(this.cell.isNode())return void(null!==t&&this.cell.attr(e,t));if(this.cell.isEdge()){const n=this.cell;if(-1===this.labelIndex){if(t){const r={position:{distance:this.distance},attrs:{}};yf(r,`attrs/${e}`,t),n.appendLabel(r)}}else null!==t?n.prop(`labels/${this.labelIndex}/attrs/${e}`,t):"number"==typeof this.labelIndex&&n.removeLabelAt(this.labelIndex)}}}else ef(e,this.cellView,{cell:this.cell,value:t,index:this.labelIndex,distance:this.distance})}onRemove(){const t=this.cellView;t&&t.off("cell:dblclick",this.dblClick),this.removeElement()}}var Sx,Ox;!function(t){t.config({tagName:"div",isSVGElement:!1,events:{mousedown:"onMouseDown"},documentEvents:{mousedown:"onDocumentMouseDown"}})}(Ex||(Ex={})),function(t){t.NodeEditor=t.define({attrs:{fontSize:14,fontFamily:"Arial, helvetica, sans-serif",color:"#000",backgroundColor:"#fff"},getText:"text/text",setText:"text/text"}),t.EdgeEditor=t.define({attrs:{fontSize:14,fontFamily:"Arial, helvetica, sans-serif",color:"#000",backgroundColor:"#fff"},labelAddable:!0,getText:"label/text",setText:"label/text"})}(Ex||(Ex={})),function(t){t.presets={boundary:yx,button:mx,"button-remove":mx.Remove,"node-editor":Ex.NodeEditor},t.registry=Qy.create({type:"node tool",process(t,n){if("function"==typeof n)return n;let r=ax.ToolItem;const{inherit:i}=n,s=e(n,["inherit"]);if(i){const t=this.get(i);null==t?this.onNotFound(i,"inherited"):r=t}return null==s.name&&(s.name=t),r.define.call(r,s)}}),t.registry.register(t.presets,!0)}(Sx||(Sx={})),function(t){t.presets={boundary:yx,vertices:vx,segments:bx,button:mx,"button-remove":mx.Remove,"source-anchor":wx,"target-anchor":Ax,"source-arrowhead":Cx,"target-arrowhead":Mx,"edge-editor":Ex.EdgeEditor},t.registry=Qy.create({type:"edge tool",process(t,n){if("function"==typeof n)return n;let r=ax.ToolItem;const{inherit:i}=n,s=e(n,["inherit"]);if(i){const t=this.get(i);null==t?this.onNotFound(i,"inherited"):r=t}return null==s.name&&(s.name=t),r.define.call(r,s)}}),t.registry.register(t.presets,!0)}(Ox||(Ox={}));const Tx=Rx("center"),kx=Rx("topCenter"),Nx=Rx("bottomCenter"),jx=Rx("leftMiddle"),_x=Rx("rightMiddle"),Lx=Rx("topLeft"),Bx=Rx("topRight"),Ix=Rx("bottomLeft"),Dx=Rx("bottomRight");function Rx(t){return function(e,n,r,i={}){const s=i.rotate?e.getUnrotatedBBoxOfElement(n):e.getBBoxOfElement(n),o=s[t];o.x+=If(i.dx,s.width),o.y+=If(i.dy,s.height);const a=e.cell;return i.rotate?o.rotate(-a.getAngle(),a.getBBox().getCenter()):o}}function Vx(t){return function(e,n,r,i){if(r instanceof Element){const s=this.graph.findViewByElem(r);let o;if(s)if(s.isEdgeElement(r)){o=zx(s,null!=i.fixedAt?i.fixedAt:"50%")}else o=s.getBBoxOfElement(r).getCenter();else o=new Ty;return t.call(this,e,n,o,i)}return t.apply(this,arguments)}}function zx(t,e){const n=Bf(e),r="string"==typeof e?parseFloat(e):e;return n?t.getPointAtRatio(r/100):t.getPointAtLength(r)}const $x=Vx((function(e,n,r,i){const s=e.cell.getAngle(),o=e.getBBoxOfElement(n),a=o.getCenter(),l=o.getTopLeft(),c=o.getBottomRight();let h=i.padding;if(Number.isFinite(h)||(h=0),l.y+h<=r.y&&r.y<=c.y-h){const e=r.y-a.y;a.x+=0===s||180===s?0:1*e/Math.tan(t.Angle.toRad(s)),a.y+=e}else if(l.x+h<=r.x&&r.x<=c.x-h){const e=r.x-a.x;a.y+=90===s||270===s?0:e*Math.tan(t.Angle.toRad(s)),a.x+=e}return a})),Fx=Vx((function(t,e,n,r){let i,s,o=0;const a=t.cell;r.rotate?(i=t.getUnrotatedBBoxOfElement(e),s=a.getBBox().getCenter(),o=a.getAngle()):i=t.getBBoxOfElement(e);const l=r.padding;null!=l&&Number.isFinite(l)&&i.inflate(l),r.rotate&&n.rotate(o,s);const c=i.getNearestSideToPoint(n);let h;switch(c){case"left":h=i.getLeftMiddle();break;case"right":h=i.getRightMiddle();break;case"top":h=i.getTopCenter();break;case"bottom":h=i.getBottomCenter()}const u=r.direction;return"H"===u?"top"!==c&&"bottom"!==c||(h=n.x<=i.x+i.width?i.getLeftMiddle():i.getRightMiddle()):"V"===u&&(h=n.y<=i.y+i.height?i.getTopCenter():i.getBottomCenter()),r.rotate?h.rotate(-o,s):h}));var Gx,Ux=Object.freeze({__proto__:null,bottom:Nx,bottomLeft:Ix,bottomRight:Dx,center:Tx,left:jx,midSide:Fx,nodeCenter:function(t,e,n,r,i){const s=t.cell.getConnectionPoint(this.cell,i);return(r.dx||r.dy)&&s.translate(r.dx||0,r.dy||0),s},orth:$x,right:_x,top:kx,topLeft:Lx,topRight:Bx});!function(t){t.presets=Ux,t.registry=Qy.create({type:"node endpoint"}),t.registry.register(t.presets,!0)}(Gx||(Gx={}));const qx=function(t,e,n,r){const i=t.getClosestPoint(n);return null!=i?i:new Ty},Wx=Vx(qx),Hx=Vx((function(t,e,n,r){const i=1e6,s=t.getConnection(),o=t.getConnectionSubdivisions(),a=new Ny(n.clone().translate(0,i),n.clone().translate(0,-i)),l=new Ny(n.clone().translate(i,0),n.clone().translate(-i,0)),c=a.intersect(s,{segmentSubdivisions:o}),h=l.intersect(s,{segmentSubdivisions:o}),u=[];return c&&u.push(...c),h&&u.push(...h),u.length>0?n.closest(u):null!=r.fallbackAt?zx(t,r.fallbackAt):ef(qx,this,t,e,n,r)}));var Jx,Xx=Object.freeze({__proto__:null,closest:Wx,length:function(t,e,n,r){const i=null!=r.length?r.length:20;return t.getPointAtLength(i)},orth:Hx,ratio:function(t,e,n,r){let i=null!=r.ratio?r.ratio:.5;return i>1&&(i/=100),t.getPointAtRatio(i)}});function Yx(t,e,n){let r;if("object"==typeof n){if(Number.isFinite(n.y)){const r=new Ny(e,t),{start:i,end:s}=r.parallel(n.y);e=i,t=s}r=n.x}else r=n;if(null==r||!Number.isFinite(r))return t;const i=t.distance(e);return 0===r&&i>0?t:t.move(e,-Math.min(r,i-1))}function Zx(t){const e=t.getAttribute("stroke-width");return null===e?0:parseFloat(e)||0}!function(t){t.presets=Xx,t.registry=Qy.create({type:"edge endpoint"}),t.registry.register(t.presets,!0)}(Jx||(Jx={}));const Kx=function(t,e,n,r){const i=e.getBBoxOfElement(n);r.stroked&&i.inflate(Zx(n)/2);const s=t.intersect(i);return Yx(s&&s.length?t.start.closest(s):t.end,t.start,r.offset)};var Qx,tw=Object.freeze({__proto__:null,anchor:function(t,e,n,r){const{alignOffset:i,align:s}=r;return s&&function(t,e,n=0){const{start:r,end:i}=t;let s,o,a,l;switch(e){case"left":l="x",s=i,o=r,a=-1;break;case"right":l="x",s=r,o=i,a=1;break;case"top":l="y",s=i,o=r,a=-1;break;case"bottom":l="y",s=r,o=i,a=1;break;default:return}r[l]<i[l]?s[l]=o[l]:o[l]=s[l],Number.isFinite(n)&&(s[l]+=a*n,o[l]+=a*n)}(t,s,i),Yx(t.end,t.start,r.offset)},bbox:Kx,boundary:function(e,n,r,i){let s,o;const a=e.end,l=i.selector;if(s="string"==typeof l?n.findOne(l):Array.isArray(l)?mf(r,l):function(t){if(null==t)return null;let e=t;do{let t=e.tagName;if("string"!=typeof t)return null;if(t=t.toUpperCase(),"G"===t)e=e.firstElementChild;else{if("TITLE"!==t)break;e=e.nextElementSibling}}while(e);return e}(r),!dp(s)){if(s===r||!dp(r))return a;s=r}const c=n.getShapeOfElement(s),h=n.getMatrixOfElement(s),u=n.getRootTranslatedMatrix(),g=n.getRootRotatedMatrix(),d=u.multiply(g).multiply(h),f=d.inverse(),p=t.Util.transformLine(e,f),m=p.start.clone(),y=n.getDataOfElement(s);if(!1===i.insideout){null==y.shapeBBox&&(y.shapeBBox=c.bbox());const t=y.shapeBBox;if(null!=t&&t.containsPoint(m))return a}let v;if(!0===i.extrapolate&&p.setLength(1e6),Ky.isPath(c)){const t=i.precision||2;null==y.segmentSubdivisions&&(y.segmentSubdivisions=c.getSegmentSubdivisions({precision:t})),v={precision:t,segmentSubdivisions:y.segmentSubdivisions},o=p.intersect(c,v)}else o=p.intersect(c);o?Array.isArray(o)&&(o=m.closest(o)):!0===i.sticky&&(o=ky.isRectangle(c)?c.getNearestPointToPoint(m):jy.isEllipse(c)?c.intersectsWithLineFromCenterToPoint(m):c.closestPoint(m,v));const b=o?t.Util.transformPoint(o,d):a;let x=i.offset||0;return!1!==i.stroked&&("object"==typeof x?(x=Object.assign({},x),null==x.x&&(x.x=0),x.x+=Zx(s)/2):x+=Zx(s)/2),Yx(b,e.start,x)},rect:function(t,e,n,r,i){const s=e.cell,o=s.isNode()?s.getAngle():0;if(0===o)return ef(Kx,this,t,e,n,r,i);const a=e.getUnrotatedBBoxOfElement(n);r.stroked&&a.inflate(Zx(n)/2);const l=a.getCenter(),c=t.clone().rotate(o,l),h=c.setLength(1e6).intersect(a);return Yx(h&&h.length?c.start.closest(h).rotate(-o,l):t.end,t.start,r.offset)}});!function(t){t.presets=tw,t.registry=Qy.create({type:"connection point"}),t.registry.register(t.presets,!0)}(Qx||(Qx={}));function ew(t){return new ky(t.x,t.y,0,0)}function nw(t={}){const e=Df(t.padding||20);return{x:-e.left,y:-e.top,width:e.left+e.right,height:e.top+e.bottom}}function rw(t,e={}){return t.sourceBBox.clone().moveAndExpand(nw(e))}function iw(t,e={}){return t.targetBBox.clone().moveAndExpand(nw(e))}const sw=function(t,e,n){let r=rw(n,e),i=iw(n,e);const s=function(t,e={}){return t.sourceAnchor?t.sourceAnchor:rw(t,e).getCenter()}(n,e),o=function(t,e={}){return t.targetAnchor?t.targetAnchor:iw(t,e).getCenter()}(n,e);r=r.union(ew(s)),i=i.union(ew(o));const a=t.map((t=>Ty.create(t)));a.unshift(s),a.push(o);let l=null;const c=[];for(let t=0,n=a.length-1;t<n;t+=1){let s=null;const o=a[t],h=a[t+1],u=null!=ow.getBearing(o,h);if(0===t)t+1===n?r.intersectsWithRect(i.clone().inflate(1))?s=ow.insideNode(o,h,r,i):u||(s=ow.nodeToNode(o,h,r,i)):r.containsPoint(h)?s=ow.insideNode(o,h,r,ew(h).moveAndExpand(nw(e))):u||(s=ow.nodeToVertex(o,h,r));else if(t+1===n){const t=u&&ow.getBearing(h,o)===l;i.containsPoint(o)||t?s=ow.insideNode(o,h,ew(o).moveAndExpand(nw(e)),i,l):u||(s=ow.vertexToNode(o,h,i,l))}else u||(s=ow.vertexToVertex(o,h,l));s?(c.push(...s.points),l=s.direction):l=ow.getBearing(o,h),t+1<n&&c.push(h)}return c};var ow;!function(e){const n={N:"S",S:"N",E:"W",W:"E"},r={N:-Math.PI/2*3,S:-Math.PI/2,E:0,W:Math.PI};function i(t,e,n){let r=new Ty(t.x,e.y);return n.containsPoint(r)&&(r=new Ty(e.x,t.y)),r}function s(t,e){return t["W"===e||"E"===e?"width":"height"]}function o(t,e){return t.x===e.x?t.y>e.y?"N":"S":t.y===e.y?t.x>e.x?"W":"E":null}function a(t,e,r){const i=new Ty(t.x,e.y),s=new Ty(e.x,t.y),a=o(t,i),l=o(t,s),c=r?n[r]:null,h=a===r||a!==c&&(l===c||l!==r)?i:s;return{points:[h],direction:o(h,e)}}function l(t,e,n){const r=i(t,e,n);return{points:[r],direction:o(r,e)}}e.getBBoxSize=s,e.getBearing=o,e.vertexToVertex=a,e.nodeToVertex=l,e.vertexToNode=function(t,e,n,r){const a=[new Ty(t.x,e.y),new Ty(e.x,t.y)],l=a.filter((t=>!n.containsPoint(t))),c=l.filter((e=>o(e,t)!==r));let h;if(c.length>0)return h=c.filter((e=>o(t,e)===r)).pop(),h=h||c[0],{points:[h],direction:o(h,e)};{h=sl(a,l)[0];const c=Ty.create(e).move(h,-s(n,r)/2);return{points:[i(c,t,n),c],direction:o(c,e)}}},e.nodeToNode=function(t,e,n,r){let i=l(e,t,r);const c=i.points[0];if(n.containsPoint(c)){i=l(t,e,n);const h=i.points[0];if(r.containsPoint(h)){const u=Ty.create(t).move(h,-s(n,o(t,h))/2),g=Ty.create(e).move(c,-s(r,o(e,c))/2),d=new Ny(u,g).getCenter(),f=l(t,d,n),p=a(d,e,f.direction);i.points=[f.points[0],p.points[0]],i.direction=p.direction}}return i},e.insideNode=function(e,n,s,a,l){const c=s.union(a).inflate(1),h=c.getCenter(),u=h.distance(n)>h.distance(e),g=u?n:e,d=u?e:n;let f,p,m,y;return l?(f=Ty.fromPolar(c.width+c.height,r[l],g),f=c.getNearestPointToPoint(f).move(f,-1)):f=c.getNearestPointToPoint(g).move(g,1),p=i(f,d,c),f.round().equals(p.round())?(p=Ty.fromPolar(c.width+c.height,t.Angle.toRad(f.theta(g))+Math.PI/2,d),p=c.getNearestPointToPoint(p).move(d,1).round(),m=i(f,p,c),y=u?[p,m,f]:[f,m,p]):y=u?[p,f]:[f,p],{points:y,direction:o(u?f:p,n)}}}(ow||(ow={}));const aw={step:10,maxLoopCount:2e3,precision:1,maxDirectionChange:90,perpendicular:!0,excludeTerminals:[],excludeNodes:[],excludeShapes:[],startDirections:["top","right","bottom","left"],endDirections:["top","right","bottom","left"],directionMap:{top:{x:0,y:-1},right:{x:1,y:0},bottom:{x:0,y:1},left:{x:-1,y:0}},cost(){return lw(this.step,this)},directions(){const t=lw(this.step,this),e=lw(this.cost,this);return[{cost:e,offsetX:t,offsetY:0},{cost:e,offsetX:-t,offsetY:0},{cost:e,offsetX:0,offsetY:t},{cost:e,offsetX:0,offsetY:-t}]},penalties(){const t=lw(this.step,this);return{0:0,45:t/2,90:t/2}},paddingBox(){const t=lw(this.step,this);return{x:-t,y:-t,width:2*t,height:2*t}},fallbackRouter:sw,draggingRouter:null,snapToGrid:!0};function lw(t,e){return"function"==typeof t?t.call(e):t}class cw{constructor(){this.items=[],this.hash={},this.values={}}add(t,e){this.hash[t]?this.items.splice(this.items.indexOf(t),1):this.hash[t]=1,this.values[t]=e;const n=vg(this.items,t,(t=>this.values[t]));this.items.splice(n,0,t)}pop(){const t=this.items.shift();return t&&(this.hash[t]=2),t}isOpen(t){return 1===this.hash[t]}isClose(t){return 2===this.hash[t]}isEmpty(){return 0===this.items.length}}class hw{constructor(t){this.options=t,this.mapGridSize=100,this.map={}}build(t,e){const n=this.options,r=n.excludeTerminals.reduce(((n,r)=>{const i=e[r];if(i){const e=t.getCell(i.cell);e&&n.push(e)}return n}),[]);let i=[];const s=t.getCell(e.getSourceCellId());s&&(i=ld(i,s.getAncestors().map((t=>t.id))));const o=t.getCell(e.getTargetCellId());o&&(i=ld(i,o.getAncestors().map((t=>t.id))));const a=this.mapGridSize;return t.getNodes().reduce(((t,e)=>{const s=r.some((t=>t.id===e.id)),o=!!e.shape&&n.excludeShapes.includes(e.shape),l=n.excludeNodes.some((t=>"string"==typeof t?e.id===t:t===e)),c=i.includes(e.id);if(!(o||s||l||c)){const r=e.getBBox().moveAndExpand(n.paddingBox),i=r.getOrigin().snapToGrid(a),s=r.getCorner().snapToGrid(a);for(let e=i.x;e<=s.x;e+=a)for(let n=i.y;n<=s.y;n+=a){const i=new Ty(e,n).toString();null==t[i]&&(t[i]=[]),t[i].push(r)}}return t}),this.map),this}isAccessible(t){const e=t.clone().snapToGrid(this.mapGridSize).toString(),n=this.map[e];return!n||n.every((e=>!e.containsPoint(t)))}}function uw(t,e){const n=t.sourceBBox.clone();return e&&e.paddingBox?n.moveAndExpand(e.paddingBox):n}function gw(t,e){const n=t.targetBBox.clone();return e&&e.paddingBox?n.moveAndExpand(e.paddingBox):n}function dw(t,e){if(t.sourceAnchor)return t.sourceAnchor;return uw(t,e).getCenter()}function fw(e,n,r,i,s){const o=360/r,a=e.theta(function(t,e,n,r){const i=r.step,s=e.x-t.x,o=e.y-t.y,a=s/n.x,l=o/n.y,c=a*i,h=l*i;return new Ty(t.x+c,t.y+h)}(e,n,i,s)),l=t.Angle.normalize(a+o/2);return o*Math.floor(l/o)}function pw(t,e){const n=Math.abs(t-e);return n>180?360-n:n}function mw(t,e){if(!t)return e;const n=Math.abs(t),r=Math.round(n/e);if(!r)return n;return e+(n-r*e)/r}function yw(t,e){return t.round(e)}function vw(e,n,r){return yw(function(e,n){const r=n.source,i=t.GeometryUtil.snapToGrid(e.x-r.x,n.x)+r.x,s=t.GeometryUtil.snapToGrid(e.y-r.y,n.y)+r.y;return new Ty(i,s)}(e.clone(),n),r)}function bw(t){return t.toString()}function xw(t){return new Ty(0===t.x?0:Math.abs(t.x)/t.x,0===t.y?0:Math.abs(t.y)/t.y)}function ww(t,e){let n=1/0;for(let r=0,i=e.length;r<i;r+=1){const i=t.manhattanDistance(e[r]);i<n&&(n=i)}return n}function Aw(t,e,n,r,i){const s=i.precision,o=i.directionMap,a=t.diff(e.getCenter()),l=Object.keys(o).reduce(((i,l)=>{if(n.includes(l)){const n=o[l],c=new Ty(t.x+n.x*(Math.abs(a.x)+e.width),t.y+n.y*(Math.abs(a.y)+e.height)),h=new Ny(t,c).intersect(e)||[];let u,g=null;for(let e=0;e<h.length;e+=1){const n=h[e],r=t.squaredDistance(n);(null==u||r>u)&&(u=r,g=n)}if(g){let t=vw(g,r,s);e.containsPoint(t)&&(t=vw(t.translate(n.x*r.x,n.y*r.y),r,s)),i.push(t)}}return i}),[]);return e.containsPoint(t)||l.push(vw(t,r,s)),l}function Pw(t,e,n,r,i){const s=[];let o,a=xw(i.diff(n)),l=bw(n),c=t[l];for(;c;){o=e[l];const n=xw(o.diff(c));n.equals(a)||(s.unshift(o),a=n),l=bw(c),c=t[l]}const h=e[l];return xw(h.diff(r)).equals(a)||s.unshift(h),s}function Cw(t,e,n,r,i){const s=i.precision;let o,a;o=ky.isRectangle(e)?yw(dw(t,i).clone(),s):yw(e.clone(),s),a=ky.isRectangle(n)?yw(function(t,e){return t.targetAnchor?t.targetAnchor:gw(t,e).getCenter()}(t,i).clone(),s):yw(n.clone(),s);const l=function(t,e,n){return{source:e.clone(),x:mw(n.x-e.x,t),y:mw(n.y-e.y,t)}}(i.step,o,a),c=o,h=a;let u,g;if(u=ky.isRectangle(e)?Aw(c,e,i.startDirections,l,i):[c],g=ky.isRectangle(n)?Aw(a,n,i.endDirections,l,i):[h],u=u.filter((t=>r.isAccessible(t))),g=g.filter((t=>r.isAccessible(t))),u.length>0&&g.length>0){const t=new cw,e={},n={},o={};for(let n=0,r=u.length;n<r;n+=1){const r=u[n],i=bw(r);t.add(i,ww(r,g)),e[i]=r,o[i]=0}const a=i.previousDirectionAngle,d=void 0===a;let f,p;const m=function(t,e){const n=e.step;return e.directions.forEach((e=>{e.gridOffsetX=e.offsetX/n*t.x,e.gridOffsetY=e.offsetY/n*t.y})),e.directions}(l,i),y=m.length,v=g.reduce(((t,e)=>{const n=bw(e);return t.push(n),t}),[]),b=Ty.equalPoints(u,g);let x=i.maxLoopCount;for(;!t.isEmpty()&&x>0;){const u=t.pop(),w=e[u],A=n[u],P=o[u],C=w.equals(c),M=null==A;let E;E=M?d?C?null:fw(c,w,y,l,i):a:fw(A,w,y,l,i);if(!(M&&b)&&v.indexOf(u)>=0)return i.previousDirectionAngle=E,Pw(n,e,w,c,h);for(let a=0;a<y;a+=1){f=m[a];const c=f.angle;if(p=pw(E,c),(!d||!C)&&p>i.maxDirectionChange)continue;const u=vw(w.clone().translate(f.gridOffsetX||0,f.gridOffsetY||0),l,s),b=bw(u);if(t.isClose(b)||!r.isAccessible(u))continue;if(v.indexOf(b)>=0){if(!u.equals(h)){if(pw(c,fw(u,h,y,l,i))>i.maxDirectionChange)continue}}const x=P+f.cost+(C?0:i.penalties[p]);(!t.isOpen(b)||x<o[b])&&(e[b]=u,n[b]=w,o[b]=x,t.add(b,x+ww(u,g)))}x-=1}}return i.fallbackRoute?ef(i.fallbackRoute,this,c,h,i):null}const Mw=function(e,n,r){const i=function(e){const n=Object.keys(e).reduce(((t,n)=>(t[n]="fallbackRouter"===n||"draggingRouter"===n||"fallbackRoute"===n?e[n]:lw(e[n],e),t)),{});if(n.padding){const t=Df(n.padding);n.paddingBox={x:-t.left,y:-t.top,width:t.left+t.right,height:t.top+t.bottom}}return n.directions.forEach((e=>{const n=new Ty(0,0),r=new Ty(e.offsetX,e.offsetY);e.angle=t.Angle.normalize(n.theta(r))})),n}(n),s=uw(r,i),o=gw(r,i),a=dw(r,i),l=new hw(i).build(r.graph.model,r.cell),c=e.map((t=>Ty.create(t))),h=[];let u,g,d=a;for(let t=0,n=c.length;t<=n;t+=1){let n=null;if(u=g||s,g=c[t],null==g){g=o;const t=r.cell;if((null==t.getSourceCellId()||null==t.getTargetCellId())&&"function"==typeof i.draggingRouter){const t=u===s?a:u,e=g.getOrigin();n=ef(i.draggingRouter,r,t,e,i)}}if(null==n&&(n=Cw(r,u,g,l,i)),null===n)return console.warn("Unable to execute manhattan algorithm, use orth instead"),ef(i.fallbackRouter,this,e,i,r);const f=n[0];f&&f.equals(d)&&n.shift(),d=n[n.length-1]||d,h.push(...n)}return i.snapToGrid?function(t,e=10){if(t.length<=1)return t;for(let n=0,r=t.length;n<r-1;n+=1){const r=t[n],i=t[n+1];if(r.x===i.x){const t=e*Math.round(r.x/e);r.x!==t&&(r.x=t,i.x=t)}else if(r.y===i.y){const t=e*Math.round(r.y/e);r.y!==t&&(r.y=t,i.y=t)}}return t}(h,r.graph.grid.getGridSize()):h},Ew=function(t,e,n){return ef(Mw,this,t,Object.assign(Object.assign({},aw),e),n)},Sw={maxDirectionChange:45,directions(){const t=lw(this.step,this),e=lw(this.cost,this),n=Math.ceil(Math.sqrt(t*t<<1));return[{cost:e,offsetX:t,offsetY:0},{cost:n,offsetX:t,offsetY:t},{cost:e,offsetX:0,offsetY:t},{cost:n,offsetX:-t,offsetY:t},{cost:e,offsetX:-t,offsetY:0},{cost:n,offsetX:-t,offsetY:-t},{cost:e,offsetX:0,offsetY:-t},{cost:n,offsetX:t,offsetY:-t}]},fallbackRoute(e,n,r){const i=e.theta(n),s=[];let o={x:n.x,y:e.y},a={x:e.x,y:n.y};if(i%180>90){const t=o;o=a,a=t}const l=i%90<45?o:a,c=new Ny(e,l),h=90*Math.ceil(i/90),u=Ty.fromPolar(c.squaredLength(),t.Angle.toRad(h+135),l),g=new Ny(n,u),d=c.intersectsWithLine(g),f=d||n,p=d?f:e,m=360/r.directions.length,y=p.theta(n),v=t.Angle.normalize(y+m/2),b=m*Math.floor(v/m);return r.previousDirectionAngle=b,f&&s.push(f.round()),s.push(n),s}};function Ow(t,e){if(null!=e&&!1!==e){const n="boolean"==typeof e?0:e;if(n>0){const e=Ty.create(t[1]).move(t[2],n),r=Ty.create(t[1]).move(t[0],n);return[e.toJSON(),...t,r.toJSON()]}{const e=t[1];return[Object.assign({},e),...t,Object.assign({},e)]}}return t}var Tw,kw=Object.freeze({__proto__:null,er:function(t,e,n){const r=e.offset||32,i=null==e.min?16:e.min;let s=0,o=e.direction;const a=n.sourceBBox,l=n.targetBBox,c=a.getCenter(),h=l.getCenter();if("number"==typeof r&&(s=r),null==o){let t=l.left-a.right,e=l.top-a.bottom;t>=0&&e>=0?o=t>=e?"L":"T":t<=0&&e>=0?(t=a.left-l.right,o=t>=0&&t>=e?"R":"T"):t>=0&&e<=0?(e=a.top-l.bottom,o=e>=0?t>=e?"L":"B":"L"):(t=a.left-l.right,e=a.top-l.bottom,o=t>=0&&e>=0?t>=e?"R":"B":t<=0&&e>=0?"B":t>=0&&e<=0||Math.abs(t)>Math.abs(e)?"R":"B")}let u,g,d;"H"===o?o=h.x-c.x>=0?"L":"R":"V"===o&&(o=h.y-c.y>=0?"T":"B"),"center"===r&&("L"===o?s=(l.left-a.right)/2:"R"===o?s=(a.left-l.right)/2:"T"===o?s=(l.top-a.bottom)/2:"B"===o&&(s=(a.top-l.bottom)/2));const f="L"===o||"R"===o;if(f){if(h.y===c.y)return[...t];d="L"===o?1:-1,u="x",g="width"}else{if(h.x===c.x)return[...t];d="T"===o?1:-1,u="y",g="height"}const p=c.clone(),m=h.clone();if(p[u]+=d*(a[g]/2+s),m[u]-=d*(l[g]/2+s),f){const t=p.x,e=m.x,n=a.width/2+i,r=l.width/2+i;h.x>c.x?e<=t&&(p.x=Math.max(e,c.x+n),m.x=Math.min(t,h.x-r)):e>=t&&(p.x=Math.min(e,c.x-n),m.x=Math.max(t,h.x+r))}else{const t=p.y,e=m.y,n=a.height/2+i,r=l.height/2+i;h.y>c.y?e<=t&&(p.y=Math.max(e,c.y+n),m.y=Math.min(t,h.y-r)):e>=t&&(p.y=Math.min(e,c.y-n),m.y=Math.max(t,h.y+r))}return[p.toJSON(),...t,m.toJSON()]},loop:function(e,n,r){const i=n.width||50,s=(n.height||80)/2,o=n.angle||"auto",a=r.sourceAnchor,l=r.targetAnchor,c=r.sourceBBox,h=r.targetBBox;if(a.equals(l)){const e=e=>{const n=t.Angle.toRad(e),r=Math.sin(n),o=Math.cos(n),l=new Ty(a.x+o*i,a.y+r*i),c=new Ty(l.x-o*s,l.y-r*s),h=c.clone().rotate(-90,l),u=c.clone().rotate(90,l);return[h.toJSON(),l.toJSON(),u.toJSON()]},r=t=>{const e=a.clone().move(t,-1),n=new Ny(e,t);return!c.containsPoint(t)&&!c.intersectsWithLine(n)},l=[0,90,180,270,45,135,225,315];if("number"==typeof o)return Ow(e(o),n.merge);const h=c.getCenter();if(h.equals(a))return Ow(e(0),n.merge);const u=h.angleBetween(a,h.clone().translate(1,0));let g=e(u);if(r(g[1]))return Ow(g,n.merge);for(let t=1,i=l.length;t<i;t+=1)if(g=e(u+l[t]),r(g[1]))return Ow(g,n.merge);return Ow(g,n.merge)}{const t=new Ny(a,l);let e=t.parallel(-i),o=e.getCenter(),u=e.start.clone().move(e.end,s),g=e.end.clone().move(e.start,s);const d=t.parallel(-1),f=new Ny(d.start,o),p=new Ny(d.end,o);if((c.containsPoint(o)||h.containsPoint(o)||c.intersectsWithLine(f)||c.intersectsWithLine(p)||h.intersectsWithLine(f)||h.intersectsWithLine(p))&&(e=t.parallel(i),o=e.getCenter(),u=e.start.clone().move(e.end,s),g=e.end.clone().move(e.start,s)),n.merge){const t=new Ny(a,l),e=new Ny(o,t.center).setLength(Number.MAX_SAFE_INTEGER),n=c.intersectsWithLine(e),i=h.intersectsWithLine(e),s=n?Array.isArray(n)?n:[n]:[];i&&(Array.isArray(i)?s.push(...i):s.push(i));const u=t.center.closest(s);u?(r.sourceAnchor=u.clone(),r.targetAnchor=u.clone()):(r.sourceAnchor=t.center.clone(),r.targetAnchor=t.center.clone())}return Ow([u.toJSON(),o.toJSON(),g.toJSON()],n.merge)}},manhattan:Ew,metro:function(t,e,n){return ef(Ew,this,t,Object.assign(Object.assign({},Sw),e),n)},normal:function(t){return[...t]},oneSide:function(t,e,n){const r=e.side||"bottom",i=Df(e.padding||40),s=n.sourceBBox,o=n.targetBBox,a=s.getCenter(),l=o.getCenter();let c,h,u;switch(r){case"top":u=-1,c="y",h="height";break;case"left":u=-1,c="x",h="width";break;case"right":u=1,c="x",h="width";break;default:u=1,c="y",h="height"}return a[c]+=u*(s[h]/2+i[r]),l[c]+=u*(o[h]/2+i[r]),u*(a[c]-l[c])>0?l[c]=a[c]:a[c]=l[c],[a.toJSON(),...t,l.toJSON()]},orth:sw});!function(t){t.presets=kw,t.registry=Qy.create({type:"router"}),t.registry.register(t.presets,!0)}(Tw||(Tw={}));const Nw=1,jw=1/3,_w=2/3;function Lw(t,e,n=[]){const r=[t,...n,e],i=[];return r.forEach(((t,e)=>{const n=r[e+1];null!=n&&i.push(new Ny(t,n))})),i}function Bw(t,e){return new Ny(t,e).squaredLength()}function Iw(t,e,n,r){const i=new Ky;let s;return s=Ky.createSegment("M",t[0].start),i.appendSegment(s),t.forEach(((o,a)=>{if(Dw.includes(o)){let t,r,a,l;if("arc"===n){t=-90,r=o.start.diff(o.end);(r.x<0||0===r.x&&r.y<0)&&(t+=180);const e=o.getCenter(),n=new Ny(e,o.end).rotate(t,e);let c;c=new Ny(o.start,e),a=c.pointAt(2/3).rotate(t,o.start),l=n.pointAt(1/3).rotate(-t,n.end),s=Ky.createSegment("C",a,l,n.end),i.appendSegment(s),c=new Ny(e,o.end),a=n.pointAt(1/3).rotate(t,n.end),l=c.pointAt(1/3).rotate(-t,o.end),s=Ky.createSegment("C",a,l,o.end),i.appendSegment(s)}else if("gap"===n)s=Ky.createSegment("M",o.end),i.appendSegment(s);else if("cubic"===n){t=o.start.theta(o.end);const n=.6*e;let c=1.35*e;r=o.start.diff(o.end);(r.x<0||0===r.x&&r.y<0)&&(c*=-1),a=new Ty(o.start.x+n,o.start.y+c).rotate(t,o.start),l=new Ty(o.end.x-n,o.end.y+c).rotate(t,o.end),s=Ky.createSegment("C",a,l,o.end),i.appendSegment(s)}}else{const e=t[a+1];0===r||!e||Dw.includes(e)?(s=Ky.createSegment("L",o.end),i.appendSegment(s)):function(t,e,n,r,i){const s=n.distance(r)/2,o=n.distance(i)/2,a=-Math.min(t,s),l=-Math.min(t,o),c=n.clone().move(r,a).round(),h=n.clone().move(i,l).round(),u=new Ty(jw*c.x+_w*n.x,_w*n.y+jw*c.y),g=new Ty(jw*h.x+_w*n.x,_w*n.y+jw*h.y);let d;d=Ky.createSegment("L",c),e.appendSegment(d),d=Ky.createSegment("C",u,g,h),e.appendSegment(d)}(r,i,o.end,o.start,e.end)}})),i}let Dw,Rw;var Vw,zw=Object.freeze({__proto__:null,jumpover:function(t,e,n,r={}){Dw=[],Rw=[],function(t){let e=t.graph._jumpOverUpdateList;if(null==e&&(e=t.graph._jumpOverUpdateList=[],t.graph.on("cell:mouseup",(()=>{const e=t.graph._jumpOverUpdateList;setTimeout((()=>{for(let t=0;t<e.length;t+=1)e[t].update()}))})),t.graph.on("model:reseted",(()=>{e=t.graph._jumpOverUpdateList=[]}))),e.indexOf(t)<0){e.push(t);const n=()=>e.splice(e.indexOf(t),1);t.cell.once("change:connector",n),t.cell.once("removed",n)}}(this);const i=r.size||5,s=r.type||"arc",o=r.radius||0,a=r.ignoreConnectors||["smooth"],l=this.graph,c=l.model.getEdges();if(1===c.length)return Iw(Lw(t,e,n),i,s,o);const h=this.cell,u=c.indexOf(h),g=l.options.connecting.connector||{},d=c.filter(((t,e)=>{const n=t.getConnector()||g;return!a.includes(n.name)&&(!(e>u)||"jumpover"!==n.name)})),f=d.map((t=>l.findViewByCell(t))),p=Lw(t,e,n),m=f.map((t=>null==t?[]:t===this?p:Lw(t.sourcePoint,t.targetPoint,t.routePoints))),y=[];p.forEach((t=>{const e=d.reduce(((e,n,r)=>{if(n!==h){const n=function(t,e){const n=[];return e.forEach((e=>{const r=t.intersectsWithLine(e);r&&n.push(r)})),n}(t,m[r]);e.push(...n)}return e}),[]).sort(((e,n)=>Bw(t.start,e)-Bw(t.start,n)));e.length>0?y.push(...function(t,e,n){return e.reduce(((r,i,s)=>{if(Rw.includes(i))return r;const o=r.pop()||t,a=Ty.create(i).move(o.start,-n);let l=Ty.create(i).move(o.start,+n);const c=e[s+1];if(null!=c){const t=l.distance(c);t<=n&&(l=c.move(o.start,t),Rw.push(c))}else if(a.distance(o.end)<2*n+Nw)return r.push(o),r;if(l.distance(o.start)<2*n+Nw)return r.push(o),r;const h=new Ny(a,l);return Dw.push(h),r.push(new Ny(o.start,a),h,new Ny(l,o.end)),r}),[])}(t,e,i)):y.push(t)}));const v=Iw(y,i,s,o);return Dw=[],Rw=[],r.raw?v:v.serialize()},loop:function(t,e,n,r={}){const i=3===n.length?0:1,s=Ty.create(n[0+i]),o=Ty.create(n[2+i]),a=Ty.create(n[1+i]);if(!Ty.equals(t,e)){const n=new Ty((t.x+e.x)/2,(t.y+e.y)/2),r=n.angleBetween(Ty.create(t).rotate(90,n),a);r>1&&(s.rotate(180-r,n),o.rotate(180-r,n),a.rotate(180-r,n))}const l=`\n     M ${t.x} ${t.y}\n     Q ${s.x} ${s.y} ${a.x} ${a.y}\n     Q ${o.x} ${o.y} ${e.x} ${e.y}\n  `;return r.raw?Ky.parse(l):l},normal:function(t,e,n,r={}){const i=[t,...n,e],s=new Vy(i),o=new Ky(s);return r.raw?o:o.serialize()},rounded:function(t,e,n,r={}){const i=new Ky;i.appendSegment(Ky.createSegment("M",t));const s=1/3,o=2/3,a=r.radius||10;let l,c;for(let r=0,h=n.length;r<h;r+=1){const h=Ty.create(n[r]),u=n[r-1]||t,g=n[r+1]||e;l=c||h.distance(u)/2,c=h.distance(g)/2;const d=-Math.min(a,l),f=-Math.min(a,c),p=h.clone().move(u,d).round(),m=h.clone().move(g,f).round(),y=new Ty(s*p.x+o*h.x,o*h.y+s*p.y),v=new Ty(s*m.x+o*h.x,o*h.y+s*m.y);i.appendSegment(Ky.createSegment("L",p)),i.appendSegment(Ky.createSegment("C",y,v,m))}return i.appendSegment(Ky.createSegment("L",e)),r.raw?i:i.serialize()},smooth:function(t,e,n,r={}){let i,s=r.direction;if(n&&0!==n.length){const r=[t,...n,e],s=zy.throughPoints(r);i=new Ky(s)}else if(i=new Ky,i.appendSegment(Ky.createSegment("M",t)),s||(s=Math.abs(t.x-e.x)>=Math.abs(t.y-e.y)?"H":"V"),"H"===s){const n=(t.x+e.x)/2;i.appendSegment(Ky.createSegment("C",n,t.y,n,e.y,e.x,e.y))}else{const n=(t.y+e.y)/2;i.appendSegment(Ky.createSegment("C",t.x,n,e.x,n,e.x,e.y))}return r.raw?i:i.serialize()}});!function(t){t.presets=zw,t.registry=Qy.create({type:"connector"}),t.registry.register(t.presets,!0)}(Vw||(Vw={}));var $w,Fw=Object.freeze({__proto__:null,get Attr(){return Tb},get Background(){return cv},get ConnectionPoint(){return Qx},get Connector(){return Vw},get EdgeAnchor(){return Jx},get EdgeTool(){return Ox},get Filter(){return dv},get Grid(){return sv},get Highlighter(){return Vb},get Marker(){return db},get NodeAnchor(){return Gx},get NodeTool(){return Sx},get PortLabelLayout(){return Qb},get PortLayout(){return qb},get Registry(){return Qy},get Router(){return Tw}});class Gw extends xf{constructor(t={}){super(),this.pending=!1,this.changing=!1,this.data={},this.mutate(xo(t)),this.changed={}}mutate(t,e={}){const n=!0===e.unset,r=!0===e.silent,i=[],s=this.changing;this.changing=!0,s||(this.previous=xo(this.data),this.changed={});const o=this.data,a=this.previous,l=this.changed;if(Object.keys(t).forEach((e=>{const r=e,s=t[r];Yc(o[r],s)||i.push(r),Yc(a[r],s)?delete l[r]:l[r]=s,n?delete o[r]:o[r]=s})),!r&&i.length>0&&(this.pending=!0,this.pendingOptions=e,i.forEach((t=>{this.emit("change:*",{key:t,options:e,store:this,current:o[t],previous:a[t]})}))),s)return this;if(!r)for(;this.pending;)this.pending=!1,this.emit("changed",{current:o,previous:a,store:this,options:this.pendingOptions});return this.pending=!1,this.changing=!1,this.pendingOptions=null,this}get(t,e){if(null==t)return this.data;const n=this.data[t];return null==n?e:n}getPrevious(t){if(this.previous){const e=this.previous[t];return null==e?void 0:e}}set(t,e,n){return null!=t&&("object"==typeof t?this.mutate(t,e):this.mutate({[t]:e},n)),this}remove(t,e){const n=void 0,r={};let i;if("string"==typeof t)r[t]=n,i=e;else if(Array.isArray(t))t.forEach((t=>r[t]=n)),i=e;else{for(const t in this.data)r[t]=n;i=t}return this.mutate(r,Object.assign(Object.assign({},i),{unset:!0})),this}getByPath(t){return mf(this.data,t,"/")}setByPath(t,e,n={}){const r="/",i=Array.isArray(t)?[...t]:t.split(r),s=Array.isArray(t)?t.join(r):t,o=i[0],a=i.length;if(n.propertyPath=s,n.propertyValue=e,n.propertyPathArray=i,1===a)this.set(o,e,n);else{const s={};let l=s,c=o;for(let t=1;t<a;t+=1){const e=i[t],n=Number.isFinite(Number(e));l=l[c]=n?[]:{},c=e}yf(s,i,e,r);const h=xo(this.data);n.rewrite&&vf(h,t,r);const u=Sh(h,s);this.set(o,u[o],n)}return this}removeByPath(t,e){const n=Array.isArray(t)?t:t.split("/"),r=n[0];if(1===n.length)this.remove(r,e);else{const t=n.slice(1),i=xo(this.get(r));i&&vf(i,t),this.set(r,i,e)}return this}hasChanged(t){return null==t?Object.keys(this.changed).length>0:t in this.changed}getChanges(t){if(null==t)return this.hasChanged()?xo(this.changed):null;const e=this.changing?this.previous:this.data,n={};let r;for(const i in t){const s=t[i];Yc(e[i],s)||(n[i]=s,r=!0)}return r?xo(n):null}toJSON(){return xo(this.data)}clone(){return new(0,this.constructor)(this.data)}dispose(){this.off(),this.data={},this.previous={},this.changed={},this.pending=!1,this.changing=!1,this.pendingOptions=null,this.trigger("disposed",{store:this})}}n([xf.dispose()],Gw.prototype,"dispose",null);class Uw{constructor(t){this.cell=t,this.ids={},this.cache={}}get(){return Object.keys(this.ids)}start(t,e,n={},r="/"){const i=this.cell.getPropByPath(t),s=Ga(n,Uw.defaultOptions),o=this.getTiming(s.timing),a=this.getInterp(s.interp,i,e);let l=0;const c=Array.isArray(t)?t.join(r):t,h=Array.isArray(t)?t:t.split(r),u=()=>{const t=(new Date).getTime();0===l&&(l=t);let e=(t-l)/s.duration;e<1?this.ids[c]=requestAnimationFrame(u):e=1;const r=a(o(e));this.cell.setPropByPath(h,r),n.progress&&n.progress(Object.assign({progress:e,currentValue:r},this.getArgs(c))),1===e&&(this.cell.notify("transition:complete",this.getArgs(c)),n.complete&&n.complete(this.getArgs(c)),this.cell.notify("transition:finish",this.getArgs(c)),n.finish&&n.finish(this.getArgs(c)),this.clean(c))};return setTimeout((()=>{this.stop(t,void 0,r),this.cache[c]={startValue:i,targetValue:e,options:s},this.ids[c]=requestAnimationFrame(u),this.cell.notify("transition:start",this.getArgs(c)),n.start&&n.start(this.getArgs(c))}),n.delay),this.stop.bind(this,t,r,n)}stop(t,e={},n="/"){const r=Array.isArray(t)?t:t.split(n);return Object.keys(this.ids).filter((t=>Yc(r,t.split(n).slice(0,r.length)))).forEach((t=>{cancelAnimationFrame(this.ids[t]);const n=this.cache[t],r=this.getArgs(t),i=Object.assign(Object.assign({},n.options),e),s=i.jumpedToEnd;s&&null!=n.targetValue&&(this.cell.setPropByPath(t,n.targetValue),this.cell.notify("transition:end",Object.assign({},r)),this.cell.notify("transition:complete",Object.assign({},r)),i.complete&&i.complete(Object.assign({},r)));const o=Object.assign({jumpedToEnd:s},r);this.cell.notify("transition:stop",Object.assign({},o)),i.stop&&i.stop(Object.assign({},o)),this.cell.notify("transition:finish",Object.assign({},r)),i.finish&&i.finish(Object.assign({},r)),this.clean(t)})),this}clean(t){delete this.ids[t],delete this.cache[t]}getTiming(e){return"string"==typeof e?t.Timing[e]:e}getInterp(e,n,r){return e?e(n,r):"number"==typeof r?t.Interp.number(n,r):"string"==typeof r?"#"===r[0]?t.Interp.color(n,r):t.Interp.unit(n,r):t.Interp.object(n,r)}getArgs(t){const e=this.cache[t];return{path:t,startValue:e.startValue,targetValue:e.targetValue,cell:this.cell}}}!function(t){t.defaultOptions={delay:10,duration:100,timing:"linear"}}(Uw||(Uw={}));class qw extends xf{static config(t){const{markup:n,propHooks:r,attrHooks:i}=t,s=e(t,["markup","propHooks","attrHooks"]);null!=n&&(this.markup=n),r&&(this.propHooks=this.propHooks.slice(),Array.isArray(r)?this.propHooks.push(...r):"function"==typeof r?this.propHooks.push(r):Object.values(r).forEach((t=>{"function"==typeof t&&this.propHooks.push(t)}))),i&&(this.attrHooks=Object.assign(Object.assign({},this.attrHooks),i)),this.defaults=Sh({},this.defaults,s)}static getMarkup(){return this.markup}static getDefaults(t){return t?this.defaults:xo(this.defaults)}static getAttrHooks(){return this.attrHooks}static applyPropHooks(t,e){return this.propHooks.reduce(((e,n)=>n?ef(n,t,e):e),e)}get[Symbol.toStringTag](){return qw.toStringTag}constructor(t={}){super();const e=this.constructor.getDefaults(!0),n=Sh({},this.preprocess(e),this.preprocess(t));this.id=n.id||Nf(),this.store=new Gw(n),this.animation=new Uw(this),this.setup(),this.init(),this.postprocess(t)}init(){}get model(){return this._model}set model(t){this._model!==t&&(this._model=t)}preprocess(t,e){const n=t.id,r=this.constructor.applyPropHooks(this,t);return null==n&&!0!==e&&(r.id=Nf()),r}postprocess(t){}setup(){this.store.on("change:*",(t=>{const{key:e,current:n,previous:r,options:i}=t;this.notify("change:*",{key:e,options:i,current:n,previous:r,cell:this}),this.notify(`change:${e}`,{options:i,current:n,previous:r,cell:this});const s=e;"source"!==s&&"target"!==s||this.notify("change:terminal",{type:s,current:n,previous:r,options:i,cell:this})})),this.store.on("changed",(({options:t})=>this.notify("changed",{options:t,cell:this})))}notify(t,e){this.trigger(t,e);const n=this.model;return n&&(n.notify(`cell:${t}`,e),this.isNode()?n.notify(`node:${t}`,Object.assign(Object.assign({},e),{node:this})):this.isEdge()&&n.notify(`edge:${t}`,Object.assign(Object.assign({},e),{edge:this}))),this}isNode(){return!1}isEdge(){return!1}isSameStore(t){return this.store===t.store}get view(){return this.store.get("view")}get shape(){return this.store.get("shape","")}getProp(t,e){return null==t?this.store.get():this.store.get(t,e)}setProp(t,e,n){if("string"==typeof t)this.store.set(t,e,n);else{const n=this.preprocess(t,!0);this.store.set(Sh({},this.getProp(),n),e),this.postprocess(t)}return this}removeProp(t,e){return"string"==typeof t||Array.isArray(t)?this.store.removeByPath(t,e):this.store.remove(e),this}hasChanged(t){return null==t?this.store.hasChanged():this.store.hasChanged(t)}getPropByPath(t){return this.store.getByPath(t)}setPropByPath(t,e,n={}){return this.model&&("children"===t?this._children=e?e.map((t=>this.model.getCell(t))).filter((t=>null!=t)):null:"parent"===t&&(this._parent=e?this.model.getCell(e):null)),this.store.setByPath(t,e,n),this}removePropByPath(t,e={}){const n=Array.isArray(t)?t:t.split("/");return"attrs"===n[0]&&(e.dirty=!0),this.store.removeByPath(n,e),this}prop(t,e,n){return null==t?this.getProp():"string"==typeof t||Array.isArray(t)?1===arguments.length?this.getPropByPath(t):null==e?this.removePropByPath(t,n||{}):this.setPropByPath(t,e,n||{}):this.setProp(t,e||{})}previous(t){return this.store.getPrevious(t)}get zIndex(){return this.getZIndex()}set zIndex(t){null==t?this.removeZIndex():this.setZIndex(t)}getZIndex(){return this.store.get("zIndex")}setZIndex(t,e={}){return this.store.set("zIndex",t,e),this}removeZIndex(t={}){return this.store.remove("zIndex",t),this}toFront(t={}){const e=this.model;if(e){let n,r=e.getMaxZIndex();t.deep?(n=this.getDescendants({deep:!0,breadthFirst:!0}),n.unshift(this)):n=[this],r=r-n.length+1;const i=e.total();let s=e.indexOf(this)!==i-n.length;s||(s=n.some(((t,e)=>t.getZIndex()!==r+e))),s&&this.batchUpdate("to-front",(()=>{r+=n.length,n.forEach(((e,n)=>{e.setZIndex(r+n,t)}))}))}return this}toBack(t={}){const e=this.model;if(e){let n,r=e.getMinZIndex();t.deep?(n=this.getDescendants({deep:!0,breadthFirst:!0}),n.unshift(this)):n=[this];let i=0!==e.indexOf(this);i||(i=n.some(((t,e)=>t.getZIndex()!==r+e))),i&&this.batchUpdate("to-back",(()=>{r-=n.length,n.forEach(((e,n)=>{e.setZIndex(r+n,t)}))}))}return this}get markup(){return this.getMarkup()}set markup(t){null==t?this.removeMarkup():this.setMarkup(t)}getMarkup(){let t=this.store.get("markup");if(null==t){t=this.constructor.getMarkup()}return t}setMarkup(t,e={}){return this.store.set("markup",t,e),this}removeMarkup(t={}){return this.store.remove("markup",t),this}get attrs(){return this.getAttrs()}set attrs(t){null==t?this.removeAttrs():this.setAttrs(t)}getAttrs(){const t=this.store.get("attrs");return t?Object.assign({},t):{}}setAttrs(t,e={}){if(null==t)this.removeAttrs(e);else{const n=t=>this.store.set("attrs",t,e);if(!0===e.overwrite)n(t);else{const r=this.getAttrs();!1===e.deep?n(Object.assign(Object.assign({},r),t)):n(Sh({},r,t))}}return this}replaceAttrs(t,e={}){return this.setAttrs(t,Object.assign(Object.assign({},e),{overwrite:!0}))}updateAttrs(t,e={}){return this.setAttrs(t,Object.assign(Object.assign({},e),{deep:!1}))}removeAttrs(t={}){return this.store.remove("attrs",t),this}getAttrDefinition(t){if(!t)return null;const e=this.constructor.getAttrHooks()||{};let n=e[t]||Tb.registry.get(t);if(!n){const r=Ri(t);n=e[r]||Tb.registry.get(r)}return n||null}getAttrByPath(t){return null==t||""===t?this.getAttrs():this.getPropByPath(this.prefixAttrPath(t))}setAttrByPath(t,e,n={}){return this.setPropByPath(this.prefixAttrPath(t),e,n),this}removeAttrByPath(t,e={}){return this.removePropByPath(this.prefixAttrPath(t),e),this}prefixAttrPath(t){return Array.isArray(t)?["attrs"].concat(t):`attrs/${t}`}attr(t,e,n){return null==t?this.getAttrByPath():"string"==typeof t||Array.isArray(t)?1===arguments.length?this.getAttrByPath(t):null==e?this.removeAttrByPath(t,n||{}):this.setAttrByPath(t,e,n||{}):this.setAttrs(t,e||{})}get visible(){return this.isVisible()}set visible(t){this.setVisible(t)}setVisible(t,e={}){return this.store.set("visible",t,e),this}isVisible(){return!1!==this.store.get("visible")}show(t={}){return this.isVisible()||this.setVisible(!0,t),this}hide(t={}){return this.isVisible()&&this.setVisible(!1,t),this}toggleVisible(t,e={}){const n="boolean"==typeof t?e:t;return("boolean"==typeof t?t:!this.isVisible())?this.show(n):this.hide(n),this}get data(){return this.getData()}set data(t){this.setData(t)}getData(){return this.store.get("data")}setData(t,e={}){if(null==t)this.removeData(e);else{const n=t=>this.store.set("data",t,e);if(!0===e.overwrite)n(t);else{const r=this.getData();!1===e.deep?n("object"==typeof t?Object.assign(Object.assign({},r),t):t):n(Sh({},r,t))}}return this}replaceData(t,e={}){return this.setData(t,Object.assign(Object.assign({},e),{overwrite:!0}))}updateData(t,e={}){return this.setData(t,Object.assign(Object.assign({},e),{deep:!1}))}removeData(t={}){return this.store.remove("data",t),this}get parent(){return this.getParent()}get children(){return this.getChildren()}getParentId(){return this.store.get("parent")}getParent(){const t=this.getParentId();if(t&&this.model){const e=this.model.getCell(t);return this._parent=e,e}return null}getChildren(){const t=this.store.get("children");if(t&&t.length&&this.model){const e=t.map((t=>{var e;return null===(e=this.model)||void 0===e?void 0:e.getCell(t)})).filter((t=>null!=t));return this._children=e,[...e]}return null}hasParent(){return null!=this.parent}isParentOf(t){return null!=t&&t.getParent()===this}isChildOf(t){return null!=t&&this.getParent()===t}eachChild(t,e){return this.children&&this.children.forEach(t,e),this}filterChild(t,e){return this.children?this.children.filter(t,e):[]}getChildCount(){return null==this.children?0:this.children.length}getChildIndex(t){return null==this.children?-1:this.children.indexOf(t)}getChildAt(t){return null!=this.children&&t>=0?this.children[t]:null}getAncestors(t={}){const e=[];let n=this.getParent();for(;n;)e.push(n),n=!1!==t.deep?n.getParent():null;return e}getDescendants(t={}){if(!1!==t.deep){if(t.breadthFirst){const t=[],e=this.getChildren()||[];for(;e.length>0;){const n=e.shift(),r=n.getChildren();t.push(n),r&&e.push(...r)}return t}{const e=this.getChildren()||[];return e.forEach((n=>{e.push(...n.getDescendants(t))})),e}}return this.getChildren()||[]}isDescendantOf(t,e={}){if(null==t)return!1;if(!1!==e.deep){let e=this.getParent();for(;e;){if(e===t)return!0;e=e.getParent()}return!1}return this.isChildOf(t)}isAncestorOf(t,e={}){return null!=t&&t.isDescendantOf(this,e)}contains(t){return this.isAncestorOf(t)}getCommonAncestor(...t){return qw.getCommonAncestor(this,...t)}setParent(t,e={}){return this._parent=t,t?this.store.set("parent",t.id,e):this.store.remove("parent",e),this}setChildren(t,e={}){return this._children=t,null!=t?this.store.set("children",t.map((t=>t.id)),e):this.store.remove("children",e),this}unembed(t,e={}){const n=this.children;if(null!=n&&null!=t){const r=this.getChildIndex(t);-1!==r&&(n.splice(r,1),t.setParent(null,e),this.setChildren(n,e))}return this}embed(t,e={}){return t.addTo(this,e),this}addTo(t,e={}){return qw.isCell(t)?t.addChild(this,e):t.addCell(this,e),this}insertTo(t,e,n={}){return t.insertChild(this,e,n),this}addChild(t,e={}){return this.insertChild(t,void 0,e)}insertChild(t,e,n={}){if(null!=t&&t!==this){const r=t.getParent(),i=this!==r;let s=e;if(null==s&&(s=this.getChildCount(),i||(s-=1)),r){const e=r.getChildren();if(e){const i=e.indexOf(t);i>=0&&(t.setParent(null,n),e.splice(i,1),r.setChildren(e,n))}}let o=this.children;if(null==o?(o=[],o.push(t)):o.splice(s,0,t),t.setParent(this,n),this.setChildren(o,n),i&&this.model){const t=this.model.getIncomingEdges(this),e=this.model.getOutgoingEdges(this);t&&t.forEach((t=>t.updateParent(n))),e&&e.forEach((t=>t.updateParent(n)))}this.model&&this.model.addCell(t,n)}return this}removeFromParent(t={}){const e=this.getParent();if(null!=e){const n=e.getChildIndex(this);e.removeChildAt(n,t)}return this}removeChild(t,e={}){const n=this.getChildIndex(t);return this.removeChildAt(n,e)}removeChildAt(t,e={}){const n=this.getChildAt(t);return null!=this.children&&null!=n&&(this.unembed(n,e),n.remove(e)),n}remove(t={}){return this.batchUpdate("remove",(()=>{const e=this.getParent();e&&e.removeChild(this,t),!1!==t.deep&&this.eachChild((e=>e.remove(t))),this.model&&this.model.removeCell(this,t)})),this}transition(t,e,n={},r="/"){return this.animation.start(t,e,n,r)}stopTransition(t,e,n="/"){return this.animation.stop(t,e,n),this}getTransitions(){return this.animation.get()}translate(t,e,n){return this}scale(t,e,n,r){return this}addTools(t,e,n){const r=Array.isArray(t)?t:[t],i="string"==typeof e?e:null,s="object"==typeof e?e:"object"==typeof n?n:{};if(s.reset)return this.setTools({name:i,items:r,local:s.local},s);let o=xo(this.getTools());return null==o||null==i||o.name===i?(null==o&&(o={}),o.items||(o.items=[]),o.name=i,o.items=[...o.items,...r],this.setTools(Object.assign({},o),s)):void 0}setTools(t,e={}){return null==t?this.removeTools():this.store.set("tools",qw.normalizeTools(t),e),this}getTools(){return this.store.get("tools")}removeTools(t={}){return this.store.remove("tools",t),this}hasTools(t){const e=this.getTools();return null!=e&&(null==t||e.name===t)}hasTool(t){const e=this.getTools();return null!=e&&e.items.some((e=>"string"==typeof e?e===t:e.name===t))}removeTool(t,e={}){const n=xo(this.getTools());if(n){let r=!1;const i=n.items.slice(),s=t=>{i.splice(t,1),r=!0};if("number"==typeof t)s(t);else for(let e=i.length-1;e>=0;e-=1){const n=i[e];("string"==typeof n?n===t:n.name===t)&&s(e)}r&&(n.items=i,this.setTools(n,e))}return this}getBBox(t){return new ky}getConnectionPoint(t,e){return new Ty}toJSON(t={}){const e=Object.assign({},this.store.get()),n=Object.prototype.toString,r=this.isNode()?"node":this.isEdge()?"edge":"cell";if(!e.shape){const t=this.constructor;throw new Error(`Unable to serialize ${r} missing "shape" prop, check the ${r} "${t.name||n.call(t)}"`)}const i=this.constructor,s=!0===t.diff,o=e.attrs||{},a=i.getDefaults(!0),l=s?this.preprocess(a,!0):a,c=l.attrs||{},h={};Object.entries(e).forEach((([t,i])=>{if(null!=i&&!Array.isArray(i)&&"object"==typeof i&&!Tr(i))throw new Error(`Can only serialize ${r} with plain-object props, but got a "${n.call(i)}" type of key "${t}" on ${r} "${this.id}"`);if("attrs"!==t&&"shape"!==t&&s){Yc(i,l[t])&&delete e[t]}})),Object.keys(o).forEach((t=>{const e=o[t],n=c[t];Object.keys(e).forEach((r=>{const i=e[r],s=n?n[r]:null;null==i||"object"!=typeof i||Array.isArray(i)?null!=n&&Yc(s,i)||(null==h[t]&&(h[t]={}),h[t][r]=i):Object.keys(i).forEach((e=>{const o=i[e];if(null==n||null==s||!B(s)||!Yc(s[e],o)){null==h[t]&&(h[t]={}),null==h[t][r]&&(h[t][r]={});h[t][r][e]=o}}))}))}));const u=Object.assign(Object.assign({},e),{attrs:Xc(h)?void 0:h});null==u.attrs&&delete u.attrs;const g=u;return 0===g.angle&&delete g.angle,xo(g)}clone(t={}){if(!t.deep){const e=Object.assign({},this.store.get());t.keepId||delete e.id,delete e.parent,delete e.children;return new(0,this.constructor)(e)}return qw.deepClone(this)[this.id]}findView(t){return t.findViewByCell(this)}startBatch(t,e={},n=this.model){return this.notify("batch:start",{name:t,data:e,cell:this}),n&&n.startBatch(t,Object.assign(Object.assign({},e),{cell:this})),this}stopBatch(t,e={},n=this.model){return n&&n.stopBatch(t,Object.assign(Object.assign({},e),{cell:this})),this.notify("batch:stop",{name:t,data:e,cell:this}),this}batchUpdate(t,e,n){const r=this.model;this.startBatch(t,n,r);const i=e();return this.stopBatch(t,n,r),i}dispose(){this.removeFromParent(),this.store.dispose()}}qw.defaults={},qw.attrHooks={},qw.propHooks=[],n([xf.dispose()],qw.prototype,"dispose",null),function(t){t.normalizeTools=function(t){return"string"==typeof t?{items:[t]}:Array.isArray(t)?{items:t}:t.items?t:{items:[t]}}}(qw||(qw={})),function(t){t.toStringTag=`X6.${t.name}`,t.isCell=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"==typeof r.isNode&&"function"==typeof r.isEdge&&"function"==typeof r.prop&&"function"==typeof r.attr}}(qw||(qw={})),function(t){t.getCommonAncestor=function(...t){const e=t.filter((t=>null!=t)).map((t=>t.getAncestors())).sort(((t,e)=>t.length-e.length));return e.shift().find((t=>e.every((e=>e.includes(t)))))||null},t.getCellsBBox=function(t,e={}){let n=null;for(let r=0,i=t.length;r<i;r+=1){const i=t[r];let s=i.getBBox(e);if(s){if(i.isNode()){const t=i.getAngle();null!=t&&0!==t&&(s=s.bbox(t))}n=null==n?s:n.union(s)}}return n},t.deepClone=function(e){const n=[e,...e.getDescendants({deep:!0})];return t.cloneCells(n)},t.cloneCells=function(t){const e=ud(t),n=e.reduce(((t,e)=>(t[e.id]=e.clone(),t)),{});return e.forEach((t=>{const e=n[t.id];if(e.isEdge()){const t=e.getSourceCellId(),r=e.getTargetCellId();t&&n[t]&&e.setSource(Object.assign(Object.assign({},e.getSource()),{cell:n[t].id})),r&&n[r]&&e.setTarget(Object.assign(Object.assign({},e.getTarget()),{cell:n[r].id}))}const r=t.getParent();r&&n[r.id]&&e.setParent(n[r.id]);const i=t.getChildren();if(i&&i.length){const t=i.reduce(((t,e)=>(n[e.id]&&t.push(n[e.id]),t)),[]);t.length>0&&e.setChildren(t)}})),n}}(qw||(qw={})),function(t){t.config({propHooks(n){var{tools:r}=n,i=e(n,["tools"]);return r&&(i.tools=t.normalizeTools(r)),i}})}(qw||(qw={})),function(t){let e,n;t.exist=function(t,r){return r?null!=e&&e.exist(t):null!=n&&n.exist(t)},t.setEdgeRegistry=function(t){e=t},t.setNodeRegistry=function(t){n=t}}($w||($w={}));class Ww{constructor(t){this.ports=[],this.groups={},this.init(xo(t))}getPorts(){return this.ports}getGroup(t){return null!=t?this.groups[t]:null}getPortsByGroup(t){return this.ports.filter((e=>e.group===t||null==e.group&&null==t))}getPortsLayoutByGroup(t,e){const n=this.getPortsByGroup(t),r=t?this.getGroup(t):null,i=r?r.position:null,s=i?i.name:null;let o;if(null!=s){const t=qb.registry.get(s);if(null==t)return qb.registry.onNotFound(s);o=t}else o=qb.presets.left;const a=n.map((t=>t&&t.position&&t.position.args||{})),l=i&&i.args||{};return o(a,e,l).map(((t,r)=>{const i=n[r];return{portLayout:t,portId:i.id,portSize:i.size,portAttrs:i.attrs,labelSize:i.label.size,labelLayout:this.getPortLabelLayout(i,Ty.create(t.position),e)}}))}init(t){const{groups:e,items:n}=t;null!=e&&Object.keys(e).forEach((t=>{this.groups[t]=this.parseGroup(e[t])})),Array.isArray(n)&&n.forEach((t=>{this.ports.push(this.parsePort(t))}))}parseGroup(t){return Object.assign(Object.assign({},t),{label:this.getLabel(t,!0),position:this.getPortPosition(t.position,!0)})}parsePort(t){const e=Object.assign({},t),n=this.getGroup(t.group)||{};return e.markup=e.markup||n.markup,e.attrs=Sh({},n.attrs,e.attrs),e.position=this.createPosition(n,e),e.label=Sh({},n.label,this.getLabel(e)),e.zIndex=this.getZIndex(n,e),e.size=Object.assign(Object.assign({},n.size),e.size),e}getZIndex(t,e){return"number"==typeof e.zIndex?e.zIndex:"number"==typeof t.zIndex||"auto"===t.zIndex?t.zIndex:"auto"}createPosition(t,e){return Sh({name:"left",args:{}},t.position,{args:e.args})}getPortPosition(t,e=!1){if(null==t){if(e)return{name:"left",args:{}}}else{if("string"==typeof t)return{name:t,args:{}};if(Array.isArray(t))return{name:"absolute",args:{x:t[0],y:t[1]}};if("object"==typeof t)return t}return{args:{}}}getPortLabelPosition(t,e=!1){if(null==t){if(e)return{name:"left",args:{}}}else{if("string"==typeof t)return{name:t,args:{}};if("object"==typeof t)return t}return{args:{}}}getLabel(t,e=!1){const n=t.label||{};return n.position=this.getPortLabelPosition(n.position,e),n}getPortLabelLayout(t,e,n){const r=t.label.position.name||"left",i=t.label.position.args||{},s=Qb.registry.get(r)||Qb.presets.left;return s?s(e,n,i):null}}let Hw=class n extends qw{get[Symbol.toStringTag](){return n.toStringTag}constructor(t={}){super(t),this.initPorts()}preprocess(t,n){const{x:r,y:i,width:s,height:o}=t,a=e(t,["x","y","width","height"]);if(null!=r||null!=i){const t=a.position;a.position=Object.assign(Object.assign({},t),{x:null!=r?r:t?t.x:0,y:null!=i?i:t?t.y:0})}if(null!=s||null!=o){const t=a.size;a.size=Object.assign(Object.assign({},t),{width:null!=s?s:t?t.width:0,height:null!=o?o:t?t.height:0})}return super.preprocess(a,n)}isNode(){return!0}size(t,e,n){return void 0===t?this.getSize():"number"==typeof t?this.setSize(t,e,n):this.setSize(t,e)}getSize(){const t=this.store.get("size");return t?Object.assign({},t):{width:1,height:1}}setSize(t,e,n){return"object"==typeof t?this.resize(t.width,t.height,e):this.resize(t,e,n),this}resize(e,n,r={}){this.startBatch("resize",r);const i=r.direction;if(i){const s=this.getSize();switch(i){case"left":case"right":n=s.height;break;case"top":case"bottom":e=s.width}let o={right:0,"top-right":0,top:1,"top-left":1,left:2,"bottom-left":2,bottom:3,"bottom-right":3}[i];const a=t.Angle.normalize(this.getAngle()||0);r.absolute&&(o+=Math.floor((a+45)/90),o%=4);const l=this.getBBox();let c;c=0===o?l.getBottomLeft():1===o?l.getCorner():2===o?l.getTopRight():l.getOrigin();const h=c.clone().rotate(-a,l.getCenter()),u=Math.sqrt(e*e+n*n)/2;let g=o*Math.PI/2;g+=Math.atan(o%2==0?n/e:e/n),g-=t.Angle.toRad(a);const d=Ty.fromPolar(u,g,h).clone().translate(e/-2,n/-2);this.store.set("size",{width:e,height:n},r),this.setPosition(d.x,d.y,r)}else this.store.set("size",{width:e,height:n},r);return this.stopBatch("resize",r),this}scale(t,e,n,r={}){const i=this.getBBox().scale(t,e,null==n?void 0:n);return this.startBatch("scale",r),this.setPosition(i.x,i.y,r),this.resize(i.width,i.height,r),this.stopBatch("scale"),this}position(t,e,n){return"number"==typeof t?this.setPosition(t,e,n):this.getPosition(t)}getPosition(t={}){if(t.relative){const t=this.getParent();if(null!=t&&t.isNode()){const e=this.getPosition(),n=t.getPosition();return{x:e.x-n.x,y:e.y-n.y}}}const e=this.store.get("position");return e?Object.assign({},e):{x:0,y:0}}setPosition(t,e,n={}){let r,i,s;if("object"==typeof t?(r=t.x,i=t.y,s=e||{}):(r=t,i=e,s=n||{}),s.relative){const t=this.getParent();if(null!=t&&t.isNode()){const e=t.getPosition();r+=e.x,i+=e.y}}if(s.deep){const t=this.getPosition();this.translate(r-t.x,i-t.y,s)}else this.store.set("position",{x:r,y:i},s);return this}translate(e=0,n=0,r={}){if(0===e&&0===n)return this;r.translateBy=r.translateBy||this.id;const i=this.getPosition();if(null!=r.restrict&&r.translateBy===this.id){const t=this.getBBox({deep:!0}),s=r.restrict,o=i.x-t.x,a=i.y-t.y,l=Math.max(s.x+o,Math.min(s.x+s.width+o-t.width,i.x+e)),c=Math.max(s.y+a,Math.min(s.y+s.height+a-t.height,i.y+n));e=l-i.x,n=c-i.y}const s={x:i.x+e,y:i.y+n};return r.tx=e,r.ty=n,r.transition?("object"!=typeof r.transition&&(r.transition={}),this.transition("position",s,Object.assign(Object.assign({},r.transition),{interp:t.Interp.object})),this.eachChild((t=>{var i;(null===(i=r.exclude)||void 0===i?void 0:i.includes(t))||t.translate(e,n,r)}))):(this.startBatch("translate",r),this.store.set("position",s,r),this.eachChild((t=>{var i;(null===(i=r.exclude)||void 0===i?void 0:i.includes(t))||t.translate(e,n,r)})),this.stopBatch("translate",r)),this}angle(t,e){return null==t?this.getAngle():this.rotate(t,e)}getAngle(){return this.store.get("angle",0)}rotate(t,e={}){const n=this.getAngle();if(e.center){const r=this.getSize(),i=this.getPosition(),s=this.getBBox().getCenter();s.rotate(n-t,e.center);const o=s.x-r.width/2-i.x,a=s.y-r.height/2-i.y;this.startBatch("rotate",{angle:t,options:e}),this.setPosition(i.x+o,i.y+a,e),this.rotate(t,Object.assign(Object.assign({},e),{center:null})),this.stopBatch("rotate")}else this.store.set("angle",e.absolute?t:(n+t)%360,e);return this}getBBox(t={}){if(t.deep){const t=this.getDescendants({deep:!0,breadthFirst:!0});return t.push(this),qw.getCellsBBox(t)}return ky.fromPositionAndSize(this.getPosition(),this.getSize())}getConnectionPoint(t,e){const n=this.getBBox(),r=n.getCenter(),i=t.getTerminal(e);if(null==i)return r;const s=i.port;if(!s||!this.hasPort(s))return r;const o=this.getPort(s);if(!o||!o.group)return r;const a=this.getPortsPosition(o.group)[s].position,l=Ty.create(a).translate(n.getOrigin()),c=this.getAngle();return c&&l.rotate(-c,r),l}fit(t={}){const e=(this.getChildren()||[]).filter((t=>t.isNode()));if(0===e.length)return this;this.startBatch("fit-embeds",t),t.deep&&e.forEach((e=>e.fit(t)));let{x:n,y:r,width:i,height:s}=qw.getCellsBBox(e);const o=Df(t.padding);return n-=o.left,r-=o.top,i+=o.left+o.right,s+=o.bottom+o.top,this.store.set({position:{x:n,y:r},size:{width:i,height:s}},t),this.stopBatch("fit-embeds"),this}get portContainerMarkup(){return this.getPortContainerMarkup()}set portContainerMarkup(t){this.setPortContainerMarkup(t)}getDefaultPortContainerMarkup(){return this.store.get("defaultPortContainerMarkup")||t.Markup.getPortContainerMarkup()}getPortContainerMarkup(){return this.store.get("portContainerMarkup")||this.getDefaultPortContainerMarkup()}setPortContainerMarkup(e,n={}){return this.store.set("portContainerMarkup",t.Markup.clone(e),n),this}get portMarkup(){return this.getPortMarkup()}set portMarkup(t){this.setPortMarkup(t)}getDefaultPortMarkup(){return this.store.get("defaultPortMarkup")||t.Markup.getPortMarkup()}getPortMarkup(){return this.store.get("portMarkup")||this.getDefaultPortMarkup()}setPortMarkup(e,n={}){return this.store.set("portMarkup",t.Markup.clone(e),n),this}get portLabelMarkup(){return this.getPortLabelMarkup()}set portLabelMarkup(t){this.setPortLabelMarkup(t)}getDefaultPortLabelMarkup(){return this.store.get("defaultPortLabelMarkup")||t.Markup.getPortLabelMarkup()}getPortLabelMarkup(){return this.store.get("portLabelMarkup")||this.getDefaultPortLabelMarkup()}setPortLabelMarkup(e,n={}){return this.store.set("portLabelMarkup",t.Markup.clone(e),n),this}get ports(){const t=this.store.get("ports",{items:[]});return null==t.items&&(t.items=[]),t}getPorts(){return xo(this.ports.items)}getPortsByGroup(t){return this.getPorts().filter((e=>e.group===t))}getPort(t){return xo(this.ports.items.find((e=>e.id&&e.id===t)))}getPortAt(t){return this.ports.items[t]||null}hasPorts(){return this.ports.items.length>0}hasPort(t){return-1!==this.getPortIndex(t)}getPortIndex(t){const e="string"==typeof t?t:t.id;return null!=e?this.ports.items.findIndex((t=>t.id===e)):-1}getPortsPosition(t){const e=this.getSize();return this.port.getPortsLayoutByGroup(t,new ky(0,0,e.width,e.height)).reduce(((t,e)=>{const n=e.portLayout;return t[e.portId]={position:Object.assign({},n.position),angle:n.angle||0},t}),{})}getPortProp(t,e){return this.getPropByPath(this.prefixPortPath(t,e))}setPortProp(t,e,n,r){if("string"==typeof e||Array.isArray(e)){const i=this.prefixPortPath(t,e),s=n;return this.setPropByPath(i,s,r)}const i=this.prefixPortPath(t),s=e;return this.setPropByPath(i,s,n)}removePortProp(t,e,n){return"string"==typeof e||Array.isArray(e)?this.removePropByPath(this.prefixPortPath(t,e),n):this.removePropByPath(this.prefixPortPath(t),e)}portProp(t,e,n,r){return null==e?this.getPortProp(t):"string"==typeof e||Array.isArray(e)?2===arguments.length?this.getPortProp(t,e):null==n?this.removePortProp(t,e,r):this.setPortProp(t,e,n,r):this.setPortProp(t,e,n)}prefixPortPath(t,e){const n=this.getPortIndex(t);if(-1===n)throw new Error(`Unable to find port with id: "${t}"`);return null==e||""===e?["ports","items",`${n}`]:Array.isArray(e)?["ports","items",`${n}`,...e]:`ports/items/${n}/${e}`}addPort(t,e){const n=[...this.ports.items];return n.push(t),this.setPropByPath("ports/items",n,e),this}addPorts(t,e){return this.setPropByPath("ports/items",[...this.ports.items,...t],e),this}insertPort(t,e,n){const r=[...this.ports.items];return r.splice(t,0,e),this.setPropByPath("ports/items",r,n),this}removePort(t,e={}){return this.removePortAt(this.getPortIndex(t),e)}removePortAt(t,e={}){if(t>=0){const n=[...this.ports.items];n.splice(t,1),e.rewrite=!0,this.setPropByPath("ports/items",n,e)}return this}removePorts(t,e){let n;if(Array.isArray(t)){if(n=e||{},t.length){n.rewrite=!0;const e=[...this.ports.items].filter((e=>!t.some((t=>{const n="string"==typeof t?t:t.id;return e.id===n}))));this.setPropByPath("ports/items",e,n)}}else n=t||{},n.rewrite=!0,this.setPropByPath("ports/items",[],n);return this}getParsedPorts(){return this.port.getPorts()}getParsedGroups(){return this.port.groups}getPortsLayoutByGroup(t,e){return this.port.getPortsLayoutByGroup(t,e)}initPorts(){this.updatePortData(),this.on("change:ports",(()=>{this.processRemovedPort(),this.updatePortData()}))}processRemovedPort(){const t=this.ports,e={};t.items.forEach((t=>{t.id&&(e[t.id]=!0)}));const n={};(this.store.getPrevious("ports")||{items:[]}).items.forEach((t=>{t.id&&!e[t.id]&&(n[t.id]=!0)}));const r=this.model;if(r&&!Xc(n)){r.getConnectedEdges(this,{incoming:!0}).forEach((t=>{const e=t.getTargetPortId();e&&n[e]&&t.remove()}));r.getConnectedEdges(this,{outgoing:!0}).forEach((t=>{const e=t.getSourcePortId();e&&n[e]&&t.remove()}))}}validatePorts(){const t={},e=[];return this.ports.items.forEach((n=>{"object"!=typeof n&&e.push(`Invalid port ${n}.`),null==n.id&&(n.id=this.generatePortId()),t[n.id]&&e.push("Duplicitied port id."),t[n.id]=!0})),e}generatePortId(){return Nf()}updatePortData(){const t=this.validatePorts();if(t.length>0)throw this.store.set("ports",this.store.getPrevious("ports")),new Error(t.join(" "));const e=this.port?this.port.getPorts():null;this.port=new Ww(this.ports);const n=this.port.getPorts(),r=e?n.filter((t=>e.find((e=>e.id===t.id))?null:t)):[...n],i=e?e.filter((t=>n.find((e=>e.id===t.id))?null:t)):[];r.length>0&&this.notify("ports:added",{added:r,cell:this,node:this}),i.length>0&&this.notify("ports:removed",{removed:i,cell:this,node:this})}};Hw.defaults={angle:0,position:{x:0,y:0},size:{width:1,height:1}},function(t){t.toStringTag=`X6.${t.name}`,t.isNode=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"==typeof r.isNode&&"function"==typeof r.isEdge&&"function"==typeof r.prop&&"function"==typeof r.attr&&"function"==typeof r.size&&"function"==typeof r.position}}(Hw||(Hw={})),function(t){t.config({propHooks(t){var{ports:n}=t,r=e(t,["ports"]);return n&&(r.ports=Array.isArray(n)?{items:n}:n),r}})}(Hw||(Hw={})),function(t){t.registry=Qy.create({type:"node",process(n,r){if($w.exist(n,!0))throw new Error(`Node with name '${n}' was registered by anthor Edge`);if("function"==typeof r)return r.config({shape:n}),r;let i=t;const{inherit:s}=r,o=e(r,["inherit"]);if(s)if("string"==typeof s){const t=this.get(s);null==t?this.onNotFound(s,"inherited"):i=t}else i=s;null==o.constructorName&&(o.constructorName=n);const a=i.define.call(i,o);return a.config({shape:n}),a}}),$w.setNodeRegistry(t.registry)}(Hw||(Hw={})),function(t){let n=0;t.define=function(r){const{constructorName:i,overwrite:s}=r,o=e(r,["constructorName","overwrite"]),a=df((l=i||o.shape)?Cf(l):(n+=1,`CustomNode${n}`),this);var l;return a.config(o),o.shape&&t.registry.register(o.shape,a,s),a},t.create=function(e){const n=e.shape||"rect",r=t.registry.get(n);return r?new r(e):t.registry.onNotFound(n)}}(Hw||(Hw={}));let Jw=class n extends qw{get[Symbol.toStringTag](){return n.toStringTag}constructor(t={}){super(t)}preprocess(t,n){const{source:r,sourceCell:i,sourcePort:s,sourcePoint:o,target:a,targetCell:l,targetPort:c,targetPoint:h}=t,u=e(t,["source","sourceCell","sourcePort","sourcePoint","target","targetCell","targetPort","targetPoint"]),g=t=>"string"==typeof t||"number"==typeof t;if(null!=r)if(qw.isCell(r))u.source={cell:r.id};else if(g(r))u.source={cell:r};else if(Ty.isPoint(r))u.source=r.toJSON();else if(Array.isArray(r))u.source={x:r[0],y:r[1]};else{const t=r.cell;qw.isCell(t)?u.source=Object.assign(Object.assign({},r),{cell:t.id}):u.source=r}if(null!=i||null!=s){let t=u.source;if(null!=i){const e=g(i)?i:i.id;t?t.cell=e:t=u.source={cell:e}}null!=s&&t&&(t.port=s)}else null!=o&&(u.source=Ty.create(o).toJSON());if(null!=a)if(qw.isCell(a))u.target={cell:a.id};else if(g(a))u.target={cell:a};else if(Ty.isPoint(a))u.target=a.toJSON();else if(Array.isArray(a))u.target={x:a[0],y:a[1]};else{const t=a.cell;qw.isCell(t)?u.target=Object.assign(Object.assign({},a),{cell:t.id}):u.target=a}if(null!=l||null!=c){let t=u.target;if(null!=l){const e=g(l)?l:l.id;t?t.cell=e:t=u.target={cell:e}}null!=c&&t&&(t.port=c)}else null!=h&&(u.target=Ty.create(h).toJSON());return super.preprocess(u,n)}setup(){super.setup(),this.on("change:labels",(t=>this.onLabelsChanged(t))),this.on("change:vertices",(t=>this.onVertexsChanged(t)))}isEdge(){return!0}disconnect(t={}){return this.store.set({source:{x:0,y:0},target:{x:0,y:0}},t),this}get source(){return this.getSource()}set source(t){this.setSource(t)}getSource(){return this.getTerminal("source")}getSourceCellId(){return this.source.cell}getSourcePortId(){return this.source.port}setSource(t,e,n={}){return this.setTerminal("source",t,e,n)}get target(){return this.getTarget()}set target(t){this.setTarget(t)}getTarget(){return this.getTerminal("target")}getTargetCellId(){return this.target.cell}getTargetPortId(){return this.target.port}setTarget(t,e,n={}){return this.setTerminal("target",t,e,n)}getTerminal(t){return Object.assign({},this.store.get(t))}setTerminal(t,e,n,r={}){if(qw.isCell(e))return this.store.set(t,Sh({},n,{cell:e.id}),r),this;const i=e;return Ty.isPoint(e)||null!=i.x&&null!=i.y?(this.store.set(t,Sh({},n,{x:i.x,y:i.y}),r),this):(this.store.set(t,xo(e),r),this)}getSourcePoint(){return this.getTerminalPoint("source")}getTargetPoint(){return this.getTerminalPoint("target")}getTerminalPoint(t){const e=this[t];if(Ty.isPointLike(e))return Ty.create(e);const n=this.getTerminalCell(t);return n?n.getConnectionPoint(this,t):new Ty}getSourceCell(){return this.getTerminalCell("source")}getTargetCell(){return this.getTerminalCell("target")}getTerminalCell(t){if(this.model){const e="source"===t?this.getSourceCellId():this.getTargetCellId();if(e)return this.model.getCell(e)}return null}getSourceNode(){return this.getTerminalNode("source")}getTargetNode(){return this.getTerminalNode("target")}getTerminalNode(t){let e=this;const n={};for(;e&&e.isEdge();){if(n[e.id])return null;n[e.id]=!0,e=e.getTerminalCell(t)}return e&&e.isNode()?e:null}get router(){return this.getRouter()}set router(t){null==t?this.removeRouter():this.setRouter(t)}getRouter(){return this.store.get("router")}setRouter(t,e,n){return"object"==typeof t?this.store.set("router",t,e):this.store.set("router",{name:t,args:e},n),this}removeRouter(t={}){return this.store.remove("router",t),this}get connector(){return this.getConnector()}set connector(t){null==t?this.removeConnector():this.setConnector(t)}getConnector(){return this.store.get("connector")}setConnector(t,e,n){return"object"==typeof t?this.store.set("connector",t,e):this.store.set("connector",{name:t,args:e},n),this}removeConnector(t={}){return this.store.remove("connector",t)}getDefaultLabel(){const t=this.constructor;return xo(this.store.get("defaultLabel")||t.defaultLabel||{})}get labels(){return this.getLabels()}set labels(t){this.setLabels(t)}getLabels(){return[...this.store.get("labels",[])].map((t=>this.parseLabel(t)))}setLabels(t,e={}){return this.store.set("labels",Array.isArray(t)?t:[t],e),this}insertLabel(t,e,n={}){const r=this.getLabels(),i=r.length;let s=null!=e&&Number.isFinite(e)?e:i;return s<0&&(s=i+s+1),r.splice(s,0,this.parseLabel(t)),this.setLabels(r,n)}appendLabel(t,e={}){return this.insertLabel(t,-1,e)}getLabelAt(t){const e=this.getLabels();return null!=t&&Number.isFinite(t)?this.parseLabel(e[t]):null}setLabelAt(t,e,n={}){if(null!=t&&Number.isFinite(t)){const r=this.getLabels();r[t]=this.parseLabel(e),this.setLabels(r,n)}return this}removeLabelAt(t,e={}){const n=this.getLabels(),r=null!=t&&Number.isFinite(t)?t:-1,i=n.splice(r,1);return this.setLabels(n,e),i.length?i[0]:null}parseLabel(t){if("string"==typeof t){return this.constructor.parseStringLabel(t)}return t}onLabelsChanged({previous:t,current:e}){const n=t&&e?e.filter((e=>t.find((t=>e===t||Yc(e,t)))?null:e)):e?[...e]:[],r=t&&e?t.filter((t=>e.find((e=>t===e||Yc(t,e)))?null:t)):t?[...t]:[];n.length>0&&this.notify("labels:added",{added:n,cell:this,edge:this}),r.length>0&&this.notify("labels:removed",{removed:r,cell:this,edge:this})}get vertices(){return this.getVertices()}set vertices(t){this.setVertices(t)}getVertices(){return[...this.store.get("vertices",[])]}setVertices(t,e={}){const n=Array.isArray(t)?t:[t];return this.store.set("vertices",n.map((t=>Ty.toJSON(t))),e),this}insertVertex(t,e,n={}){const r=this.getVertices(),i=r.length;let s=null!=e&&Number.isFinite(e)?e:i;return s<0&&(s=i+s+1),r.splice(s,0,Ty.toJSON(t)),this.setVertices(r,n)}appendVertex(t,e={}){return this.insertVertex(t,-1,e)}getVertexAt(t){if(null!=t&&Number.isFinite(t)){return this.getVertices()[t]}return null}setVertexAt(t,e,n={}){if(null!=t&&Number.isFinite(t)){const r=this.getVertices();r[t]=e,this.setVertices(r,n)}return this}removeVertexAt(t,e={}){const n=this.getVertices(),r=null!=t&&Number.isFinite(t)?t:-1;return n.splice(r,1),this.setVertices(n,e)}onVertexsChanged({previous:t,current:e}){const n=t&&e?e.filter((e=>t.find((t=>Ty.equals(e,t)))?null:e)):e?[...e]:[],r=t&&e?t.filter((t=>e.find((e=>Ty.equals(t,e)))?null:t)):t?[...t]:[];n.length>0&&this.notify("vertexs:added",{added:n,cell:this,edge:this}),r.length>0&&this.notify("vertexs:removed",{removed:r,cell:this,edge:this})}getDefaultMarkup(){return this.store.get("defaultMarkup")||t.Markup.getEdgeMarkup()}getMarkup(){return super.getMarkup()||this.getDefaultMarkup()}translate(t,e,n={}){return n.translateBy=n.translateBy||this.id,n.tx=t,n.ty=e,this.applyToPoints((n=>({x:(n.x||0)+t,y:(n.y||0)+e})),n)}scale(t,e,n,r={}){return this.applyToPoints((r=>Ty.create(r).scale(t,e,n).toJSON()),r)}applyToPoints(t,e={}){const n={},r=this.getSource(),i=this.getTarget();Ty.isPointLike(r)&&(n.source=t(r)),Ty.isPointLike(i)&&(n.target=t(i));const s=this.getVertices();return s.length>0&&(n.vertices=s.map(t)),this.store.set(n,e),this}getBBox(){return this.getPolyline().bbox()}getConnectionPoint(){return this.getPolyline().pointAt(.5)}getPolyline(){const t=[this.getSourcePoint(),...this.getVertices().map((t=>Ty.create(t))),this.getTargetPoint()];return new Vy(t)}updateParent(t){let e=null;const n=this.getSourceCell(),r=this.getTargetCell(),i=this.getParent();return n&&r&&(e=n===r||n.isDescendantOf(r)?r:r.isDescendantOf(n)?n:qw.getCommonAncestor(n,r)),!i||e&&e.id===i.id||i.unembed(this,t),!e||i&&i.id===e.id||e.embed(this,t),e}hasLoop(t={}){const e=this.getSource(),n=this.getTarget(),r=e.cell,i=n.cell;if(!r||!i)return!1;let s=r===i;if(!s&&t.deep&&this._model){const e=this.getSourceCell(),n=this.getTargetCell();e&&n&&(s=e.isAncestorOf(n,t)||n.isAncestorOf(e,t))}return s}getFragmentAncestor(){const t=[this,this.getSourceNode(),this.getTargetNode()].filter((t=>null!=t));return this.getCommonAncestor(...t)}isFragmentDescendantOf(t){const e=this.getFragmentAncestor();return!!e&&(e.id===t.id||e.isDescendantOf(t))}};Jw.defaults={},function(t){t.equalTerminals=function(t,e){const n=t,r=e;return n.cell===r.cell&&(n.port===r.port||null==n.port&&null==r.port)}}(Jw||(Jw={})),function(t){t.defaultLabel={markup:[{tagName:"rect",selector:"body"},{tagName:"text",selector:"label"}],attrs:{text:{fill:"#000",fontSize:14,textAnchor:"middle",textVerticalAnchor:"middle",pointerEvents:"none"},rect:{ref:"label",fill:"#fff",rx:3,ry:3,refWidth:1,refHeight:1,refX:0,refY:0}},position:{distance:.5}},t.parseStringLabel=function(t){return{attrs:{label:{text:t}}}}}(Jw||(Jw={})),function(t){t.toStringTag=`X6.${t.name}`,t.isEdge=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"==typeof r.isNode&&"function"==typeof r.isEdge&&"function"==typeof r.prop&&"function"==typeof r.attr&&"function"==typeof r.disconnect&&"function"==typeof r.getSource&&"function"==typeof r.getTarget}}(Jw||(Jw={})),function(t){t.registry=Qy.create({type:"edge",process(n,r){if($w.exist(n,!1))throw new Error(`Edge with name '${n}' was registered by anthor Node`);if("function"==typeof r)return r.config({shape:n}),r;let i=t;const{inherit:s="edge"}=r,o=e(r,["inherit"]);if("string"==typeof s){const t=this.get(s||"edge");null==t&&s?this.onNotFound(s,"inherited"):i=t}else i=s;null==o.constructorName&&(o.constructorName=n);const a=i.define.call(i,o);return a.config({shape:n}),a}}),$w.setEdgeRegistry(t.registry)}(Jw||(Jw={})),function(t){let n=0;t.define=function(r){const{constructorName:i,overwrite:s}=r,o=e(r,["constructorName","overwrite"]),a=df((l=i||o.shape)?Cf(l):(n+=1,`CustomEdge${n}`),this);var l;return a.config(o),o.shape&&t.registry.register(o.shape,a,s),a},t.create=function(e){const n=e.shape||"edge",r=t.registry.get(n);return r?new r(e):t.registry.onNotFound(n)}}(Jw||(Jw={})),function(t){const n="basic.edge";t.config({shape:n,propHooks(n){const{label:r,vertices:i}=n,s=e(n,["label","vertices"]);if(r){null==s.labels&&(s.labels=[]);const e="string"==typeof r?t.parseStringLabel(r):r;s.labels.push(e)}return i&&Array.isArray(i)&&(s.vertices=i.map((t=>Ty.create(t).toJSON()))),s}}),t.registry.register(n,t)}(Jw||(Jw={}));class Xw extends xf{constructor(t,e={}){super(),this.length=0,this.comparator=e.comparator||"zIndex",this.clean(),t&&this.reset(t,{silent:!0})}toJSON(){return this.cells.map((t=>t.toJSON()))}add(t,e,n){let r,i;"number"==typeof e?(r=e,i=Object.assign({merge:!1},n)):(r=this.length,i=Object.assign({merge:!1},e)),r>this.length&&(r=this.length),r<0&&(r+=this.length+1);const s=Array.isArray(t)?t:[t],o=this.comparator&&"number"!=typeof e&&!1!==i.sort,a=this.comparator||null;let l=!1;const c=[],h=[];return s.forEach((t=>{const e=this.get(t);e?i.merge&&!t.isSameStore(e)&&(e.setProp(t.getProp(),n),h.push(e),o&&!l&&(l=null==a||"function"==typeof a?e.hasChanged():"string"==typeof a?e.hasChanged(a):a.some((t=>e.hasChanged(t))))):(c.push(t),this.reference(t))})),c.length&&(o&&(l=!0),this.cells.splice(r,0,...c),this.length=this.cells.length),l&&this.sort({silent:!0}),i.silent||(c.forEach(((t,e)=>{const n={cell:t,index:r+e,options:i};this.trigger("added",n),i.dryrun||t.notify("added",Object.assign({},n))})),l&&this.trigger("sorted"),(c.length||h.length)&&this.trigger("updated",{added:c,merged:h,removed:[],options:i})),this}remove(t,e={}){const n=Array.isArray(t)?t:[t],r=this.removeCells(n,e);return!e.silent&&r.length>0&&this.trigger("updated",{options:e,removed:r,added:[],merged:[]}),Array.isArray(t)?r:r[0]}removeCells(t,e){const n=[];for(let r=0;r<t.length;r+=1){const i=this.get(t[r]);if(null==i)continue;const s=this.cells.indexOf(i);this.cells.splice(s,1),this.length-=1,delete this.map[i.id],n.push(i),this.unreference(i),e.dryrun||i.remove(),e.silent||(this.trigger("removed",{cell:i,index:s,options:e}),e.dryrun||i.notify("removed",{cell:i,index:s,options:e}))}return n}reset(t,e={}){const n=this.cells.slice();if(n.forEach((t=>this.unreference(t))),this.clean(),this.add(t,Object.assign({silent:!0},e)),!e.silent){const t=this.cells.slice();this.trigger("reseted",{options:e,previous:n,current:t});const r=[],i=[];t.forEach((t=>{n.some((e=>e.id===t.id))||r.push(t)})),n.forEach((e=>{t.some((t=>t.id===e.id))||i.push(e)})),this.trigger("updated",{options:e,added:r,removed:i,merged:[]})}return this}push(t,e){return this.add(t,this.length,e)}pop(t){const e=this.at(this.length-1);return this.remove(e,t)}unshift(t,e){return this.add(t,0,e)}shift(t){const e=this.at(0);return this.remove(e,t)}get(t){if(null==t)return null;const e="string"==typeof t||"number"==typeof t?t:t.id;return this.map[e]||null}has(t){return null!=this.get(t)}at(t){return t<0&&(t+=this.length),this.cells[t]||null}first(){return this.at(0)}last(){return this.at(-1)}indexOf(t){return this.cells.indexOf(t)}toArray(){return this.cells.slice()}sort(t={}){return null!=this.comparator&&(this.cells=hg(this.cells,this.comparator),t.silent||this.trigger("sorted")),this}clone(){return new(0,this.constructor)(this.cells.slice(),{comparator:this.comparator})}reference(t){this.map[t.id]=t,t.on("*",this.notifyCellEvent,this)}unreference(t){t.off("*",this.notifyCellEvent,this),delete this.map[t.id]}notifyCellEvent(t,e){const n=e.cell;this.trigger(`cell:${t}`,e),n&&(n.isNode()?this.trigger(`node:${t}`,Object.assign(Object.assign({},e),{node:n})):n.isEdge()&&this.trigger(`edge:${t}`,Object.assign(Object.assign({},e),{edge:n})))}clean(){this.length=0,this.cells=[],this.map={}}}class Yw extends xf{get[Symbol.toStringTag](){return Yw.toStringTag}constructor(t=[]){super(),this.batches={},this.addings=new WeakMap,this.nodes={},this.edges={},this.outgoings={},this.incomings={},this.collection=new Xw(t),this.setup()}notify(t,e){this.trigger(t,e);const n=this.graph;return n&&("sorted"===t||"reseted"===t||"updated"===t?n.trigger(`model:${t}`,e):n.trigger(t,e)),this}setup(){const t=this.collection;t.on("sorted",(()=>this.notify("sorted",null))),t.on("updated",(t=>this.notify("updated",t))),t.on("cell:change:zIndex",(()=>this.sortOnChangeZ())),t.on("added",(({cell:t})=>{this.onCellAdded(t)})),t.on("removed",(t=>{const e=t.cell;this.onCellRemoved(e,t.options),this.notify("cell:removed",t),e.isNode()?this.notify("node:removed",Object.assign(Object.assign({},t),{node:e})):e.isEdge()&&this.notify("edge:removed",Object.assign(Object.assign({},t),{edge:e}))})),t.on("reseted",(t=>{this.onReset(t.current),this.notify("reseted",t)})),t.on("edge:change:source",(({edge:t})=>this.onEdgeTerminalChanged(t,"source"))),t.on("edge:change:target",(({edge:t})=>{this.onEdgeTerminalChanged(t,"target")}))}sortOnChangeZ(){this.collection.sort()}onCellAdded(t){const e=t.id;t.isEdge()?(t.updateParent(),this.edges[e]=!0,this.onEdgeTerminalChanged(t,"source"),this.onEdgeTerminalChanged(t,"target")):this.nodes[e]=!0}onCellRemoved(t,e){const n=t.id;if(t.isEdge()){delete this.edges[n];const e=t.getSource(),r=t.getTarget();if(e&&e.cell){const t=this.outgoings[e.cell],r=t?t.indexOf(n):-1;r>=0&&(t.splice(r,1),0===t.length&&delete this.outgoings[e.cell])}if(r&&r.cell){const t=this.incomings[r.cell],e=t?t.indexOf(n):-1;e>=0&&(t.splice(e,1),0===t.length&&delete this.incomings[r.cell])}}else delete this.nodes[n];e.clear||(e.disconnectEdges?this.disconnectConnectedEdges(t,e):this.removeConnectedEdges(t,e)),t.model===this&&(t.model=null)}onReset(t){this.nodes={},this.edges={},this.outgoings={},this.incomings={},t.forEach((t=>this.onCellAdded(t)))}onEdgeTerminalChanged(t,e){const n="source"===e?this.outgoings:this.incomings,r=t.previous(e);if(r&&r.cell){const e=qw.isCell(r.cell)?r.cell.id:r.cell,i=n[e],s=i?i.indexOf(t.id):-1;s>=0&&(i.splice(s,1),0===i.length&&delete n[e])}const i=t.getTerminal(e);if(i&&i.cell){const e=qw.isCell(i.cell)?i.cell.id:i.cell,r=n[e]||[];-1===r.indexOf(t.id)&&r.push(t.id),n[e]=r}}prepareCell(t,e){return t.model||e&&e.dryrun||(t.model=this),null==t.zIndex&&t.setZIndex(this.getMaxZIndex()+1,{silent:!0}),t}resetCells(t,e={}){return t.map((t=>this.prepareCell(t,Object.assign(Object.assign({},e),{dryrun:!0})))),this.collection.reset(t,e),t.map((t=>this.prepareCell(t,{options:e}))),this}clear(t={}){const e=this.getCells();if(0===e.length)return this;const n=Object.assign(Object.assign({},t),{clear:!0});return this.batchUpdate("clear",(()=>{const t=e.sort(((t,e)=>(t.isEdge()?1:2)-(e.isEdge()?1:2)));for(;t.length>0;){const e=t.shift();e&&e.remove(n)}}),n),this}addNode(t,e={}){const n=Hw.isNode(t)?t:this.createNode(t);return this.addCell(n,e),n}updateNode(t,e={}){const n=this.createNode(t),r=n.getProp();return n.dispose(),this.updateCell(r,e)}createNode(t){return Hw.create(t)}addEdge(t,e={}){const n=Jw.isEdge(t)?t:this.createEdge(t);return this.addCell(n,e),n}createEdge(t){return Jw.create(t)}updateEdge(t,e={}){const n=this.createEdge(t),r=n.getProp();return n.dispose(),this.updateCell(r,e)}addCell(t,e={}){return Array.isArray(t)?this.addCells(t,e):(this.collection.has(t)||this.addings.has(t)||(this.addings.set(t,!0),this.collection.add(this.prepareCell(t,e),e),t.eachChild((t=>this.addCell(t,e))),this.addings.delete(t)),this)}addCells(t,e={}){const n=t.length;if(0===n)return this;const r=Object.assign(Object.assign({},e),{position:n-1,maxPosition:n-1});return this.startBatch("add",Object.assign(Object.assign({},r),{cells:t})),t.forEach((t=>{this.addCell(t,r),r.position-=1})),this.stopBatch("add",Object.assign(Object.assign({},r),{cells:t})),this}updateCell(t,e={}){const n=t.id&&this.getCell(t.id);return!!n&&this.batchUpdate("update",(()=>(Object.entries(t).forEach((([t,r])=>n.setProp(t,r,e))),!0)),t)}removeCell(t,e={}){const n="string"==typeof t?this.getCell(t):t;return n&&this.has(n)?this.collection.remove(n,e):null}updateCellId(t,e){this.startBatch("update",{id:e}),t.prop("id",e);const n=t.clone({keepId:!0});this.addCell(n);return this.getConnectedEdges(t).forEach((n=>{const r=n.getSourceCell(),i=n.getTargetCell();r===t&&n.setSource(Object.assign(Object.assign({},n.getSource()),{cell:e})),i===t&&n.setTarget(Object.assign(Object.assign({},n.getTarget()),{cell:e}))})),this.removeCell(t),this.stopBatch("update",{id:e}),n}removeCells(t,e={}){return t.length?this.batchUpdate("remove",(()=>t.map((t=>this.removeCell(t,e))))):[]}removeConnectedEdges(t,e={}){const n=this.getConnectedEdges(t);return n.forEach((t=>{t.remove(e)})),n}disconnectConnectedEdges(t,e={}){const n="string"==typeof t?t:t.id;this.getConnectedEdges(t).forEach((t=>{const r=t.getSourceCellId(),i=t.getTargetCellId();r===n&&t.setSource({x:0,y:0},e),i===n&&t.setTarget({x:0,y:0},e)}))}has(t){return this.collection.has(t)}total(){return this.collection.length}indexOf(t){return this.collection.indexOf(t)}getCell(t){return this.collection.get(t)}getCells(){return this.collection.toArray()}getFirstCell(){return this.collection.first()}getLastCell(){return this.collection.last()}getMinZIndex(){const t=this.collection.first();return t&&t.getZIndex()||0}getMaxZIndex(){const t=this.collection.last();return t&&t.getZIndex()||0}getCellsFromCache(t){return t?Object.keys(t).map((t=>this.getCell(t))).filter((t=>null!=t)):[]}getNodes(){return this.getCellsFromCache(this.nodes)}getEdges(){return this.getCellsFromCache(this.edges)}getOutgoingEdges(t){const e="string"==typeof t?t:t.id,n=this.outgoings[e];return n?n.map((t=>this.getCell(t))).filter((t=>t&&t.isEdge())):null}getIncomingEdges(t){const e="string"==typeof t?t:t.id,n=this.incomings[e];return n?n.map((t=>this.getCell(t))).filter((t=>t&&t.isEdge())):null}getConnectedEdges(t,e={}){const n=[],r="string"==typeof t?this.getCell(t):t;if(null==r)return n;const i={},s=e.indirect;let o=e.incoming,a=e.outgoing;null==o&&null==a&&(o=a=!0);const l=(t,e)=>{const r=e?this.getOutgoingEdges(t):this.getIncomingEdges(t);if(null!=r&&r.forEach((t=>{i[t.id]||(n.push(t),i[t.id]=!0,s&&(o&&l(t,!1),a&&l(t,!0)))})),s&&t.isEdge()){const r=e?t.getTargetCell():t.getSourceCell();r&&r.isEdge()&&(i[r.id]||(n.push(r),l(r,e)))}};if(a&&l(r,!0),o&&l(r,!1),e.deep){const t=r.getDescendants({deep:!0}),s={};t.forEach((t=>{t.isNode()&&(s[t.id]=!0)}));const l=(t,r)=>{const o=r?this.getOutgoingEdges(t.id):this.getIncomingEdges(t.id);null!=o&&o.forEach((t=>{if(!i[t.id]){const r=t.getSourceCell(),o=t.getTargetCell();if(!e.enclosed&&r&&s[r.id]&&o&&s[o.id])return;n.push(t),i[t.id]=!0}}))};t.forEach((t=>{t.isEdge()||(a&&l(t,!0),o&&l(t,!1))}))}return n}isBoundary(t,e){const n="string"==typeof t?this.getCell(t):t,r=e?this.getIncomingEdges(n):this.getOutgoingEdges(n);return null==r||0===r.length}getBoundaryNodes(t){const e=[];return Object.keys(this.nodes).forEach((n=>{if(this.isBoundary(n,t)){const t=this.getCell(n);t&&e.push(t)}})),e}getRoots(){return this.getBoundaryNodes(!0)}getLeafs(){return this.getBoundaryNodes(!1)}isRoot(t){return this.isBoundary(t,!0)}isLeaf(t){return this.isBoundary(t,!1)}getNeighbors(t,e={}){let n=e.incoming,r=e.outgoing;null==n&&null==r&&(n=r=!0);const i=this.getConnectedEdges(t,e).reduce(((i,s)=>{const o=s.hasLoop(e),a=s.getSourceCell(),l=s.getTargetCell();return n&&a&&a.isNode()&&!i[a.id]&&(!o&&(a===t||e.deep&&a.isDescendantOf(t))||(i[a.id]=a)),r&&l&&l.isNode()&&!i[l.id]&&(!o&&(l===t||e.deep&&l.isDescendantOf(t))||(i[l.id]=l)),i}),{});if(t.isEdge()){if(n){const e=t.getSourceCell();e&&e.isNode()&&!i[e.id]&&(i[e.id]=e)}if(r){const e=t.getTargetCell();e&&e.isNode()&&!i[e.id]&&(i[e.id]=e)}}return Object.keys(i).map((t=>i[t]))}isNeighbor(t,e,n={}){let r=n.incoming,i=n.outgoing;return null==r&&null==i&&(r=i=!0),this.getConnectedEdges(t,n).some((t=>{const n=t.getSourceCell(),s=t.getTargetCell();return!(!r||!n||n.id!==e.id)||!(!i||!s||s.id!==e.id)}))}getSuccessors(t,e={}){const n=[];return this.search(t,((r,i)=>{r!==t&&this.matchDistance(i,e.distance)&&n.push(r)}),Object.assign(Object.assign({},e),{outgoing:!0})),n}isSuccessor(t,e,n={}){let r=!1;return this.search(t,((i,s)=>{if(i===e&&i!==t&&this.matchDistance(s,n.distance))return r=!0,!1}),Object.assign(Object.assign({},n),{outgoing:!0})),r}getPredecessors(t,e={}){const n=[];return this.search(t,((r,i)=>{r!==t&&this.matchDistance(i,e.distance)&&n.push(r)}),Object.assign(Object.assign({},e),{incoming:!0})),n}isPredecessor(t,e,n={}){let r=!1;return this.search(t,((i,s)=>{if(i===e&&i!==t&&this.matchDistance(s,n.distance))return r=!0,!1}),Object.assign(Object.assign({},n),{incoming:!0})),r}matchDistance(t,e){return null==e||("function"==typeof e?e(t):!(!Array.isArray(e)||!e.includes(t))||t===e)}getCommonAncestor(...t){const e=[];return t.forEach((t=>{t&&(Array.isArray(t)?e.push(...t):e.push(t))})),qw.getCommonAncestor(...e)}getSubGraph(t,e={}){const n=[],r={},i=[],s=[],o=t=>{r[t.id]||(n.push(t),r[t.id]=t,t.isEdge()&&s.push(t),t.isNode()&&i.push(t))};return t.forEach((t=>{if(o(t),e.deep){t.getDescendants({deep:!0}).forEach((t=>o(t)))}})),s.forEach((t=>{const e=t.getSourceCell(),s=t.getTargetCell();e&&!r[e.id]&&(n.push(e),r[e.id]=e,e.isNode()&&i.push(e)),s&&!r[s.id]&&(n.push(s),r[s.id]=s,s.isNode()&&i.push(s))})),i.forEach((t=>{this.getConnectedEdges(t,e).forEach((t=>{const e=t.getSourceCell(),i=t.getTargetCell();!r[t.id]&&e&&r[e.id]&&i&&r[i.id]&&(n.push(t),r[t.id]=t)}))})),n}cloneSubGraph(t,e={}){const n=this.getSubGraph(t,e);return this.cloneCells(n)}cloneCells(t){return qw.cloneCells(t)}getNodesFromPoint(t,e){const n="number"==typeof t?{x:t,y:e||0}:t;return this.getNodes().filter((t=>t.getBBox().containsPoint(n)))}getNodesInArea(t,e,n,r,i){const s="number"==typeof t?new ky(t,e,n,r):ky.create(t),o="number"==typeof t?i:e,a=o&&o.strict;return this.getNodes().filter((t=>{const e=t.getBBox();return a?s.containsRect(e):s.isIntersectWithRect(e)}))}getEdgesInArea(t,e,n,r,i){const s="number"==typeof t?new ky(t,e,n,r):ky.create(t),o="number"==typeof t?i:e,a=o&&o.strict;return this.getEdges().filter((t=>{const e=t.getBBox();return 0===e.width?e.inflate(1,0):0===e.height&&e.inflate(0,1),a?s.containsRect(e):s.isIntersectWithRect(e)}))}getNodesUnderNode(t,e={}){const n=t.getBBox();return(null==e.by||"bbox"===e.by?this.getNodesInArea(n):this.getNodesFromPoint(n[e.by])).filter((e=>t.id!==e.id&&!e.isDescendantOf(t)))}getAllCellsBBox(){return this.getCellsBBox(this.getCells())}getCellsBBox(t,e={}){return qw.getCellsBBox(t,e)}search(t,e,n={}){n.breadthFirst?this.breadthFirstSearch(t,e,n):this.depthFirstSearch(t,e,n)}breadthFirstSearch(t,e,n={}){const r=[],i={},s={};for(r.push(t),s[t.id]=0;r.length>0;){const t=r.shift();if(null==t||i[t.id])continue;if(i[t.id]=!0,!1===ef(e,this,t,s[t.id]))continue;this.getNeighbors(t,n).forEach((e=>{s[e.id]=s[t.id]+1,r.push(e)}))}}depthFirstSearch(t,e,n={}){const r=[],i={},s={};for(r.push(t),s[t.id]=0;r.length>0;){const t=r.pop();if(null==t||i[t.id])continue;if(i[t.id]=!0,!1===ef(e,this,t,s[t.id]))continue;const o=this.getNeighbors(t,n),a=r.length;o.forEach((e=>{s[e.id]=s[t.id]+1,r.splice(a,0,e)}))}}getShortestPath(e,n,r={}){const i={};this.getEdges().forEach((t=>{const e=t.getSourceCellId(),n=t.getTargetCellId();e&&n&&(i[e]||(i[e]=[]),i[n]||(i[n]=[]),i[e].push(n),r.directed||i[n].push(e))}));const s="string"==typeof e?e:e.id,o=t.Dijkstra.run(i,s,r.weight),a=[];let l="string"==typeof n?n:n.id;for(o[l]&&a.push(l);l=o[l];)a.unshift(l);return a}translate(t,e,n){return this.getCells().filter((t=>!t.hasParent())).forEach((r=>r.translate(t,e,n))),this}resize(t,e,n){return this.resizeCells(t,e,this.getCells(),n)}resizeCells(t,e,n,r={}){const i=this.getCellsBBox(n);if(i){const s=Math.max(t/i.width,0),o=Math.max(e/i.height,0),a=i.getOrigin();n.forEach((t=>t.scale(s,o,a,r)))}return this}toJSON(t={}){return Yw.toJSON(this.getCells(),t)}parseJSON(t){return Yw.fromJSON(t)}fromJSON(t,e={}){const n=this.parseJSON(t);return this.resetCells(n,e),this}startBatch(t,e={}){return this.batches[t]=(this.batches[t]||0)+1,this.notify("batch:start",{name:t,data:e}),this}stopBatch(t,e={}){return this.batches[t]=(this.batches[t]||0)-1,this.notify("batch:stop",{name:t,data:e}),this}batchUpdate(t,e,n={}){this.startBatch(t,n);const r=e();return this.stopBatch(t,n),r}hasActiveBatch(t=Object.keys(this.batches)){return(Array.isArray(t)?t:[t]).some((t=>this.batches[t]>0))}}!function(t){t.toStringTag=`X6.${t.name}`,t.isModel=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"==typeof r.addNode&&"function"==typeof r.addEdge&&null!=r.collection}}(Yw||(Yw={})),function(t){t.toJSON=function(t,e={}){return{cells:t.map((t=>t.toJSON(e)))}},t.fromJSON=function(t){const e=[];return Array.isArray(t)?e.push(...t):(t.cells&&e.push(...t.cells),t.nodes&&t.nodes.forEach((t=>{null==t.shape&&(t.shape="rect"),e.push(t)})),t.edges&&t.edges.forEach((t=>{null==t.shape&&(t.shape="edge"),e.push(t)}))),e.map((t=>{const e=t.shape;if(e){if(Hw.registry.exist(e))return Hw.create(t);if(Jw.registry.exist(e))return Jw.create(t)}throw new Error("The `shape` should be specified when creating a node/edge instance")}))}}(Yw||(Yw={}));let Zw=class extends Hw{get label(){return this.getLabel()}set label(t){this.setLabel(t)}getLabel(){return this.getAttrByPath("text/text")}setLabel(t,e){return null==t?this.removeLabel():this.setAttrByPath("text/text",t,e),this}removeLabel(){return this.removeAttrByPath("text/text"),this}};function Kw(t,e="body"){return[{tagName:t,selector:e},{tagName:"text",selector:"label"}]}function Qw(t,e,n={}){const r={constructorName:t,markup:Kw(t,n.selector),attrs:{[t]:Object.assign({},Zw.bodyAttr)}};return(n.parent||Zw).define(Sh(r,e,{shape:t}))}!function(t){t.bodyAttr={fill:"#ffffff",stroke:"#333333",strokeWidth:2},t.labelAttr={fontSize:14,fill:"#000000",refX:.5,refY:.5,textAnchor:"middle",textVerticalAnchor:"middle",fontFamily:"Arial, helvetica, sans-serif"},t.config({attrs:{text:Object.assign({},t.labelAttr)},propHooks(t){const{label:n}=t,r=e(t,["label"]);return n&&yf(r,"attrs/text/text",n),r},visible:!0})}(Zw||(Zw={}));const tA=Qw("rect",{attrs:{body:{refWidth:"100%",refHeight:"100%"}}}),eA=Jw.define({shape:"edge",markup:[{tagName:"path",selector:"wrap",groupSelector:"lines",attrs:{fill:"none",cursor:"pointer",stroke:"transparent",strokeLinecap:"round"}},{tagName:"path",selector:"line",groupSelector:"lines",attrs:{fill:"none",pointerEvents:"none"}}],attrs:{lines:{connection:!0,strokeLinejoin:"round"},wrap:{strokeWidth:10},line:{stroke:"#333",strokeWidth:2,targetMarker:"classic"}}}),nA=Qw("ellipse",{attrs:{body:{refCx:"50%",refCy:"50%",refRx:"50%",refRy:"50%"}}});class rA extends Zw{get points(){return this.getPoints()}set points(t){this.setPoints(t)}getPoints(){return this.getAttrByPath("body/refPoints")}setPoints(t,e){return null==t?this.removePoints():this.setAttrByPath("body/refPoints",rA.pointsToString(t),e),this}removePoints(){return this.removeAttrByPath("body/refPoints"),this}}!function(t){function n(t){return"string"==typeof t?t:t.map((t=>Array.isArray(t)?t.join(","):Ty.isPointLike(t)?`${t.x}, ${t.y}`:"")).join(" ")}t.pointsToString=n,t.config({propHooks(t){const{points:r}=t,i=e(t,["points"]);if(r){const t=n(r);t&&yf(i,"attrs/body/refPoints",t)}return i}})}(rA||(rA={}));const iA=Qw("polygon",{},{parent:rA}),sA=Qw("polyline",{},{parent:rA}),oA=Zw.define({shape:"path",markup:[{tagName:"rect",selector:"bg"},{tagName:"path",selector:"body"},{tagName:"text",selector:"label"}],attrs:{bg:{refWidth:"100%",refHeight:"100%",fill:"none",stroke:"none",pointerEvents:"all"},body:{fill:"none",stroke:"#000",strokeWidth:2}},propHooks(t){const{path:n}=t,r=e(t,["path"]);return n&&yf(r,"attrs/body/refD",n),r}}),aA=Zw.define({shape:"text-block",markup:[{tagName:"rect",selector:"body"},t.Platform.SUPPORT_FOREIGNOBJECT?{tagName:"foreignObject",selector:"foreignObject",children:[{tagName:"div",ns:fp.xhtml,selector:"label",style:{width:"100%",height:"100%",position:"static",backgroundColor:"transparent",textAlign:"center",margin:0,padding:"0px 5px",boxSizing:"border-box",display:"flex",alignItems:"center",justifyContent:"center"}}]}:{tagName:"text",selector:"label",attrs:{textAnchor:"middle"}}],attrs:{body:Object.assign(Object.assign({},Zw.bodyAttr),{refWidth:"100%",refHeight:"100%"}),foreignObject:{refWidth:"100%",refHeight:"100%"},label:{style:{fontSize:14}}},propHooks(t){const{text:n}=t,r=e(t,["text"]);return n&&yf(r,"attrs/label/text",n),r},attrHooks:{text:{set(t,{cell:e,view:n,refBBox:r,elem:i,attrs:s}){if(!(i instanceof HTMLElement)){const o=s.style||{},a={text:t,width:-5,height:"100%"},l=Object.assign({textVerticalAnchor:"middle"},o);return ef(Tb.presets.textWrap.set,this,a,{cell:e,view:n,elem:i,refBBox:r,attrs:l}),{fill:o.color||null}}i.textContent=t},position(t,{refBBox:e,elem:n}){if(n instanceof SVGElement)return e.getCenter()}}}}),lA=Qw("image",{attrs:{image:{refWidth:"100%",refHeight:"100%"}},propHooks:function(t="xlink:href"){return n=>{const{imageUrl:r,imageWidth:i,imageHeight:s}=n,o=e(n,["imageUrl","imageWidth","imageHeight"]);if(null!=r||null!=i||null!=s){const e=()=>{if(o.attrs){const e=o.attrs.image;null!=r&&(e[t]=r),null!=i&&(e.width=i),null!=s&&(e.height=s),o.attrs.image=e}};o.attrs?(null==o.attrs.image&&(o.attrs.image={}),e()):(o.attrs={image:{}},e())}return o}}()},{selector:"image"}),cA=Qw("circle",{attrs:{body:{refCx:"50%",refCy:"50%",refR:"50%"}}});class hA extends ox{constructor(){super(...arguments),this.portsCache={}}get[Symbol.toStringTag](){return hA.toStringTag}getContainerClassName(){const t=[super.getContainerClassName(),this.prefixClassName("node")];return this.can("nodeMovable")||t.push(this.prefixClassName("node-immovable")),t.join(" ")}updateClassName(t){const e=t.target;if(e.hasAttribute("magnet")){const t=this.prefixClassName("port-unconnectable");this.can("magnetConnectable")?lp(e,t):ap(e,t)}else{const t=this.prefixClassName("node-immovable");this.can("nodeMovable")?this.removeClass(t):this.addClass(t)}}isNodeView(){return!0}confirmUpdate(t,e={}){let n=t;return this.hasAction(n,"ports")&&(this.removePorts(),this.cleanPortsCache()),this.hasAction(n,"render")?(this.render(),n=this.removeAction(n,["render","update","resize","translate","rotate","ports","tools"])):(n=this.handleAction(n,"resize",(()=>this.resize()),"update"),n=this.handleAction(n,"update",(()=>this.update()),Nb.useCSSSelector?"ports":null),n=this.handleAction(n,"translate",(()=>this.translate())),n=this.handleAction(n,"rotate",(()=>this.rotate())),n=this.handleAction(n,"ports",(()=>this.renderPorts())),n=this.handleAction(n,"tools",(()=>{this.getFlag("tools")===t?this.renderTools():this.updateTools(e)}))),n}update(t){this.cleanCache(),Nb.useCSSSelector&&this.removePorts();const e=this.cell,n=e.getSize(),r=e.getAttrs();this.updateAttrs(this.container,r,{attrs:t===r?null:t,rootBBox:new ky(0,0,n.width,n.height),selectors:this.selectors}),Nb.useCSSSelector&&this.renderPorts()}renderMarkup(){const t=this.cell.markup;if(t){if("string"==typeof t)throw new TypeError("Not support string markup.");return this.renderJSONMarkup(t)}throw new TypeError("Invalid node markup.")}renderJSONMarkup(t){const e=this.parseJSONMarkup(t,this.container);this.selectors=e.selectors,this.container.appendChild(e.fragment)}render(){return this.empty(),this.renderMarkup(),this.resize(),this.updateTransform(),Nb.useCSSSelector||this.renderPorts(),this.renderTools(),this}resize(){this.cell.getAngle()&&this.rotate(),this.update()}translate(){this.updateTransform()}rotate(){this.updateTransform()}getTranslationString(){const t=this.cell.getPosition();return`translate(${t.x},${t.y})`}getRotationString(){const t=this.cell.getAngle();if(t){const e=this.cell.getSize();return`rotate(${t},${e.width/2},${e.height/2})`}}updateTransform(){let t=this.getTranslationString();const e=this.getRotationString();e&&(t+=` ${e}`),this.container.setAttribute("transform",t)}findPortElem(t,e){const n=t?this.portsCache[t]:null;if(!n)return null;const r=n.portContentElement,i=n.portContentSelectors||{};return this.findOne(e,r,i)}cleanPortsCache(){this.portsCache={}}removePorts(){Object.values(this.portsCache).forEach((t=>{Sp(t.portElement)}))}renderPorts(){const t=this.container,e=[];t.childNodes.forEach((t=>{e.push(t)}));const n=this.cell.getParsedPorts(),r=oc(n,"zIndex"),i="auto";r[i]&&r[i].forEach((n=>{const r=this.getPortElement(n);t.append(r),e.push(r)})),Object.keys(r).forEach((t=>{if(t!==i){const n=parseInt(t,10);this.appendPorts(r[t],n,e)}})),this.updatePorts()}appendPorts(t,e,n){const r=t.map((t=>this.getPortElement(t)));n[e]||e<0?Np(n[Math.max(e,0)],r):Tp(this.container,r)}getPortElement(t){const e=this.portsCache[t.id];return e?e.portElement:this.createPortElement(t)}createPortElement(e){let n=t.Markup.renderMarkup(this.cell.getPortContainerMarkup());const r=n.elem;if(null==r)throw new Error("Invalid port container markup.");n=t.Markup.renderMarkup(this.getPortMarkup(e));const i=n.elem,s=n.selectors;if(null==i)throw new Error("Invalid port markup.");this.setAttrs({port:e.id,"port-group":e.group},i);let o="x6-port";e.group&&(o+=` x6-port-${e.group}`),ap(r,o),ap(r,"x6-port"),ap(i,"x6-port-body"),r.appendChild(i);let a,l,c=s;if(this.existPortLabel(e)){if(n=t.Markup.renderMarkup(this.getPortLabelMarkup(e.label)),a=n.elem,l=n.selectors,null==a)throw new Error("Invalid port label markup.");if(s&&l){for(const t in l)if(s[t]&&t!==this.rootSelector)throw new Error("Selectors within port must be unique.");c=Object.assign(Object.assign({},s),l)}ap(a,"x6-port-label"),r.appendChild(a)}return this.portsCache[e.id]={portElement:r,portSelectors:c,portLabelElement:a,portLabelSelectors:l,portContentElement:i,portContentSelectors:s},this.graph.options.onPortRendered&&this.graph.options.onPortRendered({port:e,node:this.cell,container:r,selectors:c,labelContainer:a,labelSelectors:l,contentContainer:i,contentSelectors:s}),r}updatePorts(){const t=this.cell.getParsedGroups(),e=Object.keys(t);0===e.length?this.updatePortGroup():e.forEach((t=>this.updatePortGroup(t)))}updatePortGroup(t){const e=ky.fromSize(this.cell.getSize()),n=this.cell.getPortsLayoutByGroup(t,e);for(let t=0,e=n.length;t<e;t+=1){const e=n[t],r=e.portId,i=this.portsCache[r]||{},s=e.portLayout;if(this.applyPortTransform(i.portElement,s),null!=e.portAttrs){const t={selectors:i.portSelectors||{}};e.portSize&&(t.rootBBox=ky.fromSize(e.portSize)),this.updateAttrs(i.portElement,e.portAttrs,t)}const o=e.labelLayout;if(o&&i.portLabelElement&&(this.applyPortTransform(i.portLabelElement,o,-(s.angle||0)),o.attrs)){const t={selectors:i.portLabelSelectors||{}};e.labelSize&&(t.rootBBox=ky.fromSize(e.labelSize)),this.updateAttrs(i.portLabelElement,o.attrs,t)}}}applyPortTransform(t,e,n=0){const r=e.angle,i=e.position;Zm(t,$m().rotate(n).translate(i.x||0,i.y||0).rotate(r||0),{absolute:!0})}getPortMarkup(t){return t.markup||this.cell.portMarkup}getPortLabelMarkup(t){return t.markup||this.cell.portLabelMarkup}existPortLabel(t){return t.attrs&&t.attrs.text}getEventArgs(t,e,n){const r=this,i=r.cell;return null==e||null==n?{e:t,view:r,node:i,cell:i}:{e:t,x:e,y:n,view:r,node:i,cell:i}}getPortEventArgs(t,e,n){const r=this,i=r.cell,s=i;return n?{e:t,x:n.x,y:n.y,view:r,node:i,cell:s,port:e}:{e:t,view:r,node:i,cell:s,port:e}}notifyMouseDown(t,e,n){super.onMouseDown(t,e,n),this.notify("node:mousedown",this.getEventArgs(t,e,n))}notifyMouseMove(t,e,n){super.onMouseMove(t,e,n),this.notify("node:mousemove",this.getEventArgs(t,e,n))}notifyMouseUp(t,e,n){super.onMouseUp(t,e,n),this.notify("node:mouseup",this.getEventArgs(t,e,n))}notifyPortEvent(t,e,n){const r=this.findAttr("port",e.target);if(r){const i=e.type;"node:port:mouseenter"===t?e.type="mouseenter":"node:port:mouseleave"===t&&(e.type="mouseleave"),this.notify(t,this.getPortEventArgs(e,r,n)),e.type=i}}onClick(t,e,n){super.onClick(t,e,n),this.notify("node:click",this.getEventArgs(t,e,n)),this.notifyPortEvent("node:port:click",t,{x:e,y:n})}onDblClick(t,e,n){super.onDblClick(t,e,n),this.notify("node:dblclick",this.getEventArgs(t,e,n)),this.notifyPortEvent("node:port:dblclick",t,{x:e,y:n})}onContextMenu(t,e,n){super.onContextMenu(t,e,n),this.notify("node:contextmenu",this.getEventArgs(t,e,n)),this.notifyPortEvent("node:port:contextmenu",t,{x:e,y:n})}onMouseDown(t,e,n){this.isPropagationStopped(t)||(this.notifyMouseDown(t,e,n),this.notifyPortEvent("node:port:mousedown",t,{x:e,y:n}),this.startNodeDragging(t,e,n))}onMouseMove(t,e,n){const r=this.getEventData(t),i=r.action;if("magnet"===i)this.dragMagnet(t,e,n);else{if("move"===i){const i=r.targetView||this;i.dragNode(t,e,n),i.notify("node:moving",{e:t,x:e,y:n,view:i,cell:i.cell,node:i.cell})}this.notifyMouseMove(t,e,n),this.notifyPortEvent("node:port:mousemove",t,{x:e,y:n})}this.setEventData(t,r)}onMouseUp(t,e,n){const r=this.getEventData(t),i=r.action;if("magnet"===i)this.stopMagnetDragging(t,e,n);else if(this.notifyMouseUp(t,e,n),this.notifyPortEvent("node:port:mouseup",t,{x:e,y:n}),"move"===i){(r.targetView||this).stopNodeDragging(t,e,n)}const s=r.targetMagnet;s&&this.onMagnetClick(t,s,e,n),this.checkMouseleave(t)}onMouseOver(t){super.onMouseOver(t),this.notify("node:mouseover",this.getEventArgs(t)),this.notifyPortEvent("node:port:mouseenter",t),this.notifyPortEvent("node:port:mouseover",t)}onMouseOut(t){super.onMouseOut(t),this.notify("node:mouseout",this.getEventArgs(t)),this.notifyPortEvent("node:port:mouseleave",t),this.notifyPortEvent("node:port:mouseout",t)}onMouseEnter(t){this.updateClassName(t),super.onMouseEnter(t),this.notify("node:mouseenter",this.getEventArgs(t))}onMouseLeave(t){super.onMouseLeave(t),this.notify("node:mouseleave",this.getEventArgs(t))}onMouseWheel(t,e,n,r){super.onMouseWheel(t,e,n,r),this.notify("node:mousewheel",Object.assign({delta:r},this.getEventArgs(t,e,n)))}onMagnetClick(t,e,n,r){const i=this.graph;i.view.getMouseMovedCount(t)>i.options.clickThreshold||this.notify("node:magnet:click",Object.assign({magnet:e},this.getEventArgs(t,n,r)))}onMagnetDblClick(t,e,n,r){this.notify("node:magnet:dblclick",Object.assign({magnet:e},this.getEventArgs(t,n,r)))}onMagnetContextMenu(t,e,n,r){this.notify("node:magnet:contextmenu",Object.assign({magnet:e},this.getEventArgs(t,n,r)))}onMagnetMouseDown(t,e,n,r){this.startMagnetDragging(t,n,r)}onCustomEvent(t,e,n,r){this.notify("node:customevent",Object.assign({name:e},this.getEventArgs(t,n,r))),super.onCustomEvent(t,e,n,r)}prepareEmbedding(t){const e=this.graph,n=this.getEventData(t).cell||this.cell,r=e.findViewByCell(n),i=e.snapToGrid(t.clientX,t.clientY);this.notify("node:embed",{e:t,node:n,view:r,cell:n,x:i.x,y:i.y,currentParent:n.getParent()})}processEmbedding(t,e){const n=e.cell||this.cell,r=e.graph||this.graph,i=r.options.embedding,s=i.findParent;let o="function"==typeof s?ef(s,r,{view:this,node:this.cell}).filter((t=>qw.isCell(t)&&this.cell.id!==t.id&&!t.isDescendantOf(this.cell))):r.model.getNodesUnderNode(n,{by:s});if(i.frontOnly&&o.length>0){const t=oc(o,"zIndex"),e=Ph(Object.keys(t).map((t=>parseInt(t,10))));e&&(o=t[e])}o=o.filter((t=>t.visible));let a=null;const l=e.candidateEmbedView,c=i.validate;for(let t=o.length-1;t>=0;t-=1){const e=o[t];if(l&&l.cell.id===e.id){a=l;break}{const t=e.findView(r);if(ef(c,r,{child:this.cell,parent:t.cell,childView:this,parentView:t})){a=t;break}}}this.clearEmbedding(e),a&&a.highlight(null,{type:"embedding"}),e.candidateEmbedView=a;const h=r.snapToGrid(t.clientX,t.clientY);this.notify("node:embedding",{e:t,cell:n,node:n,view:r.findViewByCell(n),x:h.x,y:h.y,currentParent:n.getParent(),candidateParent:a?a.cell:null})}clearEmbedding(t){const e=t.candidateEmbedView;e&&(e.unhighlight(null,{type:"embedding"}),t.candidateEmbedView=null)}finalizeEmbedding(t,e){this.graph.startBatch("embedding");const n=e.cell||this.cell,r=e.graph||this.graph,i=r.findViewByCell(n),s=n.getParent(),o=e.candidateEmbedView;if(o?(o.unhighlight(null,{type:"embedding"}),e.candidateEmbedView=null,null!=s&&s.id===o.cell.id||o.cell.insertChild(n,void 0,{ui:!0})):s&&s.unembed(n,{ui:!0}),r.model.getConnectedEdges(n,{deep:!0}).forEach((t=>{t.updateParent({ui:!0})})),i&&o){const e=r.snapToGrid(t.clientX,t.clientY);i.notify("node:embedded",{e:t,cell:n,x:e.x,y:e.y,node:n,view:r.findViewByCell(n),previousParent:s,currentParent:n.getParent()})}this.graph.stopBatch("embedding")}getDelegatedView(){let t=this.cell,e=this;for(;e&&!t.isEdge();){if(!t.hasParent()||e.can("stopDelegateOnDragging"))return e;t=t.getParent(),e=this.graph.findViewByCell(t)}return null}validateMagnet(t,e,n){if("passive"!==e.getAttribute("magnet")){const r=this.graph.options.connecting.validateMagnet;return!r||ef(r,this.graph,{e:n,magnet:e,view:t,cell:t.cell})}return!1}startMagnetDragging(t,e,n){if(!this.can("magnetConnectable"))return;t.stopPropagation();const r=t.currentTarget,i=this.graph;this.setEventData(t,{targetMagnet:r}),this.validateMagnet(this,r,t)?(i.options.magnetThreshold<=0&&this.startConnectting(t,r,e,n),this.setEventData(t,{action:"magnet"}),this.stopPropagation(t)):this.onMouseDown(t,e,n),i.view.delegateDragEvents(t,this)}startConnectting(t,e,n,r){this.graph.model.startBatch("add-edge");const i=this.createEdgeFromMagnet(e,n,r);i.setEventData(t,i.prepareArrowheadDragging("target",{x:n,y:r,isNewEdge:!0,fallbackAction:"remove"})),this.setEventData(t,{edgeView:i}),i.notifyMouseDown(t,n,r)}getDefaultEdge(t,e){let n;const r=this.graph.options.connecting.createEdge;return r&&(n=ef(r,this.graph,{sourceMagnet:e,sourceView:t,sourceCell:t.cell})),n}createEdgeFromMagnet(t,e,n){const r=this.graph,i=r.model,s=this.getDefaultEdge(this,t);return s.setSource(Object.assign(Object.assign({},s.getSource()),this.getEdgeTerminal(t,e,n,s,"source"))),s.setTarget(Object.assign(Object.assign({},s.getTarget()),{x:e,y:n})),s.addTo(i,{async:!1,ui:!0}),s.findView(r)}dragMagnet(t,e,n){const r=this.getEventData(t),i=r.edgeView;if(i)i.onMouseMove(t,e,n),this.autoScrollGraph(t.clientX,t.clientY);else{const i=this.graph,s=i.options.magnetThreshold,o=this.getEventTarget(t),a=r.targetMagnet;if("onleave"===s){if(a===o||a.contains(o))return}else if(i.view.getMouseMovedCount(t)<=s)return;this.startConnectting(t,a,e,n)}}stopMagnetDragging(t,e,n){const r=this.eventData(t).edgeView;r&&(r.onMouseUp(t,e,n),this.graph.model.stopBatch("add-edge"))}notifyUnhandledMouseDown(t,e,n){this.notify("node:unhandled:mousedown",{e:t,x:e,y:n,view:this,cell:this.cell,node:this.cell})}notifyNodeMove(t,e,n,r,i){let s=[i];const o=this.graph.getPlugin("selection");if(o&&o.isSelectionMovable()){const t=o.getSelectedCells();t.includes(i)&&(s=t.filter((t=>t.isNode())))}s.forEach((i=>{this.notify(t,{e:e,x:n,y:r,cell:i,node:i,view:i.findView(this.graph)})}))}getRestrictArea(t){const e=this.graph.options.translating.restrict,n="function"==typeof e?ef(e,this.graph,t):e;return"number"==typeof n?this.graph.transform.getGraphArea().inflate(n):!0===n?this.graph.transform.getGraphArea():n||null}startNodeDragging(t,e,n){const r=this.getDelegatedView();if(null==r||!r.can("nodeMovable"))return this.notifyUnhandledMouseDown(t,e,n);this.setEventData(t,{targetView:r,action:"move"});const i=Ty.create(r.cell.getPosition());r.setEventData(t,{moving:!1,offset:i.diff(e,n),restrict:this.getRestrictArea(r)})}dragNode(e,n,r){const i=this.cell,s=this.graph,o=s.getGridSize(),a=this.getEventData(e),l=a.offset,c=a.restrict;a.moving||(a.moving=!0,this.addClass("node-moving"),this.notifyNodeMove("node:move",e,n,r,this.cell)),this.autoScrollGraph(e.clientX,e.clientY);const h=t.GeometryUtil.snapToGrid(n+l.x,o),u=t.GeometryUtil.snapToGrid(r+l.y,o);i.setPosition(h,u,{restrict:c,deep:!0,ui:!0}),s.options.embedding.enabled&&(a.embedding||(this.prepareEmbedding(e),a.embedding=!0),this.processEmbedding(e,a))}stopNodeDragging(t,e,n){const r=this.getEventData(t);r.embedding&&this.finalizeEmbedding(t,r),r.moving&&(this.removeClass("node-moving"),this.notifyNodeMove("node:moved",t,e,n,this.cell)),r.moving=!1,r.embedding=!1}autoScrollGraph(t,e){const n=this.graph.getPlugin("scroller");n&&n.autoScroll(t,e)}}!function(t){t.toStringTag=`X6.${t.name}`,t.isNodeView=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"==typeof r.isNodeView&&"function"==typeof r.isEdgeView&&"function"==typeof r.confirmUpdate&&"function"==typeof r.update&&"function"==typeof r.findPortElem&&"function"==typeof r.resize&&"function"==typeof r.rotate&&"function"==typeof r.translate}}(hA||(hA={})),hA.config({isSvgElement:!0,priority:0,bootstrap:["render"],actions:{view:["render"],markup:["render"],attrs:["update"],size:["resize","ports","tools"],angle:["rotate","tools"],position:["translate","tools"],ports:["ports"],tools:["tools"]}}),hA.registry.register("node",hA,!0);class uA extends ox{constructor(){super(...arguments),this.POINT_ROUNDING=2}get[Symbol.toStringTag](){return uA.toStringTag}getContainerClassName(){return[super.getContainerClassName(),this.prefixClassName("edge")].join(" ")}get sourceBBox(){const t=this.sourceView;if(!t){const t=this.cell.getSource();return new ky(t.x,t.y)}const e=this.sourceMagnet;return t.isEdgeElement(e)?new ky(this.sourceAnchor.x,this.sourceAnchor.y):t.getBBoxOfElement(e||t.container)}get targetBBox(){const t=this.targetView;if(!t){const t=this.cell.getTarget();return new ky(t.x,t.y)}const e=this.targetMagnet;return t.isEdgeElement(e)?new ky(this.targetAnchor.x,this.targetAnchor.y):t.getBBoxOfElement(e||t.container)}isEdgeView(){return!0}confirmUpdate(t,e={}){let n=t;if(this.hasAction(n,"source")){if(!this.updateTerminalProperties("source"))return n;n=this.removeAction(n,"source")}if(this.hasAction(n,"target")){if(!this.updateTerminalProperties("target"))return n;n=this.removeAction(n,"target")}return this.hasAction(n,"render")?(this.render(),n=this.removeAction(n,["render","update","labels","tools"]),n):(n=this.handleAction(n,"update",(()=>this.update(e))),n=this.handleAction(n,"labels",(()=>this.onLabelsChange(e))),n=this.handleAction(n,"tools",(()=>this.renderTools())),n)}render(){return this.empty(),this.renderMarkup(),this.labelContainer=null,this.renderLabels(),this.update(),this.renderTools(),this}renderMarkup(){const t=this.cell.markup;if(t){if("string"==typeof t)throw new TypeError("Not support string markup.");return this.renderJSONMarkup(t)}throw new TypeError("Invalid edge markup.")}renderJSONMarkup(t){const e=this.parseJSONMarkup(t,this.container);this.selectors=e.selectors,this.container.append(e.fragment)}customizeLabels(){if(this.labelContainer){const t=this.cell,e=t.labels;for(let n=0,r=e.length;n<r;n+=1){const r=e[n],i=this.labelCache[n],s=this.labelSelectors[n],o=this.graph.options.onEdgeLabelRendered;o&&o({edge:t,label:r,container:i,selectors:s})}}}renderLabels(){const t=this.cell,e=t.getLabels(),n=e.length;let r=this.labelContainer;if(this.labelCache={},this.labelSelectors={},n<=0)return r&&r.parentNode&&r.parentNode.removeChild(r),this;r?this.empty(r):(r=vp("g"),this.addClass(this.prefixClassName("edge-labels"),r),this.labelContainer=r);for(let n=0,i=e.length;n<i;n+=1){const i=e[n],s=this.normalizeLabelMarkup(this.parseLabelMarkup(i.markup));let o,a;if(s)o=s.node,a=s.selectors;else{const e=t.getDefaultLabel(),n=this.normalizeLabelMarkup(this.parseLabelMarkup(e.markup));o=n.node,a=n.selectors}o.setAttribute("data-index",`${n}`),r.appendChild(o);const l=this.rootSelector;if(a[l])throw new Error("Ambiguous label root selector.");a[l]=o,this.labelCache[n]=o,this.labelSelectors[n]=a}return null==r.parentNode&&this.container.appendChild(r),this.updateLabels(),this.customizeLabels(),this}onLabelsChange(t={}){this.shouldRerenderLabels(t)?this.renderLabels():this.updateLabels(),this.updateLabelPositions()}shouldRerenderLabels(t={}){const e=this.cell.previous("labels");if(null==e)return!0;if("propertyPathArray"in t&&"propertyValue"in t){const n=t.propertyPathArray||[],r=n.length;if(r>1){if(e[n[1]]){if(2===r)return"object"==typeof t.propertyValue&&dc(t.propertyValue,"markup");if("markup"!==n[2])return!1}}}return!0}parseLabelMarkup(t){return t?"string"==typeof t?this.parseLabelStringMarkup(t):this.parseJSONMarkup(t):null}parseLabelStringMarkup(t){const e=dm.createVectors(t),n=document.createDocumentFragment();for(let t=0,r=e.length;t<r;t+=1){const r=e[t].node;n.appendChild(r)}return{fragment:n,selectors:{}}}normalizeLabelMarkup(t){if(null==t)return;const e=t.fragment;if(!(e instanceof DocumentFragment&&e.hasChildNodes()))throw new Error("Invalid label markup.");let n;const r=e.childNodes;return n=r.length>1||"G"!==r[0].nodeName.toUpperCase()?dm.create("g").append(e):dm.create(r[0]),n.addClass(this.prefixClassName("edge-label")),{node:n.node,selectors:t.selectors}}updateLabels(){if(this.labelContainer){const t=this.cell,e=t.labels,n=this.can("edgeLabelMovable"),r=t.getDefaultLabel();for(let t=0,i=e.length;t<i;t+=1){const i=this.labelCache[t],s=this.labelSelectors[t];i.setAttribute("cursor",n?"move":"default");const o=e[t],a=Sh({},r.attrs,o.attrs);this.updateAttrs(i,a,{selectors:s,rootBBox:o.size?ky.fromSize(o.size):void 0})}}}renderTools(){const t=this.cell.getTools();return this.addTools(t),this}update(t={}){this.cleanCache(),this.updateConnection(t);const e=this.cell.getAttrs();return null!=e&&this.updateAttrs(this.container,e,{selectors:this.selectors}),this.updateLabelPositions(),this.updateTools(t),this}removeRedundantLinearVertices(t={}){const e=this.cell,n=e.getVertices(),r=[this.sourceAnchor,...n,this.targetAnchor],i=r.length,s=new Vy(r);s.simplify({threshold:.01});const o=s.points.map((t=>t.toJSON())),a=o.length;return i===a?0:(e.setVertices(o.slice(1,a-1),t),i-a)}getTerminalView(t){switch(t){case"source":return this.sourceView||null;case"target":return this.targetView||null;default:throw new Error(`Unknown terminal type '${t}'`)}}getTerminalAnchor(t){switch(t){case"source":return Ty.create(this.sourceAnchor);case"target":return Ty.create(this.targetAnchor);default:throw new Error(`Unknown terminal type '${t}'`)}}getTerminalConnectionPoint(t){switch(t){case"source":return Ty.create(this.sourcePoint);case"target":return Ty.create(this.targetPoint);default:throw new Error(`Unknown terminal type '${t}'`)}}getTerminalMagnet(t,e={}){switch(t){case"source":{if(e.raw)return this.sourceMagnet;const t=this.sourceView;return t?this.sourceMagnet||t.container:null}case"target":{if(e.raw)return this.targetMagnet;const t=this.targetView;return t?this.targetMagnet||t.container:null}default:throw new Error(`Unknown terminal type '${t}'`)}}updateConnection(t={}){const e=this.cell;if(t.translateBy&&e.isFragmentDescendantOf(t.translateBy)){const e=t.tx||0,n=t.ty||0;this.routePoints=new Vy(this.routePoints).translate(e,n).points,this.translateConnectionPoints(e,n),this.path.translate(e,n)}else{const t=e.getVertices(),n=this.findAnchors(t);this.sourceAnchor=n.source,this.targetAnchor=n.target,this.routePoints=this.findRoutePoints(t);const r=this.findConnectionPoints(this.routePoints,this.sourceAnchor,this.targetAnchor);this.sourcePoint=r.source,this.targetPoint=r.target;const i=this.findMarkerPoints(this.routePoints,this.sourcePoint,this.targetPoint);this.path=this.findPath(this.routePoints,i.source||this.sourcePoint,i.target||this.targetPoint)}this.cleanCache()}findAnchors(t){const e=this.cell,n=e.source,r=e.target,i=t[0],s=t[t.length-1];return r.priority&&!n.priority?this.findAnchorsOrdered("target",s,"source",i):this.findAnchorsOrdered("source",i,"target",s)}findAnchorsOrdered(t,e,n,r){let i,s;const o=this.cell,a=o[t],l=o[n],c=this.getTerminalView(t),h=this.getTerminalView(n),u=this.getTerminalMagnet(t),g=this.getTerminalMagnet(n);if(c){let n;n=e?Ty.create(e):h?g:Ty.create(l),i=this.getAnchor(a.anchor,c,u,n,t)}else i=Ty.create(a);if(h){const t=Ty.create(r||i);s=this.getAnchor(l.anchor,h,g,t,n)}else s=Ty.isPointLike(l)?Ty.create(l):new Ty;return{[t]:i,[n]:s}}getAnchor(t,e,n,r,i){const s=e.isEdgeElement(n),o=this.graph.options.connecting;let a,l="string"==typeof t?{name:t}:t;if(!l){const t=s?("source"===i?o.sourceEdgeAnchor:o.targetEdgeAnchor)||o.edgeAnchor:("source"===i?o.sourceAnchor:o.targetAnchor)||o.anchor;l="string"==typeof t?{name:t}:t}if(!l)throw new Error("Anchor should be specified.");const c=l.name;if(s){const t=Jx.registry.get(c);if("function"!=typeof t)return Jx.registry.onNotFound(c);a=ef(t,this,e,n,r,l.args||{},i)}else{const t=Gx.registry.get(c);if("function"!=typeof t)return Gx.registry.onNotFound(c);a=ef(t,this,e,n,r,l.args||{},i)}return a?a.round(this.POINT_ROUNDING):new Ty}findRoutePoints(t=[]){const e=this.graph.options.connecting.router||Tw.presets.normal,n=this.cell.getRouter()||e;let r;if("function"==typeof n)r=ef(n,this,t,{},this);else{const e="string"==typeof n?n:n.name,i="string"==typeof n?{}:n.args||{},s=e?Tw.registry.get(e):Tw.presets.normal;if("function"!=typeof s)return Tw.registry.onNotFound(e);r=ef(s,this,t,i,this)}return null==r?t.map((t=>Ty.create(t))):r.map((t=>Ty.create(t)))}findConnectionPoints(t,e,n){const r=this.cell,i=this.graph.options.connecting,s=r.getSource(),o=r.getTarget(),a=this.sourceView,l=this.targetView,c=t[0],h=t[t.length-1];let u,g;if(a&&!a.isEdgeElement(this.sourceMagnet)){const t=this.sourceMagnet||a.container,r=new Ny(c||n,e),o=s.connectionPoint||i.sourceConnectionPoint||i.connectionPoint;u=this.getConnectionPoint(o,a,t,r,"source")}else u=e;if(l&&!l.isEdgeElement(this.targetMagnet)){const t=this.targetMagnet||l.container,r=o.connectionPoint||i.targetConnectionPoint||i.connectionPoint,s=new Ny(h||e,n);g=this.getConnectionPoint(r,l,t,s,"target")}else g=n;return{source:u,target:g}}getConnectionPoint(t,e,n,r,i){const s=r.end;if(null==t)return s;const o="string"==typeof t?t:t.name,a="string"==typeof t?{}:t.args,l=Qx.registry.get(o);if("function"!=typeof l)return Qx.registry.onNotFound(o);const c=ef(l,this,r,e,n,a||{},i);return c?c.round(this.POINT_ROUNDING):s}findMarkerPoints(t,e,n){const r=t=>{const e=this.cell.getAttrs(),n=Object.keys(e);for(let r=0,i=n.length;r<i;r+=1){const i=e[n[r]];if(i[`${t}Marker`]||i[`${t}-marker`]){const t=i.strokeWidth||i["stroke-width"];if(t)return parseFloat(t);break}}return null},i=t[0],s=t[t.length-1];let o,a;const l=r("source");l&&(o=e.clone().move(i||n,-l));const c=r("target");return c&&(a=n.clone().move(s||e,-c)),this.sourceMarkerPoint=o||e.clone(),this.targetMarkerPoint=a||n.clone(),{source:o,target:a}}findPath(t,e,n){const r=this.cell.getConnector()||this.graph.options.connecting.connector;let i,s,o;if("string"==typeof r?i=r:(i=r.name,s=r.args),i){const t=Vw.registry.get(i);if("function"!=typeof t)return Vw.registry.onNotFound(i);o=t}else o=Vw.presets.normal;const a=ef(o,this,e,n,t,Object.assign(Object.assign({},s),{raw:!0}),this);return"string"==typeof a?Ky.parse(a):a}translateConnectionPoints(t,e){this.sourcePoint.translate(t,e),this.targetPoint.translate(t,e),this.sourceAnchor.translate(t,e),this.targetAnchor.translate(t,e),this.sourceMarkerPoint.translate(t,e),this.targetMarkerPoint.translate(t,e)}updateLabelPositions(){if(null==this.labelContainer)return this;if(!this.path)return this;const t=this.cell,e=t.getLabels();if(0===e.length)return this;const n=t.getDefaultLabel(),r=this.normalizeLabelPosition(n.position);for(let t=0,n=e.length;t<n;t+=1){const n=e[t],i=this.normalizeLabelPosition(n.position),s=Sh({},r,i),o=this.getLabelTransformationMatrix(s);this.labelCache[t].setAttribute("transform",Um(o))}return this}updateTerminalProperties(t){const e=this.cell,n=this.graph,r=e[t],i=r&&r.cell,s=`${t}View`;if(!i)return this[s]=null,this.updateTerminalMagnet(t),!0;const o=n.getCellById(i);if(!o)throw new Error(`Edge's ${t} node with id "${i}" not exists`);const a=o.findView(n);return!!a&&(this[s]=a,this.updateTerminalMagnet(t),!0)}updateTerminalMagnet(t){const e=`${t}Magnet`,n=this.getTerminalView(t);if(n){let r=n.getMagnetFromEdgeTerminal(this.cell[t]);r===n.container&&(r=null),this[e]=r}else this[e]=null}getLabelPositionAngle(t){const e=this.cell.getLabelAt(t);return e&&e.position&&"object"==typeof e.position&&e.position.angle||0}getLabelPositionArgs(t){const e=this.cell.getLabelAt(t);if(e&&e.position&&"object"==typeof e.position)return e.position.options}getDefaultLabelPositionArgs(){const t=this.cell.getDefaultLabel();if(t&&t.position&&"object"==typeof t.position)return t.position.options}mergeLabelPositionArgs(t,e){return null===t?null:void 0===t?null===e?null:e:Sh({},e,t)}getConnection(){return null!=this.path?this.path.clone():null}getConnectionPathData(){if(null==this.path)return"";const t=this.cache.pathCache;return dc(t,"data")||(t.data=this.path.serialize()),t.data||""}getConnectionSubdivisions(){if(null==this.path)return null;const t=this.cache.pathCache;return dc(t,"segmentSubdivisions")||(t.segmentSubdivisions=this.path.getSegmentSubdivisions()),t.segmentSubdivisions}getConnectionLength(){if(null==this.path)return 0;const t=this.cache.pathCache;return dc(t,"length")||(t.length=this.path.length({segmentSubdivisions:this.getConnectionSubdivisions()})),t.length}getPointAtLength(t){return null==this.path?null:this.path.pointAtLength(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getPointAtRatio(t){return null==this.path?null:(Bf(t)&&(t=parseFloat(t)/100),this.path.pointAt(t,{segmentSubdivisions:this.getConnectionSubdivisions()}))}getTangentAtLength(t){return null==this.path?null:this.path.tangentAtLength(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getTangentAtRatio(t){return null==this.path?null:this.path.tangentAt(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getClosestPoint(t){return null==this.path?null:this.path.closestPoint(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getClosestPointLength(t){return null==this.path?null:this.path.closestPointLength(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getClosestPointRatio(t){return null==this.path?null:this.path.closestPointNormalizedLength(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getLabelPosition(t,e,n,r){const i={distance:0};let s,o=0;"number"==typeof n?(o=n,s=r):s=n,null!=s&&(i.options=s);const a=s&&s.absoluteOffset,l=!(s&&s.absoluteDistance),c=s&&s.absoluteDistance&&s.reverseDistance,h=this.path,u={segmentSubdivisions:this.getConnectionSubdivisions()},g=new Ty(t,e),d=h.closestPointT(g,u),f=this.getConnectionLength()||0;let p,m,y=h.lengthAtT(d,u);if(l&&(y=f>0?y/f:0),c&&(y=-1*(f-y)||1),i.distance=y,a||(p=h.tangentAtT(d)),p)m=p.pointOffset(g);else{const t=h.pointAtT(d),e=g.diff(t);m={x:e.x,y:e.y}}return i.offset=m,i.angle=o,i}normalizeLabelPosition(t){return"number"==typeof t?{distance:t}:t}getLabelTransformationMatrix(e){const n=this.normalizeLabelPosition(e),r=n.options||{},i=n.angle||0,s=n.distance,o=s>0&&s<=1;let a=0;const l={x:0,y:0},c=n.offset;c&&("number"==typeof c?a=c:(null!=c.x&&(l.x=c.x),null!=c.y&&(l.y=c.y)));const h=0!==l.x||0!==l.y||0===a,u=r.keepGradient,g=r.ensureLegibility,d=this.path,f={segmentSubdivisions:this.getConnectionSubdivisions()},p=o?s*this.getConnectionLength():s,m=d.tangentAtLength(p,f);let y,v=i;if(m){if(h)y=m.start,y.translate(l);else{const t=m.clone();t.rotate(-90,m.start),t.setLength(a),y=t.end}u&&(v=m.angle()+i,g&&(v=t.Angle.normalize((v+90)%180-90)))}else y=d.start,h&&y.translate(l);return $m().translate(y.x,y.y).rotate(v)}getVertexIndex(t,e){const n=this.cell.getVertices(),r=this.getClosestPointLength(new Ty(t,e));let i=0;if(null!=r)for(const t=n.length;i<t;i+=1){const t=n[i],e=this.getClosestPointLength(t);if(null!=e&&r<e)break}return i}getEventArgs(t,e,n){const r=this,i=r.cell;return null==e||null==n?{e:t,view:r,edge:i,cell:i}:{e:t,x:e,y:n,view:r,edge:i,cell:i}}notifyUnhandledMouseDown(t,e,n){this.notify("edge:unhandled:mousedown",{e:t,x:e,y:n,view:this,cell:this.cell,edge:this.cell})}notifyMouseDown(t,e,n){super.onMouseDown(t,e,n),this.notify("edge:mousedown",this.getEventArgs(t,e,n))}notifyMouseMove(t,e,n){super.onMouseMove(t,e,n),this.notify("edge:mousemove",this.getEventArgs(t,e,n))}notifyMouseUp(t,e,n){super.onMouseUp(t,e,n),this.notify("edge:mouseup",this.getEventArgs(t,e,n))}onClick(t,e,n){super.onClick(t,e,n),this.notify("edge:click",this.getEventArgs(t,e,n))}onDblClick(t,e,n){super.onDblClick(t,e,n),this.notify("edge:dblclick",this.getEventArgs(t,e,n))}onContextMenu(t,e,n){super.onContextMenu(t,e,n),this.notify("edge:contextmenu",this.getEventArgs(t,e,n))}onMouseDown(t,e,n){this.notifyMouseDown(t,e,n),this.startEdgeDragging(t,e,n)}onMouseMove(t,e,n){const r=this.getEventData(t);switch(r.action){case"drag-label":this.dragLabel(t,e,n);break;case"drag-arrowhead":this.dragArrowhead(t,e,n);break;case"drag-edge":this.dragEdge(t,e,n)}return this.notifyMouseMove(t,e,n),r}onMouseUp(t,e,n){const r=this.getEventData(t);switch(r.action){case"drag-label":this.stopLabelDragging(t,e,n);break;case"drag-arrowhead":this.stopArrowheadDragging(t,e,n);break;case"drag-edge":this.stopEdgeDragging(t,e,n)}return this.notifyMouseUp(t,e,n),this.checkMouseleave(t),r}onMouseOver(t){super.onMouseOver(t),this.notify("edge:mouseover",this.getEventArgs(t))}onMouseOut(t){super.onMouseOut(t),this.notify("edge:mouseout",this.getEventArgs(t))}onMouseEnter(t){super.onMouseEnter(t),this.notify("edge:mouseenter",this.getEventArgs(t))}onMouseLeave(t){super.onMouseLeave(t),this.notify("edge:mouseleave",this.getEventArgs(t))}onMouseWheel(t,e,n,r){super.onMouseWheel(t,e,n,r),this.notify("edge:mousewheel",Object.assign({delta:r},this.getEventArgs(t,e,n)))}onCustomEvent(t,e,n,r){if(Mp(t.target,"edge-tool",this.container)){if(t.stopPropagation(),this.can("useEdgeTools")){if("edge:remove"===e)return void this.cell.remove({ui:!0});this.notify("edge:customevent",Object.assign({name:e},this.getEventArgs(t,n,r)))}this.notifyMouseDown(t,n,r)}else this.notify("edge:customevent",Object.assign({name:e},this.getEventArgs(t,n,r))),super.onCustomEvent(t,e,n,r)}onLabelMouseDown(t,e,n){this.notifyMouseDown(t,e,n),this.startLabelDragging(t,e,n);this.getEventData(t).stopPropagation&&t.stopPropagation()}startEdgeDragging(t,e,n){this.can("edgeMovable")?this.setEventData(t,{x:e,y:n,moving:!1,action:"drag-edge"}):this.notifyUnhandledMouseDown(t,e,n)}dragEdge(t,e,n){const r=this.getEventData(t);r.moving||(r.moving=!0,this.addClass("edge-moving"),this.notify("edge:move",{e:t,x:e,y:n,view:this,cell:this.cell,edge:this.cell})),this.cell.translate(e-r.x,n-r.y,{ui:!0}),this.setEventData(t,{x:e,y:n}),this.notify("edge:moving",{e:t,x:e,y:n,view:this,cell:this.cell,edge:this.cell})}stopEdgeDragging(t,e,n){const r=this.getEventData(t);r.moving&&(this.removeClass("edge-moving"),this.notify("edge:moved",{e:t,x:e,y:n,view:this,cell:this.cell,edge:this.cell})),r.moving=!1}prepareArrowheadDragging(t,e){const n=this.getTerminalMagnet(t),r={action:"drag-arrowhead",x:e.x,y:e.y,isNewEdge:!0===e.isNewEdge,terminalType:t,initialMagnet:n,initialTerminal:yo(this.cell[t]),fallbackAction:e.fallbackAction||"revert",getValidateConnectionArgs:this.createValidateConnectionArgs(t),options:e.options};return this.beforeArrowheadDragging(r),r}createValidateConnectionArgs(t){const e=[];let n;e[4]=t,e[5]=this;let r=0,i=0;"source"===t?(r=2,n="target"):(i=2,n="source");const s=this.cell[n],o=s.cell;if(o){let t;const n=e[r]=this.graph.findViewByCell(o);n&&(t=n.getMagnetFromEdgeTerminal(s),t===n.container&&(t=void 0)),e[r+1]=t}return(t,n)=>(e[i]=t,e[i+1]=t.container===n?void 0:n,e)}beforeArrowheadDragging(t){t.zIndex=this.cell.zIndex,this.cell.toFront();const e=this.container.style;t.pointerEvents=e.pointerEvents,e.pointerEvents="none",this.graph.options.connecting.highlight&&this.highlightAvailableMagnets(t)}afterArrowheadDragging(t){null!=t.zIndex&&(this.cell.setZIndex(t.zIndex,{ui:!0}),t.zIndex=null);this.container.style.pointerEvents=t.pointerEvents||"",this.graph.options.connecting.highlight&&this.unhighlightAvailableMagnets(t)}validateConnection(t,e,n,r,i,s,o){const a=this.graph.options.connecting,l=a.allowLoop,c=a.allowNode,h=a.allowEdge,u=a.allowPort,g=a.allowMulti,d=a.validateConnection,f=s?s.cell:null,p="target"===i?n:t,m="target"===i?r:e;let y=!0;const v=a=>{const l="source"===i?o?o.port:null:f?f.getSourcePortId():null,c="target"===i?o?o.port:null:f?f.getTargetPortId():null;return ef(a,this.graph,{edge:f,edgeView:s,sourceView:t,targetView:n,sourcePort:l,targetPort:c,sourceMagnet:e,targetMagnet:r,sourceCell:t?t.cell:null,targetCell:n?n.cell:null,type:i})};if(null!=l&&("boolean"==typeof l?l||t!==n||(y=!1):y=v(l)),y&&null!=u&&("boolean"==typeof u?!u&&m&&(y=!1):y=v(u)),y&&null!=h&&("boolean"==typeof h?!h&&uA.isEdgeView(p)&&(y=!1):y=v(h)),y&&null!=c&&null==m&&("boolean"==typeof c?!c&&hA.isNodeView(p)&&(y=!1):y=v(c)),y&&null!=g&&s){const t=s.cell,e="source"===i?o:t.getSource(),n="target"===i?o:t.getTarget(),r=o?this.graph.getCellById(o.cell):null;if(e&&n&&e.cell&&n.cell&&r)if("function"==typeof g)y=v(g);else{const t=this.graph.model.getConnectedEdges(r,{outgoing:"source"===i,incoming:"target"===i});if(t.length)if("withPort"===g){t.some((t=>{const r=t.getSource(),i=t.getTarget();return r&&i&&r.cell===e.cell&&i.cell===n.cell&&null!=r.port&&r.port===e.port&&null!=i.port&&i.port===n.port}))&&(y=!1)}else if(!g){t.some((t=>{const r=t.getSource(),i=t.getTarget();return r&&i&&r.cell===e.cell&&i.cell===n.cell}))&&(y=!1)}}}return y&&null!=d&&(y=v(d)),y}allowConnectToBlank(t){const e=this.graph,n=e.options.connecting.allowBlank;if("function"!=typeof n)return!!n;const r=e.findViewByCell(t),i=t.getSourceCell(),s=t.getTargetCell(),o=e.findViewByCell(i),a=e.findViewByCell(s);return ef(n,e,{edge:t,edgeView:r,sourceCell:i,targetCell:s,sourceView:o,targetView:a,sourcePort:t.getSourcePortId(),targetPort:t.getTargetPortId(),sourceMagnet:r.sourceMagnet,targetMagnet:r.targetMagnet})}validateEdge(t,e,n){const r=this.graph;if(!this.allowConnectToBlank(t)){const e=t.getSourceCellId(),n=t.getTargetCellId();if(!e||!n)return!1}const i=r.options.connecting.validateEdge;return!i||ef(i,r,{edge:t,type:e,previous:n})}arrowheadDragging(t,e,n,r){r.x=e,r.y=n,r.currentTarget!==t&&(r.currentMagnet&&r.currentView&&r.currentView.unhighlight(r.currentMagnet,{type:"magnetAdsorbed"}),r.currentView=this.graph.findViewByElem(t),r.currentView?(r.currentMagnet=r.currentView.findMagnet(t),r.currentMagnet&&this.validateConnection(...r.getValidateConnectionArgs(r.currentView,r.currentMagnet),r.currentView.getEdgeTerminal(r.currentMagnet,e,n,this.cell,r.terminalType))?r.currentView.highlight(r.currentMagnet,{type:"magnetAdsorbed"}):r.currentMagnet=null):r.currentMagnet=null),r.currentTarget=t,this.cell.prop(r.terminalType,{x:e,y:n},Object.assign(Object.assign({},r.options),{ui:!0}))}arrowheadDragged(t,e,n){const r=t.currentView,i=t.currentMagnet;if(!i||!r)return;r.unhighlight(i,{type:"magnetAdsorbed"});const s=t.terminalType,o=r.getEdgeTerminal(i,e,n,this.cell,s);this.cell.setTerminal(s,o,{ui:!0})}snapArrowhead(t,e,n){const r=this.graph,{snap:i,allowEdge:s}=r.options.connecting,o="object"==typeof i&&i.radius||50,a=r.renderer.findViewsInArea({x:t-o,y:e-o,width:2*o,height:2*o},{nodeOnly:!0});if(s){const n=r.renderer.findEdgeViewsFromPoint({x:t,y:e},o).filter((t=>t!==this));a.push(...n)}const l=n.closestView||null,c=n.closestMagnet||null;let h;n.closestView=null,n.closestMagnet=null;let u=Number.MAX_SAFE_INTEGER;const g=new Ty(t,e);let d;a.forEach((r=>{if("false"!==r.container.getAttribute("magnet")){if(r.isNodeView())h=r.cell.getBBox().getCenter().distance(g);else if(r.isEdgeView()){const t=r.getClosestPoint(g);h=t?t.distance(g):Number.MAX_SAFE_INTEGER}h<o&&h<u&&(c===r.container||this.validateConnection(...n.getValidateConnectionArgs(r,null),r.getEdgeTerminal(r.container,t,e,this.cell,n.terminalType)))&&(u=h,n.closestView=r,n.closestMagnet=r.container)}r.container.querySelectorAll("[magnet]").forEach((i=>{if("false"!==i.getAttribute("magnet")){const s=r.getBBoxOfElement(i);h=g.distance(s.getCenter()),h<o&&h<u&&(c===i||this.validateConnection(...n.getValidateConnectionArgs(r,i),r.getEdgeTerminal(i,t,e,this.cell,n.terminalType)))&&(u=h,n.closestView=r,n.closestMagnet=i)}}))}));const f=n.terminalType,p=n.closestView,m=n.closestMagnet,y=c!==m;if(l&&y&&l.unhighlight(c,{type:"magnetAdsorbed"}),p){if(!y)return;p.highlight(m,{type:"magnetAdsorbed"}),d=p.getEdgeTerminal(m,t,e,this.cell,f)}else d={x:t,y:e};this.cell.setTerminal(f,d,{},Object.assign(Object.assign({},n.options),{ui:!0}))}snapArrowheadEnd(t){const e=t.closestView,n=t.closestMagnet;e&&n&&(e.unhighlight(n,{type:"magnetAdsorbed"}),t.currentMagnet=e.findMagnet(n)),t.closestView=null,t.closestMagnet=null}finishEmbedding(t){this.graph.options.embedding.enabled&&this.cell.updateParent()&&(t.zIndex=null)}fallbackConnection(t){if("remove"===t.fallbackAction)this.cell.remove({ui:!0});else this.cell.prop(t.terminalType,t.initialTerminal,{ui:!0})}notifyConnectionEvent(t,e){const n=t.terminalType,r=t.initialTerminal,i=this.cell[n];if(i&&!Jw.equalTerminals(r,i)){const s=this.graph,o=r,a=o.cell?s.getCellById(o.cell):null,l=o.port,c=a?s.findViewByCell(a):null,h=a||t.isNewEdge?null:Ty.create(r).toJSON(),u=i,g=u.cell?s.getCellById(u.cell):null,d=u.port,f=g?s.findViewByCell(g):null,p=g?null:Ty.create(i).toJSON();this.notify("edge:connected",{e:e,previousCell:a,previousPort:l,previousView:c,previousPoint:h,currentCell:g,currentView:f,currentPort:d,currentPoint:p,previousMagnet:t.initialMagnet,currentMagnet:t.currentMagnet,edge:this.cell,view:this,type:n,isNew:t.isNewEdge})}}highlightAvailableMagnets(t){const e=this.graph,n=e.model.getCells();t.marked={};for(let r=0,i=n.length;r<i;r+=1){const i=e.findViewByCell(n[r]);if(!i||i.cell.id===this.cell.id)continue;const s=Array.prototype.slice.call(i.container.querySelectorAll("[magnet]"));"false"!==i.container.getAttribute("magnet")&&s.push(i.container);const o=s.filter((e=>this.validateConnection(...t.getValidateConnectionArgs(i,e),i.getEdgeTerminal(e,t.x,t.y,this.cell,t.terminalType))));if(o.length>0){for(let t=0,e=o.length;t<e;t+=1)i.highlight(o[t],{type:"magnetAvailable"});i.highlight(null,{type:"nodeAvailable"}),t.marked[i.cell.id]=o}}}unhighlightAvailableMagnets(t){const e=t.marked||{};Object.keys(e).forEach((t=>{const n=this.graph.findViewByCell(t);if(n){e[t].forEach((t=>{n.unhighlight(t,{type:"magnetAvailable"})})),n.unhighlight(null,{type:"nodeAvailable"})}})),t.marked=null}startArrowheadDragging(t,e,n){if(!this.can("arrowheadMovable"))return void this.notifyUnhandledMouseDown(t,e,n);const r=t.target.getAttribute("data-terminal"),i=this.prepareArrowheadDragging(r,{x:e,y:n});this.setEventData(t,i)}dragArrowhead(t,e,n){const r=this.getEventData(t);this.graph.options.connecting.snap?this.snapArrowhead(e,n,r):this.arrowheadDragging(this.getEventTarget(t),e,n,r)}stopArrowheadDragging(t,e,n){const r=this.graph,i=this.getEventData(t);r.options.connecting.snap?this.snapArrowheadEnd(i):this.arrowheadDragged(i,e,n);this.validateEdge(this.cell,i.terminalType,i.initialTerminal)?(this.finishEmbedding(i),this.notifyConnectionEvent(i,t)):this.fallbackConnection(i),this.afterArrowheadDragging(i)}startLabelDragging(t,e,n){if(this.can("edgeLabelMovable")){const e=t.currentTarget,n=parseInt(e.getAttribute("data-index"),10),r=this.getLabelPositionAngle(n),i=this.getLabelPositionArgs(n),s=this.getDefaultLabelPositionArgs(),o=this.mergeLabelPositionArgs(i,s);this.setEventData(t,{index:n,positionAngle:r,positionArgs:o,stopPropagation:!0,action:"drag-label"})}else this.setEventData(t,{stopPropagation:!0});this.graph.view.delegateDragEvents(t,this)}dragLabel(t,e,n){const r=this.getEventData(t),i=this.cell.getLabelAt(r.index),s=Sh({},i,{position:this.getLabelPosition(e,n,r.positionAngle,r.positionArgs)});this.cell.setLabelAt(r.index,s)}stopLabelDragging(t,e,n){}}!function(t){t.toStringTag=`X6.${t.name}`,t.isEdgeView=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"==typeof r.isNodeView&&"function"==typeof r.isEdgeView&&"function"==typeof r.confirmUpdate&&"function"==typeof r.update&&"function"==typeof r.getConnection}}(uA||(uA={})),uA.config({isSvgElement:!0,priority:1,bootstrap:["render","source","target"],actions:{view:["render"],markup:["render"],attrs:["update"],source:["source","update"],target:["target","update"],router:["update"],connector:["update"],labels:["labels"],defaultLabel:["labels"],tools:["tools"],vertices:["vertices","update"]}}),uA.registry.register("edge",uA,!0);class gA extends nx{get options(){return this.graph.options}constructor(e){super(),this.graph=e;const{selectors:n,fragment:r}=t.Markup.parseJSONMarkup(gA.markup);this.background=n.background,this.grid=n.grid,this.svg=n.svg,this.defs=n.defs,this.viewport=n.viewport,this.primer=n.primer,this.stage=n.stage,this.decorator=n.decorator,this.overlay=n.overlay,this.container=this.options.container,this.restore=gA.snapshoot(this.container),ap(this.container,this.prefixClassName("graph")),Tp(this.container,r),this.delegateEvents()}delegateEvents(){const t=this.constructor;return super.delegateEvents(t.events),this}guard(t,e){return"mousedown"===t.type&&2===t.button||(!(!this.options.guard||!this.options.guard(t,e))||(t.data&&void 0!==t.data.guarded?t.data.guarded:!(e&&e.cell&&qw.isCell(e.cell))&&(this.svg!==t.target&&this.container!==t.target&&!this.svg.contains(t.target))))}findView(t){return this.graph.findViewByElem(t)}onDblClick(t){this.options.preventDefaultDblClick&&t.preventDefault();const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=this.graph.snapToGrid(e.clientX,e.clientY);n?n.onDblClick(e,r.x,r.y):this.graph.trigger("blank:dblclick",{e:e,x:r.x,y:r.y})}onClick(t){if(this.getMouseMovedCount(t)<=this.options.clickThreshold){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=this.graph.snapToGrid(e.clientX,e.clientY);n?n.onClick(e,r.x,r.y):this.graph.trigger("blank:click",{e:e,x:r.x,y:r.y})}}isPreventDefaultContextMenu(t){let e=this.options.preventDefaultContextMenu;return"function"==typeof e&&(e=ef(e,this.graph,{view:t})),e}onContextMenu(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.isPreventDefaultContextMenu(n)&&t.preventDefault(),this.guard(e,n))return;const r=this.graph.snapToGrid(e.clientX,e.clientY);n?n.onContextMenu(e,r.x,r.y):this.graph.trigger("blank:contextmenu",{e:e,x:r.x,y:r.y})}delegateDragEvents(t,e){null==t.data&&(t.data={}),this.setEventData(t,{currentView:e||null,mouseMovedCount:0,startPosition:{x:t.clientX,y:t.clientY}});const n=this.constructor;this.delegateDocumentEvents(n.documentEvents,t.data),this.undelegateEvents()}getMouseMovedCount(t){return this.getEventData(t).mouseMovedCount||0}onMouseDown(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;this.options.preventDefaultMouseDown&&t.preventDefault();const r=this.graph.snapToGrid(e.clientX,e.clientY);n?n.onMouseDown(e,r.x,r.y):(this.options.preventDefaultBlankAction&&["touchstart"].includes(e.type)&&t.preventDefault(),this.graph.trigger("blank:mousedown",{e:e,x:r.x,y:r.y})),this.delegateDragEvents(e,n)}onMouseMove(t){const e=this.getEventData(t),n=e.startPosition;if(n&&n.x===t.clientX&&n.y===t.clientY)return;null==e.mouseMovedCount&&(e.mouseMovedCount=0),e.mouseMovedCount+=1;if(e.mouseMovedCount<=this.options.moveThreshold)return;const r=this.normalizeEvent(t),i=this.graph.snapToGrid(r.clientX,r.clientY),s=e.currentView;s?s.onMouseMove(r,i.x,i.y):this.graph.trigger("blank:mousemove",{e:r,x:i.x,y:i.y}),this.setEventData(r,e)}onMouseUp(t){this.undelegateDocumentEvents();const e=this.normalizeEvent(t),n=this.graph.snapToGrid(e.clientX,e.clientY),r=this.getEventData(t).currentView;if(r?r.onMouseUp(e,n.x,n.y):this.graph.trigger("blank:mouseup",{e:e,x:n.x,y:n.y}),!t.isPropagationStopped()){const e=new ay(t,{type:"click",data:t.data});this.onClick(e)}t.stopImmediatePropagation(),this.delegateEvents()}onMouseOver(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(!this.guard(e,n))if(n)n.onMouseOver(e);else{if(this.container===e.target)return;this.graph.trigger("blank:mouseover",{e:e})}}onMouseOut(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(!this.guard(e,n))if(n)n.onMouseOut(e);else{if(this.container===e.target)return;this.graph.trigger("blank:mouseout",{e:e})}}onMouseEnter(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=this.graph.findViewByElem(e.relatedTarget);if(n){if(r===n)return;n.onMouseEnter(e)}else{if(r)return;this.graph.trigger("graph:mouseenter",{e:e})}}onMouseLeave(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=this.graph.findViewByElem(e.relatedTarget);if(n){if(r===n)return;n.onMouseLeave(e)}else{if(r)return;this.graph.trigger("graph:mouseleave",{e:e})}}onMouseWheel(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=e.originalEvent,i=this.graph.snapToGrid(r.clientX,r.clientY),s=Math.max(-1,Math.min(1,r.wheelDelta||-r.detail));n?n.onMouseWheel(e,i.x,i.y,s):this.graph.trigger("blank:mousewheel",{e:e,delta:s,x:i.x,y:i.y})}onCustomEvent(t){const e=t.currentTarget,n=e.getAttribute("event")||e.getAttribute("data-event");if(n){const r=this.findView(e);if(r){const e=this.normalizeEvent(t);if(this.guard(e,r))return;const i=this.graph.snapToGrid(e.clientX,e.clientY);r.onCustomEvent(e,n,i.x,i.y)}}}handleMagnetEvent(t,e){const n=t.currentTarget,r=n.getAttribute("magnet");if(r&&"false"!==r.toLowerCase()){const r=this.findView(n);if(r){const i=this.normalizeEvent(t);if(this.guard(i,r))return;const s=this.graph.snapToGrid(i.clientX,i.clientY);ef(e,this.graph,r,i,n,s.x,s.y)}}}onMagnetMouseDown(t){this.handleMagnetEvent(t,((t,e,n,r,i)=>{t.onMagnetMouseDown(e,n,r,i)}))}onMagnetDblClick(t){this.handleMagnetEvent(t,((t,e,n,r,i)=>{t.onMagnetDblClick(e,n,r,i)}))}onMagnetContextMenu(t){const e=this.findView(t.target);this.isPreventDefaultContextMenu(e)&&t.preventDefault(),this.handleMagnetEvent(t,((t,e,n,r,i)=>{t.onMagnetContextMenu(e,n,r,i)}))}onLabelMouseDown(t){const e=t.currentTarget,n=this.findView(e);if(n){const e=this.normalizeEvent(t);if(this.guard(e,n))return;const r=this.graph.snapToGrid(e.clientX,e.clientY);n.onLabelMouseDown(e,r.x,r.y)}}onImageDragStart(){return!1}dispose(){this.undelegateEvents(),this.undelegateDocumentEvents(),this.restore(),this.restore=()=>{}}}n([nx.dispose()],gA.prototype,"dispose",null),function(t){const e=`${Nb.prefixCls}-graph`;t.markup=[{ns:fp.xhtml,tagName:"div",selector:"background",className:`${e}-background`},{ns:fp.xhtml,tagName:"div",selector:"grid",className:`${e}-grid`},{ns:fp.svg,tagName:"svg",selector:"svg",className:`${e}-svg`,attrs:{width:"100%",height:"100%","xmlns:xlink":fp.xlink},children:[{tagName:"defs",selector:"defs"},{tagName:"g",selector:"viewport",className:`${e}-svg-viewport`,children:[{tagName:"g",selector:"primer",className:`${e}-svg-primer`},{tagName:"g",selector:"stage",className:`${e}-svg-stage`},{tagName:"g",selector:"decorator",className:`${e}-svg-decorator`},{tagName:"g",selector:"overlay",className:`${e}-svg-overlay`}]}]}],t.snapshoot=function(t){const e=t.cloneNode();return t.childNodes.forEach((t=>e.appendChild(t))),()=>{for(Op(t);t.attributes.length>0;)t.removeAttribute(t.attributes[0].name);for(let n=0,r=e.attributes.length;n<r;n+=1){const r=e.attributes[n];t.setAttribute(r.name,r.value)}e.childNodes.forEach((e=>t.appendChild(e)))}}}(gA||(gA={})),function(t){const e=Nb.prefixCls;t.events={dblclick:"onDblClick",contextmenu:"onContextMenu",touchstart:"onMouseDown",mousedown:"onMouseDown",mouseover:"onMouseOver",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",mousewheel:"onMouseWheel",DOMMouseScroll:"onMouseWheel",[`mouseenter  .${e}-cell`]:"onMouseEnter",[`mouseleave  .${e}-cell`]:"onMouseLeave",[`mouseenter  .${e}-cell-tools`]:"onMouseEnter",[`mouseleave  .${e}-cell-tools`]:"onMouseLeave",[`mousedown   .${e}-cell [event]`]:"onCustomEvent",[`touchstart  .${e}-cell [event]`]:"onCustomEvent",[`mousedown   .${e}-cell [data-event]`]:"onCustomEvent",[`touchstart  .${e}-cell [data-event]`]:"onCustomEvent",[`dblclick    .${e}-cell [magnet]`]:"onMagnetDblClick",[`contextmenu .${e}-cell [magnet]`]:"onMagnetContextMenu",[`mousedown   .${e}-cell [magnet]`]:"onMagnetMouseDown",[`touchstart  .${e}-cell [magnet]`]:"onMagnetMouseDown",[`dblclick    .${e}-cell [data-magnet]`]:"onMagnetDblClick",[`contextmenu .${e}-cell [data-magnet]`]:"onMagnetContextMenu",[`mousedown   .${e}-cell [data-magnet]`]:"onMagnetMouseDown",[`touchstart  .${e}-cell [data-magnet]`]:"onMagnetMouseDown",[`dragstart   .${e}-cell image`]:"onImageDragStart",[`mousedown   .${e}-edge .${e}-edge-label`]:"onLabelMouseDown",[`touchstart  .${e}-edge .${e}-edge-label`]:"onLabelMouseDown"},t.documentEvents={mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"}}(gA||(gA={}));class dA extends r{get options(){return this.graph.options}get model(){return this.graph.model}get view(){return this.graph.view}constructor(t){super(),this.graph=t,this.init()}init(){}}class fA extends dA{init(){Nb.autoInsertCSS&&Py("core",".x6-graph {\n  position: relative;\n  overflow: hidden;\n  outline: none;\n  touch-action: none;\n}\n.x6-graph-background,\n.x6-graph-grid,\n.x6-graph-svg {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n.x6-graph-background-stage,\n.x6-graph-grid-stage,\n.x6-graph-svg-stage {\n  user-select: none;\n}\n.x6-graph.x6-graph-pannable {\n  cursor: grab;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n}\n.x6-graph.x6-graph-panning {\n  cursor: grabbing;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n  user-select: none;\n}\n.x6-node {\n  cursor: move;\n  /* stylelint-disable-next-line */\n}\n.x6-node.x6-node-immovable {\n  cursor: default;\n}\n.x6-node * {\n  -webkit-user-drag: none;\n}\n.x6-node .scalable * {\n  vector-effect: non-scaling-stroke;\n}\n.x6-node [magnet='true'] {\n  cursor: crosshair;\n  transition: opacity 0.3s;\n}\n.x6-node [magnet='true']:hover {\n  opacity: 0.7;\n}\n.x6-node foreignObject {\n  display: block;\n  overflow: visible;\n  background-color: transparent;\n}\n.x6-node foreignObject > body {\n  position: static;\n  width: 100%;\n  height: 100%;\n  margin: 0;\n  padding: 0;\n  overflow: visible;\n  background-color: transparent;\n}\n.x6-edge .source-marker,\n.x6-edge .target-marker {\n  vector-effect: non-scaling-stroke;\n}\n.x6-edge .connection {\n  stroke-linejoin: round;\n  fill: none;\n}\n.x6-edge .connection-wrap {\n  cursor: move;\n  opacity: 0;\n  fill: none;\n  stroke: #000;\n  stroke-width: 15;\n  stroke-linecap: round;\n  stroke-linejoin: round;\n}\n.x6-edge .connection-wrap:hover {\n  opacity: 0.4;\n  stroke-opacity: 0.4;\n}\n.x6-edge .vertices {\n  cursor: move;\n  opacity: 0;\n}\n.x6-edge .vertices .vertex {\n  fill: #1abc9c;\n}\n.x6-edge .vertices .vertex :hover {\n  fill: #34495e;\n  stroke: none;\n}\n.x6-edge .vertices .vertex-remove {\n  cursor: pointer;\n  fill: #fff;\n}\n.x6-edge .vertices .vertex-remove-area {\n  cursor: pointer;\n  opacity: 0.1;\n}\n.x6-edge .vertices .vertex-group:hover .vertex-remove-area {\n  opacity: 1;\n}\n.x6-edge .arrowheads {\n  cursor: move;\n  opacity: 0;\n}\n.x6-edge .arrowheads .arrowhead {\n  fill: #1abc9c;\n}\n.x6-edge .arrowheads .arrowhead :hover {\n  fill: #f39c12;\n  stroke: none;\n}\n.x6-edge .tools {\n  cursor: pointer;\n  opacity: 0;\n}\n.x6-edge .tools .tool-options {\n  display: none;\n}\n.x6-edge .tools .tool-remove circle {\n  fill: #f00;\n}\n.x6-edge .tools .tool-remove path {\n  fill: #fff;\n}\n.x6-edge:hover .vertices,\n.x6-edge:hover .arrowheads,\n.x6-edge:hover .tools {\n  opacity: 1;\n}\n.x6-highlight-opacity {\n  opacity: 0.3;\n}\n.x6-cell-tool-editor {\n  position: relative;\n  display: inline-block;\n  min-height: 1em;\n  margin: 0;\n  padding: 0;\n  line-height: 1;\n  white-space: normal;\n  text-align: center;\n  vertical-align: top;\n  overflow-wrap: normal;\n  outline: none;\n  transform-origin: 0 0;\n  -webkit-user-drag: none;\n}\n.x6-edge-tool-editor {\n  border: 1px solid #275fc5;\n  border-radius: 2px;\n}\n")}dispose(){Cy("core")}}var pA,mA;n([fA.dispose()],fA.prototype,"dispose",null),function(t){t.get=function(n){const{grid:r,panning:i,mousewheel:s,embedding:o}=n,a=e(n,["grid","panning","mousewheel","embedding"]),l=n.container;if(null==l)throw new Error("Ensure the container of the graph is specified and valid");null==a.width&&(a.width=l.clientWidth),null==a.height&&(a.height=l.clientHeight);const c=Sh({},t.defaults,a),h={size:10,visible:!1};return c.grid="number"==typeof r?{size:r,visible:!1}:"boolean"==typeof r?Object.assign(Object.assign({},h),{visible:r}):Object.assign(Object.assign({},h),r),["panning","mousewheel","embedding"].forEach((t=>{const e=n[t];"boolean"==typeof e?c[t].enabled=e:c[t]=Object.assign(Object.assign({},c[t]),e)})),c}}(pA||(pA={})),function(t){t.defaults={x:0,y:0,scaling:{min:.01,max:16},grid:{size:10,visible:!1},background:!1,panning:{enabled:!1,eventTypes:["leftMouseDown"]},mousewheel:{enabled:!1,factor:1.2,zoomAtMousePosition:!0},highlighting:{default:{name:"stroke",args:{padding:3}},nodeAvailable:{name:"className",args:{className:Nb.prefix("available-node")}},magnetAvailable:{name:"className",args:{className:Nb.prefix("available-magnet")}}},connecting:{snap:!1,allowLoop:!0,allowNode:!0,allowEdge:!1,allowPort:!0,allowBlank:!0,allowMulti:!0,highlight:!1,anchor:"center",edgeAnchor:"ratio",connectionPoint:"boundary",router:"normal",connector:"normal",validateConnection:({type:t,sourceView:e,targetView:n})=>null!=("target"===t?n:e),createEdge:()=>new eA},translating:{restrict:!1},embedding:{enabled:!1,findParent:"bbox",frontOnly:!0,validate:()=>!0},moveThreshold:0,clickThreshold:0,magnetThreshold:0,preventDefaultDblClick:!0,preventDefaultMouseDown:!1,preventDefaultContextMenu:!0,preventDefaultBlankAction:!0,interacting:{edgeLabelMovable:!1},async:!0,virtual:!1,guard:()=>!1}}(pA||(pA={}));class yA extends dA{get elem(){return this.view.grid}get grid(){return this.options.grid}init(){this.startListening(),this.draw(this.grid)}startListening(){this.graph.on("scale",this.update,this),this.graph.on("translate",this.update,this)}stopListening(){this.graph.off("scale",this.update,this),this.graph.off("translate",this.update,this)}setVisible(t){this.grid.visible!==t&&(this.grid.visible=t,this.update())}getGridSize(){return this.grid.size}setGridSize(t){this.grid.size=Math.max(t,1),this.update()}show(){this.setVisible(!0),this.update()}hide(){this.setVisible(!1),this.update()}clear(){this.elem.style.backgroundImage=""}draw(t){this.clear(),this.instance=null,Object.assign(this.grid,t),this.patterns=this.resolveGrid(t),this.update()}update(t={}){const n=this.grid.size;if(n<=1||!this.grid.visible)return this.clear();const r=this.graph.matrix(),i=this.getInstance(),s=Array.isArray(t)?t:[t];this.patterns.forEach(((t,o)=>{const a=`pattern_${o}`,l=r.a||1,c=r.d||1,{update:h,markup:u}=t,g=e(t,["update","markup"]),d=Object.assign(Object.assign(Object.assign({},g),s[o]),{sx:l,sy:c,ox:r.e||0,oy:r.f||0,width:n*l,height:n*c});i.has(a)||i.add(a,dm.create("pattern",{id:a,patternUnits:"userSpaceOnUse"},dm.createVectors(u)).node);const f=i.get(a);"function"==typeof h&&h(f.childNodes[0],d);let p=d.ox%d.width;p<0&&(p+=d.width);let m=d.oy%d.height;m<0&&(m+=d.height),zp(f,{x:p,y:m,width:d.width,height:d.height})}));const o=(new XMLSerializer).serializeToString(i.root),a=`url(data:image/svg+xml;base64,${btoa(o)})`;this.elem.style.backgroundImage=a}getInstance(){return this.instance||(this.instance=new sv),this.instance}resolveGrid(t){if(!t)return[];const e=t.type;if(null==e)return[Object.assign(Object.assign({},sv.presets.dot),t.args)];const n=sv.registry.get(e);if(n){let e=t.args||[];return Array.isArray(e)||(e=[e]),Array.isArray(n)?n.map(((t,n)=>Object.assign(Object.assign({},t),e[n]))):[Object.assign(Object.assign({},n),e[0])]}return sv.registry.onNotFound(e)}dispose(){this.stopListening(),this.clear()}}n([dA.dispose()],yA.prototype,"dispose",null);class vA extends dA{get container(){return this.graph.view.container}get viewport(){return this.graph.view.viewport}get stage(){return this.graph.view.stage}init(){this.resize()}getMatrix(){const t=this.viewport.getAttribute("transform");return t!==this.viewportTransformString&&(this.viewportMatrix=this.viewport.getCTM(),this.viewportTransformString=t),$m(this.viewportMatrix)}setMatrix(t){const e=$m(t),n=Um(e);this.viewport.setAttribute("transform",n),this.viewportMatrix=e,this.viewportTransformString=n}resize(t,e){let n=void 0===t?this.options.width:t,r=void 0===e?this.options.height:e;this.options.width=n,this.options.height=r,"number"==typeof n&&(n=Math.round(n)),"number"==typeof r&&(r=Math.round(r)),this.container.style.width=null==n?"":`${n}px`,this.container.style.height=null==r?"":`${r}px`;const i=this.getComputedSize();return this.graph.trigger("resize",Object.assign({},i)),this}getComputedSize(){let t=this.options.width,e=this.options.height;return th(t)||(t=this.container.clientWidth),th(e)||(e=this.container.clientHeight),{width:t,height:e}}getScale(){return Jm(this.getMatrix())}scale(t,e=t,n=0,r=0){if(t=this.clampScale(t),e=this.clampScale(e),n||r){const i=this.getTranslation(),s=i.tx-n*(t-1),o=i.ty-r*(e-1);s===i.tx&&o===i.ty||this.translate(s,o)}const i=this.getMatrix();return i.a=t,i.d=e,this.setMatrix(i),this.graph.trigger("scale",{sx:t,sy:e,ox:n,oy:r}),this}clampScale(t){const e=this.graph.options.scaling;return Hi(t,e.min||.01,e.max||16)}getZoom(){return this.getScale().sx}zoom(t,e){e=e||{};let n=t,r=t;const i=this.getScale(),s=this.getComputedSize();let o=s.width/2,a=s.height/2;if(e.absolute||(n+=i.sx,r+=i.sy),e.scaleGrid&&(n=Math.round(n/e.scaleGrid)*e.scaleGrid,r=Math.round(r/e.scaleGrid)*e.scaleGrid),e.maxScale&&(n=Math.min(e.maxScale,n),r=Math.min(e.maxScale,r)),e.minScale&&(n=Math.max(e.minScale,n),r=Math.max(e.minScale,r)),e.center&&(o=e.center.x,a=e.center.y),n=this.clampScale(n),r=this.clampScale(r),o||a){const t=this.getTranslation(),e=o-(o-t.tx)*(n/i.sx),s=a-(a-t.ty)*(r/i.sy);e===t.tx&&s===t.ty||this.translate(e,s)}return this.scale(n,r),this}getRotation(){return Xm(this.getMatrix())}rotate(e,n,r){if(null==n||null==r){const e=t.Util.getBBox(this.stage);n=e.width/2,r=e.height/2}const i=this.getMatrix().translate(n,r).rotate(e).translate(-n,-r);return this.setMatrix(i),this}getTranslation(){return Ym(this.getMatrix())}translate(t,e){const n=this.getMatrix();n.e=t||0,n.f=e||0,this.setMatrix(n);const r=this.getTranslation();return this.options.x=r.tx,this.options.y=r.ty,this.graph.trigger("translate",Object.assign({},r)),this}setOrigin(t,e){return this.translate(t||0,e||0)}fitToContent(t,e,n,r){if("object"==typeof t){const i=t;t=i.gridWidth||1,e=i.gridHeight||1,n=i.padding||0,r=i}else t=t||1,e=e||1,n=n||0,null==r&&(r={});const i=Df(n),s=r.border||0,o=r.contentArea?ky.create(r.contentArea):this.getContentArea(r);s>0&&o.inflate(s);const a=this.getScale(),l=this.getTranslation(),c=a.sx,h=a.sy;o.x*=c,o.y*=h,o.width*=c,o.height*=h;let u=Math.max(Math.ceil((o.width+o.x)/t),1)*t,g=Math.max(Math.ceil((o.height+o.y)/e),1)*e,d=0,f=0;("negative"===r.allowNewOrigin&&o.x<0||"positive"===r.allowNewOrigin&&o.x>=0||"any"===r.allowNewOrigin)&&(d=Math.ceil(-o.x/t)*t,d+=i.left,u+=d),("negative"===r.allowNewOrigin&&o.y<0||"positive"===r.allowNewOrigin&&o.y>=0||"any"===r.allowNewOrigin)&&(f=Math.ceil(-o.y/e)*e,f+=i.top,g+=f),u+=i.right,g+=i.bottom,u=Math.max(u,r.minWidth||0),g=Math.max(g,r.minHeight||0),u=Math.min(u,r.maxWidth||Number.MAX_SAFE_INTEGER),g=Math.min(g,r.maxHeight||Number.MAX_SAFE_INTEGER);const p=this.getComputedSize(),m=u!==p.width||g!==p.height;return(d!==l.tx||f!==l.ty)&&this.translate(d,f),m&&this.resize(u,g),new ky(-d/c,-f/h,u/c,g/h)}scaleContentToFit(t={}){this.scaleContentToFitImpl(t)}scaleContentToFitImpl(t={},e=!0){let n,r;if(t.contentArea){const e=t.contentArea;n=this.graph.localToGraph(e),r=Ty.create(e)}else n=this.getContentBBox(t),r=this.graph.graphToLocal(n);if(!n.width||!n.height)return;const i=Df(t.padding),s=t.minScale||0,o=t.maxScale||Number.MAX_SAFE_INTEGER,a=t.minScaleX||s,l=t.maxScaleX||o,c=t.minScaleY||s,h=t.maxScaleY||o;let u;if(t.viewportArea)u=t.viewportArea;else{const t=this.getComputedSize(),e=this.getTranslation();u={x:e.tx,y:e.ty,width:t.width,height:t.height}}u=ky.create(u).moveAndExpand({x:i.left,y:i.top,width:-i.left-i.right,height:-i.top-i.bottom});const g=this.getScale();let d=u.width/n.width*g.sx,f=u.height/n.height*g.sy;!1!==t.preserveAspectRatio&&(d=f=Math.min(d,f));const p=t.scaleGrid;if(p&&(d=p*Math.floor(d/p),f=p*Math.floor(f/p)),d=Hi(d,a,l),f=Hi(f,c,h),this.scale(d,f),e){const t=this.options,e=u.x-r.x*d-t.x,n=u.y-r.y*f-t.y;this.translate(e,n)}}getContentArea(e={}){return!1!==e.useCellGeometry?this.model.getAllCellsBBox()||new ky:t.Util.getBBox(this.stage)}getContentBBox(t={}){return this.graph.localToGraph(this.getContentArea(t))}getGraphArea(){const t=ky.fromSize(this.getComputedSize());return this.graph.graphToLocal(t)}zoomToRect(t,e={}){const n=ky.create(t),r=this.graph;e.contentArea=n,null==e.viewportArea&&(e.viewportArea={x:r.options.x,y:r.options.y,width:this.options.width,height:this.options.height}),this.scaleContentToFitImpl(e,!1);const i=n.getCenter();return this.centerPoint(i.x,i.y),this}zoomToFit(t={}){return this.zoomToRect(this.getContentArea(t),t)}centerPoint(t,e){const n=this.getComputedSize(),r=this.getScale(),i=this.getTranslation(),s=n.width/2,o=n.height/2;e="number"==typeof e?e:o,t=s-(t="number"==typeof t?t:s)*r.sx,e=o-e*r.sy,i.tx===t&&i.ty===e||this.translate(t,e)}centerContent(t){const e=this.graph.getContentArea(t).getCenter();this.centerPoint(e.x,e.y)}centerCell(t){return this.positionCell(t,"center")}positionPoint(t,e,n){const r=this.getComputedSize();(e=If(e,Math.max(0,r.width)))<0&&(e=r.width+e),(n=If(n,Math.max(0,r.height)))<0&&(n=r.height+n);const i=this.getTranslation(),s=this.getScale(),o=e-t.x*s.sx,a=n-t.y*s.sy;i.tx===o&&i.ty===a||this.translate(o,a)}positionRect(t,e){const n=ky.create(t);switch(e){case"center":return this.positionPoint(n.getCenter(),"50%","50%");case"top":return this.positionPoint(n.getTopCenter(),"50%",0);case"top-right":return this.positionPoint(n.getTopRight(),"100%",0);case"right":return this.positionPoint(n.getRightMiddle(),"100%","50%");case"bottom-right":return this.positionPoint(n.getBottomRight(),"100%","100%");case"bottom":return this.positionPoint(n.getBottomCenter(),"50%","100%");case"bottom-left":return this.positionPoint(n.getBottomLeft(),0,"100%");case"left":return this.positionPoint(n.getLeftMiddle(),0,"50%");case"top-left":return this.positionPoint(n.getTopLeft(),0,0);default:return this}}positionCell(t,e){const n=t.getBBox();return this.positionRect(n,e)}positionContent(t,e){const n=this.graph.getContentArea(e);return this.positionRect(n,t)}}class bA extends dA{get elem(){return this.view.background}init(){this.startListening(),this.options.background&&this.draw(this.options.background)}startListening(){this.graph.on("scale",this.update,this),this.graph.on("translate",this.update,this)}stopListening(){this.graph.off("scale",this.update,this),this.graph.off("translate",this.update,this)}updateBackgroundImage(t={}){let e=t.size||"auto auto",n=t.position||"center";const r=this.graph.transform.getScale(),i=this.graph.translate();if("object"==typeof n){n=`${i.tx+r.sx*(n.x||0)}px ${i.ty+r.sy*(n.y||0)}px`}"object"==typeof e&&(e=ky.fromSize(e).scale(r.sx,r.sy),e=`${e.width}px ${e.height}px`),this.elem.style.backgroundSize=e,this.elem.style.backgroundPosition=n}drawBackgroundImage(t,e={}){if(!(t instanceof HTMLImageElement))return void(this.elem.style.backgroundImage="");const n=this.optionsCache;if(n&&n.image!==e.image)return;let r;const i=e.opacity,s=e.size;let o=e.repeat||"no-repeat";const a=cv.registry.get(o);if("function"==typeof a){const n=e.quality||1;t.width*=n,t.height*=n;const i=a(t,e);if(!(i instanceof HTMLCanvasElement))throw new Error("Background pattern must return an HTML Canvas instance");r=i.toDataURL("image/png"),o=e.repeat&&o!==e.repeat?e.repeat:"repeat","object"==typeof s?(s.width*=i.width/t.width,s.height*=i.height/t.height):void 0===s&&(e.size={width:i.width/n,height:i.height/n})}else r=t.src,void 0===s&&(e.size={width:t.width,height:t.height});null!=n&&"object"==typeof e.size&&e.image===n.image&&e.repeat===n.repeat&&e.quality===n.quality&&(n.size=yo(e.size));const l=this.elem.style;l.backgroundImage=`url(${r})`,l.backgroundRepeat=o,l.opacity=null==i||i>=1?"":`${i}`,this.updateBackgroundImage(e)}updateBackgroundColor(t){this.elem.style.backgroundColor=t||""}updateBackgroundOptions(t){this.graph.options.background=t}update(){this.optionsCache&&this.updateBackgroundImage(this.optionsCache)}draw(t){const e=t||{};if(this.updateBackgroundOptions(t),this.updateBackgroundColor(e.color),e.image){this.optionsCache=yo(e);const n=document.createElement("img");n.onload=()=>this.drawBackgroundImage(n,t),n.setAttribute("crossorigin","anonymous"),n.src=e.image}else this.drawBackgroundImage(null),this.optionsCache=null}clear(){this.draw()}dispose(){this.clear(),this.stopListening()}}n([dA.dispose()],bA.prototype,"dispose",null);class xA extends dA{get widgetOptions(){return this.options.panning}get pannable(){return this.widgetOptions&&!0===this.widgetOptions.enabled}init(){this.startListening(),this.updateClassName()}startListening(){const t=this.widgetOptions.eventTypes;t&&(t.includes("leftMouseDown")&&(this.graph.on("blank:mousedown",this.preparePanning,this),this.graph.on("node:unhandled:mousedown",this.preparePanning,this),this.graph.on("edge:unhandled:mousedown",this.preparePanning,this)),t.includes("rightMouseDown")&&(this.onRightMouseDown=this.onRightMouseDown.bind(this),cy.on(this.graph.container,"mousedown",this.onRightMouseDown)),t.includes("mouseWheel")&&(this.mousewheelHandle=new gy(this.graph.container,this.onMouseWheel.bind(this),this.allowMouseWheel.bind(this)),this.mousewheelHandle.enable()))}stopListening(){const t=this.widgetOptions.eventTypes;t&&(t.includes("leftMouseDown")&&(this.graph.off("blank:mousedown",this.preparePanning,this),this.graph.off("node:unhandled:mousedown",this.preparePanning,this),this.graph.off("edge:unhandled:mousedown",this.preparePanning,this)),t.includes("rightMouseDown")&&cy.off(this.graph.container,"mousedown",this.onRightMouseDown),t.includes("mouseWheel")&&this.mousewheelHandle&&this.mousewheelHandle.disable())}preparePanning({e:t}){const e=this.graph.getPlugin("selection"),n=e&&e.allowRubberband(t,!0);(this.allowPanning(t,!0)||this.allowPanning(t)&&!n)&&this.startPanning(t)}allowPanning(e,n){return this.pannable&&t.ModifierKey.isMatch(e,this.widgetOptions.modifiers,n)}startPanning(t){const e=this.view.normalizeEvent(t);this.clientX=e.clientX,this.clientY=e.clientY,this.panning=!0,this.updateClassName(),cy.on(document.body,{"mousemove.panning touchmove.panning":this.pan.bind(this),"mouseup.panning touchend.panning":this.stopPanning.bind(this),"mouseleave.panning":this.stopPanning.bind(this)}),cy.on(window,"mouseup.panning",this.stopPanning.bind(this))}pan(t){const e=this.view.normalizeEvent(t),n=e.clientX-this.clientX,r=e.clientY-this.clientY;this.clientX=e.clientX,this.clientY=e.clientY,this.graph.translateBy(n,r)}stopPanning(t){this.panning=!1,this.updateClassName(),cy.off(document.body,".panning"),cy.off(window,".panning")}updateClassName(){const t=this.view.container,e=this.view.prefixClassName("graph-panning"),n=this.view.prefixClassName("graph-pannable");this.pannable?this.panning?(ap(t,e),lp(t,n)):(lp(t,e),ap(t,n)):(lp(t,e),lp(t,n))}onRightMouseDown(t){2===t.button&&this.allowPanning(t,!0)&&this.startPanning(t)}allowMouseWheel(t){return this.pannable&&!t.ctrlKey}onMouseWheel(t,e,n){t.ctrlKey||this.graph.translateBy(-e,-n)}autoPanning(t,e){const n=10,r=this.graph.getGraphArea();let i=0,s=0;t<=r.left+n&&(i=-10),e<=r.top+n&&(s=-10),t>=r.right-n&&(i=n),e>=r.bottom-n&&(s=n),0===i&&0===s||this.graph.translateBy(-i,-s)}enablePanning(){this.pannable||(this.widgetOptions.enabled=!0,this.updateClassName())}disablePanning(){this.pannable&&(this.widgetOptions.enabled=!1,this.updateClassName())}dispose(){this.stopListening()}}n([dA.dispose()],xA.prototype,"dispose",null);class wA extends dA{constructor(){super(...arguments),this.cumulatedFactor=1}get widgetOptions(){return this.options.mousewheel}init(){this.container=this.graph.container,this.target=this.widgetOptions.global?document:this.container,this.mousewheelHandle=new gy(this.target,this.onMouseWheel.bind(this),this.allowMouseWheel.bind(this)),this.widgetOptions.enabled&&this.enable(!0)}get disabled(){return!0!==this.widgetOptions.enabled}enable(t){(this.disabled||t)&&(this.widgetOptions.enabled=!0,this.mousewheelHandle.enable())}disable(){this.disabled||(this.widgetOptions.enabled=!1,this.mousewheelHandle.disable())}allowMouseWheel(e){const n=this.widgetOptions.guard;return(null==n||n.call(e))&&t.ModifierKey.isMatch(e,this.widgetOptions.modifiers)}onMouseWheel(e){const n=this.widgetOptions.guard;if((null==n||n.call(e))&&t.ModifierKey.isMatch(e,this.widgetOptions.modifiers)){const t=this.widgetOptions.factor||1.2;null==this.currentScale&&(this.startPos={x:e.clientX,y:e.clientY},this.currentScale=this.graph.transform.getScale().sx);e.deltaY<0?this.currentScale<.15?this.cumulatedFactor=(this.currentScale+.01)/this.currentScale:(this.cumulatedFactor=Math.round(this.currentScale*t*20)/20/this.currentScale,1===this.cumulatedFactor&&(this.cumulatedFactor=1.05)):this.currentScale<=.15?this.cumulatedFactor=(this.currentScale-.01)/this.currentScale:(this.cumulatedFactor=Math.round(this.currentScale*(1/t)*20)/20/this.currentScale,1===this.cumulatedFactor&&(this.cumulatedFactor=.95)),this.cumulatedFactor=Math.max(.01,Math.min(this.currentScale*this.cumulatedFactor,160)/this.currentScale);const n=this.currentScale;let r=this.graph.transform.clampScale(n*this.cumulatedFactor);if(r=Hi(r,this.widgetOptions.minScale||Number.MIN_SAFE_INTEGER,this.widgetOptions.maxScale||Number.MAX_SAFE_INTEGER),r!==n)if(this.widgetOptions.zoomAtMousePosition){const t=!!this.graph.getPlugin("scroller")?this.graph.clientToLocal(this.startPos):this.graph.clientToGraph(this.startPos);this.graph.zoom(r,{absolute:!0,center:t.clone()})}else this.graph.zoom(r,{absolute:!0});this.currentScale=null,this.cumulatedFactor=1}}dispose(){this.disable()}}n([r.dispose()],wA.prototype,"dispose",null);class AA extends dA{init(){this.resetRenderArea=Fg(this.resetRenderArea,200,{leading:!0}),this.resetRenderArea(),this.startListening()}startListening(){this.graph.on("translate",this.resetRenderArea,this),this.graph.on("scale",this.resetRenderArea,this),this.graph.on("resize",this.resetRenderArea,this)}stopListening(){this.graph.off("translate",this.resetRenderArea,this),this.graph.off("scale",this.resetRenderArea,this),this.graph.off("resize",this.resetRenderArea,this)}enableVirtualRender(){this.options.virtual=!0,this.resetRenderArea()}disableVirtualRender(){this.options.virtual=!1,this.graph.renderer.setRenderArea(void 0)}resetRenderArea(){if(this.options.virtual){const t=this.graph.getGraphArea();this.graph.renderer.setRenderArea(t)}}dispose(){this.stopListening()}}n([dA.dispose()],AA.prototype,"dispose",null);class PA{constructor(){this.isFlushing=!1,this.isFlushPending=!1,this.scheduleId=0,this.queue=[],this.frameInterval=33,this.initialTime=Date.now()}queueJob(t){if(t.priority&mA.PRIOR)t.cb();else{const e=this.findInsertionIndex(t);e>=0&&this.queue.splice(e,0,t)}}queueFlush(){this.isFlushing||this.isFlushPending||(this.isFlushPending=!0,this.scheduleJob())}queueFlushSync(){this.isFlushing||this.isFlushPending||(this.isFlushPending=!0,this.flushJobsSync())}clearJobs(){this.queue.length=0,this.isFlushing=!1,this.isFlushPending=!1,this.cancelScheduleJob()}flushJobs(){this.isFlushPending=!1,this.isFlushing=!0;const t=this.getCurrentTime();let e;for(;(e=this.queue.shift())&&(e.cb(),!(this.getCurrentTime()-t>=this.frameInterval)););this.isFlushing=!1,this.queue.length&&this.queueFlush()}flushJobsSync(){let t;for(this.isFlushPending=!1,this.isFlushing=!0;t=this.queue.shift();)try{t.cb()}catch(t){console.log(t)}this.isFlushing=!1}findInsertionIndex(t){let e=0,n=this.queue.length,r=n-1;const i=t.priority;for(;e<=r;){const t=(r-e>>1)+e;i<=this.queue[t].priority?e=t+1:(n=t,r=t-1)}return n}scheduleJob(){"requestIdleCallback"in window?(this.scheduleId&&this.cancelScheduleJob(),this.scheduleId=window.requestIdleCallback(this.flushJobs.bind(this),{timeout:100})):(this.scheduleId&&this.cancelScheduleJob(),this.scheduleId=window.setTimeout(this.flushJobs.bind(this)))}cancelScheduleJob(){"cancelIdleCallback"in window?(this.scheduleId&&window.cancelIdleCallback(this.scheduleId),this.scheduleId=0):(this.scheduleId&&clearTimeout(this.scheduleId),this.scheduleId=0)}getCurrentTime(){return"object"==typeof performance&&"function"==typeof performance.now?performance.now():Date.now()-this.initialTime}}!function(t){t[t.RenderEdge=2]="RenderEdge",t[t.RenderNode=4]="RenderNode",t[t.Update=8]="Update",t[t.PRIOR=1048576]="PRIOR"}(mA||(mA={}));class CA extends r{get model(){return this.graph.model}get container(){return this.graph.view.stage}constructor(t){super(),this.views={},this.willRemoveViews={},this.queue=new PA,this.graph=t,this.init()}init(){this.startListening(),this.renderViews(this.model.getCells())}startListening(){this.model.on("reseted",this.onModelReseted,this),this.model.on("cell:added",this.onCellAdded,this),this.model.on("cell:removed",this.onCellRemoved,this),this.model.on("cell:change:zIndex",this.onCellZIndexChanged,this),this.model.on("cell:change:visible",this.onCellVisibleChanged,this)}stopListening(){this.model.off("reseted",this.onModelReseted,this),this.model.off("cell:added",this.onCellAdded,this),this.model.off("cell:removed",this.onCellRemoved,this),this.model.off("cell:change:zIndex",this.onCellZIndexChanged,this),this.model.off("cell:change:visible",this.onCellVisibleChanged,this)}onModelReseted({options:t}){this.queue.clearJobs(),this.removeZPivots(),this.resetViews(),this.renderViews(this.model.getCells(),t)}onCellAdded({cell:t,options:e}){this.renderViews([t],e)}onCellRemoved({cell:t}){this.removeViews([t])}onCellZIndexChanged({cell:t,options:e}){const n=this.views[t.id];n&&this.requestViewUpdate(n.view,CA.FLAG_INSERT,e,mA.Update,!0)}onCellVisibleChanged({cell:t,current:e}){this.toggleVisible(t,!!e)}requestViewUpdate(t,e,n={},r=mA.Update,i=!0){const s=t.cell.id,o=this.views[s];if(!o)return;o.flag=e,o.options=n;const a=t.hasAction(e,["translate","resize","rotate"]);t.isNodeView()&&a&&(r=mA.PRIOR,i=!1),this.queue.queueJob({id:s,priority:r,cb:()=>{this.renderViewInArea(t,e,n)}});this.getEffectedEdges(t).forEach((t=>{this.requestViewUpdate(t.view,t.flag,n,r,!1)})),i&&this.flush()}setRenderArea(t){this.renderArea=t,this.flushWaitingViews()}isViewMounted(t){if(null==t)return!1;const e=this.views[t.cell.id];return!!e&&e.state===CA.ViewState.MOUNTED}renderViews(t,e={}){t.sort(((t,e)=>t.isNode()&&e.isEdge()?-1:0)),t.forEach((t=>{const n=t.id;let r=0,i=this.views[n];if(i)r=CA.FLAG_INSERT;else{const s=this.createCellView(t);s&&(s.graph=this.graph,r=CA.FLAG_INSERT|s.getBootstrapFlag(),i={view:s,flag:r,options:e,state:CA.ViewState.CREATED},this.views[n]=i)}i&&this.requestViewUpdate(i.view,r,e,this.getRenderPriority(i.view),!1)})),this.flush()}renderViewInArea(t,e,n={}){const r=t.cell,i=r.id,s=this.views[i];if(!s)return;let o=0;this.isUpdatable(t)||s.state===CA.ViewState.MOUNTED?(o=this.updateView(t,e,n),s.flag=o):s.state=CA.ViewState.WAITING,o&&r.isEdge()&&0==(o&t.getFlag(["source","target"]))&&this.queue.queueJob({id:i,priority:mA.RenderEdge,cb:()=>{this.updateView(t,e,n)}})}removeViews(t){t.forEach((t=>{const e=t.id,n=this.views[e];n&&(this.willRemoveViews[e]=n,delete this.views[e],this.queue.queueJob({id:e,priority:this.getRenderPriority(n.view),cb:()=>{this.removeView(n.view)}}))})),this.flush()}flush(){this.graph.options.async?this.queue.queueFlush():this.queue.queueFlushSync()}flushWaitingViews(){Object.values(this.views).forEach((t=>{if(t&&t.state===CA.ViewState.WAITING){const{view:e,flag:n,options:r}=t;this.requestViewUpdate(e,n,r,this.getRenderPriority(e),!1)}})),this.flush()}updateView(t,e,n={}){if(null==t)return 0;if(ox.isCellView(t)){if(e&CA.FLAG_REMOVE)return this.removeView(t.cell),0;e&CA.FLAG_INSERT&&(this.insertView(t),e^=CA.FLAG_INSERT)}return e?t.confirmUpdate(e,n):0}insertView(t){const e=this.views[t.cell.id];if(e){const n=t.cell.getZIndex(),r=this.addZPivot(n);this.container.insertBefore(t.container,r),t.cell.isVisible()||this.toggleVisible(t.cell,!1),e.state=CA.ViewState.MOUNTED,this.graph.trigger("view:mounted",{view:t})}}resetViews(){this.willRemoveViews=Object.assign(Object.assign({},this.views),this.willRemoveViews),Object.values(this.willRemoveViews).forEach((t=>{t&&this.removeView(t.view)})),this.views={},this.willRemoveViews={}}removeView(t){const e=t.cell,n=this.willRemoveViews[e.id];n&&t&&(n.view.remove(),delete this.willRemoveViews[e.id],this.graph.trigger("view:unmounted",{view:t}))}toggleVisible(t,e){const n=this.model.getConnectedEdges(t);for(let t=0,r=n.length;t<r;t+=1){const r=n[t];if(e){const t=r.getSourceCell(),e=r.getTargetCell();if(t&&!t.isVisible()||e&&!e.isVisible())continue;this.toggleVisible(r,!0)}else this.toggleVisible(r,!1)}const r=this.views[t.id];r&&am(r.view.container,{display:e?"unset":"none"})}addZPivot(t=0){null==this.zPivots&&(this.zPivots={});const e=this.zPivots;let n=e[t];if(n)return n;n=e[t]=document.createComment(`z-index:${t+1}`);let r=-1/0;for(const n in e){const e=+n;e<t&&e>r&&(r=e)}const i=this.container;if(r!==-1/0){const t=e[r];i.insertBefore(n,t.nextSibling)}else i.insertBefore(n,i.firstChild);return n}removeZPivots(){this.zPivots&&Object.values(this.zPivots).forEach((t=>{t&&t.parentNode&&t.parentNode.removeChild(t)})),this.zPivots={}}createCellView(t){const e={graph:this.graph},n=this.graph.options.createCellView;if(n){const r=ef(n,this.graph,t);if(r)return new r(t,e);if(null===r)return null}const r=t.view;if(null!=r&&"string"==typeof r){const n=ox.registry.get(r);return n?new n(t,e):ox.registry.onNotFound(r)}return t.isNode()?new hA(t,e):t.isEdge()?new uA(t,e):null}getEffectedEdges(t){const e=[],n=t.cell,r=this.model.getConnectedEdges(n);for(let t=0,i=r.length;t<i;t+=1){const i=r[t],s=this.views[i.id];if(!s)continue;const o=s.view;if(!this.isViewMounted(o))continue;const a=["update"];i.getTargetCell()===n&&a.push("target"),i.getSourceCell()===n&&a.push("source"),e.push({id:i.id,view:o,flag:o.getFlag(a)})}return e}isUpdatable(t){if(t.isNodeView())return!this.renderArea||this.renderArea.isIntersectWithRect(t.cell.getBBox());if(t.isEdgeView()){const e=t.cell,n=e.getSourceCell(),r=e.getTargetCell();if(this.renderArea&&n&&r)return this.renderArea.isIntersectWithRect(n.getBBox())||this.renderArea.isIntersectWithRect(r.getBBox())}return!0}getRenderPriority(t){return t.cell.isNode()?mA.RenderNode:mA.RenderEdge}dispose(){this.stopListening()}}n([r.dispose()],CA.prototype,"dispose",null),function(t){t.FLAG_INSERT=1<<30,t.FLAG_REMOVE=1<<29,t.FLAG_RENDER=67108863}(CA||(CA={})),function(t){var e;(e=t.ViewState||(t.ViewState={}))[e.CREATED=0]="CREATED",e[e.MOUNTED=1]="MOUNTED",e[e.WAITING=2]="WAITING"}(CA||(CA={}));class MA extends dA{constructor(){super(...arguments),this.schedule=new CA(this.graph)}requestViewUpdate(t,e,n={}){this.schedule.requestViewUpdate(t,e,n)}isViewMounted(t){return this.schedule.isViewMounted(t)}setRenderArea(t){this.schedule.setRenderArea(t)}findViewByElem(t){if(null==t)return null;const e=this.options.container,n="string"==typeof t?e.querySelector(t):t instanceof Element?t:t[0];if(n){const t=this.graph.view.findAttr("data-cell-id",n);if(t){const e=this.schedule.views;if(e[t])return e[t].view}}return null}findViewByCell(t){if(null==t)return null;const e=qw.isCell(t)?t.id:t,n=this.schedule.views;return n[e]?n[e].view:null}findViewsFromPoint(e){const n={x:e.x,y:e.y};return this.model.getCells().map((t=>this.findViewByCell(t))).filter((e=>null!=e&&t.Util.getBBox(e.container,{target:this.view.stage}).containsPoint(n)))}findEdgeViewsFromPoint(t,e=5){return this.model.getEdges().map((t=>this.findViewByCell(t))).filter((n=>{if(null!=n){const r=n.getClosestPoint(t);if(r)return r.distance(t)<=e}return!1}))}findViewsInArea(e,n={}){const r=ky.create(e);return this.model.getCells().map((t=>this.findViewByCell(t))).filter((e=>{if(e){if(n.nodeOnly&&!e.isNodeView())return!1;const i=t.Util.getBBox(e.container,{target:this.view.stage});return 0===i.width?i.inflate(1,0):0===i.height&&i.inflate(0,1),n.strict?r.containsRect(i):r.isIntersectWithRect(i)}return!1}))}dispose(){this.schedule.dispose()}}n([dA.dispose()],MA.prototype,"dispose",null);class EA extends dA{get cid(){return this.graph.view.cid}get svg(){return this.view.svg}get defs(){return this.view.defs}isDefined(t){return null!=this.svg.getElementById(t)}filter(e){let n=e.id;const r=e.name;if(n||(n=`filter-${r}-${this.cid}-${kf(JSON.stringify(e))}`),!this.isDefined(n)){const i=dv.registry.get(r);if(null==i)return dv.registry.onNotFound(r);const s=i(e.args||{}),o=Object.assign(Object.assign({x:-1,y:-1,width:3,height:3,filterUnits:"objectBoundingBox"},e.attrs),{id:n});dm.create(t.Markup.sanitize(s),o).appendTo(this.defs)}return n}gradient(t){let e=t.id;const n=t.type;if(e||(e=`gradient-${n}-${this.cid}-${kf(JSON.stringify(t))}`),!this.isDefined(e)){const r=t.stops.map((t=>{const e=null!=t.opacity&&Number.isFinite(t.opacity)?t.opacity:1;return`<stop offset="${t.offset}" stop-color="${t.color}" stop-opacity="${e}"/>`})),i=`<${n}>${r.join("")}</${n}>`,s=Object.assign({id:e},t.attrs);dm.create(i,s).appendTo(this.defs)}return e}marker(t){const{id:n,refX:r,refY:i,markerUnits:s,markerOrient:o,tagName:a,children:l}=t,c=e(t,["id","refX","refY","markerUnits","markerOrient","tagName","children"]);let h=n;if(h||(h=`marker-${this.cid}-${kf(JSON.stringify(t))}`),!this.isDefined(h)){"path"!==a&&delete c.d;const t=dm.create("marker",{refX:r,refY:i,id:h,overflow:"visible",orient:null!=o?o:"auto",markerUnits:s||"userSpaceOnUse"},l?l.map((t=>{var{tagName:n}=t,r=e(t,["tagName"]);return dm.create(`${n}`||"path",Fp(Object.assign(Object.assign({},c),r)))})):[dm.create(a||"path",Fp(c))]);this.defs.appendChild(t.node)}return h}remove(t){const e=this.svg.getElementById(t);e&&e.parentNode&&e.parentNode.removeChild(e)}}class SA extends dA{getClientMatrix(){return $m(this.view.stage.getScreenCTM())}getClientOffset(){const t=this.view.svg.getBoundingClientRect();return new Ty(t.left,t.top)}getPageOffset(){return this.getClientOffset().translate(window.scrollX,window.scrollY)}snapToGrid(t,e){return("number"==typeof t?this.clientToLocalPoint(t,e):this.clientToLocalPoint(t.x,t.y)).snapToGrid(this.graph.getGridSize())}localToGraphPoint(e,n){const r=Ty.create(e,n);return t.Util.transformPoint(r,this.graph.matrix())}localToClientPoint(e,n){const r=Ty.create(e,n);return t.Util.transformPoint(r,this.getClientMatrix())}localToPagePoint(t,e){return("number"==typeof t?this.localToGraphPoint(t,e):this.localToGraphPoint(t)).translate(this.getPageOffset())}localToGraphRect(e,n,r,i){const s=ky.create(e,n,r,i);return t.Util.transformRectangle(s,this.graph.matrix())}localToClientRect(e,n,r,i){const s=ky.create(e,n,r,i);return t.Util.transformRectangle(s,this.getClientMatrix())}localToPageRect(t,e,n,r){return("number"==typeof t?this.localToGraphRect(t,e,n,r):this.localToGraphRect(t)).translate(this.getPageOffset())}graphToLocalPoint(e,n){const r=Ty.create(e,n);return t.Util.transformPoint(r,this.graph.matrix().inverse())}clientToLocalPoint(e,n){const r=Ty.create(e,n);return t.Util.transformPoint(r,this.getClientMatrix().inverse())}clientToGraphPoint(e,n){const r=Ty.create(e,n);return t.Util.transformPoint(r,this.graph.matrix().multiply(this.getClientMatrix().inverse()))}pageToLocalPoint(t,e){const n=Ty.create(t,e).diff(this.getPageOffset());return this.graphToLocalPoint(n)}graphToLocalRect(e,n,r,i){const s=ky.create(e,n,r,i);return t.Util.transformRectangle(s,this.graph.matrix().inverse())}clientToLocalRect(e,n,r,i){const s=ky.create(e,n,r,i);return t.Util.transformRectangle(s,this.getClientMatrix().inverse())}clientToGraphRect(e,n,r,i){const s=ky.create(e,n,r,i);return t.Util.transformRectangle(s,this.graph.matrix().multiply(this.getClientMatrix().inverse()))}pageToLocalRect(t,e,n,r){const i=ky.create(t,e,n,r),s=this.getPageOffset();return i.x-=s.x,i.y-=s.y,this.graphToLocalRect(i)}}class OA extends dA{constructor(){super(...arguments),this.highlights={}}init(){this.startListening()}startListening(){this.graph.on("cell:highlight",this.onCellHighlight,this),this.graph.on("cell:unhighlight",this.onCellUnhighlight,this)}stopListening(){this.graph.off("cell:highlight",this.onCellHighlight,this),this.graph.off("cell:unhighlight",this.onCellUnhighlight,this)}onCellHighlight({view:t,magnet:e,options:n={}}){const r=this.resolveHighlighter(n);if(!r)return;const i=this.getHighlighterId(e,r);if(!this.highlights[i]){const n=r.highlighter;n.highlight(t,e,Object.assign({},r.args)),this.highlights[i]={cellView:t,magnet:e,highlighter:n,args:r.args}}}onCellUnhighlight({magnet:t,options:e={}}){const n=this.resolveHighlighter(e);if(!n)return;const r=this.getHighlighterId(t,n);this.unhighlight(r)}resolveHighlighter(t){const e=this.options;let n=t.highlighter;if(null==n){const r=t.type;n=r&&e.highlighting[r]||e.highlighting.default}if(null==n)return null;const r="string"==typeof n?{name:n}:n,i=r.name,s=Vb.registry.get(i);return null==s?Vb.registry.onNotFound(i):(Vb.check(i,s),{name:i,highlighter:s,args:r.args||{}})}getHighlighterId(t,e){return gp(t),e.name+t.id+JSON.stringify(e.args)}unhighlight(t){const e=this.highlights[t];e&&(e.highlighter.unhighlight(e.cellView,e.magnet,e.args),delete this.highlights[t])}dispose(){Object.keys(this.highlights).forEach((t=>this.unhighlight(t))),this.stopListening()}}n([OA.dispose()],OA.prototype,"dispose",null);class TA extends dA{getScroller(){const t=this.graph.getPlugin("scroller");return t&&t.options.enabled?t:null}getContainer(){const t=this.getScroller();return t?t.container.parentElement:this.graph.container.parentElement}getSensorTarget(){const t=this.options.autoResize;if(t)return"boolean"==typeof t?this.getContainer():t}init(){if(this.options.autoResize){const e=this.getSensorTarget();e&&t.SizeSensor.bind(e,(()=>{const t=e.offsetWidth,n=e.offsetHeight;this.resize(t,n)}))}}resize(t,e){const n=this.getScroller();n?n.resize(t,e):this.graph.transform.resize(t,e)}dispose(){t.SizeSensor.clear(this.graph.container)}}n([dA.dispose()],TA.prototype,"dispose",null);class kA extends xf{get container(){return this.options.container}get[Symbol.toStringTag](){return kA.toStringTag}constructor(t){super(),this.installedPlugins=new Set,this.options=pA.get(t),this.css=new fA(this),this.view=new gA(this),this.defs=new EA(this),this.coord=new SA(this),this.transform=new vA(this),this.highlight=new OA(this),this.grid=new yA(this),this.background=new bA(this),this.options.model?this.model=this.options.model:(this.model=new Yw,this.model.graph=this),this.renderer=new MA(this),this.panning=new xA(this),this.mousewheel=new wA(this),this.virtualRender=new AA(this),this.size=new TA(this)}isNode(t){return t.isNode()}isEdge(t){return t.isEdge()}resetCells(t,e={}){return this.model.resetCells(t,e),this}clearCells(t={}){return this.model.clear(t),this}toJSON(t={}){return this.model.toJSON(t)}parseJSON(t){return this.model.parseJSON(t)}fromJSON(t,e={}){return this.model.fromJSON(t,e),this}getCellById(t){return this.model.getCell(t)}addNode(t,e={}){return this.model.addNode(t,e)}addNodes(t,e={}){return this.addCell(t.map((t=>Hw.isNode(t)?t:this.createNode(t))),e)}createNode(t){return this.model.createNode(t)}removeNode(t,e={}){return this.model.removeCell(t,e)}addEdge(t,e={}){return this.model.addEdge(t,e)}addEdges(t,e={}){return this.addCell(t.map((t=>Jw.isEdge(t)?t:this.createEdge(t))),e)}removeEdge(t,e={}){return this.model.removeCell(t,e)}createEdge(t){return this.model.createEdge(t)}addCell(t,e={}){return this.model.addCell(t,e),this}removeCell(t,e={}){return this.model.removeCell(t,e)}removeCells(t,e={}){return this.model.removeCells(t,e)}removeConnectedEdges(t,e={}){return this.model.removeConnectedEdges(t,e)}disconnectConnectedEdges(t,e={}){return this.model.disconnectConnectedEdges(t,e),this}hasCell(t){return this.model.has(t)}getCells(){return this.model.getCells()}getCellCount(){return this.model.total()}getNodes(){return this.model.getNodes()}getEdges(){return this.model.getEdges()}getOutgoingEdges(t){return this.model.getOutgoingEdges(t)}getIncomingEdges(t){return this.model.getIncomingEdges(t)}getConnectedEdges(t,e={}){return this.model.getConnectedEdges(t,e)}getRootNodes(){return this.model.getRoots()}getLeafNodes(){return this.model.getLeafs()}isRootNode(t){return this.model.isRoot(t)}isLeafNode(t){return this.model.isLeaf(t)}getNeighbors(t,e={}){return this.model.getNeighbors(t,e)}isNeighbor(t,e,n={}){return this.model.isNeighbor(t,e,n)}getSuccessors(t,e={}){return this.model.getSuccessors(t,e)}isSuccessor(t,e,n={}){return this.model.isSuccessor(t,e,n)}getPredecessors(t,e={}){return this.model.getPredecessors(t,e)}isPredecessor(t,e,n={}){return this.model.isPredecessor(t,e,n)}getCommonAncestor(...t){return this.model.getCommonAncestor(...t)}getSubGraph(t,e={}){return this.model.getSubGraph(t,e)}cloneSubGraph(t,e={}){return this.model.cloneSubGraph(t,e)}cloneCells(t){return this.model.cloneCells(t)}getNodesFromPoint(t,e){return this.model.getNodesFromPoint(t,e)}getNodesInArea(t,e,n,r,i){return this.model.getNodesInArea(t,e,n,r,i)}getNodesUnderNode(t,e={}){return this.model.getNodesUnderNode(t,e)}searchCell(t,e,n={}){return this.model.search(t,e,n),this}getShortestPath(t,e,n={}){return this.model.getShortestPath(t,e,n)}getAllCellsBBox(){return this.model.getAllCellsBBox()}getCellsBBox(t,e={}){return this.model.getCellsBBox(t,e)}startBatch(t,e={}){this.model.startBatch(t,e)}stopBatch(t,e={}){this.model.stopBatch(t,e)}batchUpdate(t,e,n){const r="string"==typeof t?t:"update",i="string"==typeof t?e:t,s="function"==typeof e?n:e;this.startBatch(r,s);const o=i();return this.stopBatch(r,s),o}updateCellId(t,e){return this.model.updateCellId(t,e)}findView(t){return qw.isCell(t)?this.findViewByCell(t):this.findViewByElem(t)}findViews(t){return ky.isRectangleLike(t)?this.findViewsInArea(t):Ty.isPointLike(t)?this.findViewsFromPoint(t):[]}findViewByCell(t){return this.renderer.findViewByCell(t)}findViewByElem(t){return this.renderer.findViewByElem(t)}findViewsFromPoint(t,e){const n="number"==typeof t?{x:t,y:e}:t;return this.renderer.findViewsFromPoint(n)}findViewsInArea(t,e,n,r,i){const s="number"==typeof t?{x:t,y:e,width:n,height:r}:t,o="number"==typeof t?i:e;return this.renderer.findViewsInArea(s,o)}matrix(t){return void 0===t?this.transform.getMatrix():(this.transform.setMatrix(t),this)}resize(t,e){const n=this.getPlugin("scroller");return n?n.resize(t,e):this.transform.resize(t,e),this}scale(t,e=t,n=0,r=0){return void 0===t?this.transform.getScale():(this.transform.scale(t,e,n,r),this)}zoom(t,e){const n=this.getPlugin("scroller");if(n){if(void 0===t)return n.zoom();n.zoom(t,e)}else{if(void 0===t)return this.transform.getZoom();this.transform.zoom(t,e)}return this}zoomTo(t,e={}){const n=this.getPlugin("scroller");return n?n.zoom(t,Object.assign(Object.assign({},e),{absolute:!0})):this.transform.zoom(t,Object.assign(Object.assign({},e),{absolute:!0})),this}zoomToRect(t,e={}){const n=this.getPlugin("scroller");return n?n.zoomToRect(t,e):this.transform.zoomToRect(t,e),this}zoomToFit(t={}){const e=this.getPlugin("scroller");return e?e.zoomToFit(t):this.transform.zoomToFit(t),this}rotate(t,e,n){return void 0===t?this.transform.getRotation():(this.transform.rotate(t,e,n),this)}translate(t,e){return void 0===t?this.transform.getTranslation():(this.transform.translate(t,e),this)}translateBy(t,e){const n=this.translate(),r=n.tx+t,i=n.ty+e;return this.translate(r,i)}getGraphArea(){return this.transform.getGraphArea()}getContentArea(t={}){return this.transform.getContentArea(t)}getContentBBox(t={}){return this.transform.getContentBBox(t)}fitToContent(t,e,n,r){return this.transform.fitToContent(t,e,n,r)}scaleContentToFit(t={}){return this.transform.scaleContentToFit(t),this}center(t){return this.centerPoint(t)}centerPoint(t,e,n){const r=this.getPlugin("scroller");return r?r.centerPoint(t,e,n):this.transform.centerPoint(t,e),this}centerContent(t){const e=this.getPlugin("scroller");return e?e.centerContent(t):this.transform.centerContent(t),this}centerCell(t,e){const n=this.getPlugin("scroller");return n?n.centerCell(t,e):this.transform.centerCell(t),this}positionPoint(t,e,n,r={}){const i=this.getPlugin("scroller");return i?i.positionPoint(t,e,n,r):this.transform.positionPoint(t,e,n),this}positionRect(t,e,n){const r=this.getPlugin("scroller");return r?r.positionRect(t,e,n):this.transform.positionRect(t,e),this}positionCell(t,e,n){const r=this.getPlugin("scroller");return r?r.positionCell(t,e,n):this.transform.positionCell(t,e),this}positionContent(t,e){const n=this.getPlugin("scroller");return n?n.positionContent(t,e):this.transform.positionContent(t,e),this}snapToGrid(t,e){return this.coord.snapToGrid(t,e)}pageToLocal(t,e,n,r){return ky.isRectangleLike(t)?this.coord.pageToLocalRect(t):"number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"number"==typeof r?this.coord.pageToLocalRect(t,e,n,r):this.coord.pageToLocalPoint(t,e)}localToPage(t,e,n,r){return ky.isRectangleLike(t)?this.coord.localToPageRect(t):"number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"number"==typeof r?this.coord.localToPageRect(t,e,n,r):this.coord.localToPagePoint(t,e)}clientToLocal(t,e,n,r){return ky.isRectangleLike(t)?this.coord.clientToLocalRect(t):"number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"number"==typeof r?this.coord.clientToLocalRect(t,e,n,r):this.coord.clientToLocalPoint(t,e)}localToClient(t,e,n,r){return ky.isRectangleLike(t)?this.coord.localToClientRect(t):"number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"number"==typeof r?this.coord.localToClientRect(t,e,n,r):this.coord.localToClientPoint(t,e)}localToGraph(t,e,n,r){return ky.isRectangleLike(t)?this.coord.localToGraphRect(t):"number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"number"==typeof r?this.coord.localToGraphRect(t,e,n,r):this.coord.localToGraphPoint(t,e)}graphToLocal(t,e,n,r){return ky.isRectangleLike(t)?this.coord.graphToLocalRect(t):"number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"number"==typeof r?this.coord.graphToLocalRect(t,e,n,r):this.coord.graphToLocalPoint(t,e)}clientToGraph(t,e,n,r){return ky.isRectangleLike(t)?this.coord.clientToGraphRect(t):"number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"number"==typeof r?this.coord.clientToGraphRect(t,e,n,r):this.coord.clientToGraphPoint(t,e)}defineFilter(t){return this.defs.filter(t)}defineGradient(t){return this.defs.gradient(t)}defineMarker(t){return this.defs.marker(t)}getGridSize(){return this.grid.getGridSize()}setGridSize(t){return this.grid.setGridSize(t),this}showGrid(){return this.grid.show(),this}hideGrid(){return this.grid.hide(),this}clearGrid(){return this.grid.clear(),this}drawGrid(t){return this.grid.draw(t),this}updateBackground(){return this.background.update(),this}drawBackground(t,e){const n=this.getPlugin("scroller");return null==n||null!=this.options.background&&e?this.background.draw(t):n.drawBackground(t,e),this}clearBackground(t){const e=this.getPlugin("scroller");return null==e||null!=this.options.background&&t?this.background.clear():e.clearBackground(t),this}enableVirtualRender(){return this.virtualRender.enableVirtualRender(),this}disableVirtualRender(){return this.virtualRender.disableVirtualRender(),this}isMouseWheelEnabled(){return!this.mousewheel.disabled}enableMouseWheel(){return this.mousewheel.enable(),this}disableMouseWheel(){return this.mousewheel.disable(),this}toggleMouseWheel(t){return null==t?this.isMouseWheelEnabled()?this.disableMouseWheel():this.enableMouseWheel():t?this.enableMouseWheel():this.disableMouseWheel(),this}isPannable(){const t=this.getPlugin("scroller");return t?t.isPannable():this.panning.pannable}enablePanning(){const t=this.getPlugin("scroller");return t?t.enablePanning():this.panning.enablePanning(),this}disablePanning(){const t=this.getPlugin("scroller");return t?t.disablePanning():this.panning.disablePanning(),this}togglePanning(t){return null==t?this.isPannable()?this.disablePanning():this.enablePanning():t!==this.isPannable()&&(t?this.enablePanning():this.disablePanning()),this}use(t,...e){return this.installedPlugins.has(t)||(this.installedPlugins.add(t),t.init(this,...e)),this}getPlugin(t){return Array.from(this.installedPlugins).find((e=>e.name===t))}getPlugins(t){return Array.from(this.installedPlugins).filter((e=>t.includes(e.name)))}enablePlugins(t){let e=t;Array.isArray(e)||(e=[e]);const n=this.getPlugins(e);return null==n||n.forEach((t=>{var e;null===(e=null==t?void 0:t.enable)||void 0===e||e.call(t)})),this}disablePlugins(t){let e=t;Array.isArray(e)||(e=[e]);const n=this.getPlugins(e);return null==n||n.forEach((t=>{var e;null===(e=null==t?void 0:t.disable)||void 0===e||e.call(t)})),this}isPluginEnabled(t){var e;const n=this.getPlugin(t);return null===(e=null==n?void 0:n.isEnabled)||void 0===e?void 0:e.call(n)}disposePlugins(t){let e=t;Array.isArray(e)||(e=[e]);const n=this.getPlugins(e);return null==n||n.forEach((t=>{t.dispose()})),this}dispose(){this.clearCells(),this.off(),this.css.dispose(),this.defs.dispose(),this.grid.dispose(),this.coord.dispose(),this.transform.dispose(),this.highlight.dispose(),this.background.dispose(),this.mousewheel.dispose(),this.panning.dispose(),this.view.dispose(),this.renderer.dispose(),this.installedPlugins.forEach((t=>{t.dispose()}))}}n([xf.dispose()],kA.prototype,"dispose",null),function(t){t.View=gA,t.Renderer=MA,t.MouseWheel=wA,t.DefsManager=EA,t.GridManager=yA,t.CoordManager=SA,t.TransformManager=vA,t.HighlightManager=OA,t.BackgroundManager=bA}(kA||(kA={})),function(t){t.toStringTag=`X6.${t.name}`,t.isGraph=function(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag];return null==n||n===t.toStringTag}}(kA||(kA={})),function(t){t.render=function(e,n){const r=e instanceof HTMLElement?new t({container:e}):new t(e);return null!=n&&r.fromJSON(n),r}}(kA||(kA={})),function(t){t.registerNode=Hw.registry.register,t.registerEdge=Jw.registry.register,t.registerView=ox.registry.register,t.registerAttr=Tb.registry.register,t.registerGrid=sv.registry.register,t.registerFilter=dv.registry.register,t.registerNodeTool=Sx.registry.register,t.registerEdgeTool=Ox.registry.register,t.registerBackground=cv.registry.register,t.registerHighlighter=Vb.registry.register,t.registerPortLayout=qb.registry.register,t.registerPortLabelLayout=Qb.registry.register,t.registerMarker=db.registry.register,t.registerRouter=Tw.registry.register,t.registerConnector=Vw.registry.register,t.registerAnchor=Gx.registry.register,t.registerEdgeAnchor=Jx.registry.register,t.registerConnectionPoint=Qx.registry.register}(kA||(kA={})),function(t){t.unregisterNode=Hw.registry.unregister,t.unregisterEdge=Jw.registry.unregister,t.unregisterView=ox.registry.unregister,t.unregisterAttr=Tb.registry.unregister,t.unregisterGrid=sv.registry.unregister,t.unregisterFilter=dv.registry.unregister,t.unregisterNodeTool=Sx.registry.unregister,t.unregisterEdgeTool=Ox.registry.unregister,t.unregisterBackground=cv.registry.unregister,t.unregisterHighlighter=Vb.registry.unregister,t.unregisterPortLayout=qb.registry.unregister,t.unregisterPortLabelLayout=Qb.registry.unregister,t.unregisterMarker=db.registry.unregister,t.unregisterRouter=Tw.registry.unregister,t.unregisterConnector=Vw.registry.unregister,t.unregisterAnchor=Gx.registry.unregister,t.unregisterEdgeAnchor=Jx.registry.unregister,t.unregisterConnectionPoint=Qx.registry.unregister}(kA||(kA={}));class NA extends Hw{}!function(t){class e extends hA{init(){super.init(),this.cell.on("change:*",(({key:e})=>{const n=t.shapeMaps[this.cell.shape];if(n){const{effect:t}=n;t&&!t.includes(e)||this.renderHTMLComponent()}}))}confirmUpdate(t){const n=super.confirmUpdate(t);return this.handleAction(n,e.action,(()=>this.renderHTMLComponent()))}renderHTMLComponent(){const e=this.selectors&&this.selectors.foContent;if(e){Op(e);const n=t.shapeMaps[this.cell.shape];if(!n)return;let{html:r}=n;"function"==typeof r&&(r=r(this.cell)),r&&("string"==typeof r?e.innerHTML=r:Tp(e,r))}}}t.View=e,function(t){t.action="html",t.config({bootstrap:[t.action],actions:{html:t.action}}),hA.registry.register("html-view",t,!0)}(e=t.View||(t.View={}))}(NA||(NA={})),function(e){e.config({view:"html-view",markup:[{tagName:"rect",selector:"body"},Object.assign({},t.Markup.getForeignObjectMarkup()),{tagName:"text",selector:"label"}],attrs:{body:{fill:"none",stroke:"none",refWidth:"100%",refHeight:"100%"},fo:{refWidth:"100%",refHeight:"100%"}}}),Hw.registry.register("html",e,!0)}(NA||(NA={})),function(t){t.shapeMaps={},t.register=function(n){const{shape:r,html:i,effect:s,inherit:o}=n,a=e(n,["shape","html","effect","inherit"]);if(!r)throw new Error("should specify shape in config");t.shapeMaps[r]={html:i,effect:s},kA.registerNode(r,Object.assign({inherit:o||"html"},a),!0)}}(NA||(NA={}));var jA=Object.freeze({__proto__:null,Circle:cA,Edge:eA,Ellipse:nA,get HTML(){return NA},Image:lA,Path:oA,Polygon:iA,Polyline:sA,Rect:tA,TextBlock:aA});t.ArrayExt=wf,t.BackgroundManager=bA,t.Basecoat=xf,t.Cell=qw,t.CellView=ox,t.Collection=Xw,t.Color=xy,t.Config=Nb,t.CssLoader=Sy,t.Curve=zy,t.Dictionary=wy,t.Disablable=class extends xf{get disabled(){return!0===this._disabled}enable(){delete this._disabled}disable(){this._disabled=!0}},t.Disposable=r,t.DisposableDelegate=class{constructor(t){this.callback=t}get disposed(){return!this.callback}dispose(){if(!this.callback)return;const t=this.callback;this.callback=null,t()}},t.DisposableSet=i,t.Dom=fy,t.Edge=Jw,t.EdgeView=uA,t.Ellipse=jy,t.Events=lf,t.FunctionExt=of,t.Graph=kA,t.GraphView=gA,t.Line=Ny,t.Model=Yw,t.Node=Hw,t.NodeView=hA,t.NumberExt=Rf,t.ObjectExt=bf,t.Path=Ky,t.Point=Ty,t.Polyline=Vy,t.PriorityQueue=yy,t.Rectangle=ky,t.Registry=Fw,t.Segment=$y,t.Shape=jA,t.StringExt=Lf,t.Text=Hp,t.ToolsView=ax,t.TransformManager=vA,t.Vector=dm,t.View=nx,t.normalizePathData=Zy}));
//# sourceMappingURL=index.js.map