package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.file.vo.*;
import cn.ac.picb.vipmap.client.FileServiceClient;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class FileServiceClientFallback implements FileServiceClient {

    private final static String SERVER_NAME = "file-service";

    @Override
    public CommonResult<FileList> listDir(PathParamVO pathParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<DirectoryTreeNode>> listDirectory(PathParamVO pathParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<TreeNode>> fileTreeNodes(PathParamVO pathParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Boolean> createFolder(FolderParamVO folderParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Boolean> uploadFile(String s, String s1, MultipartFile multipartFile) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Boolean> deleteFile(PathVO pathVO) {
        return serverError(SERVER_NAME);
    }


}
