<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
    <link rel="stylesheet" th:href="@{/css/select2.min.css}">
    <style>
        .fromMafCls {
            display: none;
        }
    </style>
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('dnaseq-somatic-adv-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">WES/WGS-somatic Advanced Analysis</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/somatic-adv/form}" class="active">Add Task</a>
                            <a th:href="@{/analysis/somatic-adv/list}">Task List</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade">
                            <div class="p-2">
                                <div class="form-group-box border-top-0">
                                    <h6 class="text-primary border-bottom pb-2">Select input files</h6>
                                    <div class="pl-4 pb-2">
                                        <div class="d-none" data-id="file1" id="errorMsgDiv">
                                            <div class="alert alert-danger alert-with-icon alert-dismissible"
                                                 role="alert">
                                                <button type="button" class="close" data-dismiss="alert"
                                                        aria-label="Close"><span aria-hidden="true">×</span>
                                                </button>
                                                <span class="alert-icon-wrap">
                                                <i class="fa fa-exclamation-circle fa-lg"></i>
                                            </span>
                                                <p class="m-0">
                                                    The header of maf file must have these columns: Hugo_Symbol,
                                                    Chromosome, Start_Position, End_Position, Reference_Allele,
                                                    Tumor_Seq_Allele2, Variant_Classification, Variant_Type,
                                                    Tumor_Sample_Barcode and aaChange
                                                </p>
                                            </div>
                                        </div>

                                        <div class="form-group radio-collapse">
                                            <div class="d-flex row align-items-center mb-2">
                                                <div class="col-auto">
                                                    <div class="custom-control custom-radio mb-1">
                                                        <input type="radio" id="file1" name="sinfile" value="fromMaf"
                                                               class="custom-control-input">
                                                        <label class="custom-control-label" for="file1">Upload your
                                                            maf <a href="javascript:;" class="text-danger"
                                                                   data-container="body" data-html="true"
                                                                   data-trigger="focus" data-toggle="popover"
                                                                   data-placement="top"
                                                                   data-content="<a target='_blank' href='https://docs.gdc.cancer.gov/Data/File_Formats/MAF_Format'>https://docs.gdc.cancer.gov/Data/File_Formats/MAF_Format</a>"><i
                                                                    class="fa fa-question-circle"></i></a>
                                                            files</label>
                                                    </div>
                                                </div>
                                                <div class="col-auto td-input">
                                                    <div class="input-group input-group-sm">
                                                        <div class="input-group-prepend">
                                                            <!--<button class="btn btn-outline-secondary btn-sm"
                                                                    type="button" data-toggle="modal"
                                                                    data-target=".sel-modal">Select
                                                            </button>-->
                                                            <button class="btn btn-primary btn-sm fromMafCls"
                                                                    type="button"
                                                                    onclick="showFileModal(this)">Select
                                                            </button>
                                                        </div>
                                                        <div class="input-group-prepend">
                                                          <span class="input-group-text" id="select_file_group">
                                                            <em class="seled">
                                                                <!--<b class="text-primary width-300"
                                                                   data-toggle="tooltip"
                                                                   title="Sample1_S1_L001_R1_001.fastq.gz">Sample1_S1_L001_R1_001.fastq.gz
                                                                    <span><a href="javascript:void(0);"
                                                                             class="text-danger"><i
                                                                            class="fa fa-times-circle"></i></a></span>
                                                                </b>-->
                                                            </em>
                                                          </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex row align-items-center">
                                                <div class="col-auto">
                                                    <div class="custom-control custom-radio mb-1">
                                                        <input type="radio" id="file2" name="sinfile" value="fromBasic"
                                                               class="custom-control-input" checked>
                                                        <label class="custom-control-label" for="file2">SNVs+indels
                                                            Basic Analysis</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div data-id="file2">
                                            <form th:action="@{/analysis/somatic-adv/form}" id="search-form">
                                                <div class="form-inline form-task my-2">
                                                    <div class="d-flex">
                                                        <label class="mx-2 font-12">Task ID</label>
                                                        <select name="batch"
                                                                class="form-control form-control-sm select2">
                                                            <option value="">Choose</option>
                                                            <th:block th:each="item:${somaticBatchQueryVO?.batchIds}">
                                                                <option th:selected="${#strings.equals(query?.batch, item)}"
                                                                        th:value="${item}" th:text="${item}"></option>
                                                            </th:block>
                                                        </select>
                                                        <label class="mx-2 font-12">Task Name</label>
                                                        <input th:value="${query?.taskName}"
                                                               type="text"
                                                               class="form-control form-control-sm" name="taskName"/>
                                                        <!--/*
                                                        <select name="taskName"
                                                                class="form-control form-control-sm select2">
                                                            <option value="">Choose</option>
                                                            <th:block th:each="item:${somaticBatchQueryVO?.taskNames}">
                                                                <option th:selected="${#strings.equals(query?.taskName, item)}"
                                                                        th:value="${item}" th:text="${item}"></option>
                                                            </th:block>
                                                        </select>
                                                        */-->

                                                        <label class="mx-2 font-12">Time</label>
                                                        <div class="input-daterange input-group">
                                                            <input th:value="${#dates.format(query?.start,'yyyy-MM-dd')}"
                                                                   type="text"
                                                                   class="form-control form-control-sm" name="start"/>
                                                            <div class="input-group-append">
                                                                <span class="input-group-text"><i
                                                                        class="fa fa-calendar"></i></span>
                                                            </div>
                                                            <input th:value="${#dates.format(query?.end,'yyyy-MM-dd')}"
                                                                   type="text" class="form-control form-control-sm"
                                                                   name="end"/>
                                                            <div class="input-group-append">
                                                                <span class="input-group-text"><i
                                                                        class="fa fa-calendar"></i></span>
                                                            </div>
                                                        </div>

                                                    </div>
                                                    <div class="d-flex">
                                                        <button type="submit" class="btn btn-primary btn-sm m-2">
                                                            Search
                                                        </button>
                                                    </div>

                                                </div>
                                            </form>
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-sm table-middle font-12">
                                                    <thead>
                                                    <tr class="thead-light">
                                                        <th>Task ID</th>
                                                        <th>Task Name</th>
                                                        <th>Genome Version</th>
                                                        <th>PON</th>
                                                        <th>Capture Regions</th>
                                                        <th>Advanced Analysis</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <th:block th:unless="${#lists.isEmpty(pageResult.content)}">
                                                        <tr th:each="item : ${pageResult.content}">
                                                            <td th:text="${item?.task?.batch}"></td>
                                                            <td th:text="${item?.task?.taskName}"></td>
                                                            <td th:text="${item?.task?.refVersion}"></td>
                                                            <td th:text="${item?.task?.pon}"></td>
                                                            <td th:text="${item?.task?.exonBed}"></td>
                                                            <td>
                                                                <div class="btn-group">
                                                                    <a href="javascript:void(0);"
                                                                       th:attr="data-run-names=${item?.runNames},data-batch-id=${item?.task?.batch}"
                                                                       onclick="selectRunNames(this)"
                                                                       class="btn btn-outline-primary btn-sm py-0">Select</a>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </th:block>
                                                    <th:block th:if="${#lists.isEmpty(pageResult.content)}">
                                                        <tr>
                                                            <td colspan="6" style="text-align: center">no data</td>
                                                        </tr>
                                                    </th:block>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="pt-1 mb-2">
                                                <div th:replace="~{base/pageable}"></div>
                                            </div>
                                        </div>

                                    </div>


                                </div>
                            </div>

                            <div class="text-center mb-3 fromMafCls">
                                <a href="javascript:void(0);" onclick="submitForm(this)"
                                   class="btn btn-outline-primary btn-custom"><span>Next</span><i
                                        class="fa fa-long-arrow-right"></i></a>
                            </div>
                        </div>

                    </div>
                </div>
            </main>
        </div>
    </div>
    <div id="file-modal"></div>

    <div id="basic-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title font-14 mt-0">Select</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body" style="max-height:450px;overflow:auto;">
                    <div class="table-responsive">
                        <table class="table table-bordered text-center table-sm mb-0">
                            <thead>
                            <tr>
                                <th>RunName</th>
                                <th width="80">vcf</th>
                                <th>RunName</th>
                                <th width="80">vcf</th>
                                <th>RunName</th>
                                <th width="80">vcf</th>
                                <th>RunName</th>
                                <th width="80">vcf</th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>

                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm font-12" data-dismiss="modal">Close</button>
                    <button type="button" onclick="submitForm(this)" class="btn btn-primary btn-sm font-12">Next<i
                            class="fa fa-long-arrow-right"></i></button>
                </div>
            </div>
        </div>
    </div>
</div>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>

    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script th:src="@{/js/select2.min.js}"></script>


    <script>
        var isValidatedMaf = false;
        var errorMsg = '';
        $("#file-modal").fileModal('/analysis/fileTree');

        $(document).ready(function () {
            $('input[name="sinfile"]').change(function () {
                let val = $(this).val();
                if ('fromMaf' == val) {
                    $('.fromMafCls').show();
                } else {
                    $('.fromMafCls').hide();
                }
            });

            $('.select2').select2();

            $('.radio-collapse input[type=radio]').click(function () {
                $(this).parents('.radio-collapse').find('input').each(function () {
                    var id = $(this).attr('id')
                    if (id) {
                        $('[data-id="' + id + '"]').addClass('d-none')
                    }
                })
                var selfId = $(this).attr('id')
                $('[data-id="' + selfId + '"]').removeClass('d-none')
            });

            $('.input-daterange').datepicker({
                format: "yyyy-mm-dd",
                toggleActive: true,
                autoclose: true,
                todayHighlight: true
            });

            $("#basic-modal").on("hidden.bs.modal", function () {
                let tbodyData = $(this).find('div[class="modal-body"] table tbody');
                tbodyData.html('');
            });

        });

        function removeFile(_this) {
            $(_this).parent().parent().remove();
            $(":radio[name='pon']:first").trigger('click');
        }

        var _selectBtn;
        var _selectFile;
        $("#file-modal").on('__SELECT_FILES__', function (e, data) {
            var nodes = data.nodes || []
            if (nodes.length === 0) {
                return;
            }

            var html = [];
            let mafFileData = {};
            $.each(nodes, function (i, node) {
                var filePath = node.path;
                var fileName = node.name;
                var fileSize = node.size;
                if (i == 0) {
                    mafFileData['name'] = fileName;
                    mafFileData['path'] = filePath;
                    mafFileData['size'] = fileSize;
                }
                html.push('<b class="text-primary width-150" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '');
                html.push('<input type="hidden" value="' + filePath + '">');
                html.push('<input type="hidden" value="' + fileName + '">');
                html.push('<input type="hidden" value="' + fileSize + '">');
                html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>');
                html.push('</b>');
            });

            let loadIndex = layer.load(2);
            $.ajax({
                url: '/analysis/somatic-adv/validateMafFile',
                dataType: 'json',
                type: 'post',
                async: true,
                cache: false,
                data: mafFileData,
                success: function (result) {
                    if (result.code == 200) {
                        let resultData = result.data;
                        if (resultData.success) {
                            if (_selectFile) {
                                if (nodes.length > 1) {
                                    layer.msg("only one file can be selected");
                                    return;
                                }
                                $(_selectFile).parent().next().find('em.seled:first').html(html.join(''));
                            } else {
                                $(_selectBtn).parent().next().find('em.seled:first').html(html.join(''));
                            }

                            $('[data-toggle="tooltip"]').tooltip();

                            isValidatedMaf = true;
                            errorMsg = '';
                        } else {
                            layer.msg("Upload failed");
                            isValidatedMaf = false;
                            errorMsg = resultData.msg;
                            if (_selectFile) {
                                $(_selectFile).parent().next().find('em.seled:first').html('');
                            } else {
                                $(_selectBtn).parent().next().find('em.seled:first').html('');
                            }
                        }
                        handleErrorMsg();
                    }
                },
                complete: function () {
                    layer.close(loadIndex);
                }
            });
        });

        function handleErrorMsg() {
            $('#errorMsgDiv').html('');
            if (errorMsg) {
                $('#errorMsgDiv').append(`<div class="alert alert-danger alert-with-icon alert-dismissible"
                                             role="alert">
                                            <button type="button" class="close" data-dismiss="alert"
                                                    aria-label="Close"><span aria-hidden="true">×</span>
                                            </button>
                                            <span class="alert-icon-wrap">
                                                <i class="fa fa-exclamation-circle fa-lg"></i>
                                            </span>
                                            <p class="m-0">${errorMsg}</p>
                                        </div>`);
            }
        }

        function showFileModal(_this) {
            _selectBtn = _this;
            _selectFile = null;

            var selectIds = [];
            $(_this).parents("td:first").find("em").find("b.text-primary").each(function () {
                selectIds.push($(this).find("input[type=hidden]:eq(0)").val());
            });
            $("#file-modal").trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: selectIds,
                func: function (data) {
                    return !data.name || /.*\.maf/.test(data.name);
                }
            });
        }

        function selectRunNames(_this) {
            let tbodyData = $('#basic-modal').find('div[class="modal-body"] table tbody');
            tbodyData.html('');
            let runNamesObj = $.parseJSON($(_this).attr('data-run-names'));
            if (runNamesObj && runNamesObj.length > 0) {

                let length = runNamesObj.length;
                let trCount = Math.ceil(length / 3);
                let index = 0;
                let trData = [];
                for (let i = 0; i < trCount; i++) {
                    trData.push('<tr>');
                    for (let j = 0; j < 4; j++) {
                        if (index <= length - 1) {
                            let element = runNamesObj[index];
                            let tdInfo = `<td>${element}</td>
                                  <td><input type="checkbox" name="basicRun" checked value="${element}"></td>`;
                            trData.push(tdInfo);
                            index = index + 1;
                        } else {
                            trData.push('<td></td><td></td>');
                        }
                    }
                    trData.push('</tr>');
                }
                let batchId = $(_this).attr('data-batch-id');
                tbodyData.html(`<input type="hidden" name="batchId" value="${batchId}" />` + trData.join(''));
            }
            $("#basic-modal").modal('show');
        }


        function submitForm(_this) {
            let sinfileVal = $('input[name="sinfile"]:checked').val();
            let formData = new FormData();
            if ('fromMaf' == sinfileVal) {
                let hasFile = false;
                $('#select_file_group').find('b.text-primary').each(function (i, e) {
                    // 只取第一个文件
                    if (i == 0) {
                        hasFile = true;
                        formData.append("mafFileVo.path", $(this).find("input[type=hidden]:eq(0)").val());
                        formData.append("mafFileVo.name", $(this).find("input[type=hidden]:eq(1)").val());
                        formData.append("mafFileVo.size", $(this).find("input[type=hidden]:eq(2)").val());
                    }
                });

                if (!hasFile) {
                    layer.msg("Please select a maf file");
                    return false;
                }

                if (!isValidatedMaf) {
                    handleErrorMsg()
                    return false;
                }
            } else {
                let tbodyObj = $('#basic-modal').find('div[class="modal-body"] table tbody');
                let checkedRuns = tbodyObj.find('input[type="checkbox"]:checked');
                if (!checkedRuns || checkedRuns.length == 0) {
                    layer.msg("Please select at least one run name");
                    return false;
                }

                checkedRuns.each(function (i, item) {
                    formData.append("runNameList['" + i + "']", $(item).val());
                });
                formData.append("basicBatchId", tbodyObj.find('input[name="batchId"]').val() || '');
            }

            if ($(_this).data('loading') == 'true') {
                return;
            }
            $(_this).data('loading', 'true');

            let loadIndex = layer.load(2);
            $.ajax({
                url: '/analysis/somatic-adv/createAdvTask',
                dataType: 'json',
                type: 'post',
                processData: false,
                contentType: false,
                data: formData,
                success: function (result) {
                    if (result.code == 200) {
                        // layer.msg('submit success');
                        var id = result.data;
                        setTimeout(function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            window.location.href = $.trim(_context_path) + '/analysis/somatic-adv/form-adv/' + id;
                        }, 200);
                    }
                },
                complete: function () {
                    layer.close(loadIndex);
                    $(_this).data('loading', 'false');
                }
            });
        }

    </script>
</th:block>
</html>
