package cn.ac.picb.vipmap.service;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.vipmap.client.WgbsServiceClient;
import cn.ac.picb.vipmap.mapper.WgbsMapper;
import cn.ac.picb.vipmap.vo.CurrentUser;
import cn.ac.picb.vipmap.vo.WgbsChartFilter;
import cn.ac.picb.vipmap.vo.WgbsTaskParam;
import cn.ac.picb.vipmap.vo.WgbsTaskSearchVO;
import cn.ac.picb.wgbs.dto.WgbsTaskDTO;
import cn.ac.picb.wgbs.po.WgbsTaskPO;
import cn.ac.picb.wgbs.vo.WgbsChartFilterVO;
import cn.ac.picb.wgbs.vo.WgbsTaskInput;
import cn.ac.picb.wgbs.vo.WgbsTaskParamVO;
import cn.ac.picb.wgbs.vo.WgbsTaskQueryVO;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> Li
 */
@Service
@RequiredArgsConstructor
public class WgbsService {

    private final WgbsServiceClient wgbsServiceClient;

    public PageResult<WgbsTaskPO> findPage(CurrentUser user, WgbsTaskSearchVO search, PageParam pageParam) {
        WgbsTaskQueryVO queryVO = WgbsMapper.INSTANCE.convert(search);
        queryVO.setUserId(user.getId());
        queryVO.setPage(pageParam.getPage());
        queryVO.setSize(pageParam.getSize());
        CommonResult<PageResult<WgbsTaskPO>> result = wgbsServiceClient.findTaskPage(queryVO);
        result.checkError();
        return result.getData();
    }

    public WgbsTaskPO createTask(CurrentUser user, WgbsTaskParam param) {
        WgbsTaskParamVO vo = WgbsMapper.INSTANCE.convertToVO(param);
        vo.setUserId(user.getId());
        vo.setUsername(user.getUsername());
        CommonResult<WgbsTaskPO> result = wgbsServiceClient.saveTask(vo);
        result.checkError();
        return result.getData();
    }

    public void deleteTask(String id) {
        CommonResult<WgbsTaskPO> result = wgbsServiceClient.deleteById(id);
        result.checkError();
    }

    public Response downloadResult(String taskId, Integer step, String displayName) {
        return wgbsServiceClient.downloadResult(taskId, step, displayName);
    }

    public WgbsTaskDTO findTaskVO(String id) {
        CommonResult<WgbsTaskDTO> result = wgbsServiceClient.findDetailById(id);
        result.checkError();
        return result.getData();
    }


    public Response downloadTemplate() {
        return wgbsServiceClient.downloadTemplateExcel();
    }

    public List<WgbsTaskInput> uploadTemplate(MultipartFile file, CurrentUser user) {
        final CommonResult<List<WgbsTaskInput>> result = wgbsServiceClient.uploadDataExcel(user.getUsername(), file);
        result.checkError();
        return result.getData();
    }


    public Object getChartData(String taskId, Integer chartNo, WgbsChartFilter filter) {
        WgbsChartFilterVO vo = WgbsMapper.INSTANCE.filterToVo(filter);
        vo.setTaskId(taskId);
        vo.setChartNo(chartNo);
        CommonResult<Object> result = wgbsServiceClient.getChartData(vo);
        result.checkError();
        return result.getData();
    }

    public Response downloadChart(WgbsChartFilter filter) {
        WgbsChartFilterVO vo = WgbsMapper.INSTANCE.filterToVo(filter);
        return wgbsServiceClient.downloadChart(vo);
    }
}
