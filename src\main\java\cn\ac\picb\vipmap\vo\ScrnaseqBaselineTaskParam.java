package cn.ac.picb.vipmap.vo;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ScrnaseqBaselineTaskParam {

    @NotBlank
    private String genomicId;

    @NotBlank
    private String taskName;

    @Max(15)
    @Min(5)
    private Integer selectPca;

    private String annMethod;

    private List<ScrnaseqBaselineParam> params;

}
