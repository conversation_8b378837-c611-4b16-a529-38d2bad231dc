<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-advanced')}"></div>
            <main style="max-width: calc(100% - 100px) !important;">
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq</h4>
                <div class="border rounded ana-content w-100">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/form/genomics}">Add Task</a>
                            <a th:href="@{/analysis/scrnaseq/list(type='advanced')}" class="active">Task list</a>
                        </div>
                    </div>
                    <div id="table-content">
                        <form class="form-custom" id="search-form" style="padding: 0 15px;">
                            <div class="form-group-box" style="border: transparent;">
                                <div class="d-flex flex-wrap pl-4 pt-2">
                                    <div class="form-group row align-items-center">
                                        <label class="cmx-2 font-12">Type</label>
                                        <div class="col-xl-4 col-lg-4 col-md-4">
                                            <div class="input-group ml--10 width-160">
                                                <select name="type" class="form-control select2 form-control-sm">
                                                    <option value="">Choose type</option>
                                                    <option value="baseline">Cluster Description</option>
                                                    <option value="paga">Pseudotime Trajectory</option>
                                                    <option value="deg">DEG and Enrichment</option>
                                                    <option value="genes">Gene Description</option>
                                                    <option value="wgcna">WGCNA</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row align-items-center mr-1 ml--8">
                                        <label class="mx-2 font-12 ml-3">Task ID</label>
                                        <div class="col-xl-4 col-lg-4 col-md-4 search">
                                            <input class="form-control form-control-sm width-100 ml--16" name="taskId"
                                                   type="text">
                                        </div>
                                    </div>
                                    <div class="basic-name form-group row align-items-center">
                                        <label class="mx-2 font-12">Task Name</label>
                                        <div class="col-xl-4 col-lg-4 col-md-4 search">
                                            <input class="form-control form-control-sm width-100 ml--16" name="taskName"
                                                   type="text" onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                        </div>
                                    </div>
                                    <div class="form-group ml-1 row align-items-center width-345">
                                        <label class="mx-2 font-12">Time</label>
                                        <div class="input-daterange input-group width-300">
                                            <input type="text" class="form-control form-control-sm max-width-97"
                                                   name="start"/>
                                            <div class="input-group-append">
                                                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                            </div>
                                            <input type="text" class="form-control form-control-sm max-width-97"
                                                   name="end"/>
                                            <div class="input-group-append">
                                                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary btn-sm h-31" id="search-btn"
                                            onclick="search()">
                                        Search
                                    </button>
                                </div>
                                <div class="table-result table-responsive" style="padding: 0 15px;">
                                    <table id="tree-all"
                                           style="table-layout: fixed; white-space: normal; text-overflow: ellipsis; overflow: hidden; width: 1058px;"
                                           class="table table-bordered table-sm table-center table-middle font-12 table-striped mb-0 treetable">
                                        <thead>
                                        <tr class="thead-light">

                                            <th>
                                                <div class="row treeTitle">
                                                    <div class="float-f col-md-3 col-xl-3 col-lg-3">Task ID</div>
                                                    <div class="float-f col-md-2 col-xl-2 col-lg-2">Task Name</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Speices</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Start time</div>
                                                    <div class="float-f col-md-2 col-xl-2 col-lg-2">Status</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Status time</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Consuming</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Action</div>
                                                </div>
                                            </th>
                                            <!-- <th>Task ID</th>
                                            <th>Task Name</th>
                                            <th>Speices</th>
                                            <th>Start time</th>
                                            <th>Status</th>
                                            <th>Status time</th>
                                            <th>Consuming</th>
                                            <th>Action</th> -->
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td style="text-align: center;">
                                                <div class="spinner-border text-muted"></div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>

                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script>

      let TREE_DATA

      $(document).ready(function () {
        $('.select2').select2()

        $('.input-daterange').datepicker({
          format: "yyyy/mm/dd",
          toggleActive: true,
          autoclose: true,
          todayHighlight: true
        })
        // 禁用按钮防止重复点击
        $('#search-btn').attr('disabled', true)
        $.ajax({
          url: "/analysis/scrnaseq/findAllTaskWithTree",
          data: $('#search-form').serialize(),
          type: 'post',
          dataType: 'json',
          success: function (result) {
            if (result.success) {
              TREE_DATA = result.data
              let source = getSource(TREE_DATA)
              obtainToAdvancedTable(source)
            } else {
              layer.alert(result.message, { icon: 2 })
            }
            setTimeout(() => { $('#search-btn').attr('disabled', false)}, 1000)
          }
        })

      })

      function search () {
        let query = {
          type: $('select[name="type"]').val(),
          taskId: $('input[name="taskId"]').val(),
          taskName: $('input[name="taskName"]').val(),
          start: $('input[name="start"]').val().replace(/\//g, ""),
          end: $('input[name="end"]').val().replace(/\//g, ""),
        }
        obtainToAdvancedTable(getSource(filter(TREE_DATA, query)))
      }

      // nodes就是树形的最原始数据，query就是关键字，最后会返回满足条件的节点数组
      function filter (nodes, query) {
        // 条件就是节点的title过滤关键字
        let predicate = function (node) {
          let flag = true
          let createTime = parseInt(node.createTime.replace(/-/g, "").substr(0, 8))
          // 过滤type
          if (query.type !== null && query.type !== undefined && query.type !== '') {
            flag = node.type === query.type
          }
          // 过滤taskId
          if (query.taskId !== null && query.taskId !== undefined && query.taskId !== '') {
            flag = node.taskId.indexOf(query.taskId) > -1
          }
          // 过滤开始时间
          if (query.start !== null && query.start !== undefined && query.start !== '') {
            flag = query.start <= createTime
          }
          // 过滤结束时间
          if (query.end !== null && query.end !== undefined && query.end !== '') {
            flag = query.end >= createTime
          }
          return flag
        }
        if (!(nodes && nodes.length)) {
          return []
        }
        let newChildren = []
        for (let node of nodes) {
          // 以下两个条件任何一个成立，当前节点都应该加入到新子节点集中
          // 1. 子孙节点中存在符合条件的，即 subs 数组中有值
          // 2. 自己本身符合条件
          let subs = this.filter(node.children, query)
          if (predicate(node)) {
            newChildren.push(node)
          } else if (subs && subs.length) {
            node.children = subs
            newChildren.push(node)
          }

          // 以下只需要考虑自身的节点满足条件即可,不用带上父节点
          /* if (predicate(node)) {
             newChildren.push(node)
             node.children = this.filter(node.children, query)
           } else {
             newChildren.push(...this.filter(node.children, query))
           }*/
        }
        return newChildren.length ? newChildren : []
      }

      function getSource (data) {
        let source = []
        data.forEach(it => {
          let item = {}
          obtainToNewTree(it, item)
          source.push(item)
        })
        return (source)
      }

      function obtainToNewTree (node, _newNode) {
        // node => n
        _newNode.title = generateTitle(node)

        if (node.children === null || node.children.length === 0) {
          _newNode.expanded = false
          _newNode.folder = false
        } else {
          _newNode.expanded = true
          _newNode.folder = true
          _newNode.children = []
          node.children.forEach(it => {
            let child = {}
            _newNode.children.push(child)
            obtainToNewTree(it, child)
          })
        }
      }

      function obtainToAdvancedTable (source) {
        $('.table-result').html(`<table id="tree-all"
                                           style="table-layout: fixed; white-space: normal; text-overflow: ellipsis; overflow: hidden; width: 1058px;"
                                           class="table table-bordered table-sm table-center table-middle font-12 table-striped mb-0 treetable">
                                        <thead>
                                        <tr class="thead-light">

                                            <th>
                                                <div class="row treeTitle">
                                                    <div class="float-f col-md-3 col-xl-3 col-lg-3">Task ID</div>
                                                    <div class="float-f col-md-2 col-xl-2 col-lg-2">Task Name</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Speices</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Start time</div>
                                                    <div class="float-f col-md-2 col-xl-2 col-lg-2">Status</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Status time</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Consuming</div>
                                                    <div class="float-f col-md-1 col-xl-1 col-lg-1">Action</div>
                                                </div>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td style="text-align: center;">
                                                <div class="spinner-border text-muted"></div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>`)

        if (source.length === 0) {
          $('#tree-all').find('tbody').html(`<tr>
                                            <td colspan="8" style="text-align: center;">
                                            </td>
                                        </tr>`)
        } else {
          $('#tree-all').find('tbody').html(`<tr>
                                            <td style="text-align: left;">
                                            </td>
                                        </tr>`)
        }

        $("#tree-all").fancytree({
          extensions: ["table"],
          checkbox: false,
          selectMode: 3,
          source: source,
          toggleEffect: {
            effect: "blind",
            options: {
              direction: "vertical",
              scale: "box"
            },
            duration: 200
          }
        })
      }

      function generateTitle (node) {
        if (!node) {
          return ''
        }
        var data = node
        var nodeType = data.type
        var html = []

        var map = {
          '-1': "<span class='text-danger'><i class='fa fa-circle'></i>Error</span>&nbsp;&nbsp;",
          '0': '<span class="text-info"><i class="fa fa-circle"></i>Deleted</span>&nbsp;&nbsp;',
          '1': '<span class="text-info"><i class="fa fa-circle"></i>Prepared</span>&nbsp;&nbsp;',
          '2': '<span class="text-info"><i class="fa fa-circle"></i>Ready</span>&nbsp;&nbsp;',
          '3': '<span class="text-info"><i class="fa fa-circle"></i>Import data</span>&nbsp;&nbsp;',
          '4': '<span class="text-success"><i class="fa fa-circle"></i>Analysis done</span>&nbsp;&nbsp;'
        }

        var statusMap = {
          '-1': "<span class='text-danger'><i class='fa fa-circle'></i>Error</span>&nbsp;&nbsp;",
          '0': '<span class="text-info"><i class="fa fa-circle"></i>Deleted</span>&nbsp;&nbsp;',
          '1': '<span class="text-info"><i class="fa fa-circle"></i>Prepared</span>&nbsp;&nbsp;',
          '2': '<span class="text-success"><i class="fa fa-circle"></i>Analysis done</span>&nbsp;&nbsp;',
        }

        switch (nodeType) {
          case "genomics":
            html.push('<div class="row treeTitle mL float-f mtb--17">')
            html.push(`    <div class="float-f col-md-3 col-xl-3 col-lg-3">Basic Analysis&nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>${data.taskId}</span></div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center">${data.taskName ? data.taskName : ''}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.species}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.createTime}</div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center">${map[data.status]}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.updateTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.useTime === null ? '' : data.useTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">
                                <div class='btn-group' >
                                    <a onclick="viewTask('genomics','${data.id}')" class= 'text-primary' data-toggle='tooltip' title = 'View Result'><i class='fa fa-eye font-14'></i></a>
                                    <a onclick="deleteTask('genomics','${data.id}')" class='text-danger' data-toggle='tooltip' title='Delete'><i class='fa fa-times font-14'></i></a>
                                 </div >
                            </div>`)
            html.push(`</div>`)
            break
          case "baseline":
            html.push('<div class="row treeTitle mL float-f mtb--17">')
            html.push(`    <div class="float-f col-md-3 col-xl-3 col-lg-3">Cluster Description&nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>${data.taskId}</span></div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center ml--18">${data.taskName ? data.taskName : ''}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center"></div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.createTime}</div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center">${statusMap[data.status]}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.updateTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.useTime === null ? '' : data.useTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">
                                <div class='btn-group' >
                                    <a onclick="viewTask('baseline','${data.id}')" class= 'text-primary' data-toggle='tooltip' title = 'View Result'><i class='fa fa-eye font-14'></i></a>
                                    <a onclick="deleteTask('baseline','${data.id}')" class='text-danger' data-toggle='tooltip' title='Delete'><i class='fa fa-times font-14'></i></a>
                                 </div >
                            </div>`)
            html.push(`</div>`)
            break
          case "paga":
            html.push('<div class="row treeTitle mL float-f mtb--17">')
            html.push(`    <div class="float-f col-md-3 col-xl-3 col-lg-3">Pseudotime Trajectory&nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>${data.taskId}</span></div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center ml--39">${data.taskName ? data.taskName : ''}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center"></div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.createTime}</div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center">${statusMap[data.status]}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.updateTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.useTime === null ? '' : data.useTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">
                                <div class='btn-group' >
                                    <a onclick="viewTask('paga','${data.id}')" class= 'text-primary' data-toggle='tooltip' title = 'View Result'><i class='fa fa-eye font-14'></i></a>
                                    <a onclick="deleteTask('paga','${data.id}')" class='text-danger' data-toggle='tooltip' title='Delete'><i class='fa fa-times font-14'></i></a>
                                 </div >
                            </div>`)
            html.push(`</div>`)
            break
          case "deg":
            html.push('<div class="row treeTitle mL float-f mtb--17">')
            html.push(`    <div class="float-f col-md-3 col-xl-3 col-lg-3">DEG and Enrichment&nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>${data.taskId}</span></div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center ml--39">${data.taskName ? data.taskName : ''}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center"></div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.createTime}</div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center">${statusMap[data.status]}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.updateTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.useTime === null ? '' : data.useTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">
                                <div class='btn-group' >
                                    <a onclick="viewTask('deg','${data.id}')" class= 'text-primary' data-toggle='tooltip' title = 'View Result'><i class='fa fa-eye font-14'></i></a>
                                    <a onclick="deleteTask('deg','${data.id}')" class='text-danger' data-toggle='tooltip' title='Delete'><i class='fa fa-times font-14'></i></a>
                                 </div >
                            </div>`)
            html.push(`</div>`)
            break
          case "wgcna":
            html.push('<div class="row treeTitle mL float-f mtb--17">')
            html.push(`    <div class="float-f col-md-3 col-xl-3 col-lg-3">WGCNA&nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>${data.taskId}</span></div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center ml--39">${data.taskName ? data.taskName : ''}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center"></div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.createTime}</div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center">${statusMap[data.status]}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.updateTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.useTime === null ? '' : data.useTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">
                                <div class='btn-group' >
                                    <a onclick="viewTask('wgcna','${data.id}')" class= 'text-primary' data-toggle='tooltip' title = 'View Result'><i class='fa fa-eye font-14'></i></a>
                                    <a onclick="deleteTask('wgcna','${data.id}')" class='text-danger' data-toggle='tooltip' title='Delete'><i class='fa fa-times font-14'></i></a>
                                 </div >
                            </div>`)
            html.push(`</div>`)
            break
          case "genes":
            html.push('<div class="row treeTitle mL float-f mtb--17">')
            html.push(`    <div class="float-f col-md-3 col-xl-3 col-lg-3">Gene Description&nbsp;&nbsp;<span class='badge badge-primary font-weight-normal'>${data.taskId}</span></div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center ml--39">${data.taskName ? data.taskName : ''}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center"></div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.createTime}</div>`)
            html.push(`    <div class="float-f col-md-2 col-xl-2 col-lg-2 text-center">${statusMap[data.status]}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.updateTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">${data.useTime === null ? '' : data.useTime}</div>`)
            html.push(`    <div class="float-f col-md-1 col-xl-1 col-lg-1 text-center">
                                <div class='btn-group' >
                                    <a onclick="viewTask('genes','${data.id}')" class= 'text-primary' data-toggle='tooltip' title = 'View Result'><i class='fa fa-eye font-14'></i></a>
                                    <a onclick="deleteTask('genes','${data.id}')" class='text-danger' data-toggle='tooltip' title='Delete'><i class='fa fa-times font-14'></i></a>
                                 </div >
                            </div>`)
            html.push(`</div>`)
            break
          default:
            html.push('')
            break
        }
        return html.join('')
      }

      $("#tree-all").tooltip({
        content: function () {
          return $(this).attr("title")
        }
      })

      function viewTask (type, id) {
        var _context_path = $("meta[name='_context_path']").attr("content")
        var url = $.trim(_context_path) + '/analysis/scrnaseq/taskDetail?type=' + type + "&id=" + id
        window.open(url)
      }

      function deleteTask (type, id) {
        layer.confirm('<p class="text-center">Are you sure you want to delete it？</p>', { btn: ['确认', '取消'] }, function () {
          var loadLayerIndex
          $.ajax({
            url: "/analysis/scrnaseq/deleteTask",
            data: { "id": id, "type": type },
            dataType: 'json',
            async: false,
            method: 'post',
            beforeSend: function () {
              loadLayerIndex = layer.load(1, {
                shade: [0.1, '#fff'] //0.1透明度的白色背景
              })
            },
            success: function (result) {
              if (result.success) {
                layer.msg("Deleted success", { time: 500 }, function () {
                  var tree = $.ui.fancytree.getTree("#tree-all")
                  tree.getActiveNode().remove()
                })
              } else {
                layer.alert(result.message, { icon: 2 })
              }
            },
            complete: function () {
              layer.close(loadLayerIndex)
            }
          })
        })
      }


    </script>
</th:block>
</html>
