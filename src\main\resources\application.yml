spring:
  application:
    name: vipmap-app
  thymeleaf:
    cache: false
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  profiles:
    active: @spring.profiles.active@
  resources:
    chain:
      strategy:
        content:
          enabled: true
          paths: /**
server:
  servlet:
    context-path: /bda
  error:
    include-stacktrace: never

security:
  cas:
    server:
      base-url: https://www.biosino.org/node-cas
      protocol-version: 2
    service:
      resolution-mode: dynamic
    authorization:
      mode: NONE


#management:
#  endpoints:
#    web:
#      exposure:
#        include: "*"
#  endpoint:
#    health:
#      show-details: always

ribbon:
  eager-load:
    enabled: true
    clients:
      - file-service
      - scrnaseq-service
      - rnaseq-service
      - paean-service
      - somatic-service
      - germline-service

feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 60000
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 15000

