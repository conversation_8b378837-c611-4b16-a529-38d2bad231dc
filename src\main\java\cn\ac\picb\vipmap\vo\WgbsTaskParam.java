package cn.ac.picb.vipmap.vo;

import cn.ac.picb.wgbs.vo.WgbsTaskInput;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class WgbsTaskParam {
    @NotBlank
    private String taskName;
    @NotNull
    private List<WgbsTaskInput> inputs;

    // mapping
    @NotBlank
    private String species;

    @NotNull
    private String control;

    @NotNull
    private String patient;

    @NotNull
    @Pattern(regexp = "^[0-9]+.?[0-9]*]*$", message = "P value 必须是数值")
    private String pvalue;

    @NotNull
    @Pattern(regexp = "^[0-9]+.?[0-9]*]*$", message = "delta 必须是数值")
    private String delta;

    @NotNull
    @Pattern(regexp = "^[0-9]+.?[0-9]*]*$", message = "p-value cutoff 必须是数值")
    private String pvalueCutoff;

    @NotNull
    @Pattern(regexp = "^[0-9]+.?[0-9]*]*$", message = "q-value cutoff 必须是数值")
    private String qvalueCutoff;


}
