<html xmlns:th="http://www.thymeleaf.org" th:with="pageTitle='分析管理', current='analysis'"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/jquery-ui.min.css}">
    <link rel="stylesheet" th:href="@{/css/skin1/ui.fancytree.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('dnaseq-somatic-cnvs-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">WES/WGS-somatic CNVs Analysis</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/somatic-cnvs/form}" class="active">Add Task</a>
                            <a th:href="@{/analysis/somatic-cnvs/list}">Task List</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade">
                            <div class="p-2">
                                <form action="" class="form-custom">
                                    <div class="form-group row align-items-center mx-0 mb-2">
                                        <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Task Name</label>
                                        <div class="col-xl-4 col-lg-3 col-md-8">
                                            <input name="taskName" type="text" class="form-control">
                                        </div>
                                    </div>

                                    <div class="pl-3 pb-2">
                                        <h6 class="text-primary border-bottom pb-2">Input file</h6>
                                        <div class="pl-3">
                                            <div class="form-group radio-collapse">
                                                <div class="d-flex row align-items-center mb-2">
                                                    <div class="col-auto">
                                                        <div class="custom-control custom-radio mb-1">
                                                            <input type="radio" id="file1" name="uploadtype"
                                                                   value="fastq"
                                                                   class="custom-control-input">
                                                            <label class="custom-control-label" for="file1">
                                                                FASTQ Files
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div data-id="file1" class="d-none mb-2">
                                                    <div class="d-flex mb-2">
                                                        <input type="file" id="excel"
                                                               class="form-control form-control-sm w-50 mr-2">
                                                        <button type="button" onclick="uploadExcel()"
                                                                class="btn btn-primary btn-sm mr-2 text-nowrap">上传
                                                        </button>
                                                        <a th:href="@{/analysis/somatic/downloadTemplate}"
                                                           class="btn btn-link btn-sm text-nowrap"><i
                                                                class="fa fa-download mr-1"></i>下载模版</a>
                                                    </div>
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered table-sm table-middle table-center">
                                                            <thead class="thead-light">
                                                            <tr>
                                                                <td width="180">RunName</td>
                                                                <td>R1(tumor)</td>
                                                                <td>R2(tumor)</td>
                                                                <td>R1(normal)</td>
                                                                <td>R2(normal)</td>
                                                                <td width="50"></td>
                                                            </tr>
                                                            </thead>
                                                            <tbody id="sample-table" class="input-file-table">
                                                            <tr>
                                                                <td class="td-input">
                                                                    <input type="text" onchange="showPnoListTable();"
                                                                           class="form-control text-center runName">
                                                                </td>
                                                                <td class="td-input">
                                                                    <div class="input-group input-group-sm">
                                                                        <div class="input-group-prepend">
                                                                            <button class="btn btn-primary btn-sm"
                                                                                    type="button"
                                                                                    onclick="showFileModal(this)">Select
                                                                            </button>
                                                                        </div>
                                                                        <div class="input-group-prepend">
                                                                          <span class="input-group-text">
                                                                            <em class="seled"></em>
                                                                          </span>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td class="td-input">
                                                                    <div class="input-group input-group-sm">
                                                                        <div class="input-group-prepend">
                                                                            <button class="btn btn-primary btn-sm"
                                                                                    type="button"
                                                                                    onclick="showFileModal(this)">Select
                                                                            </button>
                                                                        </div>
                                                                        <div class="input-group-prepend">
                                                                          <span class="input-group-text">
                                                                            <em class="seled"></em>
                                                                          </span>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td class="td-input">
                                                                    <div class="input-group input-group-sm">
                                                                        <div class="input-group-prepend">
                                                                            <button class="btn btn-primary btn-sm"
                                                                                    type="button"
                                                                                    onclick="showFileModal(this)">Select
                                                                            </button>
                                                                        </div>
                                                                        <div class="input-group-prepend">
                                                                          <span class="input-group-text">
                                                                            <em class="seled"></em>
                                                                          </span>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td class="td-input">
                                                                    <div class="input-group input-group-sm">
                                                                        <div class="input-group-prepend">
                                                                            <button class="btn btn-primary btn-sm"
                                                                                    type="button"
                                                                                    onclick="showFileModal(this)">Select
                                                                            </button>
                                                                        </div>
                                                                        <div class="input-group-prepend">
                                                                          <span class="input-group-text">
                                                                            <em class="seled"></em>
                                                                          </span>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <i class="fa fa-plus-square text-primary"
                                                                       onclick="addRow(this)"></i>
                                                                    <i class="fa fa-minus-square text-muted ml-1"
                                                                       onclick="removeRow(this)"></i>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                </div>

                                                <div class="d-flex row align-items-center">
                                                    <div class="col-auto">
                                                        <div class="custom-control custom-radio mb-2">
                                                            <input type="radio" id="file2" name="uploadtype"
                                                                   value="uploadbam"
                                                                   class="custom-control-input">
                                                            <label class="custom-control-label" for="file2">
                                                                BQSR <a href="javascript:;" class="text-danger"
                                                                        data-container="body" data-html="true"
                                                                        data-trigger="focus" data-toggle="popover"
                                                                        data-placement="top"
                                                                        data-content="Base Quality Score Recalibration by GATK"><i
                                                                    class="fa fa-question-circle"></i></a> BAM Files
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-none mb-2" data-id="file2">
                                                    <div class="d-flex mb-2">
                                                        <input type="file" id="excel2"
                                                               class="form-control form-control-sm w-50 mr-2">
                                                        <button type="button" onclick="uploadBamExcel()"
                                                                class="btn btn-primary btn-sm mr-2 text-nowrap">上传
                                                        </button>
                                                        <a th:href="@{/analysis/somatic-cnvs/downloadBamTemplate}"
                                                           class="btn btn-link btn-sm text-nowrap"><i
                                                                class="fa fa-download mr-1"></i>下载模版</a>
                                                    </div>
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered table-sm table-middle table-center">
                                                            <thead class="thead-light">
                                                            <tr>
                                                                <td width="180">RunName</td>
                                                                <td>Tumor</td>
                                                                <td>Normal</td>
                                                                <td width="50"></td>

                                                            </tr>
                                                            </thead>
                                                            <tbody id="bam-table" class="input-file-table">
                                                            <tr>

                                                                <td class="td-input">
                                                                    <input type="text" onchange="showPnoListTable();"
                                                                           class="form-control text-center runName">
                                                                </td>
                                                                <td class="td-input">
                                                                    <div class="input-group input-group-sm">
                                                                        <div class="input-group-prepend">
                                                                            <button class="btn btn-primary btn-sm"
                                                                                    type="button"
                                                                                    onclick="showFileModal(this)">Select
                                                                            </button>
                                                                        </div>
                                                                        <div class="input-group-prepend">
                                                                          <span class="input-group-text">
                                                                            <em class="seled"></em>
                                                                          </span>
                                                                        </div>
                                                                    </div>
                                                                </td>

                                                                <td class="td-input">
                                                                    <div class="input-group input-group-sm">
                                                                        <div class="input-group-prepend">
                                                                            <button class="btn btn-primary btn-sm"
                                                                                    type="button"
                                                                                    onclick="showFileModal(this)">Select
                                                                            </button>
                                                                        </div>
                                                                        <div class="input-group-prepend">
                                                                          <span class="input-group-text">
                                                                            <em class="seled"></em>
                                                                          </span>
                                                                        </div>
                                                                    </div>
                                                                </td>

                                                                <td>
                                                                    <i class="fa fa-plus-square text-primary"
                                                                       onclick="addRow(this)"></i>
                                                                    <i class="fa fa-minus-square text-muted ml-1"
                                                                       onclick="removeRow(this)"></i>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                </div>

                                            </div>
                                        </div>


                                        <h6 class="text-primary border-bottom pb-2">Quality Control</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row align-items-center mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input type="radio" class="custom-control-input" id="qc1"
                                                               name="qcmethod" value="trimmomatic" checked>
                                                        <label for="qc1"
                                                               class="custom-control-label">Trimmomatic</label>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Mapping</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row align-items-center">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Species</label>
                                                <div class="col-xl-4 col-lg-3 col-md-8">
                                                    <select class="form-control" name="species">
                                                        <option value="human" selected>Homo sapiens（Human）</option>
                                                    </select>
                                                </div>
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label">Version</label>
                                                <div class="col-xl-4 col-lg-3 col-md-4">
                                                    <select class="form-control" name="specVersion">
                                                        <option value="hg38" selected>hg38(GRCh38)</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-10 offset-xl-2">
                                                    <span class="d-block pt-2">
                                                       <small>Or if you want to use your own genome, please contact us</small>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="form-group row align-items-center mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input type="radio" class="custom-control-input" id="md1"
                                                               name="mappingmethod" value="bwa" checked>
                                                        <label for="md1" class="custom-control-label">BWA</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Somatic CNVs Calling</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row align-items-center mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Method</label>
                                                <div class="col-xl-4 col-lg-3 col-md-8">
                                                    <div class="custom-control custom-radio custom-control-inline">
                                                        <input type="radio" class="custom-control-input" id="md2"
                                                               name="variancemethod" checked>
                                                        <label for="md2" class="custom-control-label">GATK</label>
                                                    </div>
                                                </div>
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label">Version</label>
                                                <div class="col-xl-4 col-lg-3 col-md-4">
                                                    <select name="gatk_version" class="form-control">
                                                        <option value="4.1.8.1">4.1.8.1</option>
                                                        <option value="4.1.9.0">4.1.9.0</option>
                                                        <option value="4.2.2.0">4.2.2.0</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-10 offset-xl-2">
                                                    <span class="d-block">
                                                    <small>Or if you want to use other GATK version, please contact us.</small>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="form-group row mb-0" data-id="pn2">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Panel of
                                                    Normal(PON)</label>
                                                <div class="col-xl-10 col-lg-9 col-md-8 form-inline">
                                                    <div class="radio-collapse py-2">
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered text-center table-sm mb-0"
                                                                   style="width: 300px">
                                                                <thead>
                                                                <tr>
                                                                    <th>RunName</th>
                                                                    <th width="80">normal</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody id="pone_list">
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <div style="padding-left: 10px;">
                                                        <a href="javascript:void(0);" onclick="showPnoListTable()"
                                                           class="btn btn-primary btn-sm">Select All</a>
                                                    </div>

                                                </div>

                                            </div>

                                        </div>
                                        <h6 class="text-primary border-bottom pb-2">Filter & Statistics</h6>
                                        <div class="pl-4 pb-2">
                                            <div class="form-group row mb-0">
                                                <label class="col-xl-2 col-lg-3 col-md-4 col-form-label pr-0">Regions
                                                    file <a href="javascript:;" class="text-danger"
                                                            data-container="body" data-trigger="focus"
                                                            data-toggle="popover" data-placement="top"
                                                            data-content="This file is used to calculate coverage."><i
                                                            class="fa fa-question-circle"></i></a></label>

                                                <div class="col-xl-10 col-lg-9 col-md-8">
                                                    <div class="d-flex radio-collapse py-2">

                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input" id="rf2"
                                                                   name="exon_bed" value="self" checked>
                                                            <label for="rf2" class="custom-control-label">upload your
                                                                own target regions <a href="javascript:;"
                                                                                      class="text-danger"
                                                                                      data-container="body"
                                                                                      data-trigger="focus"
                                                                                      data-html="true"
                                                                                      data-toggle="popover"
                                                                                      data-placement="top"
                                                                                      data-content="The uploaded file should be bed or interval format, you can click <a target='_blank' href='https://gatk.broadinstitute.org/hc/en-us/articles/360035531852-Intervals-and-interval-lists'>here</a> to find more details."><i
                                                                        class="fa fa-question-circle"></i></a></label>
                                                        </div>
                                                        <div class="custom-control custom-radio custom-control-inline">
                                                            <input type="radio" class="custom-control-input" id="rf3"
                                                                   name="exon_bed" value="NONE">
                                                            <label for="rf3" class="custom-control-label">WGS</label>
                                                        </div>
                                                    </div>
                                                    <div data-id="rf2">
                                                        <div class="d-flex align-items-center td-input">
                                                            <span class="text-muted pr-2">Upload:</span>
                                                            <div class="input-group input-group-sm">
                                                                <div class="input-group-prepend">
                                                                    <button class="btn btn-outline-secondary btn-sm"
                                                                            type="button"
                                                                            onclick="showSelectFileModal(this)">Select
                                                                    </button>
                                                                </div>
                                                                <div class="input-group-prepend" id="exon_file_div">
                                                                      <span class="input-group-text">
                                                                        <em class="seled"></em>
                                                                      </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>


                                    </div>


                                </form>

                            </div>

                            <div class="text-center mb-3">
                                <a href="javascript:void(0)" onclick="submitForm(this)"
                                   class="btn btn-outline-primary btn-custom"><span>Submit</span><i
                                        class="fa fa-long-arrow-right"></i></a>
                            </div>
                        </div>

                    </div>
                </div>
            </main>
        </div>
    </div>
    <div id="file-modal"></div>
</div>

<th:block layout:fragment="custom-script">
    <script th:src="@{/js/jquery-ui.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree-all.min.js}"></script>
    <script th:src="@{/js/jquery.fancytree.table.js}"></script>
    <script th:src="@{/js/file-modal.js}"></script>

    <script>
        $("#file-modal").fileModal('/analysis/fileTree', 1);

        $('.radio-collapse input[type=radio]').click(function () {
            $(this).parents('.radio-collapse').find('input').each(function () {
                var id = $(this).attr('id')
                if (id) {
                    $('[data-id="' + id + '"]').addClass('d-none')
                }
            })
            var selfId = $(this).attr('id')
            $('[data-id="' + selfId + '"]').removeClass('d-none')

            if (selfId === 'pn2') {
            }
        });

        $('#file1,#file2').click(function () {
            var id = $(this).attr('id')
            if (id == "file1") {
                $('#qc1,#md1').prop('checked', true).removeAttr('disabled')
            } else {
                $('#qc1,#md1').prop('checked', false).attr('disabled', 'disabled')
            }
        });

        function showPnoListTable() {
            let uploadtype = $('input[name="uploadtype"]:checked').val();
            if (!uploadtype) {
                $("#pone_list").html('');
                return;
            }
            let i = 0;
            let groupSet = new Set();
            if (uploadtype == 'fastq') {
                $("#sample-table").find("tr").each(function () {
                    var runName = $(this).find("td:eq(0)").find('input.runName').val() || '';
                    var lNormal = $(this).find("td:eq(3)").find('input[type="hidden"]:eq(0)').val() || '';
                    var rNormal = $(this).find("td:eq(4)").find('input[type="hidden"]:eq(0)').val() || '';
                    if ($.trim(runName) !== '' && (lNormal !== '' || rNormal !== '')) {
                        groupSet.add(runName);
                        i++;
                    }
                });
            } else if (uploadtype == 'uploadbam') {
                $("#bam-table").find("tr").each(function () {
                    var runName = $(this).find("td:eq(0)").find('input.runName').val() || '';
                    var normal = $(this).find("td:eq(2)").find('input[type="hidden"]:eq(0)').val() || '';
                    if ($.trim(runName) !== '' && (normal !== '')) {
                        groupSet.add(runName);
                        i++;
                    }
                });
            }

            if (groupSet.size === 0) {
                $("#pone_list").html('');
                return;
            }
            if (i !== groupSet.size) {
                layer.msg("run name must be unique");
                $("#pone_list").html('');
                return;
            }

            var html = [];
            groupSet.forEach(function (idx, name) {
                html.push('<tr><td>' + name + '</td><td><input type="checkbox" value="' + name + '" checked></td></tr>')
            })
            $("#pone_list").html(html.join(''));
        }

        var _selectBtn;
        var _selectFile;
        $("#file-modal").on('__SELECT_FILES__', function (e, data) {
            var nodes = data.nodes || []
            if (nodes.length === 0) {
                return;
            }
            if (nodes.length > 1) {
                layer.msg("Please select only one file.");
                return;
            }

            var html = [];
            $.each(nodes, function (i, node) {
                var filePath = node.path;
                var fileName = node.name;
                var fileSize = node.size;

                html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '');
                html.push('<input type="hidden" value="' + filePath + '">');
                html.push('<input type="hidden" value="' + fileName + '">');
                html.push('<input type="hidden" value="' + fileSize + '">');
                html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>');
                html.push('</b>');
            });

            if (_selectFile) {
                if (nodes.length > 1) {
                    layer.msg("only one file can be selected");
                    return;
                }
                $(_selectFile).parent().next().find('em.seled:first').html(html.join(''));
            } else {
                $(_selectBtn).parent().next().find('em.seled:first').html(html.join(''));
            }

            showPnoListTable();

            $('[data-toggle="tooltip"]').tooltip();
        });

        function showFileModal(_this) {
            _selectBtn = _this;
            _selectFile = null;

            var selectIds = [];
            $(_this).parents("td:first").find("em").find("b.text-primary").each(function () {
                selectIds.push($(this).find("input[type=hidden]:eq(0)").val());
            });
            let uploadtype = $('input[name="uploadtype"]:checked').val();
            $("#file-modal").trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: selectIds,
                func: function (data) {
                    let filename_reg;
                    if (uploadtype == 'uploadbam') {
                        filename_reg = !data.name || /^.*\.bam$/i.test(data.name);
                    } else {
                        filename_reg = !data.name || /^.*\.fq$/i.test(data.name) || /^.*\.fastq$/i.test(data.name) || /^.*\.fastq\.gz$/i.test(data.name) || /^.*\.fq\.gz^/i.test(data.name);
                    }
                    return filename_reg;
                }
            });
        }

        function showSelectFileModal(_this) {
            _selectBtn = null;
            _selectFile = _this;
            var selectId = $(_this).parent().next().find("input[type=hidden]:eq(0)").val();

            $("#file-modal").trigger('__SHOW_SELECT_FILES_MODAL__', {
                selectIds: [selectId]
            });
        }

        function addRow(_this) {
            var trClone = $(_this).parents("tbody:first").find("tr").eq(0).clone(true);
            trClone.find('em.seled').html("");
            $(_this).parents("tbody:first").append(trClone);

            showPnoListTable();
        }

        function removeRow(_this) {
            if ($(_this).parents("tbody:first").find("tr").length < 2) {
                return;
            }
            $(_this).parents("tr:first").remove();

            showPnoListTable();
        }

        function removeFile(_this) {
            $(_this).parent().parent().remove();
            showPnoListTable();
        }

        function validate() {
            if ($.trim($('input[name="taskName"]').val()).length == 0) {
                layer.msg("Task Name must not be empty");
                return false;
            }

            let uploadtype = $('input[name="uploadtype"]:checked').val();
            if (!uploadtype) {
                layer.msg("please choose a Input file");
                return false;
            }

            var i = 0;
            var val_r1 = true;
            var groupSet = new Set();
            if (uploadtype == 'fastq') {
                $("#sample-table").find("tr").each(function () {
                    var runName = $(this).find("td:eq(0)").find('input.runName').val();
                    if ($.trim(runName) != '') {
                        groupSet.add(runName);
                        i++;
                    }
                    let r1TumorLength = $(this).find("td:eq(1)").find('b.text-primary').length;
                    let r1NormalLength = $(this).find("td:eq(3)").find('b.text-primary').length;
                    if (r1TumorLength === 0 && r1NormalLength === 0) {
                        val_r1 = false;
                    }
                });

                if (!val_r1) {
                    layer.msg("please must select R1(tumor) file or R1(nomal) file");
                    return false;
                }
            } else if (uploadtype == 'uploadbam') {
                $("#bam-table").find("tr").each(function () {
                    var runName = $(this).find("td:eq(0)").find('input.runName').val();
                    if ($.trim(runName) != '') {
                        groupSet.add(runName);
                        i++;
                    }

                    if ($(this).find("td:eq(1)").find('b.text-primary').length === 0 && $(this).find("td:eq(2)").find('b.text-primary').length === 0) {
                        val_r1 = false
                    }
                });

                if (!val_r1) {
                    layer.msg("please must select Tumor file or Normal file");
                    return false;
                }
            } else {
                layer.msg("Error uploadtype");
                return false;
            }

            if (groupSet.size == 0) {
                layer.msg("please input run name");
                return false;
            }
            if (i != groupSet.size) {
                layer.msg("run name must be unique");
                return false;
            }

            return true;
        }

        function submitForm(_this) {
            var res = validate();
            if (!res) {
                return;
            }
            var formData = new FormData();

            let ponSize = 0;
            $("#pone_list").find(':checked:checked').each(function (i, item) {
                formData.append("ponNames['" + i + "']", $(item).val());
                ponSize++;
            })
            if (ponSize == 0) {
                layer.msg("PON must not be empty");
                return false;
            }

            let uploadtype = $('input[name="uploadtype"]:checked').val();
            formData.append("taskName", $.trim($('input[name="taskName"]').val()));
            formData.append('uploadtype', uploadtype);

            const isFastq = (uploadtype == 'fastq');
            let tableId = isFastq ? 'sample-table' : 'bam-table';
            let tumorCount = 0;
            $("#" + tableId).find("tr").each(function (index) {
                let runName = $(this).find("td:eq(0)").find('input.runName').val();
                formData.append("input[" + index + "].runName", runName);
                if (isFastq) {
                    $(this).find("td:eq(1)").find('b.text-primary').each(function (i) {
                        formData.append("input[" + index + "].fFastqTumor.path", $(this).find("input[type=hidden]:eq(0)").val());
                        formData.append("input[" + index + "].fFastqTumor.name", $(this).find("input[type=hidden]:eq(1)").val());
                        formData.append("input[" + index + "].fFastqTumor.size", $(this).find("input[type=hidden]:eq(2)").val());
                        tumorCount++;
                    });
                    $(this).find("td:eq(2)").find('b.text-primary').each(function (i) {
                        formData.append("input[" + index + "].rFastqTumor.path", $(this).find("input[type=hidden]:eq(0)").val());
                        formData.append("input[" + index + "].rFastqTumor.name", $(this).find("input[type=hidden]:eq(1)").val());
                        formData.append("input[" + index + "].rFastqTumor.size", $(this).find("input[type=hidden]:eq(2)").val());
                        tumorCount++;
                    });
                    $(this).find("td:eq(3)").find('b.text-primary').each(function (i) {
                        formData.append("input[" + index + "].fFastqNormal.path", $(this).find("input[type=hidden]:eq(0)").val());
                        formData.append("input[" + index + "].fFastqNormal.name", $(this).find("input[type=hidden]:eq(1)").val());
                        formData.append("input[" + index + "].fFastqNormal.size", $(this).find("input[type=hidden]:eq(2)").val());
                    });
                    $(this).find("td:eq(4)").find('b.text-primary').each(function (i) {
                        formData.append("input[" + index + "].rFastqNormal.path", $(this).find("input[type=hidden]:eq(0)").val());
                        formData.append("input[" + index + "].rFastqNormal.name", $(this).find("input[type=hidden]:eq(1)").val());
                        formData.append("input[" + index + "].rFastqNormal.size", $(this).find("input[type=hidden]:eq(2)").val());
                    });
                } else {
                    $(this).find("td:eq(1)").find('b.text-primary').each(function (i) {
                        formData.append("input[" + index + "].fFastqTumor.path", $(this).find("input[type=hidden]:eq(0)").val());
                        formData.append("input[" + index + "].fFastqTumor.name", $(this).find("input[type=hidden]:eq(1)").val());
                        formData.append("input[" + index + "].fFastqTumor.size", $(this).find("input[type=hidden]:eq(2)").val());
                        tumorCount++;
                    });
                    $(this).find("td:eq(2)").find('b.text-primary').each(function (i) {
                        formData.append("input[" + index + "].fFastqNormal.path", $(this).find("input[type=hidden]:eq(0)").val());
                        formData.append("input[" + index + "].fFastqNormal.name", $(this).find("input[type=hidden]:eq(1)").val());
                        formData.append("input[" + index + "].fFastqNormal.size", $(this).find("input[type=hidden]:eq(2)").val());
                    });
                }
            });

            if (tumorCount == 0) {
                layer.msg("At least one Tumor file must exist");
                return false;
            }

            var qcmethod = isFastq ? ($("input[name='qcmethod']:checked").val() || '') : null;
            var specVersion = $("select[name='specVersion']").val() || '';
            var mappingMethod = isFastq ? ($("input[name='mappingmethod']:checked").val() || '') : null;
            var gatkVersion = $("select[name='gatk_version']").val() || '';
            var exonBed = $("input[name='exon_bed']:checked").val() || '';

            formData.append("qcMethod", qcmethod);
            formData.append("refVersion", specVersion);
            formData.append("mappingMethod", mappingMethod);

            formData.append("gatkVersion", gatkVersion);


            formData.append("exonBed", exonBed);
            if ('self' === exonBed) {
                if ($("#exon_file_div").find("input[type=hidden]").length === 0) {
                    layer.msg("please select target regions file");
                    return false;
                }
                formData.append("exonFileVo.path", $("#exon_file_div").find("input[type=hidden]:eq(0)").val());
                formData.append("exonFileVo.name", $("#exon_file_div").find("input[type=hidden]:eq(1)").val());
                formData.append("exonFileVo.size", $("#exon_file_div").find("input[type=hidden]:eq(2)").val());
            }

            if ($(_this).data('loading') == 'true') {
                return;
            }
            $(_this).data('loading', 'true');

            $.ajax({
                url: '/analysis/somatic-cnvs/createTask',
                dataType: 'json',
                type: 'post',
                processData: false,
                contentType: false,
                data: formData,
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg('submit success');
                        var id = result.data;
                        setTimeout(function () {
                            var _context_path = $("meta[name='_context_path']").attr("content");
                            window.location.href = $.trim(_context_path) + '/analysis/somatic-cnvs/list?batch=' + id;
                        }, 2000);
                    }
                },
                complete: function () {
                    $(_this).data('loading', 'false');
                }
            });
        }

        function uploadExcel() {
            if ($("#excel").val() === '') {
                layer.msg('please select a file');
                return;
            }
            var formData = new FormData();
            formData.append('file', $('#excel')[0].files[0])
            $.ajax({
                url: '/analysis/somatic/uploadTemplate',
                data: formData,
                dataType: 'json',
                type: 'post',
                async: false,
                processData: false,
                contentType: false,
                success: function (result) {
                    if (result.success) {
                        var data = result.data || [];
                        if (data.length === 0) {
                            layer.msg("no data");
                            return;
                        }
                        var trs = [];
                        $.each(data, function (idx, item) {
                            var html = ['<tr>'];
                            html.push('<td class="td-input"> <input type="text" onchange="showPnoListTable();" class="form-control text-center runName" value="' + $.trim(item.runName) + '"></td>');

                            html.push('<td class="td-input">');
                            html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>');
                            html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">');
                            obtainTr(html, item.r1Tumor)
                            html.push('</em></span></div></td>');

                            html.push('<td class="td-input">');
                            html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>');
                            html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">');
                            obtainTr(html, item.r2Tumor)
                            html.push('</em></span></div></td>');

                            html.push('<td class="td-input">');
                            html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>');
                            html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">');
                            obtainTr(html, item.r1Normal)
                            html.push('</em></span></div></td>');

                            html.push('<td class="td-input">');
                            html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>');
                            html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">');
                            obtainTr(html, item.r2Normal)
                            html.push('</em></span></div></td>');

                            html.push('<td><i class="fa fa-plus-square text-primary" onclick="addRow(this)"></i><i class="fa fa-minus-square text-muted ml-1" onclick="removeRow(this)"></i></td>')
                            trs.push(html.join(''))
                        })
                        $("#sample-table").html(trs.join(''));
                        showPnoListTable();
                    } else {
                        layer.msg(result.message);
                    }
                }
            })
        }

        function uploadBamExcel() {
            if ($("#excel2").val() === '') {
                layer.msg('please select a file');
                return;
            }
            var formData = new FormData();
            formData.append('file', $('#excel2')[0].files[0])
            $.ajax({
                url: '/analysis/somatic-cnvs/uploadBamTemplate',
                data: formData,
                dataType: 'json',
                type: 'post',
                async: false,
                processData: false,
                contentType: false,
                success: function (result) {
                    if (result.success) {
                        var data = result.data || [];
                        if (data.length === 0) {
                            layer.msg("no data");
                            return;
                        }
                        var trs = [];
                        $.each(data, function (idx, item) {
                            var html = ['<tr>'];
                            html.push('<td class="td-input"> <input type="text" onchange="showPnoListTable();" class="form-control text-center runName" value="' + $.trim(item.runName) + '"></td>');

                            html.push('<td class="td-input">');
                            html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>');
                            html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">');
                            obtainTr(html, item.tumor)
                            html.push('</em></span></div></td>');

                            html.push('<td class="td-input">');
                            html.push('<div class="input-group input-group-sm"><div class="input-group-prepend"><button class="btn btn-primary btn-sm" type="button" onclick="showFileModal(this)">Select</button></div>');
                            html.push('<div class="input-group-prepend"><span class="input-group-text"><em class="seled">');
                            obtainTr(html, item.normal)
                            html.push('</em></span></div></td>');

                            html.push('<td><i class="fa fa-plus-square text-primary" onclick="addRow(this)"></i><i class="fa fa-minus-square text-muted ml-1" onclick="removeRow(this)"></i></td>')
                            trs.push(html.join(''))
                        })
                        $("#bam-table").html(trs.join(''));
                        showPnoListTable();
                    } else {
                        layer.msg(result.message);
                    }
                }
            })
        }

        function obtainTr(html, node) {
            if (!node) {
                return;
            }
            var filePath = node.path;
            var fileName = node.name;
            var fileSize = node.size;

            html.push('<b class="text-primary" data-toggle="tooltip" data-placement="left"  title="' + fileName + '">' + fileName + '');
            html.push('<input type="hidden" value="' + filePath + '">');
            html.push('<input type="hidden" value="' + fileName + '">');
            html.push('<input type="hidden" value="' + fileSize + '">');
            html.push('<span><a href="javascript:void(0);" onclick="removeFile(this)" class="text-danger"><i class="fa fa-times-circle"></i></a></span>');
            html.push('</b>');
        }
    </script>
</th:block>
</html>
