package cn.ac.picb.vipmap.vo;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 */
@Data
public class ScrnaseqWgcnaTaskParam {
    @NotBlank
    private String baselineId;

    @NotBlank
    private String taskName;

    private String species;

    @Max(40)
    @Min(1)
    private Integer power;
    /**
     * network type
     */
    @NotBlank
    private String networkType;

    /**
     * Minimum module size
     */
    @Max(100)
    @Min(1)
    private Integer minModuleSize;

    /**
     * merge cutoff height
     */
    @DecimalMax("1.00")
    @DecimalMin("0.00")
    private Float mergeCutHeight;
}
