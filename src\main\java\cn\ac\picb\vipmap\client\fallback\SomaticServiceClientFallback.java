package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.somatic.dto.SomaticAdvTaskDTO;
import cn.ac.picb.somatic.dto.SomaticCnvsTaskDTO;
import cn.ac.picb.somatic.dto.SomaticTaskDTO;
import cn.ac.picb.somatic.po.SomaticAdvTaskPO;
import cn.ac.picb.somatic.po.SomaticCnvsTaskPO;
import cn.ac.picb.somatic.po.SomaticTaskPO;
import cn.ac.picb.somatic.vo.*;
import cn.ac.picb.vipmap.client.SomaticServiceClient;
import feign.Response;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class SomaticServiceClientFallback implements SomaticServiceClient {

    private final static String SERVER_NAME = "somatic-service";

    @Override
    public CommonResult<PageResult<SomaticTaskDTO>> findTaskPage(SomaticTaskQueryVO somaticTaskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PageResult<SomaticCnvsTaskDTO>> findCnvsTaskPage(SomaticTaskQueryVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PageResult<SomaticAdvTaskDTO>> findAdvTaskPage(SomaticTaskQueryVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<String> saveTask(SomaticTaskParamVO somaticTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<String> saveCnvsTask(SomaticCnvsTaskParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticTaskPO> findById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticTaskDTO> findDetailById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticCnvsTaskDTO> findCnvsDetailById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticAdvTaskDTO> findAdvDetailById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticTaskPO> deleteById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticCnvsTaskPO> deleteCnvsById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticAdvTaskPO> deleteAdvById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticTaskDetailVO> findTaskDetailVO(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticCnvsTaskDetailVO> findCnvsTaskDetailVO(String taskId) {
        return serverError(SERVER_NAME);
    }

    @Override
    public ResponseEntity<byte[]> getBaseQualityImg(String s, String s1) {
        return null;
    }

    @Override
    public ResponseEntity<byte[]> getGcContentImg(String s, String s1) {
        return null;
    }

    @Override
    public ResponseEntity<byte[]> getGenomeFractionCoverageImg(String s, String s1) {
        return null;
    }

    @Override
    public Response downloadVcf(String s, String s1, String s2) {
        return null;
    }

    @Override
    public Response downloadGsf(String taskId, String runName, String displayName) {
        return null;
    }

    @Override
    public Response downloadSsf(String taskId, String runName, String displayName) {
        return null;
    }

    @Override
    public Response downloadAnnotatedFile(String s, String s1, String s2) {
        return null;
    }

    @Override
    public Response downloadTemplateExcel() {
        return null;
    }

    @Override
    public Response downloadBamTemplateExcel() {
        return null;
    }

    @Override
    public CommonResult<List<SomaticValidateResultVO>> uploadTemplateExcel(MultipartFile multipartFile, String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<List<SomaticBamValidateResultVO>> uploadBamTemplateExcel(MultipartFile file, String username) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticAdvValidateResultVO> validateMafFile(SomaticFileVO mafFileVo, String username) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticBatchQueryVO> findAllDoneBatchQueryInfo(String userId) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PageResult<SomaticAdvTaskDTO>> findBatchTaskPage(SomaticTaskQueryVO somaticTaskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<String> saveAdvTask(SomaticAdvTaskParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<Object> chartAdvData(SomaticAdvChartParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public ResponseEntity<byte[]> getAdvImg(String taskId, String name) {
        return null;
    }

    @Override
    public CommonResult<SomaticTaskIdVO> findSomaticIdInfo(String userId) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticTaskIdVO> findSomaticCnvsIdInfo(String userId) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<SomaticTaskIdVO> findSomaticAdvIdInfo(String userId) {
        return serverError(SERVER_NAME);
    }
}
