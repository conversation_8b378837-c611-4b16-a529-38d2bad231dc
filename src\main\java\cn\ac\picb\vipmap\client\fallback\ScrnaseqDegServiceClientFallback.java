package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnaseq.dto.DegTaskDTO;
import cn.ac.picb.scrnaseq.po.DegTaskPO;
import cn.ac.picb.scrnaseq.vo.DegTaskParamVO;
import cn.ac.picb.scrnaseq.vo.TaskQueryVO;
import cn.ac.picb.vipmap.client.ScrnaseqDegServiceClient;
import org.springframework.stereotype.Component;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaseqDegServiceClientFallback implements ScrnaseqDegServiceClient {

    private final static String SERVER_NAME = "scrnaseq-service";

    @Override
    public CommonResult<PageResult<DegTaskDTO>> findDegTaskPage(TaskQueryVO taskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<DegTaskPO> saveDegTask(DegTaskParamVO degTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<DegTaskPO> findDegById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<DegTaskDTO> findDegDtoById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<DegTaskPO> deleteDegById(String s) {
        return serverError(SERVER_NAME);
    }
}
