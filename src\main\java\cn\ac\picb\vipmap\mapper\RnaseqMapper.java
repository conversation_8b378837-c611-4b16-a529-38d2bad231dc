package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.rnaseq.vo.RnaseqTaskParamVO;
import cn.ac.picb.rnaseq.vo.RnaseqTaskQueryVO;
import cn.ac.picb.vipmap.vo.RnaseqTaskParam;
import cn.ac.picb.vipmap.vo.RnaseqTaskSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface RnaseqMapper {

    RnaseqMapper INSTANCE = Mappers.getMapper(RnaseqMapper.class);

    RnaseqTaskQueryVO convert(RnaseqTaskSearchVO search);

    RnaseqTaskParamVO convertToVO(RnaseqTaskParam param);
}
