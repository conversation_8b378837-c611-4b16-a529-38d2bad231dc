//自定义dataTables检索方式
$.fn.dataTable.ext.search.push(
  function (settings, data, dataIndex) {
    var start = parseInt($('input[name="start"]').val().replace(/-/g, "")) //获取把格式改为跟你要筛选的数据的格式一样，并转换为int类型
    var end = parseInt($('input[name="end"]').val().replace(/-/g, ""))
    var search = parseInt(data[8].replace(/-/g, "").substr(0, 8))
    if ((isNaN(start) && isNaN(end)) ||
      (isNaN(start) && search <= end) ||
      (start <= search && isNaN(end)) ||
      (start <= search && search <= end)) {
      return true
    }
    return false
  }
  )

let table
$(document).ready(function () {
  $('.input-daterange').datepicker({
    format: 'yyyy-mm-dd',
    toggleActive: true,
    autoclose: true,
    todayHighlight: true
  })
  obtainToFilterTable()
})

function filterTask () {
  // 禁用按钮防止重复点击
  $('#search-btn').attr('disabled', true)
  table.column(4).search($('input[name="searchTaskId"]').val()).column(5).search($('input[name="searchTaskName"]').val()).draw()
  setTimeout(() => { $('#search-btn').attr('disabled', false)}, 1000)
}

// 搜索过滤
function obtainToFilterTable () {
  $.ajax({
    url: `/analysis/scrnaseq/findCompleteTask/${$('input[name="type"]').val()}`,
    type: 'post',
    dataType: 'json',
    contentType: false,
    processData: false,
    success: function (result) {
      if (result.data) {
        generateResultTable(result.data)
      } else {
        layer.msg(result.message)
      }
    }
  })
}

// 生成表格
function generateResultTable (data) {
  if (data === null) {
    $('#table-result').find('table tbody').html('<tr><td colspan="11"><div class="text-center">No Data</div></td></tr>')
    return
  }
  let htmlArr = []
  for (let item of data) {
    htmlArr.push(`<tr class="fw">
                                                    <td>
                                                        <div class="custom-control custom-radio mb-1 center">
                                                            <input type="radio" id="${item.baselineTask.taskId}"  name="baselineId"
                                                                   value="${item.baselineTask.id}" clusters="${item.baselineTask.clusters}" class="custom-control-input validate[required]" onclick="changeBaselineSpan(this)">
                                                            <label class="custom-control-label"
                                                                   for="${item.baselineTask.taskId}"><span
                                                                    class="text-primary"></span></label>
                                                        </div>
                                                    </td>
                                                    <td class="text-primary">${item.genomicsTask.taskId}</td>
                                                    <td>${item.genomicsTask.taskName !== null ? item.genomicsTask.taskName : ''}</td>
                                                    <td>Basic Analysis</td>
                                                    <td class="text-primary">${item.baselineTask.taskId}</td>
                                                    <td>${item.baselineTask.taskName !== null ? item.baselineTask.taskName : ''}</td>
                                                    <td class="fw">Cluster Description</td>
                                                    <td>${item.genomicsTask.species}</td>
                                                    <td>${item.baselineTask.createTime}</td>
                                                    <td>${item.baselineTask.updateTime}</td>
                                                    <td>${item.baselineTask.useTime !== null ? item.baselineTask.useTime : ''}</td>
                                                </tr>`)
  }
  if (table) {
    table.destroy()
  }
  $('#table-result').find('table tbody').html(htmlArr.join(''))
  table = $('#table-result').find('table').DataTable({
    searching: true,
    ordering: false,
    dom: 'trilp',
    retrieve: true,
    lengthChange: false,
    autoWidth: false,
    displayLength: 5,
    columnDefs: [
      { "width": "3%", "targets": 0 },
      { "width": "9%", "targets": 1 },
      { "width": "11%", "targets": 2 },
      { "width": "10%", "targets": 3 },
      { "width": "10%", "targets": 4 },
      { "width": "10%", "targets": 5 },
      { "width": "10%", "targets": 6 },
      { "width": "10%", "targets": 7 },
      { "width": "9%", "targets": 8 },
      { "width": "9%", "targets": 9 },
      { "width": "9%", "targets": 10 },
    ],
    fnDrawCallback: function () {
      setTableStyle(this)
    }
  })
}

// 设置table的样式
function setTableStyle (_this) {
  $(_this).find('tbody tr td').each(function () {
    $(this).css('word-wrap', 'break-word')
  })
}

function changeBaselineSpan (_this) {
  $('#baselineIdSpan').text($(_this).attr("id"))
  obtainToClusterSelect(_this)
}

// 生成集群节点选择下拉框
function obtainToClusterSelect (_this) {
  let clusterList = $(_this).attr('clusters').split(";")
  if ($(_this).attr('clusters') === 'null') {
    $('.mClusterSelect').html('<option value="">Please select ID</option>')
    $('.mClusterSelect1').html('<option value="">Please select ID</option>')
    $('.mClusterSelect2').html('<option value="">Please select ID</option>')
    return
  }
  let htmlArr = []
  htmlArr.push('<option value="">Choose</option>')
  for (let item of clusterList) {
    htmlArr.push(`<option value="${item}">${item}</option>`)
  }
  $('.mClusterSelect').html(htmlArr.join(''))
  $('.mClusterSelect1').html(htmlArr.join(''))
  $('.mClusterSelect2').html(htmlArr.join(''))
}
