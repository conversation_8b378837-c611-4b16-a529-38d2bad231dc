<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis-genomics')}"></div>
            <main style="max-width: calc(100% ) !important;">
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/form/genomics}">Add Task</a>
                            <a th:href="@{/analysis/scrnaseq/list(type='genomics')}" class="active">Task list</a>
                        </div>
                    </div>
                    <div id="table-content">
                        <div id="filter-form" class="form-custom" style="padding: 0 15px;">
                            <div class="form-group-box" style="border: transparent;">
                                <div class="d-flex flex-wrap pl-4 pt-2">

                                    <div class="form-group row align-items-center mr-1 ml--8">
                                        <label class="mx-2 font-12">Task ID</label>
                                        <div class="col-xl-4 col-lg-4 col-md-4 search">
                                            <input class="form-control form-control-sm width-100 ml--16" name="taskId"
                                                   type="text">
                                        </div>
                                    </div>
                                    <input class="form-control form-control-sm width-100 ml--16" name="type"
                                           value="genomics"
                                           type="hidden">
                                    <div class="basic-name form-group row align-items-center">
                                        <label class="mx-2 font-12">Task Name</label>
                                        <div class="col-xl-4 col-lg-4 col-md-4 search">
                                            <input class="form-control form-control-sm width-100 ml--16" name="taskName"
                                                   type="text" onkeyup="value=value.replace(/[^\w\.\/]/ig,'')">
                                        </div>
                                    </div>
                                    <div class="form-group ml-1 row align-items-center width-345">
                                        <label class="mx-2 font-12">Time</label>
                                        <div class="input-daterange input-group width-300">
                                            <input type="text" class="form-control form-control-sm max-width-97"
                                                   name="start"/>
                                            <div class="input-group-append">
                                                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                            </div>
                                            <input type="text" class="form-control form-control-sm max-width-97"
                                                   name="end"/>
                                            <div class="input-group-append">
                                                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <button id="search" onclick="filterTask()" type="button"
                                            class="btn btn-primary btn-sm h-31">
                                        Search
                                    </button>
                                </div>
                                <div class="table-result table-responsive">
                                    <table style="table-layout: fixed; width: 1058px;"
                                           data-toggle="table"
                                           class="table table-bordered table-sm table-center table-middle font-12 table-striped mb-0 treetable">
                                        <colgroup>
                                            <col width="100px">
                                            </col>
                                            <col width="100px">
                                            </col>
                                            <col width="100px">
                                            </col>
                                            <col width="50px">
                                            </col>
                                            <col width="128px">
                                            </col>
                                            <col width="105px">
                                            </col>
                                            <col width="128px">
                                            </col>
                                            <col width="108px">
                                            </col>
                                            <col width="50px">
                                            </col>
                                        </colgroup>
                                        <thead>
                                        <tr class="thead-light">
                                            <th>Task ID</th>
                                            <th>Task Type</th>
                                            <th>Task Name</th>
                                            <th>Speices</th>
                                            <th>Start time</th>
                                            <th>Status</th>
                                            <th>Status time</th>
                                            <th>Consuming</th>
                                            <th>Action</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr class="w-100">
                                            <td colspan="8" class="text-center">
                                                <div class="spinner-border text-muted"></div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script>
      $(document).ready(function () {
        $('.input-daterange').datepicker({
          format: 'yyyy-mm-dd',
          toggleActive: true,
          autoclose: true,
          todayHighlight: true
        })
        filterTask()
      })

      function filterTask () {
        // 禁用按钮防止重复点击
        $('#search').attr('disabled', true)
        let formData = new FormData()
        formData.append('taskId', $('input[name="taskId"]').val())
        formData.append('type', $('input[name="type"]').val())
        formData.append('taskName', $('input[name="taskName"]').val())
        formData.append('start', $('input[name="start"]').val())
        formData.append('end', $('input[name="end"]').val())
        $.ajax({
          url: '/analysis/scrnaseq/filter',
          type: 'post',
          dataType: 'json',
          contentType: false,
          processData: false,
          data: formData,
          success: function (result) {
            if (result.data) {
              obtainToFilterTable(result.data)
            } else {
              layer.msg(result.message)
            }
          }
        })
        setTimeout(() => { $('#search').attr('disabled', false)}, 1000)
      }

      let table;
      // 生成表格
      function obtainToFilterTable (data) {
        let content = data.content
        if (data.total === 0) {
          $('#table-content').find('table tbody').html('<tr><td colspan="9"><div class="text-center">No Data</div></td></tr>')
          return
        }

        var map = {
          '-1': "<span class='text-danger'><i class='fa fa-circle'></i>Error</span>&nbsp;&nbsp;",
          '0': '<span class="text-info"><i class="fa fa-circle"></i>Deleted</span>&nbsp;&nbsp;',
          '1': '<span class="text-info"><i class="fa fa-circle"></i>Prepared</span>&nbsp;&nbsp;',
          '2': '<span class="text-info"><i class="fa fa-circle"></i>Ready</span>&nbsp;&nbsp;',
          '3': '<span class="text-info"><i class="fa fa-circle"></i>Import data</span>&nbsp;&nbsp;',
          '4': '<span class="text-success"><i class="fa fa-circle"></i>Analysis done</span>&nbsp;&nbsp;'
        }

        let htmlArr = []
        for (let item of content) {
          let task = item.genomicsTask
          htmlArr.push(`<tr>
                                            <td><span class="badge badge-primary font-weight-normal">${task.taskId}</span>
                                            </td>
                                            <td>Basic Analysis</td>
                                            <td>${task.taskName ? task.taskName : ''}</td>
                                            <td>${task.species ? task.species : ''}</td>
                                            <td>${task.createTime ? task.createTime : ''}</td>

                                            <td>${map[task.status] ? map[task.status] : ''}</td>
                                            <td>${task.updateTime ? task.updateTime : ''}</td>
                                            <td>${task.useTime ? task.useTime : ''}</td>
                                            <td>
                                                <div class='btn-group' >
                                                    <a onclick="viewTask('genomics','${task.id}')" class= 'text-primary' data-toggle='tooltip' title = 'View Result'><i class='fa fa-eye font-14'></i></a>
                                                    <a onclick="deleteTask('genomics','${task.id}')" class='text-danger' data-toggle='tooltip' title='Delete'><i class='fa fa-times font-14'></i></a>
                                                </div >
                                            </td>
                                        </tr>`)
        }

        if (table) {
          table.destroy();
        }

        $('#table-content').find('table tbody').html(htmlArr.join(''))
        table = $('#table-content').find('table').DataTable({
          searching: true,
          ordering: false,
          dom: 'trilp',
          retrieve: true,
          lengthChange: false,
          autoWidth: false,
          displayLength: 5,
          columnDefs: [
            { "width": "10%", "targets": 0 },
            { "width": "10%", "targets": 1 },
            { "width": "10%", "targets": 2 },
            { "width": "9%", "targets": 3 },
            { "width": "15%", "targets": 4 },
            { "width": "11%", "targets": 5 },
            { "width": "14%", "targets": 6 },
            { "width": "11%", "targets": 7 },
            { "width": "10%", "targets": 8 },
          ],
          fnDrawCallback: function () {
            setTableStyle(this)
          }
        })
      }

      // 设置table的样式
      function setTableStyle (_this) {
        $(_this).find('tbody tr td').each(function () {
          $(this).css('word-wrap', 'break-word')
        })
      }

      function viewTask (type, id) {
        var _context_path = $("meta[name='_context_path']").attr("content")
        var url = $.trim(_context_path) + '/analysis/scrnaseq/taskDetail?type=' + type + "&id=" + id
        window.open(url)
      }

      function deleteTask (type, id) {
        layer.confirm('<p class="text-center">Are you sure you want to delete it？</p>', { btn: ['确认', '取消'] }, function () {
          var loadLayerIndex
          $.ajax({
            url: "/analysis/scrnaseq/deleteTask",
            data: { "id": id, "type": type },
            dataType: 'json',
            async: false,
            method: 'post',
            beforeSend: function () {
              loadLayerIndex = layer.load(1, {
                shade: [0.1, '#fff'] //0.1透明度的白色背景
              })
            },
            success: function (result) {
              if (result.success) {
                layer.msg("Deleted success", { time: 500 }, function () {
                    filterTask();
                })
              } else {
                layer.alert(result.message, { icon: 2 })
              }
            },
            complete: function () {
              layer.close(loadLayerIndex)
            }
          })
        })
      }
    </script>
</th:block>
</html>
