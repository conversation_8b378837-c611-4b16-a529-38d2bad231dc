<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:with="totalPages=${T(Integer).parseInt(#numbers.formatInteger(T(Math).ceil(pageResult.total / pageParam.size),1)) + 1 }, first=${pageParam.page <= 0}, last=${pageParam.page + 1 >= totalPages}">
<div class="d-flex justify-content-between align-items-center pt-1"
     th:if="${totalPages > 1}">
    <div class="d-flex align-items-center font-14">
        <span class="text-nowrap">Total：</span>
        <b class="text-danger" th:text="${pageResult.total}">11</b>
        <span class="text-nowrap">，Change page size</span>
        <select class="form-control form-control-sm ml-1" id="change-page-size" name="size">
            <option value='10' th:selected="${pageParam.size==10}">10</option>
            <option value='20' th:selected="${pageParam.size==20}">20</option>
            <option value='50' th:selected="${pageParam.size==50}">50</option>
        </select>
        <span class="text-nowrap">，Go to</span>
        <input id="change-page-num" type="text" th:max="${totalPages}" th:min="0" class="form-control form-control-sm ml-1 mr-1 text-center"
               style="width:60px"
               th:placeholder="|total ${totalPages}|" placeholder="共2页">
        <span>page</span>
    </div>

    <ul class="pagination justify-content-end pagination-sm" data-th-if="${totalPages le 7}">
        <!-- 上一页 -->
        <li class="page-item" data-th-classappend="${first} ? 'disabled' : ''">
            <a href="javascript:void(0);"
               class="page-link"
               data-th-attr="data-pageindex=${pageParam.page} - 1"
               aria-label="Previous">
                Previous
            </a>
        </li>
        <!-- 迭代生成页码 -->
        <li class="page-item" data-th-each="i : ${#numbers.sequence(1, totalPages)}"
            data-th-classappend="${(pageParam.page + 1) eq i} ? 'active' : ''">
            <a class="page-link" data-th-attr="data-pageindex=${i} - 1" href="javascript:void(0);">
                [[${i}]]
            </a>
        </li>
        <!-- 下一页 -->
        <li class="page-item" data-th-classappend="${last} ? 'disabled' : ''">
            <a href="javascript:void(0);"
               class="page-link"
               data-th-attr="data-pageindex=${pageParam.page} + 1"
               aria-label="Next">
                Next
            </a>
        </li>
    </ul>
    <!-- 处理页数大于7 的情况 -->
    <ul class="pagination justify-content-end pagination-sm" data-th-if="${totalPages gt 7}">
        <!-- 上一页 -->
        <li class="page-item" data-th-classappend="${first} ? 'disabled' : ''">
            <a href="javascript:void(0);"
               class="page-link"
               data-th-attr="data-pageindex=${pageParam.page} - 1"
               aria-label="Previous">
                Previous
            </a>
        </li>

        <!-- 首页 -->
        <li class="page-item" data-th-classappend="${(pageParam.page + 1) eq 1} ? 'active' : ''">
            <a href="javascript:void(0);" class="page-link" data-th-attr="data-pageindex=0">1</a>
        </li>

        <!-- 当前页面小于等于4 -->
        <li class="page-item" data-th-if="${(pageParam.page + 1) le 4}"
            data-th-each="i : ${#numbers.sequence(2,5)}"
            data-th-classappend="${(pageParam.page + 1) eq i} ? 'active' : ''">
            <a class="page-link" href="javascript:void(0);" data-th-attr="data-pageindex=${i} - 1">
                [[${i}]]
            </a>
        </li>

        <li class="page-item disabled" data-th-if="${(pageParam.page + 1) le 4}">
            <a href="javascript:void(0);" class="page-link">
                ...
            </a>
        </li>

        <!-- 最后一页与当前页面之差，小于等于3 -->
        <li class="page-item disabled" data-th-if="${(totalPages - (pageParam.page + 1)) le 3}">
            <a href="javascript:void(0);" class="page-link">
                ...
            </a>
        </li>
        <li class="page-item" data-th-if="${(totalPages - (pageParam.page + 1)) le 3}"
            data-th-each="i : ${#numbers.sequence(totalPages - 4, totalPages - 1)}"
            data-th-classappend="${(pageParam.page + 1) eq i} ? 'active' : ''">
            <a class="page-link" href="javascript:void(0);" data-th-attr="data-pageindex=${i} - 1">
                [[${i}]]
            </a>
        </li>

        <!-- 最后一页与当前页面之差大于3，且  当前页面大于4-->
        <li class="page-item disabled"
            data-th-if="${((pageParam.page + 1) gt 4) && ((totalPages - (pageParam.page + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link">
                ...
            </a>
        </li>
        <li class="page-item"
            data-th-if="${((pageParam.page + 1) gt 4) && ((totalPages - (pageParam.page + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link" data-th-attr="data-pageindex=${pageParam.page} - 1">[[${pageParam.page}]]</a>
        </li>
        <li class="page-item active"
            data-th-if="${((pageParam.page + 1) gt 4) && ((totalPages - (pageParam.page + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link" data-th-attr="data-pageindex=${pageParam.page}">[[${pageParam.page
                + 1}]]</a>
        </li>
        <li class="page-item"
            data-th-if="${((pageParam.page + 1) gt 4) && ((totalPages - (pageParam.page + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link" data-th-attr="data-pageindex=${pageParam.page} + 1">[[${pageParam.page
                + 2}]]</a>
        </li>

        <li class="page-item disabled"
            data-th-if="${((pageParam.page + 1) gt 4) && ((totalPages - (pageParam.page + 1)) gt 3 )}">
            <a href="javascript:void(0);" class="page-link">
                ...
            </a>
        </li>

        <!-- 最后一页 -->
        <li class="page-item" data-th-classappend="${(pageParam.page + 1) eq totalPages} ? 'active' : ''">
            <a href="javascript:void(0);" class="page-link"
               data-th-attr="data-pageindex=${totalPages} - 1">[[${totalPages}]]</a>
        </li>

        <!-- 下一页 -->
        <li class="page-item" data-th-classappend="${last} ? 'disabled' : ''">
            <a href="javascript:void(0);"
               class="page-link"
               data-th-attr="data-pageindex=${pageParam.page} + 1"
               aria-label="Next">
                Next
            </a>
        </li>
    </ul>
</div>
<script type="text/javascript">
    var size = [[${pageParam.size}]];

    function initPage(size, index) {
        var $form = $("#search-form");
        if (!$form[0]) {
            return;
        }
        var $sizeInput = $form.find("input[name='size']");
        var hiddenSizeDiv = ['<input type="hidden" name="size" value="' + size + '">'];
        if ($sizeInput[0]) {
            $sizeInput.val(size);
        } else {
            $form.append(hiddenSizeDiv.join(''));
        }

        var $pageInput = $form.find("input[name='page']");
        var hiddenPageDiv = ['<input type="hidden" name="page" value="' + index + '">'];
        if ($pageInput[0]) {
            $pageInput.val(index);
        } else {
            $form.append(hiddenPageDiv.join(''));
        }
        return $form;
    }

    $("#change-page-num").on('change', function () {
        var index = parseInt($(this).val());
        if (isNaN(index)) {
            $(this).val('');
            return;
        }
        var $form = initPage(size, index - 1);
        if ($form) {
            $form.find("._submit_btn").trigger('click');
        }
    });
    $("#change-page-size").on('change', function () {
        var size = $(this).val();
        var $form = initPage(size, 0);
        if ($form) {
            $form.find("._submit_btn").trigger('click');
        }
    });
    $("a.page-link").on('click', function () {
        var index = $(this).data("pageindex");
        var $form = initPage(size, index);
        if ($form) {
            $form.find("._submit_btn").trigger('click');
        }
    });
</script>
</html>
