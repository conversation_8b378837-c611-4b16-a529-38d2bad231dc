package cn.ac.picb.vipmap.controller;

import cn.ac.picb.ase.enums.AseTaskStatus;
import cn.ac.picb.ase.po.AseTaskPO;
import cn.ac.picb.ase.vo.AseTaskInput;
import cn.ac.picb.ase.vo.AseTaskVO;
import cn.ac.picb.common.framework.util.ResponseUtil;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageParam;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.vipmap.config.AppProperties;
import cn.ac.picb.vipmap.service.AseService;
import cn.ac.picb.vipmap.vo.AseTaskParam;
import cn.ac.picb.vipmap.vo.AseTaskSearchVO;
import cn.ac.picb.vipmap.vo.CurrentUser;
import feign.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

import static cn.ac.picb.common.framework.vo.CommonResult.success;

/**
 * <AUTHOR> Li
 * @date 2022/3/1 11:31
 */
@Controller
@RequestMapping("/analysis/ase")
@RequiredArgsConstructor
public class AseController {

    private final AseService aseService;
    private final AppProperties appProperties;

    /**
     * 转跳到ase任务创建页面
     */
    @RequestMapping("/form")
    public String form() {
        return "ase/form";
    }

    /**
     * 下载模板文件
     */
    @RequestMapping("/downloadTemplate")
    @ResponseBody
    public void downloadTemplate() {
        Response response = aseService.downloadTemplate();
        ResponseUtil.download(response);
    }

    /**
     * 解析上传的文件
     */
    @RequestMapping("/uploadTemplate")
    @ResponseBody
    public CommonResult<List<AseTaskInput>> uploadTemplate(CurrentUser user, MultipartFile file) {
        List<AseTaskInput> vos = aseService.uploadTemplate(file, user);
        return success(vos);
    }

    /**
     * 创建ase任务
     */
    @RequestMapping("/createTask")
    @ResponseBody
    public CommonResult<String> createTask(CurrentUser user, @Validated AseTaskParam param) {
        AseTaskPO task = aseService.createTask(user, param);
        return success(task.getTaskId());
    }

    @RequestMapping("/list")
    public String list(CurrentUser user, @ModelAttribute("query") AseTaskSearchVO search, PageParam pageParam, Model model) {
        PageResult<AseTaskPO> pageResult = aseService.findPage(user, search, pageParam);
        model.addAttribute("pageResult", pageResult);

        Map<Integer, String> codeDescMap = AseTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "ase/list";
    }

    @RequestMapping("/deleteTask")
    @ResponseBody
    public CommonResult<Boolean> deleteTask(String id) {
        aseService.deleteTask(id);
        return success(true);
    }

    @RequestMapping("/{id}")
    public String detail(@PathVariable("id") String id, Model model) {
        AseTaskVO vo = aseService.findTaskVO(id);
        model.addAttribute("vo", vo);

        Map<Integer, String> codeDescMap = AseTaskStatus.statusMap(appProperties.getLocale());
        model.addAttribute("codeDescMap", codeDescMap);
        return "ase/detail";
    }

    @RequestMapping("/download")
    @ResponseBody
    public void downloadResult(String taskId) {
        Response response = aseService.downloadResult(taskId, "");
        ResponseUtil.download(response);
    }

}
