package cn.ac.picb.vipmap.mapper;

import cn.ac.picb.somatic.vo.SomaticAdvTaskParamVO;
import cn.ac.picb.somatic.vo.SomaticCnvsTaskParamVO;
import cn.ac.picb.somatic.vo.SomaticTaskParamVO;
import cn.ac.picb.somatic.vo.SomaticTaskQueryVO;
import cn.ac.picb.vipmap.vo.SomaticAdvTaskParam;
import cn.ac.picb.vipmap.vo.SomaticCnvsTaskParam;
import cn.ac.picb.vipmap.vo.SomaticTaskParam;
import cn.ac.picb.vipmap.vo.SomaticTaskSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface SomaticMapper {

    SomaticMapper INSTANCE = Mappers.getMapper(SomaticMapper.class);

    SomaticTaskQueryVO convertToQueryVO(SomaticTaskSearchVO queryVO);

    SomaticTaskParamVO convertToVO(SomaticTaskParam param);

    SomaticAdvTaskParamVO convertToAdvVO(SomaticAdvTaskParam param);

    SomaticCnvsTaskParamVO convertToCnvsVO(SomaticCnvsTaskParam param);

}
