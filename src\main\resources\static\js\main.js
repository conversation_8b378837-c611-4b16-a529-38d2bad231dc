function setPageContentHeight () {
  $('.page-content>div>div:last').css('min-height', $(window).height() - $('header').outerHeight() - $('footer').outerHeight() - 60)
}

setPageContentHeight()
$(window).resize(function () {
  setPageContentHeight()
})
$('[data-toggle="tooltip"]').tooltip()
$('[data-tips="tooltip"]').tooltip()
$('[data-toggle="popover"]').popover()

function isRepeat (arr) {
  let hash = []
  for (let i in arr) {
    let v = JSON.stringify(arr[i])
    if (hash.indexOf(v) !== -1) {
      return true
    }
    // 不存在该元素，则赋值为true，可以赋任意值，相应的修改if判断条件即可
    hash.push(v)
  }
  return false
}

var _context_path = $("meta[name='_context_path']").attr("content")
