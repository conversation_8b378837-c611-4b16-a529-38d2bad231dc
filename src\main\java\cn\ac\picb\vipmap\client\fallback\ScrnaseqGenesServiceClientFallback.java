package cn.ac.picb.vipmap.client.fallback;

import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.scrnaseq.dto.GenesTaskDTO;
import cn.ac.picb.scrnaseq.po.GenesTaskPO;
import cn.ac.picb.scrnaseq.vo.GenesTaskParamVO;
import cn.ac.picb.scrnaseq.vo.TaskQueryVO;
import cn.ac.picb.vipmap.client.ScrnaseqGenesServiceClient;
import org.springframework.stereotype.Component;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 */
@Component
public class ScrnaseqGenesServiceClientFallback implements ScrnaseqGenesServiceClient {

    private final static String SERVER_NAME = "scrnaseq-service";


    @Override
    public CommonResult<PageResult<GenesTaskDTO>> findGenesTaskPage(TaskQueryVO taskQueryVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GenesTaskPO> saveGenesTask(GenesTaskParamVO genesTaskParamVO) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GenesTaskPO> findGenesById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GenesTaskDTO> findGenesDtoById(String s) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<GenesTaskPO> deleteGenesById(String s) {
        return serverError(SERVER_NAME);
    }
}
