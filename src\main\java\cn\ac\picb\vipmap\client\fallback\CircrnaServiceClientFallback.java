package cn.ac.picb.vipmap.client.fallback;


import cn.ac.picb.circrna.po.CircrnaTaskPO;
import cn.ac.picb.circrna.vo.CircrnaTaskInput;
import cn.ac.picb.circrna.vo.CircrnaTaskParamVO;
import cn.ac.picb.circrna.vo.CircrnaTaskQueryVO;
import cn.ac.picb.circrna.vo.CircrnaTaskVO;
import cn.ac.picb.common.framework.vo.CommonResult;
import cn.ac.picb.common.framework.vo.PageResult;
import cn.ac.picb.vipmap.client.CircrnaServiceClient;
import feign.Response;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.ac.picb.common.framework.vo.CommonResult.serverError;

/**
 * <AUTHOR>
 * @date 2022/3/1 15:19
 */
@Component
public class CircrnaServiceClientFallback implements CircrnaServiceClient {
    private final static String SERVER_NAME = "rnaseq-circrna-service";

    @Override
    public CommonResult<CircrnaTaskPO> saveTask(CircrnaTaskParamVO vo) {
        return serverError(SERVER_NAME);
    }

    @Override
    public Response downloadTemplateExcel() {
        return null;
    }

    @Override
    public CommonResult<List<CircrnaTaskInput>> uploadTemplate(String username, MultipartFile file) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<PageResult<CircrnaTaskPO>> findTaskPage(CircrnaTaskQueryVO query) {
        return serverError(SERVER_NAME);

    }

    @Override
    public CommonResult<CircrnaTaskPO> deleteById(String id) {
        return serverError(SERVER_NAME);
    }

    @Override
    public CommonResult<CircrnaTaskVO> findDetailById(String id) {
        return serverError(SERVER_NAME);

    }

    @Override
    public Response downloadResult(String taskId, String displayName) {
        return null;
    }
}
