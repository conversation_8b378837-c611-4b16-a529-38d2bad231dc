<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<th:block th:fragment="syncFileModal (taskId,analysisType,subUrl)">
    <div class="modal fade" id="syncToNodeModal" tabindex="-1" aria-labelledby="syncToNodeModalTitle"
         style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-primary" id="syncToNodeModalTitle">Sync result to node</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning  alert-dismissible" role="alert">
                        <p class="mb-0">This feature helps users synchronize analysis result data to the Node and
                            automatically
                            generates "Analysis" in the NODE
                        </p>
                        <p class="mb-0">
                            1. select the files you want to sync to NODE
                        </p>
                        <p class="mb-0">
                            2. you must fill in the "Target(Run No)" or "Other Target Name and Other Target Link"
                        </p>
                        <p class="mb-0">
                            3. click submit to start synchronization.
                        </p>
                        <p class="mb-0">4. The synchronization process may take several minutes. "Analysis No" in table
                            is sysnc
                            status.
                            After synchronization is complete, "Analysis No" in table will be generated. you will
                            receive an email from NODE to you</p>

                    </div>
                    <p><span class="font-weight-bolder">Analysis Id:</span> [[${taskId}]]</p>
                    <p><span class="font-weight-bolder">Analysis Type:</span> [[${analysisType}]] </p>
                    <table class="modal-table table table-bordered table-sm table-middle mb-0">
                        <thead class="modal-thead">
                        <tr>
                            <td width="30"></td>
                            <td>File Name</td>
                            <td class="d-none">File Path</td>
                            <td>Target(Run No)</td>
                            <td>Other Target Name</td>
                            <td>Other Target Link</td>
                            <td>Analysis No</td>
                        </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" onclick="syncToNode()" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        var subUrl = '[[${subUrl}]]'
        let taskId = '[[${taskId}]]'
        var analysisType = '[[${analysisType}]]'

        function openSyncToNodeModal() {
            var loadIndex = layer.msg('Loading...', {
                icon: 16,
            });
            $.ajax({
                url: `/analysis/${subUrl}/getSyncToNodeFileList?taskId=${taskId}`,
                type: "get",
                dataType: "json",
                success: function (result) {
                    if (result.success) {
                        let htmlArr = []
                        result.data.forEach(item => {
                            htmlArr.push(`<tr>
                                           <td>${item.status ? '' : `<input type="checkbox" name="file"/>`}</td>
                                           <td>${item.fileName}</td>
                                           <td class="d-none">${item.filePath}</td>
                                           <td>${item.status ? item.runNo : `<input type="text" class="w-100" value="${item.runNo == null ? '' : item.runNo}">`}</td>
                                           <td>${item.status ? (item.otherTargetName == null ? '' : item.otherTargetName) : `<input type="text" class="w-100" value="${item.otherTargetName == null ? '' : item.otherTargetName}">`}</td>
                                           <td>${item.status ? (item.otherTargetLink == null ? '' : item.otherTargetLink) : `<input type="text" class="w-100" value="${item.otherTargetLink == null ? '' : item.otherTargetLink}">`}</td>
                                           <td>${item.analysisNo == null ? (item.status !== null ? item.status : '') : item.analysisNo}</td>
                                           </tr>`)
                        })
                        $('#syncToNodeModal').find('tbody').html(htmlArr.join(''))
                        $('#syncToNodeModal').modal('show')

                    }
                    layer.close(loadIndex);
                }
            })
        }


        function syncToNode() {
            // 获取被选择的行
            let files = [];
            let checkedInputs = $('input[name="file"]:checked');

            if (checkedInputs.length == 0) {
                layer.msg("Please select the files you want to sync to NODE");
                return
            }

            for (let i = 0; i < checkedInputs.length; i++) {
                let tr = $(checkedInputs[i]).parent().parent();
                let filePath = tr.find('td').eq(2).text();
                let runNo = tr.find('td').eq(3).find('input').val();
                let otherTargetName = tr.find('td').eq(4).find('input').val();
                let otherTargetLink = tr.find('td').eq(5).find('input').val();
                if (!runNo && !otherTargetName && !otherTargetLink) {
                    layer.msg("Please input 'Run No' or 'Other Target Name and Other Target Link");
                    return
                }

                files.push({
                    'filePath': filePath,
                    'runNo': runNo,
                    'otherTargetName': otherTargetName,
                    'otherTargetLink': otherTargetLink,
                });
            }
            let data = {
                taskId: taskId,
                syncFiles: files
            }
            var loadIndex = layer.msg('Loading...', {
                icon: 16,
            });
            $.ajax({
                url: `/analysis/${subUrl}/syncToNode`,
                type: "post",
                dataType: 'json',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function (result) {
                    layer.close(loadIndex);
                    if (result.success) {
                        layer.msg("The file you selected is being synced...");
                        // 关闭模态框
                        $('#syncToNodeModal').modal('hide')
                    }
                }
            })
        }
    </script>
</th:block>
</html>