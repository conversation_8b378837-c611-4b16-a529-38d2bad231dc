/**
 * 注册节点类型，定义节点样式等
 */

// 定义泳道图最上方的标志名称

X6.Graph.registerNode(
    'lane',
    {
        inherit: 'rect',
        width: 300,
        height: 1450,
        markup: [
            {
                tagName: 'rect',
                selector: 'body',
            },
            {
                tagName: 'rect',
                selector: 'name-rect',
            },
            {
                tagName: 'rect',
                selector: 'rect2',
            },
            {
                tagName: 'text',
                selector: 'name-text',
            },
            {
                tagName: 'text',
                selector: 'rect2-text',
            },
        ],
        attrs: {
            body: {
                fill: '#FFF',
                stroke: '#C2C2C2',
                strokeWidth: 3,
                strokeDasharray: '5 5', // 虚线样式
                rx: 28, // 横向圆角
                ry: 28 // 纵向圆角
            },
            'name-rect': {
                width: 300,
                height: 55,
                fill: '#0066CC',
                stroke: '#A79D94',
                strokeWidth: 2,
                x: -1,
                rx: 10,
                ry: 10
            },
            'name-text': {
                ref: 'name-rect',
                refY: 0.5,
                refX: 0.5,
                textAnchor: 'middle',
                fill: '#fff',
                fontSize: 16,
            },
            'rect2': {
                width: 300,
                height: 60,
                fill: '#0066CC',
                stroke: '#A79D94',
                strokeWidth: 2,
                x: -1,
                y: -60,
                rx: 10,
                ry: 10
            },
            'rect2-text': {
                ref: 'rect2',
                refY: 0.5,
                refX: 0.5,
                textAnchor: 'middle',
                fontWeight: 'bold',
                fill: '#fff',
                fontSize: 20,
            },
        },
        defaults: {
            draggable: false, // 禁止拖动
        }
    },
    true,
)
X6.Graph.registerNode(
    'lane-right',
    {
        inherit: 'rect',
        width: 170,
        height: 1450,
        markup: [
            {
                tagName: 'rect',
                selector: 'body',
            },
            {
                tagName: 'rect',
                selector: 'name-rect',
            },
            {
                tagName: 'rect',
                selector: 'rect2',
            },
            {
                tagName: 'text',
                selector: 'name-text',
            },
            {
                tagName: 'text',
                selector: 'rect2-text',
            },
        ],
        attrs: {
            body: {
                fill: '#FFF',
                stroke: '#C2C2C2',
                strokeWidth: 3,
                strokeDasharray: '5 5', // 虚线样式
                rx: 28, // 横向圆角
                ry: 28 // 纵向圆角
            },
            'name-rect': {
                width: 170,
                height: 55,
                fill: '#0066CC',
                stroke: '#A79D94',
                strokeWidth: 2,
                x: -1,
                rx: 10,
                ry: 10
            },
            'name-text': {
                ref: 'name-rect',
                refY: 0.5,
                refX: 0.5,
                textAnchor: 'middle',
                fill: '#fff',
                fontSize: 16,
            },
            'rect2': {
                width: 170,
                height: 60,
                fill: '#0066CC',
                stroke: '#A79D94',
                strokeWidth: 2,
                x: -1,
                y: -60,
                rx: 10,
                ry: 10
            },
            'rect2-text': {
                ref: 'rect2',
                refY: 0.5,
                refX: 0.5,
                textAnchor: 'middle',
                fontWeight: 'bold',
                fill: '#fff',
                fontSize: 20,
            },
        },
    },
    true,
)
X6.Graph.registerNode(
    'lane-rect-left',
    {
        inherit: 'rect',
        width: 100,
        height: 49.5,
        ports: {
            groups: {
                right: {
                    position: 'right',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
                left: {
                    position: 'left',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#D7D5D5FF',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
            },
        },

        attrs: {
            body: {
                strokeWidth: 1,
                stroke: '#AECDA1',
                fill: '#A1D487',
                rx: 5, // 横向圆角半径为 10
                ry: 5 // 纵向圆角半径为 10
            },
            text: {
                fontSize: 16,
                fill: '#262626',
                fontWeight: 'bold',
            },
        }
    },
    true,
)
X6.Graph.registerNode(
    'lane-rect',
    {
        inherit: 'rect',
        width: 95,
        height: 33,
        ports: {
            groups: {
                right: {
                    position: 'right',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
                left: {
                    position: 'left',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#D7D5D5FF',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
                top: {
                    position: 'top',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
                bottom: {
                    position: 'bottom',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
            },
        },

        attrs: {
            body: {
                strokeWidth: 1,
                stroke: '#B4C9E3',
                fill: '#DAE8FC',
                rx: 5, // 横向圆角半径为 10
                ry: 5 // 纵向圆角半径为 10
            },
            text: {
                fontSize: 12,
                fill: '#625c5d',
                fontWeight: 'bold',
            },
        }
    },
    true,
)
X6.Graph.registerNode(
    'lane-rect-end',
    {
        inherit: 'rect',
        width: 95,
        height: 33,
        ports: {
            groups: {
                right: {
                    position: 'right',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
                left: {
                    position: 'left',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
            },
        },
        attrs: {
            body: {
                strokeWidth: 1,
                stroke: '#A8A8BF',
                fill: '#D0CEE2',
                rx: 5, // 横向圆角半径为 10
                ry: 5 // 纵向圆角半径为 10
            },
            text: {
                fontSize: 12,
                fill: '#262626',
            },
        }
    },
    true,
)

X6.Graph.registerNode(
    'lane-polygon',
    {
        inherit: 'polygon',
        width: 120,
        height: 45,
        ports: {
            groups: {
                right: {
                    position: 'right',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
                left: {
                    position: {
                        name: 'left',
                        args: {
                            dx: 10 // 距离形状右侧边缘的距离
                        }
                    },
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
            },
        },
        attrs: {
            body: {
                strokeWidth: 2,
                stroke: '#AECDA1',
                fill: '#A1D487',
                refPoints: '-200,0 230,0 280,10 230,20 -200,20 -160,10',
            },
            text: {
                fontSize: 14,
                fill: '#262626',
                ref: 'body',
                refX: 0.5,
                textAnchor: 'middle',
                fontWeight: 'bold',
            },
            // text2: {
            //     ref: 'text',
            //     refX: 0.5,
            //     textAnchor: 'middle',
            //     fontSize: 12,
            //     fill: '#606060',
            //     refY: 20
            // },
            image: {
                event: 'nodeTo',
                'xlink:href': './images/link.png',
                width: 16,
                height: 16,
                x: 100,
                y: 15,
            }
        },
        markup: [
            {
                tagName: 'polygon',
                selector: 'body',
                attrs: {
                    cursor: 'pointer'
                }
            },
            {
                tagName: 'image',
                selector: 'image',
                attrs: {
                    cursor: 'pointer'
                }
            },
            {
                tagName: 'text',
                selector: 'text',
                attrs: {
                    cursor: 'pointer'
                }
            },
            // {
            //     tagName: 'text',
            //     selector: 'text2',
            //     attrs: {
            //         cursor: 'pointer'
            //     }
            // }
        ]
    },
    true,
)

X6.Graph.registerNode(
    'lane-polygon2',
    {
        inherit: 'polygon',
        width: 120,
        height: 50,
        ports: {
            groups: {
                right: {
                    position: 'right',
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
                left: {
                    position: {
                        name: 'left',
                        args: {
                            dx: 10 // 距离形状右侧边缘的距离
                        }
                    },
                    attrs: {
                        circle: {
                            r: 1,
                            magnet: true,
                            stroke: '#C2C8D5',
                            strokeWidth: 1,
                            fill: '#fff',
                        },
                    },
                },
            },
        },
        attrs: {
            body: {
                strokeWidth: 2,
                stroke: '#E1B17A',
                fill: '#FAD7AC',
                refPoints: '-200,0 230,0 280,10 230,20 -200,20 -160,10',
            },
            text: {
                fontSize: 12,
                fill: '#262626',
                refY: 15
            },
            // text2: {
            //     ref: 'text',
            //     refX: 0.5,
            //     textAnchor: 'middle',
            //     fontSize: 12,
            //     fill: '#606060',
            //     refY: 20
            // },
        },
        markup: [
            {
                tagName: 'polygon',
                selector: 'body',
                attrs: {
                    cursor: 'pointer'
                }
            },
            {
                tagName: 'text',
                selector: 'text',
                attrs: {
                    cursor: 'pointer'
                }
            },
            // {
            //     tagName: 'text',
            //     selector: 'text2',
            //     attrs: {
            //         cursor: 'pointer'
            //     }
            // }
        ]
    },
    true,
)
X6.Graph.registerEdge(
    'lane-edge',
    {
        inherit: 'edge',
        attrs: {
            line: {
                stroke: '#d7d5d5',
                strokeWidth: 0.8,
                targetMarker: {
                    name: 'classic',
                    size: 8
                }
            },
        },
        label: {
            attrs: {
                label: {
                    fill: '#A2B1C3',
                    fontSize: 12,
                }

            },
        },
    },
    true,
)
