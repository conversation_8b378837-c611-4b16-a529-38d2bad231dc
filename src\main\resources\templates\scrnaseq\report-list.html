<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
    <link rel="stylesheet" th:href="@{/css/bootstrap-datepicker.min.css}">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('scrnaseq-analysis')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">scRNA-Seq Report list</h4>
                <div class="border rounded ana-content">
                    <div class="card-header card-header-tabs-basic nav" role="tablist">
                        <div class="nav-tabs-elem">
                            <a th:href="@{/analysis/scrnaseq/report/form}">Add Report</a>
                            <a th:href="@{/analysis/scrnaseq/report}" class="active">Report List</a>
                        </div>
                    </div>
                    <div class="list-group tab-content">
                        <div class="tab-pane active show fade">
                            <form th:action="@{/analysis/scrnaseq/report}" id="search-form"
                                  class="form-inline form-task mt-2">
                                <div class="d-flex flex-wrap">
                                    <div class="form-group">
                                        <label class="mx-2 font-12">Task ID</label>
                                        <input name="taskId" th:value="${search.taskId}" type="text"
                                               class="form-control form-control-sm width-100">
                                    </div>
                                    <div class="form-group">
                                        <label class="mx-2 font-12">Genomics ID</label>
                                        <input name="genomicId" th:value="${search.genomicId}" type="text"
                                               class="form-control form-control-sm width-100">
                                    </div>
                                    <div class="form-group">
                                        <label class="mx-2 font-12">Baseline ID</label>
                                        <input name="baselineId" th:value="${search.baselineId}" type="text"
                                               class="form-control form-control-sm width-100">
                                    </div>
                                    <div class="form-group">
                                        <label class="mx-2 font-12">PAGA ID</label>
                                        <input name="pagaId" th:value="${search.pagaId}" type="text"
                                               class="form-control form-control-sm width-100">
                                    </div>
                                    <div class="form-group">
                                        <label class="mx-2 font-12">DEG ID</label>
                                        <input name="degId" th:value="${search.degId}" type="text"
                                               class="form-control form-control-sm width-100">
                                    </div>
                                    <div class="form-group">
                                        <label class="mx-2 font-12">Genes ID</label>
                                        <input name="genesId" th:value="${search.genesId}" type="text"
                                               class="form-control form-control-sm width-100">
                                    </div>
                                    <div class="form-group">
                                        <label class="mx-2 font-12">WGCNA ID</label>
                                        <input name="wgcnaId" th:value="${search.wgcnaId}" type="text"
                                               class="form-control form-control-sm width-100">
                                    </div>
                                    <div class="form-group">
                                        <label class="mx-2 font-12">Time</label>
                                        <div class="input-daterange input-group">
                                            <input autocomplete="off" type="text" class="form-control form-control-sm"
                                                   name="start"
                                                   th:value="${search.start==null?'':#calendars.format(search.start,'yyyy-MM-dd')}"/>
                                            <div class="input-group-append">
                                                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                            </div>
                                            <input autocomplete="off" type="text" class="form-control form-control-sm"
                                                   name="end"
                                                   th:value="${search.end==null?'':#calendars.format(search.end,'yyyy-MM-dd')}"/>
                                            <div class="input-group-append">
                                                <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="mx-2 font-12">Status</label>
                                        <select name="status" class="custom-select custom-select-sm">
                                            <option value="">Select</option>
                                            <option value="1" th:selected="${search.status == 1}">reporting</option>
                                            <option value="2" th:selected="${search.status == 2}">success</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-sm m-2 _submit_btn">Search</button>
                                </div>
                            </form>

                            <div class="table-responsive">
                                <table class="table table-hover table-striped table-nowrap font-12 m-0">
                                    <thead>
                                    <tr>
                                        <th scope="col">Report ID</th>
                                        <th scope="col">Parameters</th>
                                        <th scope="col">Start time</th>
                                        <th scope="col">Status</th>
                                        <th scope="col">Status time</th>
                                        <th scope="col">Consuming</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <th:block th:unless="${#lists.isEmpty(pageResult.content)}">
                                        <tr th:each="item : ${pageResult.content}">
                                            <td th:text="${item.reportTask.taskId}">200302151050411</td>
                                            <td>
                                                <div class="d-flex justify-content-between align-items-center my-1">
                                                    <div class="text-right wd-73">Genomics ID:</div>
                                                    <span th:if="${item.genomicsTaskDTO != null}">
                                                        <a href="javascript:;"
                                                           th:text="${item.genomicsTaskDTO.genomicsTask.taskId}">1220091000001</a>
                                                    </span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center my-1">
                                                    <div class="text-right wd-73">Baseline ID:</div>
                                                    <span th:if="${item.baselineTask != null}">
                                                        <a href="javascript:;" th:text="${item.baselineTask.taskId}">1220091000001</a>
                                                    </span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center my-1">
                                                    <div class="text-right wd-73">PAGA ID:</div>
                                                    <span th:if="${item.pagaTask != null}">
                                                        <a href="javascript:;" th:text="${item.pagaTask.taskId}">1220091000001</a>
                                                    </span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center my-1">
                                                    <div class="text-right wd-73">DEG ID:</div>
                                                    <span th:if="${item.degTask != null}">
                                                        <a href="javascript:;" th:text="${item.degTask.taskId}">1220091000001</a>
                                                    </span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center my-1">
                                                    <div class="text-right wd-73">Genes ID:</div>
                                                    <span th:if="${item.genesTask != null}">
                                                        <a href="javascript:;" th:text="${item.genesTask.taskId}">1220091000001</a>
                                                    </span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center my-1">
                                                    <div class="text-right wd-73">WGCNA ID:</div>
                                                    <span th:if="${item.wgcnaTask != null}">
                                                        <a href="javascript:;" th:text="${item.wgcnaTask.taskId}">1220091000001</a>
                                                    </span>
                                                </div>
                                            </td>
                                            <td th:text="${#dates.format(item.reportTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td>
                                                <th:block th:switch="${item.reportTask.status}">
                                                    <th:block th:case="-1">
                                                        <span class="text-danger"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.reportTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="1">
                                                        <span class="text-info"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.reportTask.status)}]]</span>
                                                    </th:block>
                                                    <th:block th:case="2">
                                                        <span class="text-success"><i class="fa fa-circle"></i> [[${codeDescMap.get(item.reportTask.status)}]]</span>
                                                    </th:block>
                                                </th:block>
                                            </td>
                                            <td th:text="${#dates.format(item.reportTask.updateTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                            <td th:text="${item.reportTask.useTime}">26m55s</td>
                                            <td>
                                                <div class="btn-group">
                                                    <a th:href="@{/analysis/scrnaseq/report/taskDetail(id=${item.reportTask.id})}"
                                                       class="text-primary" data-toggle="tooltip" title="View">
                                                        <i class="fa fa-eye font-14"></i>
                                                    </a>
                                                    <a href="javascript:void(0);" class="text-danger"
                                                       data-toggle="tooltip"
                                                       th:onclick="deleteTask([[${item.reportTask.id}]])"
                                                       title="Delete">
                                                        <i class="fa fa-times font-14"></i>
                                                    </a>
                                                    <a th:if="${item.reportTask.status == 2}"
                                                       th:href="@{/analysis/scrnaseq/report/download(taskId=${item.reportTask.taskId})}"
                                                       class="text-secondary" title="download">
                                                        <i class="fa fa-download font-14"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </th:block>
                                    <th:block th:if="${#lists.isEmpty(pageResult.content)}">
                                        <tr>
                                            <td colspan="10">no data</td>
                                        </tr>
                                    </th:block>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pt-1 mb-2">
                                <div th:replace="~{base/pageable}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
    <script th:src="@{/js/bootstrap-datepicker.js}"></script>
    <script>
      $(document).ready(function () {
        $('.input-daterange').datepicker({
          format: "yyyy-mm-dd",
          toggleActive: true,
          autoclose: true,
          todayHighlight: true
        })
      })

      function deleteTask (id) {
        layer.confirm('<p class="text-center">确定删除吗？</p>', { btn: ['确认', '取消'] }, function () {
          var loadLayerIndex
          $.ajax({
            url: "/analysis/scrnaseq/report/deleteTask",
            data: { "id": id },
            dataType: 'json',
            async: false,
            method: 'post',
            beforeSend: function () {
              loadLayerIndex = layer.load(1, {
                shade: [0.1, '#fff'] //0.1透明度的白色背景
              })
            },
            success: function (result) {
              if (result.code == 200) {
                layer.msg("删除成功", { time: 500 }, function () {
                  location.reload()
                })
              } else {
                layer.alert(result.message, { icon: 2 })
              }
            },
            complete: function () {
              layer.close(loadLayerIndex)
            }
          })
        })
      }
    </script>
</th:block>
</html>
