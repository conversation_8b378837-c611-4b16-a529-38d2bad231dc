package cn.ac.picb.vipmap.config;


import cn.ac.picb.vipmap.vo.CurrentUser;

/**
 * <AUTHOR>
 */
public class UserContext {

    private static final ThreadLocal<CurrentUser> USER_HOLDER = new ThreadLocal<>();

    public static void setUser(CurrentUser user) {
        USER_HOLDER.set(user);
    }

    public static CurrentUser getUser() {
        return USER_HOLDER.get();
    }

    public static void removeUser() {
        USER_HOLDER.remove();
    }
}
