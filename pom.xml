<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.5.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>cn.ac.picb</groupId>
    <artifactId>vipmap-app</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>vipmap-app</name>
    <description>Vipmap</description>

    <properties>
        <java.version>1.8</java.version>
        <lombok.version>1.18.20</lombok.version>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <lombok.mapstruct.binding>0.2.0</lombok.mapstruct.binding>

        <docker.host>unix:///var/run/docker.sock</docker.host>
        <docker.registry>docker.io</docker.registry>
        <docker.namespace>docker4maling</docker.namespace>

        <docker.plugin.version>0.33.0</docker.plugin.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>common-spring-boot-starter-web</artifactId>
            <version>2021.07.01-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>methychip-service-api</artifactId>
            <version>2022.03.18-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>scrnaseq-service-api</artifactId>
            <version>2022.1.5(1)-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>scrnasmartseq-service-api</artifactId>
            <version>2021.12.8-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>rnaseq-service-api</artifactId>
            <version>2024.07.29</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>rnaseq-ase-service-api</artifactId>
            <version>2022.03.05(1)-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>rnaseq-circrna-service-api</artifactId>
            <version>2022.03.09-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>paeam-service-api</artifactId>
            <version>2021.07.01-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>somatic-service-api</artifactId>
            <version>2021.09.24-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>germline-service-api</artifactId>
            <version>2021.07.01-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>proteomics-service-api</artifactId>
            <version>2022.03.27-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>wgbs-service-api</artifactId>
            <version>2023.08.16-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>file-service-api</artifactId>
            <version>2021.07.01-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>

        <!-- <dependency>
            <groupId>com.kakawait</groupId>
            <artifactId>cas-security-spring-boot-starter</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jasig.cas.client</groupId>
                    <artifactId>cas-client-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
            <version>3.5.1</version>
        </dependency> -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>nz.net.ultraq.thymeleaf</groupId>
            <artifactId>thymeleaf-layout-dialect</artifactId>
        </dependency>
        <dependency>
            <groupId>org.thymeleaf.extras</groupId>
            <artifactId>thymeleaf-extras-springsecurity5</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cn.ac.picb</groupId>
            <artifactId>strnaseq-service-api</artifactId>
            <version>2022.03.01-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!--jpa-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <!--mysql驱动-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok.mapstruct.binding}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <!--maven  docker 打包插件 -->
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>${docker.plugin.version}</version>
                <configuration>
                    <dockerHost>unix:///var/run/docker.sock</dockerHost>
                    <registry>${docker.registry}</registry>
                    <images>
                        <image>
                            <name>${docker.registry}/${docker.namespace}/${project.name}:${project.version}</name>
                            <alias>${project.name}</alias>
                            <build>
                                <contextDir>${project.basedir}</contextDir>
                                <dockerFile>${project.basedir}/src/main/docker/Dockerfile</dockerFile>
                            </build>
                        </image>
                    </images>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <snapshotRepository>
            <!--<id>scbit-snapshots</id>
            <url>http://nexus.scbit.org:64433/nexus/content/repositories/snapshots/</url>-->
            <id>lf-snapshots</id>
            <url>http://*************:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <!--<id>scbit-release</id>
            <url>http://nexus.scbit.org:64433/nexus/content/repositories/releases/</url>-->
            <id>lf-releases</id>
            <url>http://*************:8081/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>biosino-snapshots</id>
            <url>https://dev.biosino.org/nexus/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>biosino-releases</id>
            <url>https://dev.biosino.org/nexus/repository/maven-releases/</url>
        </repository>
    </repositories>

</project>
