package cn.ac.picb.vipmap.client;

import cn.ac.picb.scrnaseq.client.ScrnaseqDegServiceApi;
import cn.ac.picb.vipmap.client.fallback.ScrnaseqDegServiceClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "scrnaseq-service", contextId = "deg", fallback = ScrnaseqDegServiceClientFallback.class)
public interface ScrnaseqDegServiceClient extends ScrnaseqDegServiceApi {
}
