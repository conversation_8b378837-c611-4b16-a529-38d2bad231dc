<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<th:block layout:fragment="custom-style">
</th:block>
<div class="page-content" layout:fragment="content">
    <div class="container-fulid">
        <div class="d-flex">
            <div th:replace="~{base/menu :: sideBar('rnaseq-paean-introduction')}"></div>
            <main>
                <h4 class="border-bottom pb-2 mb-3">Introduction</h4>
                <div class="tool-box">
                    <div class="tool-title">Summary</div>
                    <div class="tool-content">
                        <div class="row justify-content-center">
                            <div class="col-xl-12 col-lg-12">
                                <p class="pl-3 text-muted" style="line-height: 1.5">Paean(PArallEl transcriptome ANalyzer), a CPU-GPU heterogeneous computing transcriptome quantification method which reduces both computation and time costs by order of magnitude with a comparably high precision.</p>
                                <p class="pl-3 text-muted" style="line-height: 1.5">With Paean, quantification for all RNA-seq datasets in TCGA (10866 samples) is finished in about 3400 minutes.</p>
                                <div class="text-center">
                                    <img th:src="@{/images/paean.png}" alt="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center">
                    <a th:href="@{/analysis/paean/form}" class="btn btn-outline-primary btn-custom"><span>Try it Now!</span><i class="fa fa-long-arrow-right"></i></a>
                </div>
                <div class="alert alert-info mt-3">
                    Citation： Heterogeneous computing accelerates transcriptome quantification
                </div>
            </main>
        </div>
    </div>
</div>
<th:block layout:fragment="custom-script">
</th:block>
</html>
